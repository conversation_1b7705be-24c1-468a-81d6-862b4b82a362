{% extends "base.html" %}
{% block title %}Transaction Explorer - ONNYX{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Hero Section -->
    <div class="py-16 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-6xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Transaction Explorer
                    </span>
                </h1>
                <p class="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                    Explore all transactions on the ONNYX blockchain. Track covenant activities, 
                    token transfers, and biblical tokenomics operations.
                </p>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="glass-card p-6">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex-1 min-w-64">
                    <label class="block text-sm font-orbitron font-semibold text-text-secondary mb-2">Filter by Operation</label>
                    <form method="GET" class="flex gap-2">
                        <select name="op" class="glass-input flex-1" onchange="this.form.submit()">
                            <option value="">All Operations</option>
                            {% for op_type in op_types %}
                            <option value="{{ op_type }}" {% if current_op == op_type %}selected{% endif %}>
                                {{ op_type.replace('_', ' ').title() }}
                            </option>
                            {% endfor %}
                        </select>
                        <input type="hidden" name="page" value="1">
                    </form>
                </div>
                <div class="text-sm text-text-secondary">
                    <span class="font-orbitron font-semibold">{{ total_count }}</span> transactions found
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions List -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        {% if transactions %}
        <div class="space-y-4">
            {% for tx in transactions %}
            <div class="glass-card p-6 hover:bg-glass-hover transition-all duration-300 cursor-pointer transaction-item"
                 data-tx-id="{{ tx.tx_id }}"
                 onclick="showTransactionDetails('{{ tx.tx_id }}')">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 items-center">
                    <!-- Transaction Info -->
                    <div class="lg:col-span-2">
                        <div class="flex items-center space-x-3 mb-2">
                            <span class="text-lg font-orbitron font-bold text-cyber-cyan">
                                {{ tx.op.replace('_', ' ').title() if tx.op else 'Transaction' }}
                            </span>
                            {% if tx.status == 'confirmed' %}
                            <span class="px-3 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-full font-medium">CONFIRMED</span>
                            {% elif tx.status == 'pending' %}
                            <span class="px-3 py-1 bg-cyber-yellow/20 text-cyber-yellow text-xs rounded-full font-medium">PENDING</span>
                            {% else %}
                            <span class="px-3 py-1 bg-cyber-red/20 text-cyber-red text-xs rounded-full font-medium">FAILED</span>
                            {% endif %}
                        </div>
                        <div class="space-y-1 text-sm text-text-secondary">
                            <div>
                                <span class="text-cyber-purple font-mono">{{ tx.tx_id[:24] }}...</span>
                                <span class="text-xs text-gray-500 ml-2">🔍 Click for details</span>
                            </div>
                            <div>From: <span class="text-text-primary">{{ tx.sender[:16] + '...' if tx.sender and tx.sender|length > 16 else tx.sender or 'System' }}</span></div>
                        </div>
                    </div>

                    <!-- Transaction Data Preview -->
                    <div class="text-sm text-text-secondary">
                        {% if tx.data_parsed %}
                        <div class="space-y-1">
                            {% for key, value in tx.data_parsed.items() %}
                            {% if loop.index <= 3 %}
                            <div><span class="text-cyber-blue">{{ key }}:</span> {{ value[:30] + '...' if value|string|length > 30 else value }}</div>
                            {% endif %}
                            {% endfor %}
                            {% if tx.data_parsed|length > 3 %}
                            <div class="text-xs text-gray-500">+{{ tx.data_parsed|length - 3 }} more fields</div>
                            {% endif %}
                        </div>
                        {% else %}
                        <div class="text-gray-500">{{ tx.data[:50] + '...' if tx.data and tx.data|length > 50 else tx.data or 'No data' }}</div>
                        {% endif %}
                    </div>

                    <!-- Timestamp -->
                    <div class="text-right">
                        <div class="text-sm text-text-primary font-orbitron">
                            {{ tx.created_at|timestamp_to_time if tx.created_at else 'Unknown' }}
                        </div>
                        <div class="text-xs text-text-secondary">
                            {{ tx.created_at|timestamp_to_date if tx.created_at else 'Unknown Date' }}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="glass-card p-12 text-center">
            <div class="text-6xl mb-4">📋</div>
            <h3 class="text-2xl font-orbitron font-bold text-text-primary mb-4">No Transactions Found</h3>
            <p class="text-text-secondary mb-6">
                {% if current_op %}
                No transactions found for operation type "{{ current_op.replace('_', ' ').title() }}".
                {% else %}
                No transactions have been recorded on the blockchain yet.
                {% endif %}
            </p>
            {% if current_op %}
            <a href="{{ url_for('explorer.transactions') }}" class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold">
                View All Transactions
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if total_pages > 1 %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="glass-card p-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-text-secondary">
                    Page {{ page }} of {{ total_pages }}
                </div>
                <div class="flex space-x-2">
                    {% if page > 1 %}
                    <a href="{{ url_for('explorer.transactions', page=page-1, op=current_op) }}" 
                       class="glass-button px-4 py-2 rounded-lg font-orbitron font-semibold">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for p in range(max(1, page-2), min(total_pages+1, page+3)) %}
                    {% if p == page %}
                    <span class="glass-button-primary px-4 py-2 rounded-lg font-orbitron font-semibold">{{ p }}</span>
                    {% else %}
                    <a href="{{ url_for('explorer.transactions', page=p, op=current_op) }}" 
                       class="glass-button px-4 py-2 rounded-lg font-orbitron font-semibold">{{ p }}</a>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page < total_pages %}
                    <a href="{{ url_for('explorer.transactions', page=page+1, op=current_op) }}" 
                       class="glass-button px-4 py-2 rounded-lg font-orbitron font-semibold">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="glass-card p-6 text-center">
                <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">{{ total_count }}</div>
                <div class="text-text-secondary">Total Transactions</div>
            </div>
            <div class="glass-card p-6 text-center">
                <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">{{ op_types|length }}</div>
                <div class="text-text-secondary">Operation Types</div>
            </div>
            <div class="glass-card p-6 text-center">
                <div class="text-3xl font-orbitron font-bold text-cyber-green mb-2">{{ total_pages }}</div>
                <div class="text-text-secondary">Pages</div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details Modal (reuse from explorer index) -->
<div id="transaction-modal" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="glass-card-enhanced max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan">Transaction Details</h3>
                    <button onclick="closeTransactionModal()" class="text-text-secondary hover:text-cyber-red transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div id="transaction-details-content">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Transaction Details Modal Functions (reuse from explorer index)
async function showTransactionDetails(txId) {
    const modal = document.getElementById('transaction-modal');
    const content = document.getElementById('transaction-details-content');
    
    // Show modal with loading state
    modal.classList.remove('hidden');
    content.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyber-cyan mx-auto mb-3"></div>
            <div class="text-muted">Loading transaction details...</div>
        </div>
    `;
    
    try {
        const response = await fetch(`/explorer/api/transaction/${txId}`);
        const data = await response.json();
        
        if (data.success) {
            renderTransactionDetails(data);
        } else {
            showTransactionError(data.error || 'Failed to load transaction details');
        }
    } catch (error) {
        console.error('Error loading transaction details:', error);
        showTransactionError('Network error occurred');
    }
}

function renderTransactionDetails(data) {
    const content = document.getElementById('transaction-details-content');
    const tx = data.transaction;
    const sender = data.sender_identity;
    const receiver = data.receiver_identity;
    const block = data.block_info;
    
    const createdDate = new Date(tx.created_at * 1000);
    const blockDate = block ? new Date(block.block_timestamp * 1000) : null;
    
    content.innerHTML = `
        <div class="space-y-6">
            <!-- Transaction Overview -->
            <div class="glass-card p-6">
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">📋 Transaction Overview</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <div class="text-sm text-muted mb-1">Transaction ID</div>
                        <div class="font-mono text-cyber-purple break-all">${tx.tx_id}</div>
                    </div>
                    <div>
                        <div class="text-sm text-muted mb-1">Operation</div>
                        <div class="font-semibold">${tx.op || 'Transaction'}</div>
                    </div>
                    <div>
                        <div class="text-sm text-muted mb-1">Status</div>
                        <span class="px-3 py-1 rounded-full text-xs font-medium ${getStatusClass(tx.status)}">
                            ${tx.status ? tx.status.toUpperCase() : 'UNKNOWN'}
                        </span>
                    </div>
                    <div>
                        <div class="text-sm text-muted mb-1">Created</div>
                        <div>${createdDate.toLocaleString()}</div>
                    </div>
                </div>
            </div>
            
            <!-- Participants -->
            <div class="glass-card p-6">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">👥 Participants</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div class="text-sm text-muted mb-2">From</div>
                        <div class="p-4 bg-gray-800 rounded-lg">
                            <div class="font-mono text-sm text-cyber-cyan break-all">${tx.sender || 'System'}</div>
                            ${sender ? `
                                <div class="mt-2 text-sm">
                                    <div class="font-semibold">${sender.name}</div>
                                    <div class="text-muted">${sender.email}</div>
                                    ${sender.tribal_affiliation ? `<div class="text-xs text-cyber-purple">Tribe: ${sender.tribal_affiliation}</div>` : ''}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-muted mb-2">To</div>
                        <div class="p-4 bg-gray-800 rounded-lg">
                            <div class="font-mono text-sm text-cyber-cyan break-all">${tx.receiver || 'N/A'}</div>
                            ${receiver ? `
                                <div class="mt-2 text-sm">
                                    <div class="font-semibold">${receiver.name}</div>
                                    <div class="text-muted">${receiver.email}</div>
                                    ${receiver.tribal_affiliation ? `<div class="text-xs text-cyber-purple">Tribe: ${receiver.tribal_affiliation}</div>` : ''}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Transaction Data -->
            <div class="glass-card p-6">
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">📄 Transaction Data</h3>
                <div class="bg-gray-900 p-4 rounded-lg">
                    <pre class="text-sm text-gray-300 whitespace-pre-wrap break-all">${tx.data || 'No additional data'}</pre>
                </div>
            </div>
        </div>
    `;
}

function getStatusClass(status) {
    switch(status) {
        case 'confirmed': return 'bg-cyber-green/20 text-cyber-green';
        case 'pending': return 'bg-cyber-yellow/20 text-cyber-yellow';
        case 'failed': return 'bg-cyber-red/20 text-cyber-red';
        default: return 'bg-gray-500/20 text-gray-400';
    }
}

function showTransactionError(message) {
    const content = document.getElementById('transaction-details-content');
    content.innerHTML = `
        <div class="text-center py-8">
            <div class="text-cyber-red text-2xl mb-3">⚠️</div>
            <div class="text-cyber-red font-semibold mb-2">Error Loading Transaction</div>
            <div class="text-muted">${message}</div>
            <button onclick="closeTransactionModal()" class="glass-button-sm px-4 py-2 mt-4">
                Close
            </button>
        </div>
    `;
}

function closeTransactionModal() {
    const modal = document.getElementById('transaction-modal');
    modal.classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('transaction-modal');
    if (event.target === modal) {
        closeTransactionModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeTransactionModal();
    }
});
</script>

{% endblock %}
