"""
Onnyx Rotation Model

This module defines the Rotation model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.rotation")

class Rotation(BaseModel):
    """
    Rotation model for the Onnyx blockchain.
    
    A Rotation represents the validator rotation state in the Onnyx ecosystem.
    """
    
    TABLE_NAME = "rotation"
    PRIMARY_KEY = "id"
    
    def __init__(
        self,
        id: str,
        current_validator: Optional[str],
        queue: List[str],
        last_updated: int,
        update_interval: int,
        min_etzem_score: int,
        required_badges: List[str],
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a Rotation.
        
        Args:
            id: The rotation ID (usually "main")
            current_validator: The current validator Sela ID
            queue: The queue of eligible validators
            last_updated: The timestamp when the rotation was last updated
            update_interval: The queue update interval in seconds
            min_etzem_score: The minimum Etzem score required for eligibility
            required_badges: The required badges for eligibility
            metadata: Additional metadata for the rotation (optional)
        """
        self.id = id
        self.current_validator = current_validator
        self.queue = queue
        self.last_updated = last_updated
        self.update_interval = update_interval
        self.min_etzem_score = min_etzem_score
        self.required_badges = required_badges
        self.metadata = metadata or {}
    
    @classmethod
    def create_table(cls) -> None:
        """Create the Rotation table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            current_validator TEXT,
            queue TEXT NOT NULL,
            last_updated INTEGER NOT NULL,
            update_interval INTEGER NOT NULL,
            min_etzem_score INTEGER NOT NULL,
            required_badges TEXT NOT NULL,
            metadata TEXT
        )
        """)
        
        conn.commit()
        conn.close()
    
    @classmethod
    def create(
        cls,
        id: str,
        current_validator: Optional[str],
        queue: List[str],
        last_updated: Optional[int] = None,
        update_interval: int = 3600,
        min_etzem_score: int = 100,
        required_badges: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "Rotation":
        """
        Create a new Rotation.
        
        Args:
            id: The rotation ID (usually "main")
            current_validator: The current validator Sela ID
            queue: The queue of eligible validators
            last_updated: The timestamp when the rotation was last updated (optional)
            update_interval: The queue update interval in seconds (default: 3600)
            min_etzem_score: The minimum Etzem score required for eligibility (default: 100)
            required_badges: The required badges for eligibility (optional)
            metadata: Additional metadata for the rotation (optional)
        
        Returns:
            The created Rotation
        """
        # Create the table if it doesn't exist
        cls.create_table()
        
        # Create the Rotation
        rotation = cls(
            id=id,
            current_validator=current_validator,
            queue=queue,
            last_updated=last_updated or int(time.time()),
            update_interval=update_interval,
            min_etzem_score=min_etzem_score,
            required_badges=required_badges or ["VALIDATOR"],
            metadata=metadata
        )
        
        # Save the Rotation to the database
        rotation.save()
        
        return rotation
    
    def save(self) -> None:
        """Save the Rotation to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            current_validator,
            queue,
            last_updated,
            update_interval,
            min_etzem_score,
            required_badges,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.id,
            self.current_validator,
            json.dumps(self.queue),
            self.last_updated,
            self.update_interval,
            self.min_etzem_score,
            json.dumps(self.required_badges),
            json.dumps(self.metadata)
        ))
        
        conn.commit()
        conn.close()
    
    @classmethod
    def get_by_id(cls, id: str) -> Optional["Rotation"]:
        """
        Get a Rotation by ID.
        
        Args:
            id: The rotation ID
        
        Returns:
            The Rotation, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            current_validator,
            queue,
            last_updated,
            update_interval,
            min_etzem_score,
            required_badges,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return cls(
                id=row[0],
                current_validator=row[1],
                queue=json.loads(row[2]),
                last_updated=row[3],
                update_interval=row[4],
                min_etzem_score=row[5],
                required_badges=json.loads(row[6]),
                metadata=json.loads(row[7]) if row[7] else {}
            )
        
        return None
    
    @classmethod
    def get_main(cls) -> Optional["Rotation"]:
        """
        Get the main rotation.
        
        Returns:
            The main Rotation, or None if not found
        """
        return cls.get_by_id("main")
    
    def update_queue(self, queue: List[str]) -> None:
        """
        Update the queue of eligible validators.
        
        Args:
            queue: The new queue of eligible validators
        """
        self.queue = queue
        self.last_updated = int(time.time())
        self.save()
    
    def set_current_validator(self, validator: str) -> None:
        """
        Set the current validator.
        
        Args:
            validator: The current validator Sela ID
        """
        self.current_validator = validator
        self.save()
    
    def set_update_interval(self, interval: int) -> None:
        """
        Set the queue update interval.
        
        Args:
            interval: The update interval in seconds
        """
        self.update_interval = interval
        self.save()
    
    def set_min_etzem_score(self, score: int) -> None:
        """
        Set the minimum Etzem score required for eligibility.
        
        Args:
            score: The minimum Etzem score
        """
        self.min_etzem_score = score
        self.save()
    
    def set_required_badges(self, badges: List[str]) -> None:
        """
        Set the required badges for eligibility.
        
        Args:
            badges: The required badges
        """
        self.required_badges = badges
        self.save()
    
    def get_next_validator(self, height: int) -> Optional[str]:
        """
        Get the next validator for a given block height.
        
        Args:
            height: The block height
        
        Returns:
            The next validator Sela ID, or None if the queue is empty
        """
        if not self.queue:
            return None
        
        # Use a simple round-robin algorithm
        index = height % len(self.queue)
        return self.queue[index]
    
    def is_valid_proposer(self, sela_id: str, height: int) -> bool:
        """
        Check if a Sela is the valid proposer for a given block height.
        
        Args:
            sela_id: The Sela ID to check
            height: The block height
        
        Returns:
            Whether the Sela is the valid proposer
        """
        next_validator = self.get_next_validator(height)
        return sela_id == next_validator
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Rotation to a dictionary.
        
        Returns:
            The Rotation as a dictionary
        """
        return {
            "id": self.id,
            "current_validator": self.current_validator,
            "queue": self.queue,
            "last_updated": self.last_updated,
            "update_interval": self.update_interval,
            "min_etzem_score": self.min_etzem_score,
            "required_badges": self.required_badges,
            "metadata": self.metadata
        }
