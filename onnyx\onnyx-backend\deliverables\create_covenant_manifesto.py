#!/usr/bin/env python3
"""
Create ONNYX Community Covenant Manifesto PDF
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
from reportlab.platypus.frames import Frame
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from reportlab.pdfgen import canvas
import os
from datetime import datetime

# ONNYX Color Palette
CYBER_CYAN = HexColor('#00fff7')
CYBER_PURPLE = HexColor('#9a00ff')
ONYX_BLACK = HexColor('#0a0a0a')
CYBER_GOLD = HexColor('#ffd700')
TRIBAL_SILVER = HexColor('#c0c0c0')

class CovenantManifestoCanvas(canvas.Canvas):
    """Custom canvas for ONNYX styling"""
    
    def __init__(self, *args, **kwargs):
        canvas.Canvas.__init__(self, *args, **kwargs)
        
    def draw_header(self):
        """Draw the ONNYX header"""
        self.setFillColor(ONYX_BLACK)
        self.rect(0, letter[1] - 80, letter[0], 80, fill=1)
        
        self.setFillColor(CYBER_CYAN)
        self.setFont("Helvetica-Bold", 24)
        self.drawCentredText(letter[0]/2, letter[1] - 35, "ONNYX PLATFORM")
        
        self.setFillColor(CYBER_PURPLE)
        self.setFont("Helvetica", 12)
        self.drawCentredText(letter[0]/2, letter[1] - 55, "Biblical Economic Covenant Community")
        
    def draw_footer(self):
        """Draw the footer"""
        self.setFillColor(ONYX_BLACK)
        self.rect(0, 0, letter[0], 40, fill=1)
        
        self.setFillColor(TRIBAL_SILVER)
        self.setFont("Helvetica", 8)
        self.drawCentredText(letter[0]/2, 20, f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | ONNYX Platform v1.0")

def create_covenant_manifesto():
    """Create the ONNYX Community Covenant Manifesto PDF"""
    
    print("📜 Creating ONNYX Community Covenant Manifesto")
    print("=" * 50)
    
    # Ensure deliverables directory exists
    os.makedirs('deliverables', exist_ok=True)
    
    # Create PDF document
    filename = 'deliverables/ONNYX_Community_Covenant_Manifesto.pdf'
    doc = SimpleDocTemplate(filename, pagesize=letter, 
                           rightMargin=72, leftMargin=72, 
                           topMargin=100, bottomMargin=60)
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Custom styles with ONNYX branding
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=28,
        spaceAfter=30,
        textColor=CYBER_CYAN,
        alignment=TA_CENTER,
        fontName='Helvetica-Bold'
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=CYBER_PURPLE,
        fontName='Helvetica-Bold'
    )
    
    subheading_style = ParagraphStyle(
        'CustomSubHeading',
        parent=styles['Heading3'],
        fontSize=14,
        spaceAfter=10,
        textColor=CYBER_GOLD,
        fontName='Helvetica-Bold'
    )
    
    body_style = ParagraphStyle(
        'CustomBody',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=12,
        alignment=TA_JUSTIFY,
        fontName='Helvetica'
    )
    
    scripture_style = ParagraphStyle(
        'Scripture',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=12,
        alignment=TA_CENTER,
        textColor=CYBER_PURPLE,
        fontName='Helvetica-Oblique',
        leftIndent=36,
        rightIndent=36
    )
    
    # Build document content
    story = []
    
    # Title Page
    story.append(Spacer(1, 2*inch))
    story.append(Paragraph("ONNYX CODE OF BIBLICAL ECONOMIC CONDUCT", title_style))
    story.append(Spacer(1, 0.5*inch))
    story.append(Paragraph("Community Covenant Manifesto", heading_style))
    story.append(Spacer(1, 1*inch))
    
    story.append(Paragraph('"And he said unto them, Render therefore unto Caesar the things which be Caesar\'s, and unto God the things which be God\'s."', scripture_style))
    story.append(Paragraph("- Luke 20:25 (KJV)", scripture_style))
    
    story.append(Spacer(1, 1*inch))
    story.append(Paragraph(f"Version 1.0 | {datetime.now().strftime('%B %Y')}", body_style))
    
    story.append(PageBreak())
    
    # Table of Contents
    story.append(Paragraph("TABLE OF CONTENTS", heading_style))
    story.append(Spacer(1, 0.3*inch))
    
    toc_data = [
        ["1. Covenant Foundation", "3"],
        ["2. Tribal Governance Structure", "4"],
        ["3. Biblical Tokenomics Principles", "6"],
        ["4. Interest-Free Lending System", "8"],
        ["5. Sabbath Enforcement Framework", "10"],
        ["6. User Roles and Responsibilities", "12"],
        ["7. Covenant Economics Implementation", "14"],
        ["8. Compliance and Enforcement", "16"],
        ["9. Community Guidelines", "18"],
        ["10. Appendices", "20"]
    ]
    
    toc_table = Table(toc_data, colWidths=[4*inch, 1*inch])
    toc_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TEXTCOLOR', (1, 0), (1, -1), CYBER_PURPLE),
    ]))
    
    story.append(toc_table)
    story.append(PageBreak())
    
    # Chapter 1: Covenant Foundation
    story.append(Paragraph("1. COVENANT FOUNDATION", heading_style))
    story.append(Spacer(1, 0.2*inch))
    
    story.append(Paragraph("1.1 Biblical Mandate", subheading_style))
    story.append(Paragraph(
        "The ONNYX platform operates under the divine mandate established in Scripture, "
        "implementing biblical economic principles as outlined in the Torah and confirmed "
        "throughout the King James Version of the Holy Bible. Our covenant community "
        "seeks to restore the economic order established by the Almighty for His people.",
        body_style
    ))
    
    story.append(Paragraph('"If thou lend money to any of my people that is poor by thee, thou shalt not be to him as an usurer, neither shalt thou lay upon him usury."', scripture_style))
    story.append(Paragraph("- Exodus 22:25 (KJV)", scripture_style))
    
    story.append(Paragraph("1.2 Platform Purpose", subheading_style))
    story.append(Paragraph(
        "ONNYX serves as a technological manifestation of biblical covenant economics, "
        "providing a blockchain-based platform where the children of Israel and witness "
        "nations can engage in commerce according to divine law. The platform enforces "
        "sabbath observance, interest-free lending among covenant members, and tribal "
        "governance structures as established in Scripture.",
        body_style
    ))
    
    story.append(Paragraph("1.3 Covenant Commitment", subheading_style))
    story.append(Paragraph(
        "All participants in the ONNYX ecosystem commit to upholding biblical law in "
        "their economic interactions. This includes observance of sabbath periods, "
        "adherence to interest-free lending principles among Israelites, and submission "
        "to the tribal governance structure established by divine appointment.",
        body_style
    ))
    
    story.append(PageBreak())
    
    # Chapter 2: Tribal Governance Structure
    story.append(Paragraph("2. TRIBAL GOVERNANCE STRUCTURE", heading_style))
    story.append(Spacer(1, 0.2*inch))
    
    story.append(Paragraph("2.1 The Twelve Tribes of Israel", subheading_style))
    story.append(Paragraph(
        "The ONNYX platform recognizes the twelve tribes of Israel as the primary "
        "covenant community, each with distinct roles and responsibilities within "
        "the economic ecosystem:",
        body_style
    ))
    
    tribes_data = [
        ["Tribe", "Symbol", "Primary Role"],
        ["Judah (JU)", "🦁", "Leadership & Governance"],
        ["Levi (LE)", "⚖️", "Spiritual Oversight & Law"],
        ["Benjamin (BE)", "🏹", "Technology & Innovation"],
        ["Ephraim (EP)", "🌾", "Commerce & Trade"],
        ["Manasseh (MA)", "🛡️", "Security & Defense"],
        ["Reuben (RE)", "💎", "Resource Management"],
        ["Simeon (SI)", "⚔️", "Enforcement & Justice"],
        ["Zebulun (ZE)", "🚢", "Transportation & Logistics"],
        ["Issachar (IS)", "📚", "Knowledge & Wisdom"],
        ["Asher (AS)", "🍯", "Prosperity & Abundance"],
        ["Naphtali (NA)", "🦌", "Communication & Messaging"],
        ["Gad (GA)", "🛡️", "Border Security & Protection"]
    ]
    
    tribes_table = Table(tribes_data, colWidths=[1.5*inch, 0.8*inch, 2.7*inch])
    tribes_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), CYBER_PURPLE),
        ('TEXTCOLOR', (0, 0), (-1, 0), white),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [white, HexColor('#f0f0f0')]),
        ('GRID', (0, 0), (-1, -1), 1, black),
    ]))
    
    story.append(tribes_table)
    story.append(Spacer(1, 0.2*inch))
    
    story.append(Paragraph("2.2 Witness Nations", subheading_style))
    story.append(Paragraph(
        "The platform also accommodates witness nations, including:",
        body_style
    ))
    
    story.append(Paragraph("• <b>Edom's Twelve Dukes:</b> Descendants of Esau with specific economic privileges", body_style))
    story.append(Paragraph("• <b>Ishmael's Twelve Princes:</b> Arabian nations with trade partnerships", body_style))
    story.append(Paragraph("• <b>Hamitic Nations:</b> African nations with resource agreements", body_style))
    story.append(Paragraph("• <b>Japhethic Nations:</b> European and Asian nations with technology partnerships", body_style))
    
    story.append(PageBreak())

    # Chapter 3: Biblical Tokenomics Principles
    story.append(Paragraph("3. BIBLICAL TOKENOMICS PRINCIPLES", heading_style))
    story.append(Spacer(1, 0.2*inch))

    story.append(Paragraph("3.1 Covenant Economics Foundation", subheading_style))
    story.append(Paragraph(
        "The ONNYX tokenomics system implements biblical economic principles through "
        "blockchain technology, ensuring compliance with Torah law while enabling "
        "modern commerce. Our system includes:",
        body_style
    ))

    story.append(Paragraph("• <b>Yovel Cycles:</b> 50-year jubilee periods with debt forgiveness", body_style))
    story.append(Paragraph("• <b>Shmita Observance:</b> 7-year sabbatical cycles", body_style))
    story.append(Paragraph("• <b>Sabbath Enforcement:</b> Weekly and feast day restrictions", body_style))
    story.append(Paragraph("• <b>Interest-Free Lending:</b> Zero usury among covenant members", body_style))
    story.append(Paragraph("• <b>Tribal Governance:</b> Decentralized decision-making", body_style))

    story.append(Paragraph('"At the end of every seven years thou shalt make a release."', scripture_style))
    story.append(Paragraph("- Deuteronomy 15:1 (KJV)", scripture_style))

    story.append(Paragraph("3.2 Token Distribution", subheading_style))
    story.append(Paragraph(
        "ONNYX tokens (ONX) are distributed according to biblical principles of "
        "labor, contribution, and covenant membership. Distribution mechanisms include:",
        body_style
    ))

    story.append(Paragraph("• <b>Labor Rewards:</b> Tokens earned through productive work", body_style))
    story.append(Paragraph("• <b>Covenant Participation:</b> Rewards for platform engagement", body_style))
    story.append(Paragraph("• <b>Sabbath Observance:</b> Bonuses for faithful observance", body_style))
    story.append(Paragraph("• <b>Community Service:</b> Tokens for helping others", body_style))

    story.append(PageBreak())

    # Chapter 4: Interest-Free Lending System
    story.append(Paragraph("4. INTEREST-FREE LENDING SYSTEM", heading_style))
    story.append(Spacer(1, 0.2*inch))

    story.append(Paragraph("4.1 Biblical Lending Rules", subheading_style))
    story.append(Paragraph(
        "The ONNYX platform implements Torah-compliant lending based on tribal "
        "affiliation and covenant status:",
        body_style
    ))

    lending_data = [
        ["Lender", "Borrower", "Interest Rate", "Biblical Reference"],
        ["Israelite", "Israelite", "0% (Mandatory)", "Exodus 22:25"],
        ["Israelite", "Witness Nation", "Allowed", "Deuteronomy 23:20"],
        ["Witness Nation", "Israelite", "Allowed", "Negotiable"],
        ["Witness Nation", "Witness Nation", "Market Rate", "Standard Practice"]
    ]

    lending_table = Table(lending_data, colWidths=[1.3*inch, 1.3*inch, 1.2*inch, 1.2*inch])
    lending_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), CYBER_PURPLE),
        ('TEXTCOLOR', (0, 0), (-1, 0), white),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 9),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [white, HexColor('#f0f0f0')]),
        ('GRID', (0, 0), (-1, -1), 1, black),
    ]))

    story.append(lending_table)
    story.append(Spacer(1, 0.2*inch))

    story.append(Paragraph("4.2 Loan Forgiveness", subheading_style))
    story.append(Paragraph(
        "The platform implements automatic loan forgiveness mechanisms:",
        body_style
    ))

    story.append(Paragraph("• <b>80% Forgiveness:</b> Automatic forgiveness when 80% is repaid", body_style))
    story.append(Paragraph("• <b>10-Day Grace Period:</b> Sabbatical pause for short-term relief", body_style))
    story.append(Paragraph("• <b>Shmita Release:</b> 7-year debt jubilee", body_style))
    story.append(Paragraph("• <b>Yovel Forgiveness:</b> 50-year complete debt release", body_style))

    story.append(PageBreak())

    # Chapter 5: Sabbath Enforcement Framework
    story.append(Paragraph("5. SABBATH ENFORCEMENT FRAMEWORK", heading_style))
    story.append(Spacer(1, 0.2*inch))

    story.append(Paragraph("5.1 Comprehensive Sabbath System", subheading_style))
    story.append(Paragraph(
        "ONNYX enforces biblical sabbath observance through technological means, "
        "implementing 73+ sabbath periods annually:",
        body_style
    ))

    sabbath_data = [
        ["Sabbath Type", "Frequency", "Duration", "Biblical Reference"],
        ["Weekly Sabbath", "52 per year", "Sunset to Sunset", "Exodus 20:8-11"],
        ["New Moon Sabbath", "12 per year", "24 hours", "Numbers 28:11"],
        ["Passover", "Annual", "25 hours", "Leviticus 23:5-7"],
        ["Pentecost", "Annual", "25 hours", "Leviticus 23:15-16"],
        ["Trumpets", "Annual", "25 hours", "Leviticus 23:24-25"],
        ["Atonement", "Annual", "25 hours", "Leviticus 23:27-28"],
        ["Tabernacles", "Annual (7 days)", "25 hours each", "Leviticus 23:34-36"]
    ]

    sabbath_table = Table(sabbath_data, colWidths=[1.3*inch, 1.1*inch, 1.1*inch, 1.5*inch])
    sabbath_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), CYBER_PURPLE),
        ('TEXTCOLOR', (0, 0), (-1, 0), white),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 8),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [white, HexColor('#f0f0f0')]),
        ('GRID', (0, 0), (-1, -1), 1, black),
    ]))

    story.append(sabbath_table)
    story.append(Spacer(1, 0.2*inch))

    story.append(Paragraph("5.2 Enforcement Mechanisms", subheading_style))
    story.append(Paragraph(
        "Sabbath enforcement includes:",
        body_style
    ))

    story.append(Paragraph("• <b>Timezone Locking:</b> Prevents manipulation of sabbath timing", body_style))
    story.append(Paragraph("• <b>Transaction Restrictions:</b> Blocks commerce during sabbath", body_style))
    story.append(Paragraph("• <b>Violation Tracking:</b> Records and penalizes violations", body_style))
    story.append(Paragraph("• <b>Compliance Scoring:</b> Rewards faithful observance", body_style))

    story.append(PageBreak())

    print("✅ Created Biblical Tokenomics and Sabbath Enforcement sections")

    # Build the PDF
    doc.build(story)

    print(f"✅ PDF created successfully: {filename}")
    return filename

if __name__ == "__main__":
    create_covenant_manifesto()
