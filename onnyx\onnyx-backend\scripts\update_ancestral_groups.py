#!/usr/bin/env python3
"""
Update Ancestral Groups to Biblical Classification
Updates the existing biblical nations to use proper biblical ancestral groupings:
1. <PERSON><PERSON> (12 Dukes)
2. <PERSON><PERSON><PERSON> (12 Princes) 
3. Hamitic Nations (Cush, Mizraim, Canaan, Put)
4. Japhethic Nations (7 sons with their branches)
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
import time

def update_ancestral_groups():
    """Update ancestral groups to biblical classification."""
    print("📜 UPDATING ANCESTRAL GROUPS TO BIBLICAL CLASSIFICATION")
    print("=" * 60)
    
    try:
        # First, let's see what we have
        print("\n📊 CURRENT NATIONS BY CATEGORY")
        print("-" * 40)
        
        current_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, nation_type, 
                   ancestral_group, description
            FROM biblical_nations
            WHERE nation_type = 'witness'
            ORDER BY nation_name
        """)
        
        # Group by existing categories if they exist
        edom_nations = []
        ishmael_nations = []
        hamitic_nations = []
        other_nations = []
        
        for nation in current_nations:
            desc = nation.get('description', '').lower()
            name = nation.get('nation_name', '').lower()
            
            if 'edom' in desc or 'duke' in desc:
                edom_nations.append(nation)
            elif 'ishmael' in desc or 'prince' in desc:
                ishmael_nations.append(nation)
            elif any(x in desc for x in ['cush', 'mizraim', 'canaan', 'put', 'ham']):
                hamitic_nations.append(nation)
            else:
                other_nations.append(nation)
        
        print(f"   Edom nations found: {len(edom_nations)}")
        print(f"   Ishmael nations found: {len(ishmael_nations)}")
        print(f"   Hamitic nations found: {len(hamitic_nations)}")
        print(f"   Other nations: {len(other_nations)}")
        
        # Update Edom nations
        print("\n🏔️ UPDATING EDOM ANCESTRAL GROUP")
        print("-" * 40)
        
        edom_count = 0
        for nation in edom_nations:
            db.execute("""
                UPDATE biblical_nations 
                SET ancestral_group = 'Edom',
                    ancestral_description = 'The 12 Dukes of Edom, descendants of Esau (Genesis 36)',
                    historical_connection = 'Sons and grandsons of Esau who became dukes in the land of Edom'
                WHERE nation_code = ?
            """, (nation['nation_code'],))
            print(f"   ✅ Updated {nation['nation_name']} to Edom group")
            edom_count += 1
        
        # Update Ishmael nations
        print("\n🏜️ UPDATING ISHMAEL ANCESTRAL GROUP")
        print("-" * 40)
        
        ishmael_count = 0
        for nation in ishmael_nations:
            db.execute("""
                UPDATE biblical_nations 
                SET ancestral_group = 'Ishmael',
                    ancestral_description = 'The 12 Princes of Ishmael, sons of Abraham (Genesis 25)',
                    historical_connection = 'Sons of Ishmael who became princes of great nations'
                WHERE nation_code = ?
            """, (nation['nation_code'],))
            print(f"   ✅ Updated {nation['nation_name']} to Ishmael group")
            ishmael_count += 1
        
        # Update Hamitic nations
        print("\n🏺 UPDATING HAMITIC NATIONS GROUP")
        print("-" * 40)
        
        hamitic_count = 0
        for nation in hamitic_nations:
            db.execute("""
                UPDATE biblical_nations 
                SET ancestral_group = 'Hamitic Nations',
                    ancestral_description = 'Sons of Ham: Cush, Mizraim, Put, and Canaan (Genesis 10)',
                    historical_connection = 'Descendants of Ham who populated Africa and parts of Asia'
                WHERE nation_code = ?
            """, (nation['nation_code'],))
            print(f"   ✅ Updated {nation['nation_name']} to Hamitic Nations group")
            hamitic_count += 1
        
        # For other nations, let's see if we can classify them as Japhethic
        print("\n🌍 UPDATING REMAINING NATIONS AS JAPHETHIC")
        print("-" * 40)
        
        japhethic_count = 0
        for nation in other_nations:
            # Most modern European/Asian nations would be Japhethic
            db.execute("""
                UPDATE biblical_nations 
                SET ancestral_group = 'Japhethic Nations',
                    ancestral_description = 'Sons of Japheth and their descendants (Genesis 10)',
                    historical_connection = 'Descendants of Japheth who populated Europe and parts of Asia'
                WHERE nation_code = ?
            """, (nation['nation_code'],))
            print(f"   ✅ Updated {nation['nation_name']} to Japhethic Nations group")
            japhethic_count += 1
        
        # Verify the updates
        print("\n🎉 VERIFICATION")
        print("-" * 40)
        
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as count
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)
        
        print("📋 Updated Biblical Ancestral Groups:")
        total_updated = 0
        for group in ancestral_groups:
            print(f"   {group['ancestral_group']:20s}: {group['count']} nations")
            total_updated += group['count']
        
        print(f"\n✅ Total nations updated: {total_updated}")
        print(f"   - Edom: {edom_count}")
        print(f"   - Ishmael: {ishmael_count}")
        print(f"   - Hamitic Nations: {hamitic_count}")
        print(f"   - Japhethic Nations: {japhethic_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = update_ancestral_groups()
    if success:
        print("\n✅ Ancestral groups update completed successfully!")
    else:
        print("\n❌ Ancestral groups update failed!")
