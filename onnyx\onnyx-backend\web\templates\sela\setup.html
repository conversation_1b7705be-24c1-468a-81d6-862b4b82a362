{% extends "base.html" %}

{% block title %}Complete Sela Setup | ONNYX Platform{% endblock %}

{% block description %}Complete the setup of your Sela business entity on the ONNYX platform.{% endblock %}

{% block head %}
<style>
    .setup-hero {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .setup-step {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.05) 0%, rgba(154, 0, 255, 0.05) 100%);
        border: 1px solid rgba(0, 255, 247, 0.1);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }
    
    .setup-step.completed {
        border-color: rgba(0, 255, 136, 0.3);
        background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 255, 247, 0.1) 100%);
    }
    
    .progress-bar {
        background: linear-gradient(90deg, rgba(0, 255, 247, 0.2) 0%, rgba(154, 0, 255, 0.2) 100%);
        border-radius: 10px;
        overflow: hidden;
    }
    
    .progress-fill {
        background: linear-gradient(90deg, #00fff7 0%, #9a00ff 100%);
        height: 8px;
        transition: width 0.5s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="setup-hero p-8 rounded-2xl mb-8 text-center">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                    🚀 Complete Your Sela Setup
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                {% if sela.name if sela is mapping else sela[2] %}
                    Finalizing setup for "{{ sela.name if sela is mapping else sela[2] }}"
                {% else %}
                    Complete your Sela business setup
                {% endif %}
            </p>
            
            <!-- Progress Bar -->
            <div class="max-w-md mx-auto">
                <div class="flex justify-between text-sm text-gray-400 mb-2">
                    <span>Setup Progress</span>
                    <span id="progressText">25%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 25%"></div>
                </div>
            </div>
        </div>

        <!-- Setup Steps -->
        <div class="space-y-6">
            <!-- Step 1: Business Information (Completed) -->
            <div class="setup-step completed p-6 rounded-xl">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-orbitron font-bold text-green-400 mb-2">✅ Business Information</h3>
                        <p class="text-gray-300 mb-3">Your basic business information has been recorded.</p>
                        <div class="bg-gray-800 p-3 rounded-lg text-sm">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                <div><span class="text-gray-400">Name:</span> <span class="text-white">{{ sela.name if sela is mapping else sela[2] }}</span></div>
                                <div><span class="text-gray-400">Category:</span> <span class="text-white">{{ sela.category if sela is mapping else sela[3] }}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Covenant Compliance -->
            <div class="setup-step p-6 rounded-xl" id="step2">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-cyber-cyan rounded-full flex items-center justify-center text-white font-bold">
                            2
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">📖 Covenant Compliance Setup</h3>
                        <p class="text-gray-300 mb-4">Configure your business to operate according to biblical principles.</p>
                        
                        <div class="space-y-4">
                            <div class="bg-gray-800 p-4 rounded-lg">
                                <h4 class="font-semibold text-yellow-400 mb-3">Sabbath Observance</h4>
                                <div class="space-y-2">
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="covenant-checkbox" data-step="sabbath" 
                                               class="w-4 h-4 text-cyber-cyan bg-gray-700 border-gray-600 rounded">
                                        <span class="text-sm text-gray-300">I will honor the Sabbath in business operations</span>
                                    </label>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="covenant-checkbox" data-step="sabbath"
                                               class="w-4 h-4 text-cyber-cyan bg-gray-700 border-gray-600 rounded">
                                        <span class="text-sm text-gray-300">I will not conduct business during Sabbath hours</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="bg-gray-800 p-4 rounded-lg">
                                <h4 class="font-semibold text-yellow-400 mb-3">Biblical Economics</h4>
                                <div class="space-y-2">
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="covenant-checkbox" data-step="economics"
                                               class="w-4 h-4 text-cyber-cyan bg-gray-700 border-gray-600 rounded">
                                        <span class="text-sm text-gray-300">I will participate in firstfruits giving</span>
                                    </label>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="covenant-checkbox" data-step="economics"
                                               class="w-4 h-4 text-cyber-cyan bg-gray-700 border-gray-600 rounded">
                                        <span class="text-sm text-gray-300">I will comply with jubilee year practices</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <button onclick="completeCovenantSetup()" 
                                id="covenantBtn"
                                class="mt-4 glass-button-secondary px-6 py-2 rounded-lg opacity-50 cursor-not-allowed"
                                disabled>
                            Complete Covenant Setup
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Tribal Integration -->
            <div class="setup-step p-6 rounded-xl" id="step3">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white font-bold">
                            3
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-orbitron font-bold text-gray-400 mb-2">🏛️ Tribal Integration</h3>
                        <p class="text-gray-300 mb-4">Connect your Sela with tribal governance and mining operations.</p>
                        
                        <div class="bg-gray-800 p-4 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="text-2xl">🏛️</div>
                                <div>
                                    <div class="font-semibold text-white">Tribe of {{ current_user.tribal_affiliation.title() if current_user.tribal_affiliation else 'Unknown' }}</div>
                                    <div class="text-sm text-gray-400">Your tribal affiliation will be automatically integrated</div>
                                </div>
                            </div>
                        </div>
                        
                        <button onclick="completeTribalIntegration()" 
                                id="tribalBtn"
                                class="mt-4 glass-button-secondary px-6 py-2 rounded-lg opacity-50 cursor-not-allowed"
                                disabled>
                            Complete Tribal Integration
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 4: Activation -->
            <div class="setup-step p-6 rounded-xl" id="step4">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white font-bold">
                            4
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-orbitron font-bold text-gray-400 mb-2">🚀 Sela Activation</h3>
                        <p class="text-gray-300 mb-4">Activate your Sela business and begin covenant commerce.</p>
                        
                        <button onclick="activateSela()" 
                                id="activateBtn"
                                class="glass-button-primary px-8 py-3 rounded-xl font-orbitron font-bold opacity-50 cursor-not-allowed"
                                disabled>
                            🏢 Activate Sela Business
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="text-center mt-8">
            <a href="{{ url_for('dashboard.overview') }}" 
               class="text-gray-400 hover:text-white transition-colors">
                ← Return to Dashboard
            </a>
        </div>
    </div>
</div>

<script>
let currentStep = 1;
const totalSteps = 4;

function updateProgress() {
    const progress = (currentStep / totalSteps) * 100;
    document.getElementById('progressFill').style.width = progress + '%';
    document.getElementById('progressText').textContent = Math.round(progress) + '%';
}

function completeCovenantSetup() {
    const checkboxes = document.querySelectorAll('.covenant-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    if (!allChecked) {
        alert('Please complete all covenant compliance requirements.');
        return;
    }
    
    // Mark step 2 as completed
    const step2 = document.getElementById('step2');
    step2.classList.add('completed');
    step2.querySelector('.w-8').innerHTML = `
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
    `;
    step2.querySelector('.w-8').className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
    step2.querySelector('h3').className = 'text-lg font-orbitron font-bold text-green-400 mb-2';
    
    // Enable step 3
    currentStep = 3;
    updateProgress();
    
    const step3 = document.getElementById('step3');
    step3.querySelector('.w-8').className = 'w-8 h-8 bg-cyber-cyan rounded-full flex items-center justify-center text-white font-bold';
    step3.querySelector('h3').className = 'text-lg font-orbitron font-bold text-cyber-cyan mb-2';
    
    const tribalBtn = document.getElementById('tribalBtn');
    tribalBtn.disabled = false;
    tribalBtn.className = 'mt-4 glass-button-secondary px-6 py-2 rounded-lg';
}

function completeTribalIntegration() {
    // Mark step 3 as completed
    const step3 = document.getElementById('step3');
    step3.classList.add('completed');
    step3.querySelector('.w-8').innerHTML = `
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
    `;
    step3.querySelector('.w-8').className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
    step3.querySelector('h3').className = 'text-lg font-orbitron font-bold text-green-400 mb-2';
    
    // Enable step 4
    currentStep = 4;
    updateProgress();
    
    const step4 = document.getElementById('step4');
    step4.querySelector('.w-8').className = 'w-8 h-8 bg-cyber-purple rounded-full flex items-center justify-center text-white font-bold';
    step4.querySelector('h3').className = 'text-lg font-orbitron font-bold text-cyber-purple mb-2';
    
    const activateBtn = document.getElementById('activateBtn');
    activateBtn.disabled = false;
    activateBtn.className = 'glass-button-primary px-8 py-3 rounded-xl font-orbitron font-bold';
}

async function activateSela() {
    const activateBtn = document.getElementById('activateBtn');
    const originalText = activateBtn.textContent;
    
    try {
        activateBtn.disabled = true;
        activateBtn.textContent = 'Activating...';
        
        const selaId = '{{ sela.sela_id if sela is mapping else sela[0] }}';
        
        const response = await fetch(`/sela/api/${selaId}/activate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Mark step 4 as completed
            const step4 = document.getElementById('step4');
            step4.classList.add('completed');
            step4.querySelector('.w-8').innerHTML = `
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            `;
            step4.querySelector('.w-8').className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
            step4.querySelector('h3').className = 'text-lg font-orbitron font-bold text-green-400 mb-2';
            
            updateProgress();
            
            // Show success message and redirect
            alert('🎉 Sela activated successfully! Welcome to covenant commerce.');
            window.location.href = `/sela/${selaId}/dashboard`;
        } else {
            alert('Error activating Sela: ' + result.error);
            activateBtn.disabled = false;
            activateBtn.textContent = originalText;
        }
    } catch (error) {
        console.error('Activation error:', error);
        alert('Error activating Sela. Please try again.');
        activateBtn.disabled = false;
        activateBtn.textContent = originalText;
    }
}

// Enable covenant setup button when all checkboxes are checked
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.covenant-checkbox');
    const covenantBtn = document.getElementById('covenantBtn');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            if (allChecked) {
                covenantBtn.disabled = false;
                covenantBtn.className = 'mt-4 glass-button-secondary px-6 py-2 rounded-lg';
            } else {
                covenantBtn.disabled = true;
                covenantBtn.className = 'mt-4 glass-button-secondary px-6 py-2 rounded-lg opacity-50 cursor-not-allowed';
            }
        });
    });
});
</script>
{% endblock %}
