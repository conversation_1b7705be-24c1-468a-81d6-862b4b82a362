{% extends "base.html" %}

{% block title %}The 12 Tribes of Israel - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Hero Section -->
    <div class="py-20 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        The 12 Tribes of Israel
                    </span>
                </h1>
                <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                    The covenant nations - children of Jacob/Israel who require Gate Keeper verification 
                    for identity registration and receive Mikvah Tokens upon approval.
                </p>
            </div>
        </div>
    </div>

    <!-- Tribal Prophecies Overview -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-4">
                    Jacob's Prophetic Blessings
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    "And Jacob called unto his sons, and said, Gather yourselves together, 
                    that I may tell you that which shall befall you in the last days." - Genesis 49:1
                </p>
            </div>
        </div>
    </div>

    <!-- The 12 Tribes Grid -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for nation in covenant_nations %}
                <div class="glass-card-enhanced p-8 hover:scale-105 transition-all duration-300 group">
                    <!-- Tribal Symbol and Name -->
                    <div class="text-center mb-6">
                        <div class="text-6xl mb-4">{{ nation.flag_symbol }}</div>
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">
                            {{ nation.nation_name }}
                        </h3>
                        <p class="text-lg text-text-secondary">{{ nation.tribe_name }}</p>
                    </div>

                    <!-- Tribal Description -->
                    <div class="mb-6">
                        <p class="text-text-secondary leading-relaxed">
                            {{ nation.description }}
                        </p>
                    </div>

                    <!-- Prophetic Blessing -->
                    <div class="mb-6 p-4 bg-gradient-to-r from-cyber-cyan/10 to-transparent rounded-lg">
                        <h4 class="font-orbitron font-bold text-cyber-purple mb-2">Prophetic Blessing:</h4>
                        {% if nation.nation_name == 'Judah' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Judah is a lion's whelp... The sceptre shall not depart from Judah, 
                            nor a lawgiver from between his feet, until Shiloh come." - Genesis 49:9-10
                        </p>
                        {% elif nation.nation_name == 'Benjamin' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Benjamin shall ravin as a wolf: in the morning he shall devour the prey, 
                            and at night he shall divide the spoil." - Genesis 49:27
                        </p>
                        {% elif nation.nation_name == 'Levi' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Let them teach Jacob thy judgments, and Israel thy law: 
                            they shall put incense before thee." - Deuteronomy 33:10
                        </p>
                        {% elif nation.nation_name == 'Reuben' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Reuben, thou art my firstborn... unstable as water, 
                            thou shalt not excel." - Genesis 49:3-4
                        </p>
                        {% elif nation.nation_name == 'Simeon' %}
                        <p class="text-sm text-text-tertiary italic">
                            "I will divide them in Jacob, and scatter them in Israel." - Genesis 49:7
                        </p>
                        {% elif nation.nation_name == 'Gad' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Gad, a troop shall overcome him: but he shall overcome at the last." - Genesis 49:19
                        </p>
                        {% elif nation.nation_name == 'Asher' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Out of Asher his bread shall be fat, and he shall yield royal dainties." - Genesis 49:20
                        </p>
                        {% elif nation.nation_name == 'Naphtali' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Naphtali is a hind let loose: he giveth goodly words." - Genesis 49:21
                        </p>
                        {% elif nation.nation_name == 'Dan' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Dan shall judge his people, as one of the tribes of Israel." - Genesis 49:16
                        </p>
                        {% elif nation.nation_name == 'Issachar' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Issachar is a strong ass... and became a servant unto tribute." - Genesis 49:14-15
                        </p>
                        {% elif nation.nation_name == 'Zebulun' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Zebulun shall dwell at the haven of the sea; 
                            and he shall be for an haven of ships." - Genesis 49:13
                        </p>
                        {% elif nation.nation_name == 'Joseph' %}
                        <p class="text-sm text-text-tertiary italic">
                            "Joseph is a fruitful bough... The archers have sorely grieved him... 
                            But his bow abode in strength." - Genesis 49:22-24
                        </p>
                        {% else %}
                        <p class="text-sm text-text-tertiary italic">
                            Blessed among the tribes of Israel with unique calling and purpose.
                        </p>
                        {% endif %}
                    </div>

                    <!-- Modern Relevance -->
                    <div class="mb-6 p-4 bg-gradient-to-r from-cyber-purple/10 to-transparent rounded-lg">
                        <h4 class="font-orbitron font-bold text-cyber-blue mb-2">Modern Relevance:</h4>
                        {% if nation.nation_name == 'Judah' %}
                        <p class="text-sm text-text-tertiary">
                            Leadership, governance, and royal authority. Often found in positions of 
                            political power and judicial systems worldwide.
                        </p>
                        {% elif nation.nation_name == 'Benjamin' %}
                        <p class="text-sm text-text-tertiary">
                            Military prowess, strategic thinking, and protective instincts. 
                            Often in defense, security, and tactical fields.
                        </p>
                        {% elif nation.nation_name == 'Levi' %}
                        <p class="text-sm text-text-tertiary">
                            Spiritual leadership, teaching, and service. Found in religious, 
                            educational, and humanitarian roles.
                        </p>
                        {% elif nation.nation_name == 'Joseph' %}
                        <p class="text-sm text-text-tertiary">
                            Prosperity, administration, and provision. Often in business, 
                            agriculture, and economic leadership roles.
                        </p>
                        {% else %}
                        <p class="text-sm text-text-tertiary">
                            Unique gifts and callings that contribute to the covenant community 
                            and the nations of the world.
                        </p>
                        {% endif %}
                    </div>

                    <!-- Lineage Research Tips -->
                    <div class="mb-6 p-4 bg-gradient-to-r from-cyber-green/10 to-transparent rounded-lg">
                        <h4 class="font-orbitron font-bold text-cyber-green mb-2">Research Your Connection:</h4>
                        <ul class="text-sm text-text-tertiary space-y-1">
                            <li>• Family genealogy and oral traditions</li>
                            <li>• Cultural practices and customs</li>
                            <li>• Spiritual calling and gifts</li>
                            <li>• Historical migration patterns</li>
                            <li>• DNA analysis (supporting evidence)</li>
                        </ul>
                    </div>

                    <!-- Gate Keeper Verification -->
                    <div class="text-center">
                        <div class="text-xs text-cyber-cyan mb-2">
                            ⚖️ Requires Gate Keeper Verification
                        </div>
                        <a href="{{ url_for('eden_mode.step1') }}" 
                           class="glass-button-primary px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 hover:scale-105">
                            Begin Registration
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Lineage Research Guidance -->
    <div class="py-20 relative bg-gradient-to-r from-cyber-cyan/10 to-transparent">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="glass-card-premium p-12">
                <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6 text-center">
                    Discovering Your Israelite Heritage
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Research Methods:</h3>
                        <ul class="space-y-3 text-text-secondary">
                            <li class="flex items-start">
                                <span class="text-cyber-cyan mr-2">📜</span>
                                <span>Family records, genealogies, and oral traditions</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-cyan mr-2">🏛️</span>
                                <span>Cultural practices, customs, and religious observances</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-cyan mr-2">🧬</span>
                                <span>DNA analysis and genetic genealogy</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-cyan mr-2">🗺️</span>
                                <span>Historical migration patterns and settlements</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-cyan mr-2">✨</span>
                                <span>Spiritual discernment and calling</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-blue mb-4">Gate Keeper Evaluation:</h3>
                        <ul class="space-y-3 text-text-secondary">
                            <li class="flex items-start">
                                <span class="text-cyber-blue mr-2">⚖️</span>
                                <span>Multiple forms of supporting evidence</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-blue mr-2">👥</span>
                                <span>Community testimony and recognition</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-blue mr-2">📖</span>
                                <span>Biblical knowledge and understanding</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-blue mr-2">💎</span>
                                <span>Covenant commitment and lifestyle</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-blue mr-2">🎯</span>
                                <span>Tribal characteristics and calling</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="text-center">
                    <p class="text-lg text-text-secondary mb-6">
                        Ready to begin your covenant identity journey? Start with Eden Mode 
                        to discover your biblical heritage and tribal connection.
                    </p>
                    <a href="{{ url_for('eden_mode.step1') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        🌟 Begin Eden Mode
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
