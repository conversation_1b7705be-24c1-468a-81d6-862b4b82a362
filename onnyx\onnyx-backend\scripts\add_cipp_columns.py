#!/usr/bin/env python3
"""
Add CIPP Columns Script
Adds Covenant Identity Protection Protocol columns to existing identities table
"""

import os
import sys
import time
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.cipp_columns")

def add_cipp_columns():
    """Add CIPP columns to identities table."""
    try:
        logger.info("🛡️ Adding CIPP columns to identities table...")
        
        # List of CIPP columns to add
        cipp_columns = [
            ("nation_of_origin", "TEXT DEFAULT 'JU'"),
            ("role_class", "TEXT DEFAULT 'Citizen'"),
            ("etzem_score", "INTEGER DEFAULT 0"),
            ("zeman_count", "INTEGER DEFAULT 0"),
            ("protection_tier", "TEXT DEFAULT 'Basic'"),
            ("verification_level", "INTEGER DEFAULT 0"),
            ("covenant_accepted", "BOOLEAN DEFAULT 0"),
            ("vault_status", "TEXT DEFAULT 'Active'"),
            ("last_activity_season", "INTEGER DEFAULT 0"),
            ("nation_code", "TEXT DEFAULT 'IL'"),
            ("nation_name", "TEXT DEFAULT 'Israel'")
        ]
        
        for column_name, column_def in cipp_columns:
            try:
                # Check if column already exists
                result = db.query_one("PRAGMA table_info(identities)")
                existing_columns = db.query("PRAGMA table_info(identities)")
                column_names = [col['name'] for col in existing_columns]
                
                if column_name not in column_names:
                    alter_sql = f"ALTER TABLE identities ADD COLUMN {column_name} {column_def}"
                    db.execute(alter_sql)
                    logger.info(f"✅ Added column: {column_name}")
                else:
                    logger.info(f"⏭️ Column already exists: {column_name}")
                    
            except Exception as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"⏭️ Column already exists: {column_name}")
                else:
                    logger.error(f"❌ Failed to add column {column_name}: {e}")
                    raise
        
        logger.info("✅ CIPP columns added successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to add CIPP columns: {e}")
        raise

def create_cipp_indexes():
    """Create indexes for CIPP columns."""
    try:
        logger.info("📊 Creating CIPP indexes on identities table...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_identities_nation_origin ON identities(nation_of_origin)",
            "CREATE INDEX IF NOT EXISTS idx_identities_role_class ON identities(role_class)",
            "CREATE INDEX IF NOT EXISTS idx_identities_protection_tier ON identities(protection_tier)",
            "CREATE INDEX IF NOT EXISTS idx_identities_verification_level ON identities(verification_level)",
            "CREATE INDEX IF NOT EXISTS idx_identities_covenant_accepted ON identities(covenant_accepted)",
            "CREATE INDEX IF NOT EXISTS idx_identities_vault_status ON identities(vault_status)"
        ]
        
        for index_sql in indexes:
            db.execute(index_sql)
        
        logger.info(f"✅ Created {len(indexes)} CIPP indexes on identities table")
        
    except Exception as e:
        logger.error(f"❌ Failed to create CIPP indexes: {e}")
        raise

def update_existing_identities():
    """Update existing identities with default CIPP values."""
    try:
        logger.info("🔄 Updating existing identities with CIPP defaults...")
        
        # Get all identities that need CIPP updates
        identities = db.query("""
            SELECT identity_id, name FROM identities 
            WHERE nation_of_origin IS NULL OR nation_of_origin = ''
        """)
        
        if not identities:
            logger.info("No identities need CIPP updates")
            return
        
        current_time = int(time.time())
        updated_count = 0
        
        for identity in identities:
            identity_id = identity['identity_id']
            
            try:
                # Update with CIPP defaults
                db.execute("""
                    UPDATE identities SET
                        nation_of_origin = 'JU',
                        nation_code = 'JU',
                        nation_name = 'Judah',
                        role_class = 'Citizen',
                        etzem_score = 10,
                        zeman_count = 0,
                        protection_tier = 'Basic',
                        verification_level = 0,
                        covenant_accepted = 1,
                        vault_status = 'Active',
                        last_activity_season = ?
                    WHERE identity_id = ?
                """, (current_time, identity_id))
                
                updated_count += 1
                logger.info(f"✅ Updated identity: {identity['name']} ({identity_id})")
                
            except Exception as e:
                logger.error(f"❌ Failed to update identity {identity_id}: {e}")
                continue
        
        logger.info(f"🎉 Updated {updated_count} identities with CIPP defaults")
        
    except Exception as e:
        logger.error(f"❌ Failed to update existing identities: {e}")
        raise

def main():
    """Main function."""
    try:
        logger.info("🛡️ Starting CIPP Columns Addition...")
        
        # Check if database is accessible
        try:
            db.query_one("SELECT COUNT(*) as count FROM identities")
        except Exception as e:
            logger.error(f"❌ Cannot access database: {e}")
            return False
        
        # Add CIPP columns
        add_cipp_columns()
        
        # Create indexes
        create_cipp_indexes()
        
        # Update existing identities
        update_existing_identities()
        
        logger.info("🎉 CIPP Columns Addition completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ CIPP Columns Addition failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
