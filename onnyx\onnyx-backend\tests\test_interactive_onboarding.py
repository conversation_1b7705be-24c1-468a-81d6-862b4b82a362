#!/usr/bin/env python3
"""
Test Interactive Onboarding Dashboard
"""

import requests
import sys

def test_interactive_features():
    """Test the interactive features of the onboarding dashboard"""
    print("Testing interactive onboarding dashboard features...")
    
    # Test the main dashboard
    try:
        print("\n1. Testing main onboarding dashboard...")
        response = requests.get("http://127.0.0.1:5000/onboarding/", timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text.lower()
            
            # Check for interactive elements
            interactive_checks = [
                ('clickable cards', 'cursor-pointer' in content),
                ('hover effects', 'hover:scale-105' in content),
                ('click handlers', 'onclick=' in content),
                ('modal container', 'listmodal' in content),
                ('javascript functions', 'showidentitieslist' in content),
                ('api endpoints', 'fetch(' in content),
                ('statistics display', 'total identities' in content)
            ]
            
            success_count = 0
            for check_name, check_result in interactive_checks:
                if check_result:
                    print(f"✅ {check_name}: Found")
                    success_count += 1
                else:
                    print(f"❌ {check_name}: Missing")
            
            print(f"\nInteractive features: {success_count}/{len(interactive_checks)} found")
            return success_count >= 5  # Most features should be present
            
        elif response.status_code in [302, 403]:
            print("🔒 Dashboard requires authentication (expected)")
            return True  # This is correct behavior
            
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing dashboard: {e}")
        return False

def test_api_endpoints_structure():
    """Test that API endpoints are properly structured (even if they require auth)"""
    print("\n2. Testing API endpoint structure...")
    
    endpoints = [
        '/onboarding/api/identities',
        '/onboarding/api/tribal-elders',
        '/onboarding/api/validators', 
        '/onboarding/api/citizens'
    ]
    
    success_count = 0
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=5)
            
            # We expect either:
            # - 200 with JSON (if somehow accessible)
            # - 302/403 (redirect to login/forbidden - correct auth behavior)
            # - 404 would be bad (endpoint doesn't exist)
            
            if response.status_code in [200, 302, 403]:
                print(f"✅ {endpoint}: Properly configured (status {response.status_code})")
                success_count += 1
            elif response.status_code == 404:
                print(f"❌ {endpoint}: Not found (404)")
            else:
                print(f"❓ {endpoint}: Unexpected status {response.status_code}")
                
        except Exception as e:
            print(f"❌ {endpoint}: Error - {e}")
    
    return success_count == len(endpoints)

def test_database_queries():
    """Test that the underlying database queries work"""
    print("\n3. Testing database queries...")
    
    try:
        import sys
        sys.path.append('.')
        from shared.db.db import db
        
        # Test the queries used by the onboarding dashboard
        queries = [
            ("Identities count", "SELECT COUNT(*) as count FROM identities"),
            ("Active selas count", "SELECT COUNT(*) as count FROM selas WHERE status = 'active'"),
            ("Tribal elders count", "SELECT COUNT(*) as count FROM identities WHERE role = 'tribal_elder'"),
            ("Citizens count", "SELECT COUNT(*) as count FROM identities WHERE role = 'citizen'"),
            ("Recent identities", "SELECT identity_id, name, role, tribe_name, created_at FROM identities ORDER BY created_at DESC LIMIT 5"),
            ("Recent selas", "SELECT sela_id, name, identity_id, mining_tier, created_at FROM selas WHERE status = 'active' ORDER BY created_at DESC LIMIT 5")
        ]
        
        success_count = 0
        for name, query in queries:
            try:
                result = db.query(query) if "SELECT COUNT" not in query else db.query_one(query)
                count = result['count'] if isinstance(result, dict) and 'count' in result else len(result) if isinstance(result, list) else 1
                print(f"✅ {name}: Success ({count} records)")
                success_count += 1
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
        
        return success_count == len(queries)
        
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Interactive Onboarding Dashboard")
    print("=" * 60)
    
    interactive_success = test_interactive_features()
    api_success = test_api_endpoints_structure()
    db_success = test_database_queries()
    
    print("\n📊 Final Results:")
    print("=" * 30)
    
    if interactive_success:
        print("✅ Interactive features working")
    else:
        print("❌ Interactive features have issues")
        
    if api_success:
        print("✅ API endpoints properly configured")
    else:
        print("❌ API endpoints have issues")
        
    if db_success:
        print("✅ Database queries working")
    else:
        print("❌ Database queries have issues")
    
    overall_success = interactive_success and api_success and db_success
    
    if overall_success:
        print("\n🎉 SUCCESS: Interactive onboarding dashboard is fully functional!")
        print("   - Statistics cards are clickable with hover effects")
        print("   - Modal system is in place for detailed views")
        print("   - API endpoints are properly protected")
        print("   - Database queries are working correctly")
        print("   - JavaScript functions are implemented")
    else:
        print("\n⚠️ Some issues found - check the details above")
    
    sys.exit(0 if overall_success else 1)
