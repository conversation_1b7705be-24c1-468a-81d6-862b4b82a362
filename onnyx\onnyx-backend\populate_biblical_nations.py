#!/usr/bin/env python3
"""
Populate Biblical Nations Data
"""

import sqlite3
import sys
import os
import time

def populate_biblical_nations():
    """Populate the biblical_nations table with comprehensive data"""
    
    print("📜 Populating Biblical Nations Data")
    print("=" * 40)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        current_time = int(time.time())
        
        # 12 Tribes of Israel (Covenant Nations)
        israel_tribes = [
            {
                'nation_code': 'JU', 'nation_name': 'Judah', 'tribe_name': 'Tribe of Judah',
                'nation_type': 'covenant', 'description': 'The royal tribe, from which kings and the Messiah come',
                'flag_symbol': '🦁', 'biblical_reference': 'Genesis 49:9-10',
                'tribal_calling': 'Leadership and kingship', 'governance_weight': 3,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of <PERSON> and <PERSON>',
                'historical_connection': 'Royal lineage, Temple guardians'
            },
            {
                'nation_code': 'BE', 'nation_name': '<PERSON>', 'tribe_name': 'Tribe of Benjamin',
                'nation_type': 'covenant', 'description': 'The beloved tribe, fierce warriors and protectors',
                'flag_symbol': '🐺', 'biblical_reference': 'Genesis 49:27',
                'tribal_calling': 'Protection and warfare', 'governance_weight': 2,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Rachel',
                'historical_connection': 'Temple guards, skilled archers'
            },
            {
                'nation_code': 'LE', 'nation_name': 'Levi', 'tribe_name': 'Tribe of Levi',
                'nation_type': 'covenant', 'description': 'The priestly tribe, ministers of the sanctuary',
                'flag_symbol': '⚖️', 'biblical_reference': 'Numbers 3:6-8',
                'tribal_calling': 'Priesthood and teaching', 'governance_weight': 3,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Leah',
                'historical_connection': 'Temple priests, Torah teachers'
            },
            {
                'nation_code': 'RE', 'nation_name': 'Reuben', 'tribe_name': 'Tribe of Reuben',
                'nation_type': 'covenant', 'description': 'The firstborn, strong but unstable as water',
                'flag_symbol': '🌊', 'biblical_reference': 'Genesis 49:3-4',
                'tribal_calling': 'Strength and pioneering', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Leah',
                'historical_connection': 'Transjordan settlement, cattle herders'
            },
            {
                'nation_code': 'SI', 'nation_name': 'Simeon', 'tribe_name': 'Tribe of Simeon',
                'nation_type': 'covenant', 'description': 'Scattered among brothers, zealous for justice',
                'flag_symbol': '⚔️', 'biblical_reference': 'Genesis 49:5-7',
                'tribal_calling': 'Justice and zeal', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Leah',
                'historical_connection': 'Southern territory, teachers'
            },
            {
                'nation_code': 'GA', 'nation_name': 'Gad', 'tribe_name': 'Tribe of Gad',
                'nation_type': 'covenant', 'description': 'A troop shall overcome him, but he shall overcome at last',
                'flag_symbol': '🛡️', 'biblical_reference': 'Genesis 49:19',
                'tribal_calling': 'Military prowess', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Zilpah',
                'historical_connection': 'Transjordan warriors, border guards'
            },
            {
                'nation_code': 'AS', 'nation_name': 'Asher', 'tribe_name': 'Tribe of Asher',
                'nation_type': 'covenant', 'description': 'His bread shall be fat, and he shall yield royal dainties',
                'flag_symbol': '🌾', 'biblical_reference': 'Genesis 49:20',
                'tribal_calling': 'Prosperity and provision', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Zilpah',
                'historical_connection': 'Coastal territory, olive oil production'
            },
            {
                'nation_code': 'NA', 'nation_name': 'Naphtali', 'tribe_name': 'Tribe of Naphtali',
                'nation_type': 'covenant', 'description': 'A hind let loose, he gives goodly words',
                'flag_symbol': '🦌', 'biblical_reference': 'Genesis 49:21',
                'tribal_calling': 'Freedom and eloquence', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Bilhah',
                'historical_connection': 'Northern territory, swift messengers'
            },
            {
                'nation_code': 'DA', 'nation_name': 'Dan', 'tribe_name': 'Tribe of Dan',
                'nation_type': 'covenant', 'description': 'Dan shall judge his people as one of the tribes of Israel',
                'flag_symbol': '🐍', 'biblical_reference': 'Genesis 49:16-17',
                'tribal_calling': 'Judgment and cunning', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Bilhah',
                'historical_connection': 'Northern migration, skilled craftsmen'
            },
            {
                'nation_code': 'IS', 'nation_name': 'Issachar', 'tribe_name': 'Tribe of Issachar',
                'nation_type': 'covenant', 'description': 'A strong ass couching down between two burdens',
                'flag_symbol': '🐴', 'biblical_reference': 'Genesis 49:14-15',
                'tribal_calling': 'Labor and wisdom', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Leah',
                'historical_connection': 'Agricultural expertise, calendar keepers'
            },
            {
                'nation_code': 'ZE', 'nation_name': 'Zebulun', 'tribe_name': 'Tribe of Zebulun',
                'nation_type': 'covenant', 'description': 'Zebulun shall dwell at the haven of the sea',
                'flag_symbol': '⛵', 'biblical_reference': 'Genesis 49:13',
                'tribal_calling': 'Commerce and seafaring', 'governance_weight': 1,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Leah',
                'historical_connection': 'Coastal trade, maritime commerce'
            },
            {
                'nation_code': 'JO', 'nation_name': 'Joseph', 'tribe_name': 'Tribe of Joseph',
                'nation_type': 'covenant', 'description': 'Joseph is a fruitful bough by a well',
                'flag_symbol': '🌳', 'biblical_reference': 'Genesis 49:22-26',
                'tribal_calling': 'Fruitfulness and blessing', 'governance_weight': 2,
                'ancestral_group': 'Israel', 'ancestral_description': 'Son of Jacob and Rachel',
                'historical_connection': 'Double portion through Ephraim and Manasseh'
            }
        ]
        
        # 12 Dukes of Edom
        edom_dukes = [
            {
                'nation_code': 'TI', 'nation_name': 'Timnah', 'tribe_name': 'Duke Timnah',
                'nation_type': 'witness', 'description': 'First duke of Edom, descendant of Esau',
                'flag_symbol': '👑', 'biblical_reference': 'Genesis 36:40',
                'tribal_calling': 'Leadership among Edomites', 'governance_weight': 1,
                'ancestral_group': 'Edom', 'ancestral_description': 'Descendant of Esau',
                'historical_connection': 'Edomite nobility, mountain dwellers'
            },
            {
                'nation_code': 'AL', 'nation_name': 'Alvah', 'tribe_name': 'Duke Alvah',
                'nation_type': 'witness', 'description': 'Duke of Edom, keeper of ancient ways',
                'flag_symbol': '🏔️', 'biblical_reference': 'Genesis 36:40',
                'tribal_calling': 'Mountain governance', 'governance_weight': 1,
                'ancestral_group': 'Edom', 'ancestral_description': 'Descendant of Esau',
                'historical_connection': 'Seir mountain region'
            },
            {
                'nation_code': 'JE', 'nation_name': 'Jetheth', 'tribe_name': 'Duke Jetheth',
                'nation_type': 'witness', 'description': 'Duke of Edom, wise counselor',
                'flag_symbol': '🦅', 'biblical_reference': 'Genesis 36:40',
                'tribal_calling': 'Wisdom and counsel', 'governance_weight': 1,
                'ancestral_group': 'Edom', 'ancestral_description': 'Descendant of Esau',
                'historical_connection': 'Edomite wisdom tradition'
            },
            {
                'nation_code': 'AH', 'nation_name': 'Aholibamah', 'tribe_name': 'Duke Aholibamah',
                'nation_type': 'witness', 'description': 'Duke of Edom, tent of the high place',
                'flag_symbol': '⛺', 'biblical_reference': 'Genesis 36:41',
                'tribal_calling': 'Sacred places', 'governance_weight': 1,
                'ancestral_group': 'Edom', 'ancestral_description': 'Descendant of Esau',
                'historical_connection': 'Sacred site guardians'
            },
            {
                'nation_code': 'EL', 'nation_name': 'Elah', 'tribe_name': 'Duke Elah',
                'nation_type': 'witness', 'description': 'Duke of Edom, the oak tree',
                'flag_symbol': '🌳', 'biblical_reference': 'Genesis 36:41',
                'tribal_calling': 'Strength and endurance', 'governance_weight': 1,
                'ancestral_group': 'Edom', 'ancestral_description': 'Descendant of Esau',
                'historical_connection': 'Forest and woodland regions'
            },
            {
                'nation_code': 'PI', 'nation_name': 'Pinon', 'tribe_name': 'Duke Pinon',
                'nation_type': 'witness', 'description': 'Duke of Edom, the pearl',
                'flag_symbol': '💎', 'biblical_reference': 'Genesis 36:41',
                'tribal_calling': 'Precious things', 'governance_weight': 1,
                'ancestral_group': 'Edom', 'ancestral_description': 'Descendant of Esau',
                'historical_connection': 'Mining and precious stones'
            }
        ]
        
        # Insert Israel tribes
        print("📜 Inserting 12 Tribes of Israel...")
        for tribe in israel_tribes:
            cursor.execute('''
                INSERT INTO biblical_nations 
                (nation_code, nation_name, tribe_name, nation_type, description, flag_symbol,
                 biblical_reference, tribal_calling, governance_weight, ancestral_group,
                 ancestral_description, historical_connection)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                tribe['nation_code'], tribe['nation_name'], tribe['tribe_name'],
                tribe['nation_type'], tribe['description'], tribe['flag_symbol'],
                tribe['biblical_reference'], tribe['tribal_calling'], tribe['governance_weight'],
                tribe['ancestral_group'], tribe['ancestral_description'], tribe['historical_connection']
            ))
            print(f"   ✅ {tribe['nation_name']} ({tribe['flag_symbol']})")
        
        # Insert Edom dukes
        print("\n👑 Inserting 12 Dukes of Edom...")
        for duke in edom_dukes:
            cursor.execute('''
                INSERT INTO biblical_nations 
                (nation_code, nation_name, tribe_name, nation_type, description, flag_symbol,
                 biblical_reference, tribal_calling, governance_weight, ancestral_group,
                 ancestral_description, historical_connection)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                duke['nation_code'], duke['nation_name'], duke['tribe_name'],
                duke['nation_type'], duke['description'], duke['flag_symbol'],
                duke['biblical_reference'], duke['tribal_calling'], duke['governance_weight'],
                duke['ancestral_group'], duke['ancestral_description'], duke['historical_connection']
            ))
            print(f"   ✅ {duke['nation_name']} ({duke['flag_symbol']})")
        
        conn.commit()
        
        # Verify data was inserted
        cursor.execute("SELECT COUNT(*) FROM biblical_nations WHERE nation_type = 'covenant'")
        covenant_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM biblical_nations WHERE ancestral_group = 'Edom'")
        edom_count = cursor.fetchone()[0]
        
        print(f"\n📊 Data inserted successfully:")
        print(f"   - Covenant nations: {covenant_count}")
        print(f"   - Edom dukes: {edom_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error populating biblical nations: {e}")
        return False

if __name__ == "__main__":
    print("📜 Populating Biblical Nations Database")
    print("=" * 50)
    
    success = populate_biblical_nations()
    
    if success:
        print("\n🎉 SUCCESS: Biblical nations data populated!")
        print("   - 12 Tribes of Israel added")
        print("   - 12 Dukes of Edom added")
        print("   - Enhanced tribes overview page should now work")
    else:
        print("\n❌ Failed to populate biblical nations data")
    
    sys.exit(0 if success else 1)
