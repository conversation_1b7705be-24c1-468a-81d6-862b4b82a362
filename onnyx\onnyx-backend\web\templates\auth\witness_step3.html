{% extends "base.html" %}

{% block title %}Heritage Confirmation - ONNYX Eden Mode{% endblock %}
{% block meta_description %}Confirm your witness nation heritage selection and define your role in the covenant community.{% endblock %}

{% block head %}
<style>
    .confirmation-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .heritage-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border: 2px solid rgba(34, 197, 94, 0.4);
        border-radius: 16px;
        padding: 2rem;
        margin: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    .heritage-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #22C55E, #34D399);
    }
    
    .nation-display {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .nation-flag {
        font-size: 5rem;
        margin-right: 2rem;
    }
    
    .nation-info h2 {
        font-size: 2rem;
        font-weight: bold;
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .nation-details {
        color: #34D399;
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
    }
    
    .nation-description {
        color: #D1D5DB;
        line-height: 1.6;
    }
    
    .role-selection {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .role-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .role-option {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .role-option:hover {
        border-color: #34D399;
        background: rgba(34, 197, 94, 0.1);
        transform: translateY(-2px);
    }
    
    .role-option.selected {
        border-color: #34D399;
        background: rgba(34, 197, 94, 0.2);
    }
    
    .role-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .role-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .role-description {
        font-size: 0.875rem;
        color: #D1D5DB;
        line-height: 1.4;
    }
    
    .witness-privileges {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1));
        border: 1px solid rgba(34, 197, 94, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .privilege-item {
        display: flex;
        align-items: center;
        margin: 1rem 0;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
    }
    
    .privilege-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #22C55E, #34D399);
        border-radius: 50%;
        color: white;
        font-weight: bold;
    }
    
    .progress-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
    }
    
    .progress-step {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 0.5rem;
        font-weight: bold;
        color: white;
    }
    
    .progress-step.completed {
        background: linear-gradient(135deg, #10B981, #34D399);
    }
    
    .progress-step.current {
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        animation: pulse 2s infinite;
    }
    
    .progress-step.pending {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.6);
    }
    
    .progress-connector {
        width: 3rem;
        height: 3px;
        background: rgba(255, 255, 255, 0.2);
        margin: 0 0.5rem;
    }
    
    .progress-connector.completed {
        background: linear-gradient(90deg, #10B981, #34D399);
    }
    
    .confirmation-statement {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-48 h-48 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float-delayed"></div>
        <div class="absolute bottom-20 left-1/3 w-40 h-40 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
    </div>

    <div class="container mx-auto px-4 py-8 relative z-10">
        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step completed">1</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">2</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step current">3</div>
            <div class="progress-connector pending"></div>
            <div class="progress-step pending">4</div>
        </div>

        <div class="confirmation-container">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">
                    <span class="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 bg-clip-text text-transparent">
                        Heritage Confirmation
                    </span>
                </h1>
                <p class="text-gray-300 max-w-2xl mx-auto">
                    Confirm your witness nation heritage and define your role in supporting the covenant community.
                </p>
            </div>

            <!-- Selected Heritage Display -->
            <div class="heritage-card">
                <div class="nation-display">
                    <div class="nation-flag" id="nationFlag">🌍</div>
                    <div class="nation-info">
                        <h2 id="nationName">Loading...</h2>
                        <div class="nation-details">
                            <span id="tribalLineage">Tribe of ...</span> • 
                            <span id="ancestralGroup">... Heritage</span>
                        </div>
                        <p class="nation-description" id="nationDescription">
                            Heritage description loading...
                        </p>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="inline-flex items-center bg-emerald-900 bg-opacity-50 rounded-full px-4 py-2 text-emerald-200">
                        <span class="mr-2">✅</span>
                        <span class="font-semibold">Witness Nation Selection Confirmed</span>
                    </div>
                </div>
            </div>

            <!-- Role Selection -->
            <div class="role-selection">
                <h3 class="text-xl font-bold text-white mb-4">Choose Your Covenant Role</h3>
                <p class="text-gray-300 mb-4">
                    As a Witness Nation member, you can participate in the covenant community in various capacities. 
                    Select the role that best describes your calling:
                </p>
                
                <div class="role-options">
                    <div class="role-option" data-role="supporter">
                        <div class="role-icon">🤝</div>
                        <div class="role-title">Community Supporter</div>
                        <div class="role-description">Support covenant members through commerce, fellowship, and encouragement</div>
                    </div>
                    
                    <div class="role-option" data-role="merchant">
                        <div class="role-icon">🏪</div>
                        <div class="role-title">Biblical Merchant</div>
                        <div class="role-description">Engage in righteous commerce using biblical principles and Onnyx currency</div>
                    </div>
                    
                    <div class="role-option" data-role="student">
                        <div class="role-icon">📚</div>
                        <div class="role-title">Torah Student</div>
                        <div class="role-description">Learn biblical law and wisdom to understand covenant principles</div>
                    </div>
                    
                    <div class="role-option" data-role="advocate">
                        <div class="role-icon">⚖️</div>
                        <div class="role-title">Justice Advocate</div>
                        <div class="role-description">Promote biblical justice and righteous governance in your community</div>
                    </div>
                    
                    <div class="role-option" data-role="helper">
                        <div class="role-icon">🛠️</div>
                        <div class="role-title">Community Helper</div>
                        <div class="role-description">Provide practical assistance and skills to strengthen the community</div>
                    </div>
                    
                    <div class="role-option" data-role="observer">
                        <div class="role-icon">👁️</div>
                        <div class="role-title">Covenant Observer</div>
                        <div class="role-description">Learn about covenant principles while maintaining observer status</div>
                    </div>
                </div>
            </div>

            <!-- Witness Nation Privileges -->
            <div class="witness-privileges">
                <div class="flex items-center mb-4">
                    <span class="text-2xl mr-3">🌟</span>
                    <h3 class="text-xl font-bold text-emerald-100">Witness Nation Privileges</h3>
                </div>
                
                <div class="privilege-item">
                    <div class="privilege-icon">💰</div>
                    <div>
                        <div class="font-semibold text-white">Onnyx Currency Access</div>
                        <div class="text-sm text-emerald-200">Participate in biblical commerce using sound money principles</div>
                    </div>
                </div>
                
                <div class="privilege-item">
                    <div class="privilege-icon">🏛️</div>
                    <div>
                        <div class="font-semibold text-white">Community Participation</div>
                        <div class="text-sm text-emerald-200">Join covenant assemblies and community decisions</div>
                    </div>
                </div>
                
                <div class="privilege-item">
                    <div class="privilege-icon">📖</div>
                    <div>
                        <div class="font-semibold text-white">Educational Resources</div>
                        <div class="text-sm text-emerald-200">Access to biblical law, history, and covenant principles</div>
                    </div>
                </div>
                
                <div class="privilege-item">
                    <div class="privilege-icon">🤲</div>
                    <div>
                        <div class="font-semibold text-white">Mutual Support Network</div>
                        <div class="text-sm text-emerald-200">Connect with like-minded covenant supporters worldwide</div>
                    </div>
                </div>
            </div>

            <!-- Confirmation Statement -->
            <div class="confirmation-statement">
                <h4 class="text-lg font-bold text-white mb-3">Witness Nation Covenant Statement</h4>
                <p class="text-gray-300 leading-relaxed">
                    As a member of <strong id="confirmNationName">your selected nation</strong>, 
                    I acknowledge my role as a witness to God's covenant with Israel. I commit to supporting 
                    the restoration of biblical order and righteousness, participating in honest commerce, 
                    and contributing to the covenant community according to my abilities and calling.
                </p>
                
                <div class="mt-4 flex items-center">
                    <input type="checkbox" id="covenantAccept" class="mr-3 transform scale-125">
                    <label for="covenantAccept" class="text-emerald-200">
                        I accept this covenant statement and agree to participate as a Witness Nation member
                    </label>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between items-center mt-8">
                <a href="{{ url_for('eden_mode.witness_step2') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                    ← Back to Nation Selection
                </a>
                
                <button id="proceedToRegistration" disabled
                        class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100">
                    Proceed to Registration →
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load nation data from session storage or URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const selectedNationCode = urlParams.get('nation') || sessionStorage.getItem('edenMode_selectedNation');
    
    // Mock nation data (in production, this would come from the backend)
    const nationData = {
        'GRC': { name: 'Greece', tribe: 'Javan', group: 'Japhethite', flag: '🇬🇷', description: 'Descendants of Javan, known for philosophy and learning, birthplace of democracy and western civilization.' },
        'ITA': { name: 'Italy', tribe: 'Javan', group: 'Japhethite', flag: '🇮🇹', description: 'Roman heritage, center of early Christianity, known for law and governance.' },
        'DEU': { name: 'Germany', tribe: 'Ashkenaz', group: 'Japhethite', flag: '🇩🇪', description: 'Germanic tribes, reformation heritage, known for industry and precision.' },
        'FRA': { name: 'France', tribe: 'Gomer', group: 'Japhethite', flag: '🇫🇷', description: 'Gallic heritage, daughter of Zion, known for culture and enlightenment.' },
        'GBR': { name: 'Britain', tribe: 'Tarshish', group: 'Japhethite', flag: '🇬🇧', description: 'Maritime nation, ships of Tarshish, known for exploration and trade.' },
        'EGY': { name: 'Egypt', tribe: 'Mizraim', group: 'Hamitic', flag: '🇪🇬', description: 'Land of bondage and deliverance, ancient civilization and wisdom.' },
        'ETH': { name: 'Ethiopia', tribe: 'Cush', group: 'Hamitic', flag: '🇪🇹', description: 'Land of Cush, ancient kingdom, never fully colonized.' },
        'USA': { name: 'United States', tribe: 'Mixed', group: 'Mixed Heritage', flag: '🇺🇸', description: 'Nation of many peoples and tribes, beacon of liberty and opportunity.' },
        'CHN': { name: 'China', tribe: 'Sinim', group: 'Asiatic', flag: '🇨🇳', description: 'Land of Sinim, eastern kingdom, ancient civilization and wisdom.' }
    };
    
    const nation = nationData[selectedNationCode];
    let selectedRole = null;
    
    if (nation) {
        // Populate nation information
        document.getElementById('nationFlag').textContent = nation.flag;
        document.getElementById('nationName').textContent = nation.name;
        document.getElementById('tribalLineage').textContent = `Tribe of ${nation.tribe}`;
        document.getElementById('ancestralGroup').textContent = `${nation.group} Heritage`;
        document.getElementById('nationDescription').textContent = nation.description;
        document.getElementById('confirmNationName').textContent = nation.name;
        
        // Store in session
        sessionStorage.setItem('edenMode_selectedNation', selectedNationCode);
        sessionStorage.setItem('edenMode_selectedNationName', nation.name);
        sessionStorage.setItem('edenMode_selectedTribe', nation.tribe);
        sessionStorage.setItem('edenMode_ancestralGroup', nation.group);
    } else {
        // Redirect back if no valid nation
        window.location.href = "{{ url_for('eden_mode.witness_step2') }}";
        return;
    }
    
    // Role selection handling
    document.querySelectorAll('.role-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selection from all options
            document.querySelectorAll('.role-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // Select clicked option
            this.classList.add('selected');
            selectedRole = this.dataset.role;
            
            // Store in session
            sessionStorage.setItem('edenMode_selectedRole', selectedRole);
            
            // Check form completion
            checkFormCompletion();
        });
    });
    
    // Covenant acceptance handling
    document.getElementById('covenantAccept').addEventListener('change', checkFormCompletion);
    
    function checkFormCompletion() {
        const covenantAccepted = document.getElementById('covenantAccept').checked;
        const hasRole = selectedRole !== null;
        
        document.getElementById('proceedToRegistration').disabled = !(covenantAccepted && hasRole);
    }
    
    // Proceed button handling
    document.getElementById('proceedToRegistration').addEventListener('click', function() {
        if (this.disabled) return;
        
        // Store final confirmation data
        sessionStorage.setItem('edenMode_covenantAccepted', 'true');
        sessionStorage.setItem('edenMode_roleConfirmed', 'true');
        
        // Navigate to final step
        window.location.href = "{{ url_for('eden_mode.witness_step4') }}";
    });
    
    // Restore previous selections if returning to this page
    const savedRole = sessionStorage.getItem('edenMode_selectedRole');
    if (savedRole) {
        const roleOption = document.querySelector(`[data-role="${savedRole}"]`);
        if (roleOption) {
            roleOption.click();
        }
    }
    
    const covenantAccepted = sessionStorage.getItem('edenMode_covenantAccepted');
    if (covenantAccepted === 'true') {
        document.getElementById('covenantAccept').checked = true;
        checkFormCompletion();
    }
});
</script>
{% endblock %}
