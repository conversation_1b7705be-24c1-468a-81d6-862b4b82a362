"""
ONNYX Platform Configuration
Central configuration management for the ONNYX covenant platform.

This module provides:
1. Database configuration
2. Network parameters
3. Biblical tokenomics settings
4. Security configurations
5. Platform-wide constants

Author: ONNYX Development Team
Date: 2025-07-17
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ONNYXConfig:
    """Central configuration manager for ONNYX platform."""
    
    def __init__(self):
        self.base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.config_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Core configuration
        self.db_path = os.path.join(self.data_dir, 'onnyx.db')
        self.schema_path = os.path.join(self.base_dir, 'shared', 'schemas', 'production_schema.sql')
        
        # Load configuration files
        self._load_chain_parameters()
        self._load_network_parameters()
        
        logger.info(f"ONNYX Config initialized - DB: {self.db_path}")
    
    def _load_chain_parameters(self):
        """Load blockchain chain parameters."""
        try:
            chain_params_file = os.path.join(self.data_dir, 'chain_params.json')
            
            # Fallback to web data directory if main data directory doesn't have it
            if not os.path.exists(chain_params_file):
                chain_params_file = os.path.join(self.base_dir, 'web', 'data', 'chain_params.json')
            
            if os.path.exists(chain_params_file):
                with open(chain_params_file, 'r') as f:
                    self.chain_parameters = json.load(f)
            else:
                # Default chain parameters
                self.chain_parameters = {
                    "block_reward": 50.0,
                    "reward_token": "ONX",
                    "min_block_reward": 2,
                    "max_block_reward": 200,
                    "gleaning_pool_percentage": 0.02,
                    "concentration_threshold": 1000000,
                    "deed_score_multiplier": 0.1,
                    "sabbath_deed_bonus": 0.2
                }
                
                logger.warning("Using default chain parameters - chain_params.json not found")
                
        except Exception as e:
            logger.error(f"Error loading chain parameters: {e}")
            self.chain_parameters = {}
    
    def _load_network_parameters(self):
        """Load network configuration parameters."""
        try:
            network_params_file = os.path.join(self.config_dir, 'network_params.json')
            
            if os.path.exists(network_params_file):
                with open(network_params_file, 'r') as f:
                    self.network_parameters = json.load(f)
            else:
                # Default network parameters
                self.network_parameters = {
                    "network_name": "ONNYX Covenant Network",
                    "network_id": "onnyx-mainnet",
                    "default_port": 8333,
                    "max_peers": 12,
                    "tribal_nodes": 12
                }
                
                logger.warning("Using default network parameters - network_params.json not found")
                
        except Exception as e:
            logger.error(f"Error loading network parameters: {e}")
            self.network_parameters = {}
    
    @property
    def database_url(self) -> str:
        """Get database URL/path."""
        return f"sqlite:///{self.db_path}"
    
    def get_chain_parameter(self, key: str, default: Any = None) -> Any:
        """Get a chain parameter value."""
        return self.chain_parameters.get(key, default)
    
    def get_network_parameter(self, key: str, default: Any = None) -> Any:
        """Get a network parameter value."""
        return self.network_parameters.get(key, default)
    
    def update_chain_parameter(self, key: str, value: Any) -> bool:
        """Update a chain parameter value."""
        try:
            self.chain_parameters[key] = value
            
            # Save to file
            chain_params_file = os.path.join(self.data_dir, 'chain_params.json')
            with open(chain_params_file, 'w') as f:
                json.dump(self.chain_parameters, f, indent=2)
            
            logger.info(f"Updated chain parameter {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating chain parameter {key}: {e}")
            return False
    
    def get_biblical_parameters(self) -> Dict[str, Any]:
        """Get biblical tokenomics parameters."""
        return {
            "gleaning_pool_percentage": self.get_chain_parameter("gleaning_pool_percentage", 0.02),
            "jubilee_interval_blocks": self.get_chain_parameter("jubilee_interval_blocks", 50400),
            "sabbatical_interval_blocks": self.get_chain_parameter("sabbatical_interval_blocks", 7200),
            "deed_score_multiplier": self.get_chain_parameter("deed_score_multiplier", 0.1),
            "sabbath_deed_bonus": self.get_chain_parameter("sabbath_deed_bonus", 0.2),
            "concentration_threshold": self.get_chain_parameter("concentration_threshold", 1000000),
            "firstfruits_etzem_reward": self.get_chain_parameter("firstfruits_etzem_reward", 2)
        }
    
    def get_mining_parameters(self) -> Dict[str, Any]:
        """Get mining-related parameters."""
        return {
            "block_reward": self.get_chain_parameter("block_reward", 50.0),
            "reward_token": self.get_chain_parameter("reward_token", "ONX"),
            "min_block_reward": self.get_chain_parameter("min_block_reward", 2),
            "max_block_reward": self.get_chain_parameter("max_block_reward", 200),
            "target_block_time": self.get_chain_parameter("target_block_time", 60),
            "difficulty_adjustment_period": self.get_chain_parameter("difficulty_adjustment_period", 100)
        }
    
    def get_governance_parameters(self) -> Dict[str, Any]:
        """Get governance-related parameters."""
        return {
            "scroll_voting_period": self.get_chain_parameter("scroll_voting_period", 604800),  # 7 days
            "scroll_implementation_delay": self.get_chain_parameter("scroll_implementation_delay", 86400),  # 1 day
            "min_voting_threshold": self.get_chain_parameter("min_voting_threshold", 0.67),  # 2/3 majority
            "elder_consensus_threshold": self.get_chain_parameter("elder_consensus_threshold", 0.67)
        }
    
    def is_development_mode(self) -> bool:
        """Check if running in development mode."""
        return os.environ.get('ONNYX_ENV', 'development').lower() == 'development'
    
    def is_production_mode(self) -> bool:
        """Check if running in production mode."""
        return os.environ.get('ONNYX_ENV', 'development').lower() == 'production'
    
    def get_log_level(self) -> str:
        """Get logging level."""
        if self.is_development_mode():
            return os.environ.get('ONNYX_LOG_LEVEL', 'DEBUG')
        else:
            return os.environ.get('ONNYX_LOG_LEVEL', 'INFO')
    
    def get_secret_key(self) -> str:
        """Get Flask secret key."""
        return os.environ.get('ONNYX_SECRET_KEY', 'dev-secret-key-change-in-production')
    
    def get_api_keys(self) -> Dict[str, str]:
        """Get API keys for external services."""
        return {
            'openai_api_key': os.environ.get('OPENAI_API_KEY', ''),
            'anthropic_api_key': os.environ.get('ANTHROPIC_API_KEY', ''),
            'pinecone_api_key': os.environ.get('PINECONE_API_KEY', '')
        }
    
    def validate_configuration(self) -> bool:
        """Validate the current configuration."""
        try:
            # Check database path is accessible
            if not os.path.exists(os.path.dirname(self.db_path)):
                logger.error(f"Database directory does not exist: {os.path.dirname(self.db_path)}")
                return False
            
            # Check schema file exists
            if not os.path.exists(self.schema_path):
                logger.warning(f"Schema file not found: {self.schema_path}")
                # This is not a fatal error as we can create tables programmatically
            
            # Validate required chain parameters
            required_params = ['block_reward', 'reward_token', 'gleaning_pool_percentage']
            for param in required_params:
                if param not in self.chain_parameters:
                    logger.error(f"Missing required chain parameter: {param}")
                    return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def __str__(self) -> str:
        """String representation of configuration."""
        return f"ONNYXConfig(db_path={self.db_path}, env={os.environ.get('ONNYX_ENV', 'development')})"

# Global configuration instance
onnyx_config = ONNYXConfig()

# Validate configuration on import
if not onnyx_config.validate_configuration():
    logger.warning("Configuration validation failed - some features may not work correctly")

# Export commonly used values for convenience
DB_PATH = onnyx_config.db_path
SCHEMA_PATH = onnyx_config.schema_path
CHAIN_PARAMETERS = onnyx_config.chain_parameters
NETWORK_PARAMETERS = onnyx_config.network_parameters
