"""
Onnyx Vote Model

This module defines the Vote model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.vote")

class Vote(BaseModel):
    """
    Vote model for the Onnyx blockchain.
    
    A Vote represents a governance vote in the Onnyx ecosystem.
    """
    
    TABLE_NAME = "votes"
    PRIMARY_KEY = "vote_id"
    
    def __init__(
        self,
        vote_id: str,
        scroll_id: str,
        sela_id: str,
        identity_id: str,
        vote: str,
        timestamp: int,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a Vote.
        
        Args:
            vote_id: The vote ID
            scroll_id: The Voice Scroll ID
            sela_id: The Sela ID
            identity_id: The identity ID
            vote: The vote (e.g., "yes", "no", "abstain")
            timestamp: The timestamp when the vote was cast
            reason: The reason for the vote (optional)
            metadata: Additional metadata for the vote (optional)
        """
        self.vote_id = vote_id
        self.scroll_id = scroll_id
        self.sela_id = sela_id
        self.identity_id = identity_id
        self.vote = vote
        self.timestamp = timestamp
        self.reason = reason
        self.metadata = metadata or {}
    
    @classmethod
    def create_table(cls) -> None:
        """Create the Vote table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            scroll_id TEXT NOT NULL,
            sela_id TEXT NOT NULL,
            identity_id TEXT NOT NULL,
            vote TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            reason TEXT,
            metadata TEXT
        )
        """)
        
        conn.commit()
        conn.close()
    
    @classmethod
    def create(
        cls,
        vote_id: str,
        scroll_id: str,
        sela_id: str,
        identity_id: str,
        vote: str,
        timestamp: Optional[int] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "Vote":
        """
        Create a new Vote.
        
        Args:
            vote_id: The vote ID
            scroll_id: The Voice Scroll ID
            sela_id: The Sela ID
            identity_id: The identity ID
            vote: The vote (e.g., "yes", "no", "abstain")
            timestamp: The timestamp when the vote was cast (optional)
            reason: The reason for the vote (optional)
            metadata: Additional metadata for the vote (optional)
        
        Returns:
            The created Vote
        """
        # Create the table if it doesn't exist
        cls.create_table()
        
        # Create the Vote
        vote_obj = cls(
            vote_id=vote_id,
            scroll_id=scroll_id,
            sela_id=sela_id,
            identity_id=identity_id,
            vote=vote,
            timestamp=timestamp or int(time.time()),
            reason=reason,
            metadata=metadata
        )
        
        # Save the Vote to the database
        vote_obj.save()
        
        return vote_obj
    
    def save(self) -> None:
        """Save the Vote to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            scroll_id,
            sela_id,
            identity_id,
            vote,
            timestamp,
            reason,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.vote_id,
            self.scroll_id,
            self.sela_id,
            self.identity_id,
            self.vote,
            self.timestamp,
            self.reason,
            json.dumps(self.metadata)
        ))
        
        conn.commit()
        conn.close()
    
    @classmethod
    def get_by_id(cls, vote_id: str) -> Optional["Vote"]:
        """
        Get a Vote by ID.
        
        Args:
            vote_id: The vote ID
        
        Returns:
            The Vote, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            scroll_id,
            sela_id,
            identity_id,
            vote,
            timestamp,
            reason,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (vote_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return cls(
                vote_id=row[0],
                scroll_id=row[1],
                sela_id=row[2],
                identity_id=row[3],
                vote=row[4],
                timestamp=row[5],
                reason=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
        
        return None
    
    @classmethod
    def get_all(cls, limit: int = 100, offset: int = 0) -> List["Vote"]:
        """
        Get all Votes.
        
        Args:
            limit: The maximum number of Votes to return
            offset: The offset for pagination
        
        Returns:
            A list of all Votes
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            scroll_id,
            sela_id,
            identity_id,
            vote,
            timestamp,
            reason,
            metadata
        FROM {cls.TABLE_NAME}
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
        """, (limit, offset))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                vote_id=row[0],
                scroll_id=row[1],
                sela_id=row[2],
                identity_id=row[3],
                vote=row[4],
                timestamp=row[5],
                reason=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_scroll(cls, scroll_id: str) -> List["Vote"]:
        """
        Find Votes by Voice Scroll ID.
        
        Args:
            scroll_id: The Voice Scroll ID
        
        Returns:
            A list of Votes for the Voice Scroll
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            scroll_id,
            sela_id,
            identity_id,
            vote,
            timestamp,
            reason,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE scroll_id = ?
        ORDER BY timestamp DESC
        """, (scroll_id,))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                vote_id=row[0],
                scroll_id=row[1],
                sela_id=row[2],
                identity_id=row[3],
                vote=row[4],
                timestamp=row[5],
                reason=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_sela(cls, sela_id: str, limit: int = 100) -> List["Vote"]:
        """
        Find Votes by Sela ID.
        
        Args:
            sela_id: The Sela ID
            limit: The maximum number of Votes to return
        
        Returns:
            A list of Votes for the Sela
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            scroll_id,
            sela_id,
            identity_id,
            vote,
            timestamp,
            reason,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE sela_id = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (sela_id, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                vote_id=row[0],
                scroll_id=row[1],
                sela_id=row[2],
                identity_id=row[3],
                vote=row[4],
                timestamp=row[5],
                reason=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_identity(cls, identity_id: str, limit: int = 100) -> List["Vote"]:
        """
        Find Votes by identity ID.
        
        Args:
            identity_id: The identity ID
            limit: The maximum number of Votes to return
        
        Returns:
            A list of Votes for the identity
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            scroll_id,
            sela_id,
            identity_id,
            vote,
            timestamp,
            reason,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE identity_id = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (identity_id, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                vote_id=row[0],
                scroll_id=row[1],
                sela_id=row[2],
                identity_id=row[3],
                vote=row[4],
                timestamp=row[5],
                reason=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Vote to a dictionary.
        
        Returns:
            The Vote as a dictionary
        """
        return {
            "vote_id": self.vote_id,
            "scroll_id": self.scroll_id,
            "sela_id": self.sela_id,
            "identity_id": self.identity_id,
            "vote": self.vote,
            "timestamp": self.timestamp,
            "reason": self.reason,
            "metadata": self.metadata
        }
