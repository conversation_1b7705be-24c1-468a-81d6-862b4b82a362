"""
FAQ Routes - Biblical Blockchain FAQ System
Handles frequently asked questions about:
- Biblical blockchain concepts
- Covenant identity verification
- Gate Keeper voting system
- Tribal lineage registration
"""

from flask import Blueprint, render_template

faq_bp = Blueprint('faq', __name__, url_prefix='/faq')

@faq_bp.route('/biblical-blockchain')
def biblical_blockchain():
    """Biblical blockchain FAQ page."""
    
    faqs = [
        {
            'question': 'What is a biblical blockchain?',
            'answer': 'A biblical blockchain is a decentralized ledger system built on divine economic principles from Scripture. It incorporates Sabbath cycles, Yovel jubilee resets, anti-usury lending, and covenant-based governance through the 12 Tribes of Israel.'
        },
        {
            'question': 'How does Gate Keeper verification work?',
            'answer': 'The Council of 12 Gate Keepers (one from each tribe) votes on Israelite identity registrations. A quorum of 7 out of 12 Gate Keepers must approve before an Israelite covenant identity is finalized on-chain. Witness nations (Edom, Ishmael, Hamitic, Japhethic) register immediately without Gate Keeper approval.'
        },
        {
            'question': 'What are the 12 Tribes of Israel?',
            'answer': 'The covenant nations: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> (<PERSON><PERSON><PERSON><PERSON>/<PERSON>eh). These require Gate Keeper verification and receive Mikvah Tokens upon approval.'
        },
        {
            'question': 'Who are the witness nations?',
            'answer': 'Witness nations include the 12 Dukes of Edom (descendants of Esau), 12 Princes of Ishmael (sons of Abraham through Hagar), and Hamitic/Japhethic nations from Genesis genealogies. They register immediately without Gate Keeper voting.'
        },
        {
            'question': 'What is Eden Mode?',
            'answer': 'Eden Mode is the immersive covenant awakening experience that guides users through biblical identity discovery, lineage research, and tribal selection. It helps determine whether you belong to Israel or a witness nation.'
        },
        {
            'question': 'How do Sabbath cycles work?',
            'answer': 'The blockchain enforces biblical Sabbath observance by disabling mining during Sabbath periods, New Moon days, and feast days. This creates a 24/6 mining schedule that honors biblical time-keeping.'
        },
        {
            'question': 'What is the Yovel jubilee system?',
            'answer': 'Every 50 years (Yovel cycle), wealth concentration limits are reset, debts are forgiven, and token distribution is rebalanced according to Leviticus 25. This prevents permanent inequality and ensures covenant community sustainability.'
        },
        {
            'question': 'How does anti-usury lending work?',
            'answer': 'Covenant members cannot charge interest to other covenant members, following biblical principles. The system provides interest-free loans through community gleaning pools and mutual aid networks.'
        },
        {
            'question': 'What are Voice Scrolls?',
            'answer': 'Voice Scrolls are governance proposals that allow the covenant community to vote on important decisions. They include constitutional amendments, biblical tokenomics parameters, and identity verification proposals.'
        },
        {
            'question': 'How do I prove my tribal lineage?',
            'answer': 'Lineage verification involves genealogical research, family records, DNA analysis (where applicable), cultural connections, and spiritual discernment. Gate Keepers review all submitted evidence before voting.'
        },
        {
            'question': 'What happens if Gate Keepers reject my application?',
            'answer': 'Rejected Israelite applications can be resubmitted with additional evidence after a waiting period. Alternatively, applicants may register as a witness nation if they have connections to Edom, Ishmael, or other biblical lineages.'
        },
        {
            'question': 'Can I change my tribal affiliation later?',
            'answer': 'Tribal affiliations are permanent once verified by Gate Keepers and inscribed on-chain. This ensures the integrity of the covenant identity system and prevents gaming of tribal benefits.'
        }
    ]
    
    return render_template('faq/biblical_blockchain.html', faqs=faqs)

@faq_bp.route('/gate-keepers')
def gate_keepers():
    """Gate Keeper system FAQ."""
    
    faqs = [
        {
            'question': 'Who are the Gate Keepers?',
            'answer': 'Gate Keepers are elected representatives from each of the 12 Tribes of Israel who form the sacred council responsible for verifying Israelite covenant identities. Each tribe has one Gate Keeper with equal voting power.'
        },
        {
            'question': 'How are Gate Keepers chosen?',
            'answer': 'Gate Keepers are elected by verified members of their respective tribes through Voice Scroll voting. They serve terms and can be re-elected or replaced through tribal governance processes.'
        },
        {
            'question': 'What is the voting process?',
            'answer': 'When an Israelite identity application is submitted, all 12 Gate Keepers are notified. They have 30 days to review evidence and cast their vote (APPROVE or REJECT). A quorum of 7 approvals is required for verification.'
        },
        {
            'question': 'Are Gate Keeper votes public?',
            'answer': 'Yes, all Gate Keeper votes are recorded on-chain and publicly auditable. This ensures transparency and accountability in the verification process while maintaining the integrity of covenant identity.'
        },
        {
            'question': 'Can Gate Keepers be challenged?',
            'answer': 'Gate Keepers can be challenged through tribal Voice Scrolls if they consistently vote against tribal consensus or fail to fulfill their duties. The tribal community can vote to replace them.'
        }
    ]
    
    return render_template('faq/gate_keepers.html', faqs=faqs)

@faq_bp.route('/tribal-lineage')
def tribal_lineage():
    """Tribal lineage FAQ."""
    
    faqs = [
        {
            'question': 'How do I research my tribal lineage?',
            'answer': 'Start with family records, genealogical research, cultural traditions, and spiritual discernment. Many resources are available including biblical genealogy databases, DNA analysis, and community elders who can provide guidance.'
        },
        {
            'question': 'What evidence do Gate Keepers require?',
            'answer': 'Gate Keepers look for multiple forms of evidence including family trees, historical records, cultural practices, spiritual calling, and community testimony. No single piece of evidence is required, but multiple supporting factors strengthen applications.'
        },
        {
            'question': 'Can DNA testing prove tribal lineage?',
            'answer': 'DNA can provide supporting evidence but is not definitive for tribal identity. Biblical lineage involves spiritual, cultural, and covenantal aspects beyond genetics. Gate Keepers consider DNA as one factor among many.'
        },
        {
            'question': 'What if I have mixed lineage?',
            'answer': 'Many people have mixed biblical lineage. You can choose your primary tribal affiliation based on the strongest connection (paternal line traditionally, but spiritual calling is also considered). Gate Keepers evaluate each case individually.'
        },
        {
            'question': 'Do I need to be Jewish to claim Israelite lineage?',
            'answer': 'Biblical Israel includes all 12 tribes, not just Judah (Jewish). Many Israelites were scattered among the nations and may not identify as Jewish today. The Gate Keepers evaluate biblical lineage claims regardless of current religious affiliation.'
        }
    ]
    
    return render_template('faq/tribal_lineage.html', faqs=faqs)

@faq_bp.route('/covenant-economics')
def covenant_economics():
    """Covenant economics FAQ."""
    
    faqs = [
        {
            'question': 'What are biblical tokenomics?',
            'answer': 'Biblical tokenomics implement economic principles from Scripture including Sabbath cycles, Yovel jubilee resets, anti-usury lending, gleaning pools for the poor, and wealth concentration limits based on biblical law.'
        },
        {
            'question': 'How do gleaning pools work?',
            'answer': 'A percentage of all mining rewards and transaction fees goes into community gleaning pools that provide support for covenant members in need, following the biblical principle of leaving corners of fields for the poor.'
        },
        {
            'question': 'What is a Mikvah Token?',
            'answer': 'Mikvah Tokens are soulbound NFTs granted to verified covenant identities. They represent purification and covenant membership, providing access to special governance rights and economic benefits within the covenant community.'
        },
        {
            'question': 'How does wealth redistribution work?',
            'answer': 'During Yovel cycles (every 50 years), excessive wealth concentration is redistributed according to biblical principles. This prevents permanent inequality and ensures covenant community sustainability across generations.'
        }
    ]
    
    return render_template('faq/covenant_economics.html', faqs=faqs)
