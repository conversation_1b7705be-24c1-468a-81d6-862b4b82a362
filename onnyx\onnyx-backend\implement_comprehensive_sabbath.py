#!/usr/bin/env python3
"""
Implement Comprehensive Biblical Sabbath Enforcement System
"""

import sqlite3
import sys
import os
import time
from datetime import datetime, timedelta
import pytz

def fix_sabbath_database_schema():
    """Fix and standardize the sabbath enforcement database schema"""
    
    print("🔧 Fixing Sabbath Database Schema")
    print("=" * 40)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Drop and recreate sabbath_enforcement table with correct schema
        print("Recreating sabbath_enforcement table...")
        cursor.execute('DROP TABLE IF EXISTS sabbath_enforcement')
        
        cursor.execute("""
            CREATE TABLE sabbath_enforcement (
                period_id TEXT PRIMARY KEY,
                start_timestamp INTEGER NOT NULL,
                end_timestamp INTEGER NOT NULL,
                period_type TEXT NOT NULL DEFAULT 'weekly',
                active BOOLEAN DEFAULT 1,
                observers_count INTEGER DEFAULT 0,
                violations_count INTEGER DEFAULT 0,
                biblical_reference TEXT DEFAULT 'Exodus 20:8-11',
                created_at INTEGER NOT NULL
            )
        """)
        print("✅ Created sabbath_enforcement table")
        
        # Create sabbath_violations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sabbath_violations (
                violation_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                operation TEXT NOT NULL,
                violation_timestamp INTEGER NOT NULL,
                user_timezone TEXT DEFAULT 'UTC',
                severity TEXT DEFAULT 'minor',
                biblical_reference TEXT DEFAULT 'Exodus 20:8-11',
                penalty_applied BOOLEAN DEFAULT 0,
                created_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        print("✅ Created sabbath_violations table")
        
        # Create sabbath_observance table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sabbath_observance (
                observance_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                sabbath_period_id TEXT NOT NULL,
                observance_start INTEGER NOT NULL,
                observance_end INTEGER,
                compliance_score REAL DEFAULT 1.0,
                activities_logged INTEGER DEFAULT 0,
                biblical_activities TEXT DEFAULT '[]',
                created_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (sabbath_period_id) REFERENCES sabbath_enforcement(period_id)
            )
        """)
        print("✅ Created sabbath_observance table")
        
        # Add sabbath-related columns to identities table
        sabbath_columns = [
            ('sabbath_observer', 'BOOLEAN DEFAULT 1'),
            ('sabbath_violations', 'INTEGER DEFAULT 0'),
            ('last_sabbath_violation', 'INTEGER'),
            ('sabbath_compliance_score', 'REAL DEFAULT 1.0'),
            ('preferred_sabbath_timezone', 'TEXT DEFAULT "UTC"'),
            ('sabbath_start_preference', 'INTEGER DEFAULT 18'),
            ('sabbath_notifications', 'BOOLEAN DEFAULT 1')
        ]
        
        for column_name, column_def in sabbath_columns:
            try:
                cursor.execute(f'ALTER TABLE identities ADD COLUMN {column_name} {column_def}')
                print(f"✅ Added {column_name} to identities table")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"✅ {column_name} already exists in identities table")
                else:
                    print(f"⚠️ Issue with {column_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print("\n✅ Sabbath database schema fixed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing sabbath database schema: {e}")
        return False

def create_weekly_sabbath_periods():
    """Create weekly sabbath periods for the next year"""
    
    print("\n📅 Creating Weekly Sabbath Periods")
    print("=" * 40)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current time
        now = datetime.now(pytz.UTC)
        
        # Find next Friday 6 PM
        days_until_friday = (4 - now.weekday()) % 7  # 4 = Friday
        if days_until_friday == 0 and now.hour >= 18:
            days_until_friday = 7  # Next week if already past Friday 6 PM
        
        next_friday = now + timedelta(days=days_until_friday)
        sabbath_start = next_friday.replace(hour=18, minute=0, second=0, microsecond=0)
        
        created_periods = 0
        
        # Create 52 weeks of sabbath periods
        for week in range(52):
            current_sabbath_start = sabbath_start + timedelta(weeks=week)
            current_sabbath_end = current_sabbath_start + timedelta(hours=25)  # 25 hours total
            
            period_id = f"weekly_sabbath_{current_sabbath_start.strftime('%Y%m%d')}"
            
            cursor.execute("""
                INSERT OR IGNORE INTO sabbath_enforcement 
                (period_id, start_timestamp, end_timestamp, period_type, active, 
                 biblical_reference, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                period_id,
                int(current_sabbath_start.timestamp()),
                int(current_sabbath_end.timestamp()),
                'weekly',
                1,
                'Exodus 20:8-11 - Remember the Sabbath day, to keep it holy',
                int(time.time())
            ))
            
            created_periods += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ Created {created_periods} weekly sabbath periods")
        return True
        
    except Exception as e:
        print(f"❌ Error creating weekly sabbath periods: {e}")
        return False

def create_high_sabbaths():
    """Create biblical high sabbaths (annual holy days)"""
    
    print("\n🎭 Creating High Sabbaths (Annual Holy Days)")
    print("=" * 50)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # High Sabbaths for 2025 (approximate dates - would need Hebrew calendar calculation)
        high_sabbaths = [
            {
                'name': 'Passover (First Day)',
                'date': '2025-04-13',
                'reference': 'Leviticus 23:5-7',
                'duration_hours': 25
            },
            {
                'name': 'Passover (Last Day)',
                'date': '2025-04-19',
                'reference': 'Leviticus 23:8',
                'duration_hours': 25
            },
            {
                'name': 'Pentecost (Shavuot)',
                'date': '2025-06-02',
                'reference': 'Leviticus 23:15-16',
                'duration_hours': 25
            },
            {
                'name': 'Trumpets (Rosh Hashanah)',
                'date': '2025-09-16',
                'reference': 'Leviticus 23:24-25',
                'duration_hours': 25
            },
            {
                'name': 'Day of Atonement (Yom Kippur)',
                'date': '2025-09-25',
                'reference': 'Leviticus 23:27-28',
                'duration_hours': 25
            },
            {
                'name': 'Tabernacles (First Day)',
                'date': '2025-10-10',
                'reference': 'Leviticus 23:34-35',
                'duration_hours': 25
            },
            {
                'name': 'Tabernacles (Last Great Day)',
                'date': '2025-10-17',
                'reference': 'Leviticus 23:36',
                'duration_hours': 25
            }
        ]
        
        created_high_sabbaths = 0
        
        for sabbath in high_sabbaths:
            # Parse date and create timestamps
            sabbath_date = datetime.strptime(sabbath['date'], '%Y-%m-%d')
            sabbath_date = sabbath_date.replace(hour=18, minute=0, second=0)  # Start at 6 PM
            sabbath_date = pytz.UTC.localize(sabbath_date)
            
            sabbath_end = sabbath_date + timedelta(hours=sabbath['duration_hours'])
            
            period_id = f"high_sabbath_{sabbath['name'].lower().replace(' ', '_').replace('(', '').replace(')', '')}_{sabbath_date.strftime('%Y%m%d')}"
            
            cursor.execute("""
                INSERT OR IGNORE INTO sabbath_enforcement 
                (period_id, start_timestamp, end_timestamp, period_type, active, 
                 biblical_reference, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                period_id,
                int(sabbath_date.timestamp()),
                int(sabbath_end.timestamp()),
                'high_sabbath',
                1,
                f"{sabbath['reference']} - {sabbath['name']}",
                int(time.time())
            ))
            
            created_high_sabbaths += 1
            print(f"✅ Created {sabbath['name']}: {sabbath['date']}")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ Created {created_high_sabbaths} high sabbath periods")
        return True
        
    except Exception as e:
        print(f"❌ Error creating high sabbaths: {e}")
        return False

def update_sabbath_observers():
    """Update all Israelite identities to be sabbath observers"""
    
    print("\n👥 Updating Sabbath Observers")
    print("=" * 35)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all Israelite identities
        israelite_codes = ['JU', 'BE', 'LE', 'SI', 'EP', 'MA', 'IS', 'ZE', 'NA', 'GA', 'AS', 'RE', 'ISR']
        
        cursor.execute("""
            SELECT identity_id, name, nation_code, nation_of_origin 
            FROM identities 
            WHERE nation_code IN ({}) OR nation_of_origin IN ({})
        """.format(','.join(['?' for _ in israelite_codes]), ','.join(['?' for _ in israelite_codes])), 
        israelite_codes + israelite_codes)
        
        israelite_identities = cursor.fetchall()
        
        updated_count = 0
        for identity in israelite_identities:
            identity_id, name, nation_code, nation_of_origin = identity
            
            cursor.execute("""
                UPDATE identities 
                SET sabbath_observer = 1,
                    sabbath_compliance_score = 1.0,
                    sabbath_notifications = 1
                WHERE identity_id = ?
            """, (identity_id,))
            
            updated_count += 1
            print(f"✅ Updated {name} ({nation_code or nation_of_origin}) as sabbath observer")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ Updated {updated_count} Israelite identities as sabbath observers")
        return True
        
    except Exception as e:
        print(f"❌ Error updating sabbath observers: {e}")
        return False

def test_sabbath_system():
    """Test the comprehensive sabbath system"""
    
    print("\n🧪 Testing Comprehensive Sabbath System")
    print("=" * 45)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test sabbath periods
        cursor.execute("SELECT COUNT(*) FROM sabbath_enforcement WHERE period_type = 'weekly'")
        weekly_count = cursor.fetchone()[0]
        print(f"✅ Weekly sabbath periods: {weekly_count}")
        
        cursor.execute("SELECT COUNT(*) FROM sabbath_enforcement WHERE period_type = 'high_sabbath'")
        high_sabbath_count = cursor.fetchone()[0]
        print(f"✅ High sabbath periods: {high_sabbath_count}")
        
        # Test sabbath observers
        cursor.execute("SELECT COUNT(*) FROM identities WHERE sabbath_observer = 1")
        observer_count = cursor.fetchone()[0]
        print(f"✅ Sabbath observers: {observer_count}")
        
        # Test current sabbath status
        current_time = int(time.time())
        cursor.execute("""
            SELECT period_id, period_type, biblical_reference 
            FROM sabbath_enforcement 
            WHERE start_timestamp <= ? AND end_timestamp >= ? AND active = 1
        """, (current_time, current_time))
        
        current_sabbaths = cursor.fetchall()
        if current_sabbaths:
            print(f"✅ Currently in sabbath period:")
            for sabbath in current_sabbaths:
                print(f"   - {sabbath[1]}: {sabbath[2]}")
        else:
            print("✅ Not currently in sabbath period")
        
        # Test next sabbath
        cursor.execute("""
            SELECT period_id, period_type, start_timestamp, biblical_reference 
            FROM sabbath_enforcement 
            WHERE start_timestamp > ? AND active = 1 
            ORDER BY start_timestamp LIMIT 1
        """, (current_time,))
        
        next_sabbath = cursor.fetchone()
        if next_sabbath:
            next_time = datetime.fromtimestamp(next_sabbath[2])
            print(f"✅ Next sabbath: {next_sabbath[1]} on {next_time.strftime('%Y-%m-%d %H:%M')}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing sabbath system: {e}")
        return False

if __name__ == "__main__":
    print("🕯️ Implementing Comprehensive Biblical Sabbath Enforcement")
    print("=" * 70)
    
    # Fix database schema
    schema_fixed = fix_sabbath_database_schema()
    
    # Create weekly sabbath periods
    weekly_created = create_weekly_sabbath_periods()
    
    # Create high sabbaths
    high_sabbaths_created = create_high_sabbaths()
    
    # Update sabbath observers
    observers_updated = update_sabbath_observers()
    
    # Test the system
    system_tested = test_sabbath_system()
    
    print("\n📊 Implementation Results:")
    print("=" * 35)
    
    if schema_fixed:
        print("✅ Database schema: Fixed successfully")
    else:
        print("❌ Database schema: Issues found")
    
    if weekly_created:
        print("✅ Weekly sabbaths: Created successfully")
    else:
        print("❌ Weekly sabbaths: Issues found")
    
    if high_sabbaths_created:
        print("✅ High sabbaths: Created successfully")
    else:
        print("❌ High sabbaths: Issues found")
    
    if observers_updated:
        print("✅ Sabbath observers: Updated successfully")
    else:
        print("❌ Sabbath observers: Issues found")
    
    if system_tested:
        print("✅ System testing: Passed")
    else:
        print("❌ System testing: Failed")
    
    overall_success = schema_fixed and weekly_created and high_sabbaths_created and observers_updated and system_tested
    
    if overall_success:
        print("\n🎉 SUCCESS: Comprehensive sabbath enforcement implemented!")
        print("   - Database schema standardized")
        print("   - Weekly sabbath periods created (52 weeks)")
        print("   - High sabbaths (annual holy days) created")
        print("   - Israelite identities set as sabbath observers")
        print("   - System tested and verified")
    else:
        print("\n⚠️ Some issues found - check details above")
    
    sys.exit(0 if overall_success else 1)
