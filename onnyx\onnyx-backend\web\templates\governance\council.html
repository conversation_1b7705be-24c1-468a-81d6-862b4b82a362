{% extends "base.html" %}

{% block title %}Council of 12 - ONNYX Governance{% endblock %}

{% block head %}
<style>
.governance-container {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    min-height: 100vh;
    padding: 2rem 0;
}

.council-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.council-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.4);
}

.tribal-section {
    background: rgba(0, 255, 247, 0.1);
    border: 1px solid rgba(0, 255, 247, 0.3);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.member-card {
    background: rgba(154, 0, 255, 0.1);
    border: 1px solid rgba(154, 0, 255, 0.3);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.3s ease;
}

.member-card:hover {
    border-color: var(--cyber-purple);
    transform: translateY(-2px);
}

.gate-keeper-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
}

.elder-badge {
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-blue));
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
}

.biblical-quote {
    background: rgba(0, 255, 247, 0.1);
    border-left: 4px solid var(--cyber-cyan);
    padding: 1rem;
    border-radius: 0.5rem;
    font-style: italic;
    margin: 1.5rem 0;
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 50%;
    opacity: 0.6;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}
</style>
{% endblock %}

{% block content %}
<div class="governance-container">
    <div class="floating-particles"></div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl mb-6">
                <span class="text-3xl">👑</span>
            </div>
            <h1 class="text-4xl md:text-6xl font-orbitron font-bold text-white mb-4">Council of 12</h1>
            <p class="text-xl text-text-secondary max-w-4xl mx-auto mb-6">
                "And I will give you pastors according to mine heart, which shall feed you with knowledge and understanding" - Jeremiah 3:15 (KJV)
            </p>
            <div class="text-lg text-text-tertiary">
                The biblical governance structure of ONNYX, modeled after the 12 tribes of Israel
            </div>
        </div>

        <!-- Biblical Foundation -->
        <div class="council-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6 text-center">Biblical Foundation</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-4xl mb-4">📜</div>
                    <h3 class="text-xl font-semibold text-cyber-cyan mb-2">Divine Order</h3>
                    <p class="text-text-secondary">"And Moses chose able men out of all Israel, and made them heads over the people" - Exodus 18:25 (KJV)</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">⚖️</div>
                    <h3 class="text-xl font-semibold text-cyber-purple mb-2">Righteous Judgment</h3>
                    <p class="text-text-secondary">"Judges and officers shalt thou make thee in all thy gates" - Deuteronomy 16:18 (KJV)</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">🛡️</div>
                    <h3 class="text-xl font-semibold text-cyber-green mb-2">Covenant Protection</h3>
                    <p class="text-text-secondary">"And they shall keep thy charge, and the charge of all the tabernacle" - Numbers 18:3 (KJV)</p>
                </div>
            </div>
        </div>

        <!-- Council Structure -->
        <div class="council-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">Council Structure</h2>
            <div class="biblical-quote">
                "And he set twelve, that they should be with him, and that he might send them forth to preach" - Mark 3:14 (KJV)
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="member-card text-center">
                    <div class="text-3xl mb-3">🚪</div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Gate Keepers</h3>
                    <p class="text-sm text-text-secondary mb-3">Guardians of covenant entry and verification</p>
                    <div class="gate-keeper-badge">Levitical Order</div>
                </div>
                <div class="member-card text-center">
                    <div class="text-3xl mb-3">👴</div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Tribal Elders</h3>
                    <p class="text-sm text-text-secondary mb-3">Representatives of the 12 tribes of Israel</p>
                    <div class="elder-badge">Tribal Authority</div>
                </div>
                <div class="member-card text-center">
                    <div class="text-3xl mb-3">⚖️</div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-green mb-2">Judges</h3>
                    <p class="text-sm text-text-secondary mb-3">Arbiters of covenant disputes and justice</p>
                    <div class="elder-badge">Divine Justice</div>
                </div>
            </div>
        </div>

        <!-- Tribal Representation -->
        {% if tribes %}
        <div class="council-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">Tribal Representation</h2>
            <div class="biblical-quote">
                "And the children of Israel shall pitch their tents, every man by his own camp, and every man by his own standard, throughout their hosts" - Numbers 1:52 (KJV)
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for tribe_name, members in tribes.items() %}
                <div class="tribal-section">
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">
                        <span class="text-2xl mr-2">✡️</span>
                        Tribe of {{ tribe_name }}
                    </h3>
                    
                    {% for member in members %}
                    <div class="member-card mb-3">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-semibold text-white">{{ member.name }}</h4>
                                <p class="text-sm text-text-secondary">{{ member.metadata.get('tribal_role', 'Member') }}</p>
                            </div>
                            <div>
                                {% if member.metadata.get('tribal_role') == 'Gate_Keeper' %}
                                <div class="gate-keeper-badge">Gate Keeper</div>
                                {% elif member.metadata.get('tribal_role') == 'Elder' %}
                                <div class="elder-badge">Elder</div>
                                {% endif %}
                            </div>
                        </div>
                        {% if member.metadata.get('covenant_oath') %}
                        <div class="mt-2 text-xs text-cyber-cyan italic">
                            "{{ member.metadata.get('covenant_oath', 'Faithful to the covenant') }}"
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Council Responsibilities -->
        <div class="council-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">Council Responsibilities</h2>
            <div class="biblical-quote">
                "Moreover thou shalt provide out of all the people able men, such as fear God, men of truth, hating covetousness" - Exodus 18:21 (KJV)
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Governance Duties</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Verify covenant membership and identity</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Judge disputes according to biblical law</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Oversee Sabbath and feast day observance</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Manage gleaning pools and charity distribution</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-4">Covenant Obligations</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Uphold biblical principles in all decisions</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Protect the poor and stranger among us</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Ensure economic justice and fair distribution</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Maintain covenant faithfulness and righteousness</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Call to Service -->
        <div class="council-card p-8 text-center">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">Called to Serve</h2>
            <div class="biblical-quote">
                "But he that is greatest among you shall be your servant" - Matthew 23:11 (KJV)
            </div>
            <p class="text-lg text-text-secondary mb-6">
                The Council of 12 serves not for personal gain, but as faithful stewards of the covenant community, 
                following the example of righteous leadership established in Scripture.
            </p>
            <div class="flex justify-center space-x-4">
                <a href="{{ url_for('governance.public_governance') }}" class="bg-gradient-to-r from-cyber-cyan to-cyber-purple text-onyx-black font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    View Public Governance
                </a>
                <a href="{{ url_for('governance.voice_scrolls') }}" class="bg-gradient-to-r from-cyber-purple to-cyber-blue text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    Voice Scrolls
                </a>
            </div>
        </div>

        <!-- Back Navigation -->
        <div class="text-center mt-8">
            <a href="{{ url_for('index') }}" class="inline-flex items-center text-cyber-cyan hover:text-cyan-400 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Return to Home
            </a>
        </div>
    </div>
</div>

<script>
function addFloatingParticles() {
    const container = document.querySelector('.floating-particles');
    if (container) {
        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particle.style.animationDuration = (2 + Math.random() * 2) + 's';
            container.appendChild(particle);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    addFloatingParticles();
});
</script>
{% endblock %}
