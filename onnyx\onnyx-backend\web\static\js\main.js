// Onnyx Platform - Onyx Stone Theme JavaScript

// Global utilities with enhanced animations
window.Onnyx = {
    // API helper functions
    api: {
        async get(url) {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        },

        async post(url, data) {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        }
    },

    // Animation utilities
    animations: {
        // Animated counter for statistics
        animateCounter(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 16);
        },

        // Initialize all counters on page
        initCounters() {
            // Skip counter animation on homepage to prevent NaN issues
            if (window.location.pathname === '/' || window.location.pathname === '/index') {
                console.log('Skipping counter animation on homepage to preserve static values');
                return;
            }

            const counters = document.querySelectorAll('.count-up');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Try data-target first (homepage), then data-count (fallback)
                        const target = parseInt(entry.target.getAttribute('data-target')) ||
                                     parseInt(entry.target.getAttribute('data-count')) ||
                                     parseInt(entry.target.textContent) || 0;

                        // Only animate if we have a valid target
                        if (!isNaN(target) && target >= 0) {
                            this.animateCounter(entry.target, target);
                        }
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });

            counters.forEach(counter => observer.observe(counter));
        },

        // Parallax scroll effect - DISABLED (replaced by enhanced scroll effects)
        initParallax() {
            // Parallax disabled to prevent conflicts with new scroll effects system
            // The new scroll-effects.js handles all scroll animations
        },

        // Smooth scroll to sections - DISABLED (replaced by enhanced scroll effects)
        initSmoothScroll() {
            // Smooth scroll disabled to prevent conflicts with new scroll effects system
            // The new scroll-effects.js handles all scroll behavior
        },

        // Glow effect on hover
        initGlowEffects() {
            const glowElements = document.querySelectorAll('.glass-button, .glass-button-primary, .neuro-card');

            glowElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 0 30px rgba(0, 255, 255, 0.4)';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '';
                });
            });
        }
    },

    // Utility functions
    utils: {
        // Format timestamp to readable date
        formatTimestamp(timestamp) {
            return new Date(timestamp * 1000).toLocaleString();
        },

        // Truncate hash for display
        truncateHash(hash, length = 8) {
            if (!hash) return '';
            return `${hash.substring(0, length)}...${hash.substring(hash.length - 4)}`;
        },

        // Copy text to clipboard
        async copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                this.showNotification('Copied to clipboard!', 'success');
            } catch (err) {
                console.error('Failed to copy: ', err);
                this.showNotification('Failed to copy to clipboard', 'error');
            }
        },

        // Show notification
        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;
            notification.style.top = 'calc(var(--nav-height) + 1rem)'; // Position below navigation bar

            const typeClasses = {
                success: 'bg-green-500 text-white',
                error: 'bg-red-500 text-white',
                warning: 'bg-yellow-500 text-white',
                info: 'bg-blue-500 text-white'
            };

            notification.className += ` ${typeClasses[type] || typeClasses.info}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        },

        // Format large numbers
        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        },

        // Validate email
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // Generate QR code
        async generateQRCode(text, container) {
            if (typeof QRCode !== 'undefined') {
                try {
                    await QRCode.toCanvas(container, text, {
                        width: 256,
                        margin: 2,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        }
                    });
                } catch (error) {
                    console.error('QR Code generation failed:', error);
                }
            }
        }
    },

    // Search functionality
    search: {
        async performSearch(query, limit = 10) {
            try {
                const results = await Onnyx.api.get(`/api/search?q=${encodeURIComponent(query)}&limit=${limit}`);
                return results;
            } catch (error) {
                console.error('Search failed:', error);
                return [];
            }
        },

        // Initialize search autocomplete
        initSearchAutocomplete(inputElement, resultsElement) {
            let searchTimeout;

            inputElement.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);

                if (query.length < 2) {
                    resultsElement.innerHTML = '';
                    resultsElement.classList.add('hidden');
                    return;
                }

                searchTimeout = setTimeout(async () => {
                    try {
                        const results = await Onnyx.search.performSearch(query, 5);
                        Onnyx.search.displayResults(results, resultsElement);
                    } catch (error) {
                        console.error('Search error:', error);
                    }
                }, 300);
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!inputElement.contains(e.target) && !resultsElement.contains(e.target)) {
                    resultsElement.classList.add('hidden');
                }
            });
        },

        displayResults(results, container) {
            if (results.length === 0) {
                container.innerHTML = '<div class="p-3 text-gray-500 text-sm">No results found</div>';
            } else {
                container.innerHTML = results.map(result => `
                    <a href="${result.url}" class="block p-3 hover:bg-gray-100 border-b border-gray-200 last:border-b-0">
                        <div class="font-medium text-gray-900">${result.title}</div>
                        ${result.subtitle ? `<div class="text-sm text-gray-600">${result.subtitle}</div>` : ''}
                        <div class="text-xs text-blue-600">${result.type}</div>
                    </a>
                `).join('');
            }
            container.classList.remove('hidden');
        }
    },

    // Form validation
    validation: {
        // Real-time email validation
        async validateEmail(email, callback) {
            if (!Onnyx.utils.isValidEmail(email)) {
                callback(false, 'Invalid email format');
                return;
            }

            try {
                const result = await Onnyx.api.post('/api/validate/email', { email });
                callback(result.valid, result.message);
            } catch (error) {
                callback(false, 'Validation failed');
            }
        },

        // Real-time Sela name validation
        async validateSelaName(name, callback) {
            if (!name || name.length < 2) {
                callback(false, 'Business name too short');
                return;
            }

            try {
                const result = await Onnyx.api.post('/api/validate/sela-name', { name });
                callback(result.valid, result.message);
            } catch (error) {
                callback(false, 'Validation failed');
            }
        }
    },

    // Dashboard utilities
    dashboard: {
        // Load dashboard data
        async loadDashboardData() {
            try {
                return await Onnyx.api.get('/api/user/dashboard-data');
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
                return null;
            }
        },

        // Update statistics display
        updateStats(stats) {
            Object.keys(stats).forEach(key => {
                const element = document.getElementById(`stat-${key}`);
                if (element) {
                    element.textContent = Onnyx.utils.formatNumber(stats[key]);
                }
            });
        }
    },

    // Mining utilities
    mining: {
        async startMining() {
            try {
                const result = await Onnyx.api.post('/api/mine-block', {});
                if (result.success) {
                    Onnyx.utils.showNotification(`Block mined successfully! Height: ${result.block_height}`, 'success');
                    return result;
                } else {
                    Onnyx.utils.showNotification(result.error || 'Mining failed', 'error');
                    return null;
                }
            } catch (error) {
                console.error('Mining error:', error);
                Onnyx.utils.showNotification('Mining failed', 'error');
                return null;
            }
        }
    }
};

// Initialize on page load with enhanced animations
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all animations (parallax and smooth scroll now handled by scroll-effects.js)
    Onnyx.animations.initCounters();
    Onnyx.animations.initGlowEffects();

    // Initialize floating authentication orb
    // initFloatingAuthOrb(); // Disabled - handled in base.html template

    // Initialize search if search input exists
    const searchInput = document.getElementById('global-search');
    const searchResults = document.getElementById('search-results');
    if (searchInput && searchResults) {
        Onnyx.search.initSearchAutocomplete(searchInput, searchResults);
    }

    // Initialize copy buttons with enhanced feedback
    document.querySelectorAll('[data-copy]').forEach(button => {
        button.addEventListener('click', function() {
            const text = this.getAttribute('data-copy');
            Onnyx.utils.copyToClipboard(text);

            // Visual feedback
            const originalText = this.textContent;
            this.textContent = 'Copied!';
            this.classList.add('glow-effect');

            setTimeout(() => {
                this.textContent = originalText;
                this.classList.remove('glow-effect');
            }, 2000);
        });
    });

    // Initialize hash truncation with hover effects
    document.querySelectorAll('.hash-truncate').forEach(element => {
        const fullHash = element.textContent;
        element.textContent = Onnyx.utils.truncateHash(fullHash);
        element.title = fullHash; // Show full hash on hover

        // Add click to copy functionality
        element.style.cursor = 'pointer';
        element.addEventListener('click', () => {
            Onnyx.utils.copyToClipboard(fullHash);
        });
    });

    // Initialize timestamp formatting
    document.querySelectorAll('[data-timestamp]').forEach(element => {
        const timestamp = parseInt(element.getAttribute('data-timestamp'));
        element.textContent = Onnyx.utils.formatTimestamp(timestamp);
    });

    // Auto-refresh dashboard data every 30 seconds with visual feedback
    if (document.body.classList.contains('dashboard-page')) {
        setInterval(async () => {
            const data = await Onnyx.dashboard.loadDashboardData();
            if (data && data.stats) {
                Onnyx.dashboard.updateStats(data.stats);

                // Add shimmer effect to updated stats
                document.querySelectorAll('[id^="stat-"]').forEach(stat => {
                    stat.classList.add('shimmer-effect');
                    setTimeout(() => {
                        stat.classList.remove('shimmer-effect');
                    }, 1000);
                });
            }
        }, 30000);
    }

    // Initialize floating particles animation
    initFloatingParticles();

    // Initialize data stream effects
    initDataStreamEffects();

    // Initialize floating authentication orb
    // initFloatingAuthOrb(); // Disabled - handled in base.html template

    // Initialize Core Technologies animations
    initCoreTechnologiesAnimations();

    // Initialize User Journey animations
    initUserJourneyAnimations();

    // Initialize Roadmap animations
    initRoadmapAnimations();

    // Initialize Trust Indicators animations
    initTrustIndicatorsAnimations();
});

// Floating particles animation
function initFloatingParticles() {
    const heroSection = document.querySelector('.hero-gradient');
    if (!heroSection) return;

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'absolute w-1 h-1 bg-cyber-cyan rounded-full opacity-30';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 3 + 's';
        particle.style.animation = 'float 6s ease-in-out infinite';
        heroSection.appendChild(particle);
    }
}

// Data stream effects
function initDataStreamEffects() {
    const dataStreams = document.querySelectorAll('.data-stream');

    dataStreams.forEach(stream => {
        stream.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent)';
            this.style.backgroundSize = '200% 100%';
            this.style.animation = 'shimmer 1s ease-in-out';
        });

        stream.addEventListener('mouseleave', function() {
            this.style.background = '';
            this.style.animation = '';
        });
    });
}

// Enhanced floating authentication orb behavior
function initFloatingAuthOrb() {
    const floatingOrb = document.getElementById('floating-auth-orb');
    const orbMenu = document.getElementById('orb-menu');

    if (!floatingOrb || !orbMenu) return;

    let hoverTimeout;
    let isMenuHovered = false;
    let isOrbHovered = false;

    // Enhanced hover handling for orb
    floatingOrb.addEventListener('mouseenter', function() {
        isOrbHovered = true;
        clearTimeout(hoverTimeout);
        showMenu();
    });

    floatingOrb.addEventListener('mouseleave', function() {
        isOrbHovered = false;
        hoverTimeout = setTimeout(() => {
            if (!isMenuHovered && !isOrbHovered) {
                hideMenu();
            }
        }, 100); // Small delay to allow moving to menu
    });

    // Enhanced hover handling for menu
    orbMenu.addEventListener('mouseenter', function() {
        isMenuHovered = true;
        clearTimeout(hoverTimeout);
    });

    orbMenu.addEventListener('mouseleave', function() {
        isMenuHovered = false;
        hoverTimeout = setTimeout(() => {
            if (!isMenuHovered && !isOrbHovered) {
                hideMenu();
            }
        }, 100);
    });

    // Click handling for mobile/touch devices
    floatingOrb.addEventListener('click', function(e) {
        e.preventDefault();
        if (orbMenu.style.opacity === '1' || orbMenu.classList.contains('menu-visible')) {
            hideMenu();
        } else {
            showMenu();
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!floatingOrb.contains(e.target) && !orbMenu.contains(e.target)) {
            hideMenu();
        }
    });

    // Keyboard accessibility
    floatingOrb.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            if (orbMenu.style.opacity === '1' || orbMenu.classList.contains('menu-visible')) {
                hideMenu();
            } else {
                showMenu();
            }
        } else if (e.key === 'Escape') {
            hideMenu();
        }
    });

    function showMenu() {
        orbMenu.style.opacity = '1';
        orbMenu.style.visibility = 'visible';
        orbMenu.style.transform = 'translateY(0) scale(1)';
        orbMenu.style.pointerEvents = 'all';
        orbMenu.classList.add('menu-visible');
        floatingOrb.setAttribute('aria-expanded', 'true');
    }

    function hideMenu() {
        orbMenu.style.opacity = '0';
        orbMenu.style.visibility = 'hidden';
        orbMenu.style.transform = 'translateY(-8px) scale(0.95)';
        orbMenu.style.pointerEvents = 'none';
        orbMenu.classList.remove('menu-visible');
        floatingOrb.setAttribute('aria-expanded', 'false');
    }

    // Initialize ARIA attributes
    floatingOrb.setAttribute('role', 'button');
    floatingOrb.setAttribute('aria-haspopup', 'true');
    floatingOrb.setAttribute('aria-expanded', 'false');
    floatingOrb.setAttribute('tabindex', '0');
}

// Enhanced Core Technologies animations
function initCoreTechnologiesAnimations() {
    const techCards = document.querySelectorAll('.tech-card-animated');

    if (techCards.length === 0) return;

    // Enhanced intersection observer for tech cards
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const card = entry.target;

                // Add enhanced animation class when in view
                card.classList.add('tech-card-active');

                // Add specific enhancements based on card type
                if (card.classList.contains('quantum-security-card')) {
                    enhanceQuantumSecurityCard(card);
                } else if (card.classList.contains('trust-protocol-card')) {
                    enhanceTrustProtocolCard(card);
                } else if (card.classList.contains('token-economy-card')) {
                    enhanceTokenEconomyCard(card);
                }
            }
        });
    }, {
        threshold: 0.3,
        rootMargin: '50px'
    });

    techCards.forEach(card => observer.observe(card));

    // Use standardized CSS hover effects for consistent behavior
    // Removed custom JavaScript hover overrides to maintain uniform card interactions
}

// Quantum Security Card enhancements
function enhanceQuantumSecurityCard(card) {
    // Add dynamic encryption pattern overlay
    const overlay = document.createElement('div');
    overlay.className = 'quantum-encryption-overlay';
    overlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg,
            transparent 30%,
            rgba(0, 212, 255, 0.1) 50%,
            transparent 70%);
        background-size: 20px 20px;
        animation: encryptionScan 3s linear infinite;
        pointer-events: none;
        z-index: 3;
        opacity: 0;
        transition: opacity 0.5s ease;
    `;

    card.appendChild(overlay);

    // Trigger encryption effect on hover
    card.addEventListener('mouseenter', () => {
        overlay.style.opacity = '1';
    });

    card.addEventListener('mouseleave', () => {
        overlay.style.opacity = '0';
    });
}

// Trust Protocol Card enhancements
function enhanceTrustProtocolCard(card) {
    // Add dynamic trust network visualization
    const networkOverlay = document.createElement('div');
    networkOverlay.className = 'trust-network-overlay';
    networkOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 3;
        opacity: 0;
        transition: opacity 0.5s ease;
    `;

    // Create trust nodes
    for (let i = 0; i < 5; i++) {
        const node = document.createElement('div');
        node.style.cssText = `
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgba(154, 0, 255, 0.8);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(154, 0, 255, 0.6);
            animation: trustNodePulse 2s ease-in-out infinite;
            animation-delay: ${i * 0.4}s;
        `;

        // Position nodes randomly
        node.style.left = `${20 + Math.random() * 60}%`;
        node.style.top = `${20 + Math.random() * 60}%`;

        networkOverlay.appendChild(node);
    }

    card.appendChild(networkOverlay);

    // Show network on hover
    card.addEventListener('mouseenter', () => {
        networkOverlay.style.opacity = '1';
    });

    card.addEventListener('mouseleave', () => {
        networkOverlay.style.opacity = '0';
    });
}

// Token Economy Card enhancements
function enhanceTokenEconomyCard(card) {
    // Add dynamic token flow visualization
    const tokenOverlay = document.createElement('div');
    tokenOverlay.className = 'token-flow-overlay';
    tokenOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 3;
        opacity: 0;
        transition: opacity 0.5s ease;
        overflow: hidden;
    `;

    // Create flowing token particles
    for (let i = 0; i < 8; i++) {
        const token = document.createElement('div');
        token.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(0, 128, 255, 0.8);
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(0, 128, 255, 0.6);
            animation: tokenFlow 3s linear infinite;
            animation-delay: ${i * 0.3}s;
        `;

        // Start from random positions on the left
        token.style.left = '-4px';
        token.style.top = `${Math.random() * 100}%`;

        tokenOverlay.appendChild(token);
    }

    card.appendChild(tokenOverlay);

    // Show token flow on hover
    card.addEventListener('mouseenter', () => {
        tokenOverlay.style.opacity = '1';
    });

    card.addEventListener('mouseleave', () => {
        tokenOverlay.style.opacity = '0';
    });
}

// Enhanced User Journey animations
function initUserJourneyAnimations() {
    const journeyCards = document.querySelectorAll('.journey-card-animated');

    if (journeyCards.length === 0) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const card = entry.target;
                card.classList.add('journey-card-active');

                // Add specific enhancements based on card type
                if (card.classList.contains('covenant-awakening-card')) {
                    enhanceCovenantAwakeningCard(card);
                } else if (card.classList.contains('business-registration-card')) {
                    enhanceBusinessRegistrationCard(card);
                } else if (card.classList.contains('rewards-earning-card')) {
                    enhanceRewardsEarningCard(card);
                }
            }
        });
    }, {
        threshold: 0.3,
        rootMargin: '50px'
    });

    journeyCards.forEach(card => observer.observe(card));
}

// Enhanced Roadmap animations - DISABLED to prevent conflicts
function initRoadmapAnimations() {
    console.log('🚫 Roadmap animations disabled to prevent positioning conflicts');
    // All roadmap animations are now handled by CSS only
    return;
}

// Enhanced Trust Indicators animations
function initTrustIndicatorsAnimations() {
    const trustIndicators = document.querySelectorAll('.trust-indicator-animated');

    if (trustIndicators.length === 0) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const indicator = entry.target;
                indicator.classList.add('trust-indicator-active');

                // Add staggered animation
                const delay = Array.from(trustIndicators).indexOf(indicator) * 300;
                setTimeout(() => {
                    indicator.style.transform = 'translateY(-8px)';
                    const iconContainer = indicator.querySelector('div:first-child');
                    if (iconContainer) {
                        iconContainer.style.transform = 'scale(1.1)';
                        iconContainer.style.boxShadow = '0 10px 30px rgba(0, 212, 255, 0.3)';
                    }
                }, delay);
            }
        });
    }, {
        threshold: 0.5,
        rootMargin: '50px'
    });

    trustIndicators.forEach(indicator => observer.observe(indicator));
}

// Journey card specific enhancements
function enhanceCovenantAwakeningCard(card) {
    // Add spiritual energy effect
    const energyOverlay = document.createElement('div');
    energyOverlay.className = 'covenant-energy-overlay';
    energyOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
        animation: covenantEnergy 4s ease-in-out infinite;
        pointer-events: none;
        z-index: 3;
        opacity: 0;
        transition: opacity 0.5s ease;
    `;

    card.appendChild(energyOverlay);

    card.addEventListener('mouseenter', () => {
        energyOverlay.style.opacity = '1';
    });

    card.addEventListener('mouseleave', () => {
        energyOverlay.style.opacity = '0';
    });
}

function enhanceBusinessRegistrationCard(card) {
    // Add business network effect
    const networkOverlay = document.createElement('div');
    networkOverlay.className = 'business-network-overlay';
    networkOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(45deg, transparent 40%, rgba(154, 0, 255, 0.1) 50%, transparent 60%),
            linear-gradient(-45deg, transparent 40%, rgba(154, 0, 255, 0.08) 50%, transparent 60%);
        background-size: 30px 30px, 20px 20px;
        animation: businessNetwork 3s linear infinite;
        pointer-events: none;
        z-index: 3;
        opacity: 0;
        transition: opacity 0.5s ease;
    `;

    card.appendChild(networkOverlay);

    card.addEventListener('mouseenter', () => {
        networkOverlay.style.opacity = '1';
    });

    card.addEventListener('mouseleave', () => {
        networkOverlay.style.opacity = '0';
    });
}

function enhanceRewardsEarningCard(card) {
    // Add rewards flow effect
    const rewardsOverlay = document.createElement('div');
    rewardsOverlay.className = 'rewards-flow-overlay';
    rewardsOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 3;
        opacity: 0;
        transition: opacity 0.5s ease;
        overflow: hidden;
    `;

    // Create reward particles
    for (let i = 0; i < 6; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(0, 128, 255, 0.8);
            border-radius: 50%;
            box-shadow: 0 0 6px rgba(0, 128, 255, 0.6);
            animation: rewardParticles 2s linear infinite;
            animation-delay: ${i * 0.3}s;
        `;

        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;

        rewardsOverlay.appendChild(particle);
    }

    card.appendChild(rewardsOverlay);

    card.addEventListener('mouseenter', () => {
        rewardsOverlay.style.opacity = '1';
    });

    card.addEventListener('mouseleave', () => {
        rewardsOverlay.style.opacity = '0';
    });
}

// Export for use in other scripts
window.Onnyx = Onnyx;
