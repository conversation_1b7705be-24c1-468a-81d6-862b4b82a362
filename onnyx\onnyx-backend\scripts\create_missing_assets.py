#!/usr/bin/env python3
"""
Create Missing Static Assets

This script creates placeholder files for missing static assets to prevent 404 errors.
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_placeholder_js_files():
    """Create placeholder JavaScript files"""
    try:
        js_dir = os.path.join('web', 'static', 'js')
        
        missing_js_files = [
            'enhanced-navigation.js',
            'production-validation.js'
        ]
        
        for js_file in missing_js_files:
            file_path = os.path.join(js_dir, js_file)
            
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""/**
 * {js_file}
 * 
 * Placeholder file to prevent 404 errors.
 * This file can be implemented with specific functionality as needed.
 */

console.log('Loaded {js_file}');

// Placeholder implementation
(function() {{
    'use strict';
    
    // Add your implementation here
    
}})();
""")
                logger.info(f"✅ Created placeholder: {js_file}")
            else:
                logger.info(f"✅ File already exists: {js_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating JS files: {e}")
        return False

def create_favicon_files():
    """Create simple favicon files"""
    try:
        images_dir = os.path.join('web', 'static', 'images')
        
        # Create images directory if it doesn't exist
        os.makedirs(images_dir, exist_ok=True)
        
        # Simple 1x1 transparent PNG data (base64 encoded)
        # This is a minimal valid PNG file
        png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
        
        favicon_files = [
            'favicon.ico',
            'favicon-16x16.png',
            'favicon-32x32.png',
            'favicon-48x48.png'
        ]
        
        for favicon_file in favicon_files:
            file_path = os.path.join(images_dir, favicon_file)
            
            if not os.path.exists(file_path):
                with open(file_path, 'wb') as f:
                    if favicon_file.endswith('.png'):
                        f.write(png_data)
                    else:
                        # For .ico files, write the same PNG data (browsers will handle it)
                        f.write(png_data)
                
                logger.info(f"✅ Created favicon: {favicon_file}")
            else:
                logger.info(f"✅ Favicon already exists: {favicon_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating favicon files: {e}")
        return False

def create_apple_touch_icon():
    """Create Apple touch icon"""
    try:
        images_dir = os.path.join('web', 'static', 'images')
        
        # Simple 1x1 transparent PNG data
        png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
        
        apple_icon_path = os.path.join(images_dir, 'apple-touch-icon.png')
        
        if not os.path.exists(apple_icon_path):
            with open(apple_icon_path, 'wb') as f:
                f.write(png_data)
            logger.info("✅ Created apple-touch-icon.png")
        else:
            logger.info("✅ Apple touch icon already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating Apple touch icon: {e}")
        return False

def verify_assets():
    """Verify that all assets were created"""
    try:
        logger.info("Verifying created assets...")
        
        # Check JS files
        js_dir = os.path.join('web', 'static', 'js')
        js_files = ['enhanced-navigation.js', 'production-validation.js']
        
        for js_file in js_files:
            file_path = os.path.join(js_dir, js_file)
            if os.path.exists(file_path):
                logger.info(f"✅ Verified: {js_file}")
            else:
                logger.warning(f"⚠️ Missing: {js_file}")
        
        # Check favicon files
        images_dir = os.path.join('web', 'static', 'images')
        favicon_files = [
            'favicon.ico', 'favicon-16x16.png', 'favicon-32x32.png', 
            'favicon-48x48.png', 'apple-touch-icon.png'
        ]
        
        for favicon_file in favicon_files:
            file_path = os.path.join(images_dir, favicon_file)
            if os.path.exists(file_path):
                logger.info(f"✅ Verified: {favicon_file}")
            else:
                logger.warning(f"⚠️ Missing: {favicon_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error verifying assets: {e}")
        return False

def main():
    """Main function to create missing assets"""
    logger.info("🎨 Creating Missing Static Assets...")
    logger.info("=" * 50)
    
    try:
        # Step 1: Create placeholder JS files
        if not create_placeholder_js_files():
            logger.error("❌ Failed to create JS files")
            return 1
        
        # Step 2: Create favicon files
        if not create_favicon_files():
            logger.error("❌ Failed to create favicon files")
            return 1
        
        # Step 3: Create Apple touch icon
        if not create_apple_touch_icon():
            logger.error("❌ Failed to create Apple touch icon")
            return 1
        
        # Step 4: Verify assets
        if not verify_assets():
            logger.error("❌ Asset verification failed")
            return 1
        
        logger.info("=" * 50)
        logger.info("🎉 Missing Assets Creation Complete!")
        logger.info("✅ 404 errors for static assets should be resolved")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Asset creation failed: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
