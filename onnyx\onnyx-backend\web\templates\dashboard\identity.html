{% extends "base.html" %}

{% block title %}Identity Details - {{ identity.name }} | ONNYX Platform{% endblock %}

{% block description %}Comprehensive identity management dashboard with covenant verification, tribal affiliation, and security settings.{% endblock %}

{% block head %}
<style>
    .identity-card {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .verification-badge {
        background: linear-gradient(135deg, #00fff7 0%, #9a00ff 100%);
        color: #000;
        font-weight: bold;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
    }
    
    .status-active {
        color: #00ff88;
    }
    
    .status-pending {
        color: #ffaa00;
    }
    
    .tribal-symbol {
        font-size: 3rem;
        background: linear-gradient(135deg, #00fff7 0%, #9a00ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-orbitron font-bold mb-2">
                        <span class="bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                            Identity Details
                        </span>
                    </h1>
                    <p class="text-gray-300">Manage your covenant identity and verification status</p>
                </div>
                <div class="text-right">
                    <a href="{{ url_for('dashboard.overview') }}" class="glass-button-secondary px-6 py-3 rounded-xl font-orbitron">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Identity Card -->
        <div class="identity-card p-8 rounded-2xl mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Section -->
                <div class="lg:col-span-2">
                    <div class="flex items-start space-x-6">
                        <div class="tribal-symbol">
                            {% if identity.tribal_affiliation %}
                                {% set tribal_symbols = {
                                    'judah': '🦁', 'benjamin': '🐺', 'levi': '⚖️', 'issachar': '🐴',
                                    'zebulun': '⛵', 'simeon': '⚔️', 'gad': '🛡️', 'asher': '🌾',
                                    'naphtali': '🦌', 'dan': '🐍', 'reuben': '💧', 'ephraim': '🌳',
                                    'manasseh': '🌿'
                                } %}
                                {{ tribal_symbols.get(identity.tribal_affiliation.lower(), '🏛️') }}
                            {% else %}
                                🏛️
                            {% endif %}
                        </div>
                        <div class="flex-1">
                            <h2 class="text-3xl font-orbitron font-bold text-white mb-2">{{ identity.name }}</h2>
                            <p class="text-gray-300 mb-4">{{ identity.email }}</p>
                            
                            <div class="flex flex-wrap gap-3 mb-4">
                                <span class="verification-badge">
                                    {{ identity.verification_level or 'Unverified' }}
                                </span>
                                <span class="px-3 py-1 rounded-full text-xs font-semibold 
                                    {% if identity.status == 'active' %}bg-green-900 text-green-300{% else %}bg-yellow-900 text-yellow-300{% endif %}">
                                    {{ identity.status.title() }}
                                </span>
                                {% if identity.sabbath_observer %}
                                <span class="px-3 py-1 rounded-full text-xs font-semibold bg-blue-900 text-blue-300">
                                    Sabbath Observer
                                </span>
                                {% endif %}
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-400">Nation:</span>
                                    <span class="text-white ml-2">{{ identity.nation_name or 'Unknown' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-400">Tribal Affiliation:</span>
                                    <span class="text-white ml-2">{{ identity.tribal_affiliation.title() if identity.tribal_affiliation else 'None' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-400">Role Class:</span>
                                    <span class="text-white ml-2">{{ identity.role_class.title() if identity.role_class else 'Citizen' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-400">Protection Tier:</span>
                                    <span class="text-white ml-2">{{ identity.protection_tier or 'Basic' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Stats Section -->
                <div class="space-y-4">
                    <div class="glass-card p-4 rounded-xl text-center">
                        <div class="text-2xl font-bold text-cyber-cyan">{{ identity.etzem_score or 0 }}</div>
                        <div class="text-sm text-gray-400">Etzem Score</div>
                    </div>
                    <div class="glass-card p-4 rounded-xl text-center">
                        <div class="text-2xl font-bold text-cyber-purple">{{ identity.zeman_count or 0 }}</div>
                        <div class="text-sm text-gray-400">Zeman Credits</div>
                    </div>
                    <div class="glass-card p-4 rounded-xl text-center">
                        <div class="text-2xl font-bold text-cyber-green">{{ "%.1f"|format(identity.deeds_score or 0) }}</div>
                        <div class="text-sm text-gray-400">Deeds Score</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Information Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Technical Details -->
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Technical Details</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Identity ID:</span>
                        <span class="text-white font-mono text-xs">{{ identity.identity_id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Public Key:</span>
                        <span class="text-white font-mono text-xs">{{ identity.public_key[:16] }}...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Wallet Address:</span>
                        <span class="text-white font-mono text-xs">
                            {% if identity.wallet_address %}
                                {{ identity.wallet_address[:16] }}...
                            {% else %}
                                Not Generated
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Created:</span>
                        <span class="text-white">
                            {% if identity.created_at %}
                                {{ moment(identity.created_at).format('MMM DD, YYYY') }}
                            {% else %}
                                Unknown
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Covenant Information -->
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Covenant Status</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Covenant Accepted:</span>
                        <span class="{% if identity.covenant_accepted %}text-green-400{% else %}text-red-400{% endif %}">
                            {% if identity.covenant_accepted %}✓ Accepted{% else %}✗ Not Accepted{% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Eden Mode Completed:</span>
                        <span class="{% if identity.eden_mode_completed %}text-green-400{% else %}text-yellow-400{% endif %}">
                            {% if identity.eden_mode_completed %}✓ Completed{% else %}⏳ Pending{% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Vault Status:</span>
                        <span class="text-white">{{ identity.vault_status.title() if identity.vault_status else 'Unknown' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">CIPP Tier:</span>
                        <span class="text-cyber-cyan">Tier {{ identity.cipp_tier or 1 }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex flex-wrap gap-4 justify-center">
            <a href="{{ url_for('tokenomics.dashboard') }}" class="glass-button-primary px-6 py-3 rounded-xl font-orbitron">
                📊 Biblical Tokenomics
            </a>
            <a href="{{ url_for('sela.dashboard') }}" class="glass-button-secondary px-6 py-3 rounded-xl font-orbitron">
                🏢 Sela Management
            </a>
            {% if identity.role_class == 'israelite' %}
            <a href="{{ url_for('tribes.israel') }}" class="glass-button-enhanced px-6 py-3 rounded-xl font-orbitron">
                🏛️ Tribal Council
            </a>
            {% endif %}
        </div>

        <!-- Metadata Display (for debugging) -->
        {% if identity.metadata and config.DEBUG %}
        <div class="mt-8 glass-card p-6 rounded-xl">
            <h3 class="text-lg font-orbitron font-bold text-gray-400 mb-4">Debug: Metadata</h3>
            <pre class="text-xs text-gray-500 overflow-auto">{{ identity.metadata | tojson(indent=2) }}</pre>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Add any interactive functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Add copy functionality for technical details
    const technicalDetails = document.querySelectorAll('.font-mono');
    technicalDetails.forEach(element => {
        element.style.cursor = 'pointer';
        element.title = 'Click to copy';
        element.addEventListener('click', function() {
            navigator.clipboard.writeText(this.textContent).then(() => {
                // Show brief feedback
                const original = this.textContent;
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = original;
                }, 1000);
            });
        });
    });
});
</script>
{% endblock %}
