#!/usr/bin/env python3
"""
Elder Validation System Migration
Creates tables and systems for elder validation tracking, tribal rotation, and governance oversight.

This migration adds:
1. Elder validation tracking tables
2. Tribal elder assignment management
3. Consensus tracking and metrics
4. Elder dashboard support systems

Author: ONNYX Development Team
Date: 2025-07-17
Priority: HIGH - Required for elder governance system
"""

import sqlite3
import sys
import os
import logging
import json
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_connection():
    """Get database connection."""
    try:
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        if backend_dir.endswith('migrations'):
            backend_dir = os.path.dirname(backend_dir)
        
        db_path = os.path.join(backend_dir, 'data', 'onnyx.db')
        logger.info(f"Connecting to database: {db_path}")
        
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
        
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return None

def create_elder_validations_table(conn):
    """Create elder validation tracking table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS elder_validations (
                validation_id TEXT PRIMARY KEY,
                elder_id TEXT NOT NULL,
                block_hash TEXT NOT NULL,
                validation_decision TEXT NOT NULL,
                biblical_justification TEXT,
                biblical_compliance_score REAL DEFAULT 0.0,
                timestamp INTEGER NOT NULL,
                signature TEXT,
                FOREIGN KEY (elder_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_elder_validations_elder ON elder_validations(elder_id)",
            "CREATE INDEX IF NOT EXISTS idx_elder_validations_block ON elder_validations(block_hash)",
            "CREATE INDEX IF NOT EXISTS idx_elder_validations_timestamp ON elder_validations(timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created elder_validations table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create elder_validations table: {e}")
        return False

def create_tribal_elder_assignments_table(conn):
    """Create tribal elder assignments table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tribal_elder_assignments (
                assignment_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                tribe_code TEXT NOT NULL,
                role_type TEXT NOT NULL,
                assigned_at INTEGER NOT NULL,
                active_until INTEGER,
                rotation_order INTEGER DEFAULT 0,
                voting_weight INTEGER DEFAULT 1,
                status TEXT DEFAULT 'ACTIVE',
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_tribal_elder_assignments_identity ON tribal_elder_assignments(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_tribal_elder_assignments_tribe ON tribal_elder_assignments(tribe_code)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created tribal_elder_assignments table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create tribal_elder_assignments table: {e}")
        return False

def create_elder_consensus_records_table(conn):
    """Create elder consensus tracking table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS elder_consensus_records (
                consensus_id TEXT PRIMARY KEY,
                proposal_id TEXT NOT NULL,
                proposal_type TEXT NOT NULL,
                participating_elders TEXT,
                votes_cast TEXT,
                consensus_reached BOOLEAN DEFAULT FALSE,
                consensus_timestamp INTEGER,
                required_threshold REAL DEFAULT 0.67,
                actual_approval_rate REAL DEFAULT 0.0,
                tribal_participation TEXT,
                created_at INTEGER NOT NULL
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_elder_consensus_records_proposal ON elder_consensus_records(proposal_id)",
            "CREATE INDEX IF NOT EXISTS idx_elder_consensus_records_timestamp ON elder_consensus_records(created_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created elder_consensus_records table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create elder_consensus_records table: {e}")
        return False

def seed_initial_elder_assignments(conn):
    """Seed initial tribal elder assignments."""
    try:
        cursor = conn.cursor()
        
        # Get existing tribal elders or high-tier users
        potential_elders = cursor.execute("""
            SELECT identity_id, name, nation_code, verification_level, etzem_score
            FROM identities 
            WHERE role_class = 'Tribal_Elder' OR verification_level >= 3
            ORDER BY nation_code, etzem_score DESC
        """).fetchall()
        
        # Create assignments for each tribe
        tribal_codes = ['JU', 'LE', 'EP', 'BE', 'SI', 'MA', 'IS', 'ZE', 'NA', 'GA', 'AS', 'RE']
        
        assignments_created = 0
        
        for tribe_code in tribal_codes:
            # Find elders for this tribe
            tribe_elders = [e for e in potential_elders if e['nation_code'] == tribe_code]
            
            if not tribe_elders:
                # If no specific tribal elders, assign from general pool
                tribe_elders = [e for e in potential_elders if e['verification_level'] >= 3][:1]
            
            for i, elder in enumerate(tribe_elders[:3]):  # Max 3 elders per tribe
                assignment_id = f"TEA_{tribe_code}_{elder['identity_id'][:8]}_{int(time.time())}"
                
                role_type = "PRIMARY_ELDER" if i == 0 else "BACKUP_ELDER"
                voting_weight = 2 if tribe_code in ['JU', 'LE', 'EP'] else 1  # Biblical weights
                
                cursor.execute("""
                    INSERT OR IGNORE INTO tribal_elder_assignments 
                    (assignment_id, identity_id, tribe_code, role_type, 
                     assigned_at, rotation_order, voting_weight, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    assignment_id, elder['identity_id'], tribe_code, role_type,
                    int(time.time()), i, voting_weight, 'ACTIVE'
                ))
                
                assignments_created += 1
        
        conn.commit()
        logger.info(f"✅ Created {assignments_created} tribal elder assignments")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to seed elder assignments: {e}")
        return False

def create_sample_validation_records(conn):
    """Create sample validation records for testing."""
    try:
        cursor = conn.cursor()
        
        # Get some elders
        elders = cursor.execute("""
            SELECT identity_id FROM tribal_elder_assignments LIMIT 5
        """).fetchall()
        
        if not elders:
            logger.info("No elders found for sample validation records")
            return True
        
        # Get some recent blocks
        blocks = cursor.execute("""
            SELECT block_hash FROM blocks ORDER BY block_height DESC LIMIT 3
        """).fetchall()
        
        if not blocks:
            logger.info("No blocks found for sample validation records")
            return True
        
        # Create sample validations
        validations_created = 0
        
        for block in blocks:
            for elder in elders[:3]:  # 3 elders per block
                validation_id = f"VAL_{elder['identity_id'][:8]}_{block['block_hash'][:8]}"
                
                cursor.execute("""
                    INSERT OR IGNORE INTO elder_validations 
                    (validation_id, elder_id, block_hash, validation_decision,
                     biblical_justification, biblical_compliance_score, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    validation_id, elder['identity_id'], block['block_hash'],
                    'APPROVE', 'Block meets biblical compliance standards',
                    0.85, int(time.time()) - (validations_created * 300)  # Spread over time
                ))
                
                validations_created += 1
        
        conn.commit()
        logger.info(f"✅ Created {validations_created} sample validation records")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create sample validation records: {e}")
        return False

def verify_elder_system_integrity(conn):
    """Verify the elder validation system is properly set up."""
    try:
        cursor = conn.cursor()
        
        # Check all required tables exist
        required_tables = [
            'elder_validations',
            'tribal_elder_assignments', 
            'elder_consensus_records'
        ]
        
        missing_tables = []
        
        for table in required_tables:
            result = cursor.execute("""
                SELECT name FROM sqlite_master WHERE type='table' AND name=?
            """, (table,)).fetchone()
            
            if not result:
                missing_tables.append(table)
        
        if missing_tables:
            logger.error(f"❌ Missing elder system tables: {missing_tables}")
            return False
        
        # Check data integrity
        elder_count = cursor.execute("SELECT COUNT(*) as count FROM tribal_elder_assignments").fetchone()['count']
        validation_count = cursor.execute("SELECT COUNT(*) as count FROM elder_validations").fetchone()['count']
        
        logger.info(f"✅ Elder system verification complete:")
        logger.info(f"   - Elder assignments: {elder_count}")
        logger.info(f"   - Validation records: {validation_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Elder system verification failed: {e}")
        return False

def run_elder_validation_migration():
    """Run the complete elder validation system migration."""
    logger.info("👑 Starting Elder Validation System Migration")
    logger.info("=" * 60)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        migration_steps = [
            ("Elder Validations Table", create_elder_validations_table),
            ("Tribal Elder Assignments", create_tribal_elder_assignments_table),
            ("Elder Consensus Records", create_elder_consensus_records_table),
            ("Initial Elder Assignments", seed_initial_elder_assignments),
            ("Sample Validation Records", create_sample_validation_records),
            ("System Integrity Check", verify_elder_system_integrity)
        ]
        
        success_count = 0
        
        for step_name, step_function in migration_steps:
            logger.info(f"📋 {step_name}...")
            if step_function(conn):
                success_count += 1
        
        logger.info("\n📊 Elder Validation Migration Results:")
        logger.info("=" * 50)
        logger.info(f"✅ Components completed successfully: {success_count}/{len(migration_steps)}")
        
        overall_success = success_count == len(migration_steps)
        
        if overall_success:
            logger.info("\n🎉 SUCCESS: Elder Validation System migration completed!")
            logger.info("   - Elder validation tracking implemented")
            logger.info("   - Tribal elder assignments created")
            logger.info("   - Consensus tracking system established")
            logger.info("   - Elder dashboard support systems ready")
            logger.info("   - Tribal rotation infrastructure prepared")
        else:
            logger.error("\n⚠️ Migration completed with issues - manual review required")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Elder validation migration failed: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = run_elder_validation_migration()
    sys.exit(0 if success else 1)
