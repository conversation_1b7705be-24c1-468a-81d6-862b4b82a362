"""
Wisdom Engine - Solomonic Judgment System
Implements divine governance through biblical wisdom application.

This module provides:
1. Wisdom scroll management and retrieval
2. Judgment precedent analysis
3. Dispute resolution guidance
4. Biblical principle application
5. Elder decision support

Author: ONNYX Development Team
Date: 2025-07-17
"""

import sqlite3
import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from shared.config.onnyx_config import onnyx_config

logger = logging.getLogger(__name__)

class WisdomType(Enum):
    PARABLE = "PARABLE"
    TEACHING = "TEACHING"
    PROPHECY = "PROPHECY"
    JUDGMENT_TEMPLATE = "JUDGMENT_TEMPLATE"
    PROVERB = "PROVERB"

class WisdomCategory(Enum):
    JUSTICE = "JUSTICE"
    MERCY = "MERCY"
    GOVERNANCE = "GOVERNANCE"
    ECONOMICS = "ECONOMICS"
    RELATIONSHIPS = "RELATIONSHIPS"

class DisputeType(Enum):
    PROPERTY = "PROPERTY"
    DEBT = "DEBT"
    LABOR = "LABOR"
    COVENANT_VIOLATION = "COVENANT_VIOLATION"
    INHERITANCE = "INHERITANCE"

@dataclass
class WisdomScroll:
    scroll_id: str
    title: str
    content: str
    scroll_type: WisdomType
    wisdom_category: WisdomCategory
    application_context: str
    precedent_weight: int
    biblical_references: List[str]
    usage_count: int = 0
    effectiveness_score: float = 0.0
    author_identity_id: Optional[str] = None
    created_at: Optional[int] = None
    last_applied: Optional[int] = None
    metadata: Dict[str, Any] = None

@dataclass
class JudgmentRecord:
    judgment_id: str
    case_title: str
    case_description: str
    dispute_type: DisputeType
    plaintiff_id: str
    defendant_id: str
    presiding_elder_id: str
    judgment_text: str
    biblical_basis: str
    wisdom_scrolls_cited: List[str]
    judgment_type: str
    enforcement_method: str
    created_at: int
    executed_at: Optional[int] = None
    additional_judges: List[str] = None
    appeal_status: str = "NONE"
    metadata: Dict[str, Any] = None

class WisdomEngine:
    """
    The Wisdom Engine implements Solomonic judgment principles for ONNYX governance.
    It provides biblical wisdom application, precedent analysis, and decision support.
    """
    
    def __init__(self):
        self.db_path = onnyx_config.db_path
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def search_wisdom(self, 
                     query: str, 
                     category: Optional[WisdomCategory] = None,
                     scroll_type: Optional[WisdomType] = None,
                     min_weight: int = 1) -> List[WisdomScroll]:
        """
        Search for relevant wisdom scrolls based on query and filters.
        
        Args:
            query: Search query for title/content
            category: Filter by wisdom category
            scroll_type: Filter by scroll type
            min_weight: Minimum precedent weight
            
        Returns:
            List of matching wisdom scrolls
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            sql = """
                SELECT * FROM wisdom_scrolls 
                WHERE (title LIKE ? OR content LIKE ? OR application_context LIKE ?)
                AND precedent_weight >= ?
            """
            params = [f"%{query}%", f"%{query}%", f"%{query}%", min_weight]
            
            if category:
                sql += " AND wisdom_category = ?"
                params.append(category.value)
            
            if scroll_type:
                sql += " AND scroll_type = ?"
                params.append(scroll_type.value)
            
            sql += " ORDER BY precedent_weight DESC, effectiveness_score DESC"
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            
            scrolls = []
            for row in rows:
                scroll = WisdomScroll(
                    scroll_id=row['scroll_id'],
                    title=row['title'],
                    content=row['content'],
                    scroll_type=WisdomType(row['scroll_type']),
                    wisdom_category=WisdomCategory(row['wisdom_category']),
                    application_context=row['application_context'],
                    precedent_weight=row['precedent_weight'],
                    biblical_references=json.loads(row['biblical_references'] or '[]'),
                    usage_count=row['usage_count'],
                    effectiveness_score=row['effectiveness_score'],
                    author_identity_id=row['author_identity_id'],
                    created_at=row['created_at'],
                    last_applied=row['last_applied'],
                    metadata=json.loads(row['metadata'] or '{}')
                )
                scrolls.append(scroll)
            
            conn.close()
            return scrolls
            
        except Exception as e:
            logger.error(f"Error searching wisdom: {e}")
            return []
    
    def get_judgment_precedents(self, dispute_type: DisputeType, limit: int = 10) -> List[JudgmentRecord]:
        """
        Get relevant judgment precedents for a dispute type.
        
        Args:
            dispute_type: Type of dispute
            limit: Maximum number of precedents to return
            
        Returns:
            List of relevant judgment records
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM judgment_records 
                WHERE dispute_type = ? 
                AND judgment_type IN ('BINDING', 'PRECEDENT_SETTING')
                ORDER BY created_at DESC
                LIMIT ?
            """, (dispute_type.value, limit))
            
            rows = cursor.fetchall()
            
            judgments = []
            for row in rows:
                judgment = JudgmentRecord(
                    judgment_id=row['judgment_id'],
                    case_title=row['case_title'],
                    case_description=row['case_description'],
                    dispute_type=DisputeType(row['dispute_type']),
                    plaintiff_id=row['plaintiff_id'],
                    defendant_id=row['defendant_id'],
                    presiding_elder_id=row['presiding_elder_id'],
                    judgment_text=row['judgment_text'],
                    biblical_basis=row['biblical_basis'],
                    wisdom_scrolls_cited=json.loads(row['wisdom_scrolls_cited'] or '[]'),
                    judgment_type=row['judgment_type'],
                    enforcement_method=row['enforcement_method'],
                    created_at=row['created_at'],
                    executed_at=row['executed_at'],
                    additional_judges=json.loads(row['additional_judges'] or '[]'),
                    appeal_status=row['appeal_status'],
                    metadata=json.loads(row['metadata'] or '{}')
                )
                judgments.append(judgment)
            
            conn.close()
            return judgments
            
        except Exception as e:
            logger.error(f"Error getting judgment precedents: {e}")
            return []
    
    def suggest_wisdom_for_case(self, case_description: str, dispute_type: DisputeType) -> Dict[str, Any]:
        """
        Suggest relevant wisdom and precedents for a case.
        
        Args:
            case_description: Description of the case
            dispute_type: Type of dispute
            
        Returns:
            Dictionary with suggested wisdom, precedents, and biblical references
        """
        try:
            # Search for relevant wisdom scrolls
            wisdom_scrolls = self.search_wisdom(case_description, min_weight=2)
            
            # Get judgment precedents
            precedents = self.get_judgment_precedents(dispute_type, limit=5)
            
            # Get relevant biblical references
            biblical_refs = self._get_biblical_references_for_case(case_description, dispute_type)
            
            # Calculate confidence score based on available wisdom
            confidence_score = self._calculate_confidence_score(wisdom_scrolls, precedents, biblical_refs)
            
            return {
                "wisdom_scrolls": [self._wisdom_scroll_to_dict(ws) for ws in wisdom_scrolls[:5]],
                "precedents": [self._judgment_record_to_dict(jr) for jr in precedents],
                "biblical_references": biblical_refs,
                "confidence_score": confidence_score,
                "recommendation": self._generate_recommendation(wisdom_scrolls, precedents, biblical_refs),
                "suggested_approach": self._suggest_approach(dispute_type, wisdom_scrolls)
            }
            
        except Exception as e:
            logger.error(f"Error suggesting wisdom for case: {e}")
            return {"error": str(e)}
    
    def record_wisdom_application(self, 
                                wisdom_scroll_id: str,
                                applied_by_elder_id: str,
                                application_context: str,
                                case_id: Optional[str] = None,
                                effectiveness_rating: Optional[int] = None) -> bool:
        """
        Record the application of wisdom to a case.
        
        Args:
            wisdom_scroll_id: ID of the wisdom scroll applied
            applied_by_elder_id: ID of the elder who applied the wisdom
            application_context: Context of the application
            case_id: Optional case ID
            effectiveness_rating: Optional effectiveness rating (1-5)
            
        Returns:
            True if recorded successfully
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            application_id = f"WAPP_{int(time.time())}_{wisdom_scroll_id[:8]}"
            
            cursor.execute("""
                INSERT INTO wisdom_applications 
                (application_id, wisdom_scroll_id, applied_to_case_id, applied_by_elder_id,
                 application_context, effectiveness_rating, applied_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                application_id, wisdom_scroll_id, case_id, applied_by_elder_id,
                application_context, effectiveness_rating, int(time.time())
            ))
            
            # Update usage count for the wisdom scroll
            cursor.execute("""
                UPDATE wisdom_scrolls 
                SET usage_count = usage_count + 1, last_applied = ?
                WHERE scroll_id = ?
            """, (int(time.time()), wisdom_scroll_id))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Recorded wisdom application: {application_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording wisdom application: {e}")
            return False
    
    def _get_biblical_references_for_case(self, case_description: str, dispute_type: DisputeType) -> List[Dict[str, Any]]:
        """Get relevant biblical references for a case."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Map dispute types to principle categories
            category_map = {
                DisputeType.PROPERTY: "JUSTICE",
                DisputeType.DEBT: "ECONOMICS", 
                DisputeType.LABOR: "ECONOMICS",
                DisputeType.COVENANT_VIOLATION: "GOVERNANCE",
                DisputeType.INHERITANCE: "JUSTICE"
            }
            
            category = category_map.get(dispute_type, "JUSTICE")
            
            cursor.execute("""
                SELECT * FROM biblical_references 
                WHERE principle_category = ? 
                OR text_kjv LIKE ?
                ORDER BY usage_count DESC
                LIMIT 5
            """, (category, f"%{case_description[:50]}%"))
            
            rows = cursor.fetchall()
            
            refs = []
            for row in rows:
                refs.append({
                    "reference_id": row['reference_id'],
                    "book": row['book'],
                    "chapter": row['chapter'],
                    "verse_start": row['verse_start'],
                    "verse_end": row['verse_end'],
                    "text_kjv": row['text_kjv'],
                    "principle_category": row['principle_category'],
                    "application_context": row['application_context']
                })
            
            conn.close()
            return refs
            
        except Exception as e:
            logger.error(f"Error getting biblical references: {e}")
            return []
    
    def _calculate_confidence_score(self, wisdom_scrolls: List[WisdomScroll], 
                                  precedents: List[JudgmentRecord], 
                                  biblical_refs: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for wisdom suggestions."""
        score = 0.0
        
        # Weight based on available wisdom
        if wisdom_scrolls:
            avg_weight = sum(ws.precedent_weight for ws in wisdom_scrolls) / len(wisdom_scrolls)
            score += min(avg_weight * 0.2, 0.4)  # Max 0.4 from wisdom
        
        # Weight based on precedents
        if precedents:
            score += min(len(precedents) * 0.1, 0.3)  # Max 0.3 from precedents
        
        # Weight based on biblical references
        if biblical_refs:
            score += min(len(biblical_refs) * 0.06, 0.3)  # Max 0.3 from scripture
        
        return min(score, 1.0)
    
    def _generate_recommendation(self, wisdom_scrolls: List[WisdomScroll], 
                               precedents: List[JudgmentRecord], 
                               biblical_refs: List[Dict[str, Any]]) -> str:
        """Generate a recommendation based on available wisdom."""
        if not wisdom_scrolls and not precedents:
            return "Insufficient wisdom data. Seek counsel from senior elders and study relevant scriptures."
        
        recommendation = "Based on available wisdom and precedents:\n\n"
        
        if wisdom_scrolls:
            top_wisdom = wisdom_scrolls[0]
            recommendation += f"Primary Wisdom: {top_wisdom.title}\n"
            recommendation += f"Application: {top_wisdom.application_context}\n\n"
        
        if precedents:
            recommendation += f"Consider {len(precedents)} similar precedent(s) for guidance.\n\n"
        
        if biblical_refs:
            recommendation += f"Key Scripture: {biblical_refs[0]['book']} {biblical_refs[0]['chapter']}:{biblical_refs[0]['verse_start']}\n"
        
        return recommendation
    
    def _suggest_approach(self, dispute_type: DisputeType, wisdom_scrolls: List[WisdomScroll]) -> str:
        """Suggest an approach based on dispute type and available wisdom."""
        approaches = {
            DisputeType.PROPERTY: "Focus on rightful ownership and biblical property rights",
            DisputeType.DEBT: "Consider biblical debt forgiveness principles and ability to repay",
            DisputeType.LABOR: "Ensure fair wages and righteous treatment of workers",
            DisputeType.COVENANT_VIOLATION: "Emphasize restoration and community healing",
            DisputeType.INHERITANCE: "Follow biblical inheritance laws and tribal customs"
        }
        
        base_approach = approaches.get(dispute_type, "Seek wisdom through prayer and scripture")
        
        if wisdom_scrolls and wisdom_scrolls[0].scroll_type == WisdomType.JUDGMENT_TEMPLATE:
            base_approach += f". Apply the template: {wisdom_scrolls[0].title}"
        
        return base_approach
    
    def _wisdom_scroll_to_dict(self, ws: WisdomScroll) -> Dict[str, Any]:
        """Convert WisdomScroll to dictionary."""
        return {
            "scroll_id": ws.scroll_id,
            "title": ws.title,
            "content": ws.content,
            "scroll_type": ws.scroll_type.value,
            "wisdom_category": ws.wisdom_category.value,
            "application_context": ws.application_context,
            "precedent_weight": ws.precedent_weight,
            "biblical_references": ws.biblical_references,
            "usage_count": ws.usage_count,
            "effectiveness_score": ws.effectiveness_score
        }
    
    def _judgment_record_to_dict(self, jr: JudgmentRecord) -> Dict[str, Any]:
        """Convert JudgmentRecord to dictionary."""
        return {
            "judgment_id": jr.judgment_id,
            "case_title": jr.case_title,
            "case_description": jr.case_description,
            "dispute_type": jr.dispute_type.value,
            "judgment_text": jr.judgment_text,
            "biblical_basis": jr.biblical_basis,
            "wisdom_scrolls_cited": jr.wisdom_scrolls_cited,
            "judgment_type": jr.judgment_type,
            "created_at": jr.created_at
        }

    def create_wisdom_scroll(self,
                           title: str,
                           content: str,
                           scroll_type: WisdomType,
                           wisdom_category: WisdomCategory,
                           application_context: str,
                           author_identity_id: str,
                           biblical_references: List[str] = None,
                           precedent_weight: int = 1) -> str:
        """
        Create a new wisdom scroll.

        Args:
            title: Title of the wisdom scroll
            content: Content/teaching of the scroll
            scroll_type: Type of wisdom scroll
            wisdom_category: Category of wisdom
            application_context: When/how to apply this wisdom
            author_identity_id: ID of the author
            biblical_references: List of biblical references
            precedent_weight: Weight/importance of this wisdom (1-5)

        Returns:
            ID of the created wisdom scroll
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            scroll_id = f"WS_{int(time.time())}_{title[:10].upper().replace(' ', '_')}"
            biblical_refs_json = json.dumps(biblical_references or [])

            cursor.execute("""
                INSERT INTO wisdom_scrolls
                (scroll_id, title, content, scroll_type, author_identity_id,
                 biblical_references, wisdom_category, application_context,
                 precedent_weight, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                scroll_id, title, content, scroll_type.value, author_identity_id,
                biblical_refs_json, wisdom_category.value, application_context,
                precedent_weight, int(time.time())
            ))

            conn.commit()
            conn.close()

            logger.info(f"Created wisdom scroll: {scroll_id}")
            return scroll_id

        except Exception as e:
            logger.error(f"Error creating wisdom scroll: {e}")
            return None
