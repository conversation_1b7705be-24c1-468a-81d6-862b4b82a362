{% extends "base.html" %}

{% block title %}Sela Management - Admin Panel | ONNYX{% endblock %}

{% block head %}
<style>
.admin-warning {
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.1), rgba(255, 69, 0, 0.1));
    border: 1px solid rgba(255, 165, 0, 0.3);
}

.sela-card {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    border: 1px solid rgba(0, 255, 247, 0.2);
    transition: all 0.3s ease;
}

.sela-card:hover {
    border-color: rgba(0, 255, 247, 0.4);
    transform: translateY(-2px);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.status-active { background: rgba(0, 255, 0, 0.2); color: #00ff00; }
.status-pending { background: rgba(255, 165, 0, 0.2); color: #ffa500; }
.status-inactive { background: rgba(128, 128, 128, 0.2); color: #808080; }
.status-suspended { background: rgba(255, 69, 0, 0.2); color: #ff4500; }
.status-cancelled { background: rgba(255, 0, 0, 0.2); color: #ff0000; }

.category-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background: linear-gradient(135deg, var(--dark-900), var(--dark-800));
    margin: 5% auto;
    padding: 2rem;
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    color: var(--text-secondary);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--cyber-cyan);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: rgba(0, 255, 247, 0.1);
    border: 1px solid rgba(0, 255, 247, 0.2);
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.action-button {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    margin: 0.25rem;
}

.btn-view { background: rgba(0, 255, 247, 0.2); color: var(--cyber-cyan); }
.btn-view:hover { background: rgba(0, 255, 247, 0.3); }

.btn-edit { background: rgba(154, 0, 255, 0.2); color: var(--cyber-purple); }
.btn-edit:hover { background: rgba(154, 0, 255, 0.3); }

.btn-suspend { background: rgba(255, 165, 0, 0.2); color: #ffa500; }
.btn-suspend:hover { background: rgba(255, 165, 0, 0.3); }

.btn-delete { background: rgba(255, 69, 0, 0.2); color: #ff4500; }
.btn-delete:hover { background: rgba(255, 69, 0, 0.3); }

.search-filter {
    background: var(--dark-800);
    border: 1px solid var(--glass-border);
    border-radius: 0.5rem;
    padding: 0.75rem;
    color: white;
    width: 100%;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Admin Header -->
        <div class="admin-warning p-4 rounded-xl mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl">🏢</div>
                    <div>
                        <h1 class="text-2xl font-orbitron font-bold text-orange-400">Sela Management</h1>
                        <p class="text-orange-300">Manage all Sela businesses on the ONNYX platform</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button onclick="generateSelaPages()" class="glass-button-primary px-4 py-2 rounded-lg">
                        🌐 Generate Web Pages
                    </button>
                    <a href="{{ url_for('admin.dashboard') }}" class="glass-button-secondary px-4 py-2 rounded-lg">
                        ← Back to Admin Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Overview -->
        <div class="glass-card p-6 rounded-xl mb-8">
            <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Platform Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="text-2xl font-bold text-cyber-cyan">{{ stats.total_selas }}</div>
                    <div class="text-sm text-secondary">Total Selas</div>
                </div>
                <div class="stat-card">
                    <div class="text-2xl font-bold text-green-400">{{ stats.active_selas }}</div>
                    <div class="text-sm text-secondary">Active</div>
                </div>
                <div class="stat-card">
                    <div class="text-2xl font-bold text-orange-400">{{ stats.pending_selas }}</div>
                    <div class="text-sm text-secondary">Pending</div>
                </div>
                <div class="stat-card">
                    <div class="text-2xl font-bold text-red-400">{{ stats.inactive_selas }}</div>
                    <div class="text-sm text-secondary">Inactive/Suspended</div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="glass-card p-6 rounded-xl mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Search Selas</label>
                    <input type="text" id="searchInput" placeholder="Search by name, owner, or ID..." 
                           class="search-filter" onkeyup="filterSelas()">
                </div>
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Filter by Status</label>
                    <select id="statusFilter" class="search-filter" onchange="filterSelas()">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Filter by Category</label>
                    <select id="categoryFilter" class="search-filter" onchange="filterSelas()">
                        <option value="">All Categories</option>
                        {% for category, count in stats.categories.items() %}
                        <option value="{{ category }}">{{ category.title() }} ({{ count }})</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <!-- Selas Grid -->
        <div id="selasGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for sela in selas %}
            <div class="sela-card p-6 rounded-xl" 
                 data-name="{{ sela.name|lower }}" 
                 data-owner="{{ sela.owner_name|lower }}" 
                 data-status="{{ sela.status }}" 
                 data-category="{{ sela.category }}">
                
                <div class="flex items-center justify-between mb-4">
                    <div class="category-badge">{{ sela.category or 'Uncategorized' }}</div>
                    <div class="status-badge status-{{ sela.status }}">{{ sela.status.title() }}</div>
                </div>
                
                <h3 class="text-lg font-orbitron font-bold text-white mb-2">{{ sela.name }}</h3>
                <p class="text-sm text-secondary mb-2">{{ sela.description[:100] }}{% if sela.description|length > 100 %}...{% endif %}</p>
                
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-sm">
                        <span class="text-secondary w-16">Owner:</span>
                        <span class="text-cyber-cyan">{{ sela.owner_name or 'Unknown' }}</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <span class="text-secondary w-16">ID:</span>
                        <span class="text-cyber-purple font-mono text-xs">{{ sela.sela_id }}</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <span class="text-secondary w-16">Created:</span>
                        <span class="text-white">{{ moment(sela.created_at).format('%b %d, %Y') }}</span>
                    </div>
                </div>
                
                <div class="flex flex-wrap gap-2">
                    <button onclick="viewSela('{{ sela.sela_id }}')" class="action-button btn-view">
                        👁️ Dashboard
                    </button>
                    <button onclick="viewPublicPage('{{ sela.sela_id }}')" class="action-button btn-view">
                        🌐 Public Page
                    </button>
                    <button onclick="openConfigModal('{{ sela.sela_id }}')" class="action-button btn-edit">
                        ⚙️ Configure
                    </button>
                    {% if sela.status == 'active' %}
                    <button onclick="updateSelaStatus('{{ sela.sela_id }}', 'suspended')" class="action-button btn-suspend">
                        ⏸️ Suspend
                    </button>
                    {% elif sela.status == 'suspended' %}
                    <button onclick="updateSelaStatus('{{ sela.sela_id }}', 'active')" class="action-button btn-view">
                        ▶️ Activate
                    </button>
                    {% endif %}
                    <button onclick="confirmDeleteSela('{{ sela.sela_id }}', '{{ sela.name }}')" class="action-button btn-delete">
                        🗑️ Delete
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not selas %}
        <div class="text-center py-12">
            <div class="text-6xl mb-4">🏢</div>
            <h3 class="text-xl font-orbitron font-bold text-secondary mb-2">No Selas Found</h3>
            <p class="text-secondary">No Sela businesses have been created yet.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Configuration Modal -->
<div id="configModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeConfigModal()">&times;</span>
        <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">Configure Sela</h2>
        
        <form id="configForm" onsubmit="configureSela(event)">
            <input type="hidden" id="configSelaId" name="sela_id">
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Sela Name</label>
                    <input type="text" id="configName" name="name" 
                           class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Category</label>
                    <select id="configCategory" name="category" class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                        <option value="technology">Technology</option>
                        <option value="retail">Retail</option>
                        <option value="services">Services</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="agriculture">Agriculture</option>
                        <option value="finance">Finance</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="education">Education</option>
                        <option value="entertainment">Entertainment</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Description</label>
                    <textarea id="configDescription" name="description" rows="3"
                              class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white"></textarea>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-secondary mb-2">Mining Tier</label>
                        <select id="configMiningTier" name="mining_tier" class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                            <option value="bronze">Bronze</option>
                            <option value="silver">Silver</option>
                            <option value="gold">Gold</option>
                            <option value="platinum">Platinum</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-secondary mb-2">Mining Power</label>
                        <input type="number" id="configMiningPower" name="mining_power" step="0.1" min="0"
                               class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                    </div>
                </div>
                
                <div class="flex space-x-4 pt-4">
                    <button type="submit" class="flex-1 glass-button-primary py-3 rounded-lg">
                        Save Configuration
                    </button>
                    <button type="button" onclick="closeConfigModal()" class="flex-1 glass-button-secondary py-3 rounded-lg">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function filterSelas() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    
    const selaCards = document.querySelectorAll('.sela-card');
    
    selaCards.forEach(card => {
        const name = card.dataset.name;
        const owner = card.dataset.owner;
        const status = card.dataset.status;
        const category = card.dataset.category;
        
        const matchesSearch = !searchTerm || name.includes(searchTerm) || owner.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesCategory = !categoryFilter || category === categoryFilter;
        
        if (matchesSearch && matchesStatus && matchesCategory) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function viewSela(selaId) {
    window.open(`/sela/${selaId}/dashboard`, '_blank');
}

function viewPublicPage(selaId) {
    window.open(`/sela/${selaId}`, '_blank');
}

async function generateSelaPages() {
    if (!confirm('Generate default web pages for all active Selas? This will create public business pages for customer viewing.')) {
        return;
    }

    try {
        const response = await fetch('/admin/api/selas/generate-pages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(`Success! Generated web pages for ${result.updated_count} out of ${result.total_selas} Selas.`);
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        alert(`Error generating pages: ${error.message}`);
    }
}

async function updateSelaStatus(selaId, newStatus) {
    try {
        const response = await fetch(`/admin/api/selas/${selaId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: newStatus })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        alert(`Error updating status: ${error.message}`);
    }
}

async function openConfigModal(selaId) {
    try {
        // Load current Sela data
        const response = await fetch(`/admin/api/selas/${selaId}/details`);
        if (response.ok) {
            const sela = await response.json();

            // Populate form with current data
            document.getElementById('configSelaId').value = selaId;
            document.getElementById('configName').value = sela.name || '';
            document.getElementById('configCategory').value = sela.category || '';
            document.getElementById('configDescription').value = sela.description || '';
            document.getElementById('configMiningTier').value = sela.mining_tier || 'bronze';
            document.getElementById('configMiningPower').value = sela.mining_power || 1.0;
        } else {
            // If API fails, just open with empty form
            document.getElementById('configSelaId').value = selaId;
        }

        document.getElementById('configModal').style.display = 'block';
    } catch (error) {
        console.error('Error loading Sela data:', error);
        // Fallback to empty form
        document.getElementById('configSelaId').value = selaId;
        document.getElementById('configModal').style.display = 'block';
    }
}

function closeConfigModal() {
    document.getElementById('configModal').style.display = 'none';
    document.getElementById('configForm').reset();
}

async function configureSela(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const selaId = formData.get('sela_id');
    
    const data = {
        name: formData.get('name'),
        category: formData.get('category'),
        description: formData.get('description'),
        mining_config: {
            mining_tier: formData.get('mining_tier'),
            mining_power: parseFloat(formData.get('mining_power'))
        }
    };
    
    try {
        const response = await fetch(`/admin/api/selas/${selaId}/configure`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message);
            closeConfigModal();
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        alert(`Error configuring Sela: ${error.message}`);
    }
}

function confirmDeleteSela(selaId, selaName) {
    if (confirm(`Are you sure you want to delete "${selaName}"? This action cannot be undone and will remove all associated data.`)) {
        deleteSela(selaId);
    }
}

async function deleteSela(selaId) {
    try {
        const response = await fetch(`/admin/api/validators/${selaId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        alert(`Error deleting Sela: ${error.message}`);
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('configModal');
    if (event.target == modal) {
        closeConfigModal();
    }
}
</script>
{% endblock %}
