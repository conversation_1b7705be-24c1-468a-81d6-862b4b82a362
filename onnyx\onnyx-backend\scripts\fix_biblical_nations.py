#!/usr/bin/env python3
"""
Fix Biblical Nations - Proper Biblical Classification
Correctly implements the biblical tribal system:
1. Edom: EXACTLY the 12 Dukes from Genesis 36:40-43
2. <PERSON><PERSON><PERSON>: EXACTLY the 12 Princes from Genesis 25:13-15
3. Hamitic Nations: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> (Genesis 10:6)
4. Japhethic Nations: The 7 sons of <PERSON><PERSON><PERSON> and their branches (Genesis 10:2-4)
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
import time

def fix_biblical_nations():
    """Fix biblical nations with correct biblical classification."""
    print("📜 FIXING BIBLICAL NATIONS - PROPER BIBLICAL CLASSIFICATION")
    print("=" * 70)
    
    try:
        current_time = int(time.time())
        
        # First, clear any incorrect classifications
        print("\n🧹 CLEARING INCORRECT CLASSIFICATIONS")
        print("-" * 50)
        
        # Reset all ancestral groups
        db.execute("UPDATE biblical_nations SET ancestral_group = NULL, ancestral_description = NULL")
        print("   ✅ Cleared all ancestral groups")
        
        # Delete any incorrectly categorized nations
        db.execute("DELETE FROM biblical_nations WHERE nation_type = 'witness' AND nation_name NOT IN (SELECT name FROM (SELECT 'Teman' as name UNION SELECT 'Omar' UNION SELECT 'Zepho' UNION SELECT 'Kenaz' UNION SELECT 'Korah' UNION SELECT 'Gatam' UNION SELECT 'Amalek' UNION SELECT 'Timnah' UNION SELECT 'Alvah' UNION SELECT 'Jetheth' UNION SELECT 'Aholibamah' UNION SELECT 'Elah' UNION SELECT 'Pinon' UNION SELECT 'Mibzar' UNION SELECT 'Magdiel' UNION SELECT 'Iram' UNION SELECT 'Nebajoth' UNION SELECT 'Kedar' UNION SELECT 'Adbeel' UNION SELECT 'Mibsam' UNION SELECT 'Mishma' UNION SELECT 'Dumah' UNION SELECT 'Massa' UNION SELECT 'Hadad' UNION SELECT 'Tema' UNION SELECT 'Jetur' UNION SELECT 'Naphish' UNION SELECT 'Kedemah' UNION SELECT 'Cush' UNION SELECT 'Mizraim' UNION SELECT 'Canaan' UNION SELECT 'Put' UNION SELECT 'Gomer' UNION SELECT 'Magog' UNION SELECT 'Madai' UNION SELECT 'Javan' UNION SELECT 'Tubal' UNION SELECT 'Meshech' UNION SELECT 'Tiras' UNION SELECT 'Ashkenaz' UNION SELECT 'Riphath' UNION SELECT 'Togarmah' UNION SELECT 'Elishah' UNION SELECT 'Tarshish' UNION SELECT 'Kittim' UNION SELECT 'Dodanim'))")
        
        # Now add the correct biblical nations
        print("\n🏔️ ADDING THE 12 DUKES OF EDOM (Genesis 36:40-43)")
        print("-" * 50)
        
        edom_dukes = [
            ('TI', 'Timnah', 'Duke Timnah', '🏛️', 'Duke of Edom, the restraint'),
            ('AL', 'Alvah', 'Duke Alvah', '⚡', 'Duke of Edom, the sublime'),
            ('JT', 'Jetheth', 'Duke Jetheth', '🔨', 'Duke of Edom, the nail'),
            ('AO', 'Aholibamah', 'Duke Aholibamah', '⛺', 'Duke of Edom, tent of the high place'),
            ('EL', 'Elah', 'Duke Elah', '🌳', 'Duke of Edom, the oak'),
            ('PI', 'Pinon', 'Duke Pinon', '💎', 'Duke of Edom, the pearl'),
            ('KE', 'Kenaz', 'Duke Kenaz', '👑', 'Duke of Edom, the hunter'),
            ('TE', 'Teman', 'Duke Teman', '🏔️', 'Duke of Edom from the south country'),
            ('MB', 'Mibzar', 'Duke Mibzar', '🏰', 'Duke of Edom, the fortress'),
            ('MG', 'Magdiel', 'Duke Magdiel', '🗡️', 'Duke of Edom, God is noble'),
            ('IR', 'Iram', 'Duke Iram', '🏙️', 'Duke of Edom, the city'),
            ('OM', 'Omar', 'Duke Omar', '🗡️', 'Duke of Edom, the eloquent')
        ]
        
        for nation_code, nation_name, tribe_name, flag_symbol, description in edom_dukes:
            # Check if exists
            existing = db.query_one("SELECT nation_code FROM biblical_nations WHERE nation_code = ?", (nation_code,))
            if existing:
                # Update existing
                db.execute("""
                    UPDATE biblical_nations 
                    SET nation_name = ?, tribe_name = ?, flag_symbol = ?, description = ?,
                        ancestral_group = 'Edom', 
                        ancestral_description = 'The 12 Dukes of Edom, descendants of Esau (Genesis 36:40-43)',
                        historical_connection = 'Dukes who ruled in the land of Edom before any king reigned over Israel'
                    WHERE nation_code = ?
                """, (nation_name, tribe_name, flag_symbol, description, nation_code))
                print(f"   ✅ Updated {nation_name}")
            else:
                # Insert new
                db.execute("""
                    INSERT INTO biblical_nations (
                        nation_code, nation_name, tribe_name, nation_type, flag_symbol, description,
                        ancestral_group, ancestral_description, historical_connection
                    ) VALUES (?, ?, ?, 'witness', ?, ?, 'Edom', 
                             'The 12 Dukes of Edom, descendants of Esau (Genesis 36:40-43)',
                             'Dukes who ruled in the land of Edom before any king reigned over Israel')
                """, (nation_code, nation_name, tribe_name, flag_symbol, description))
                print(f"   ✅ Added {nation_name}")
        
        print("\n🏜️ ADDING THE 12 PRINCES OF ISHMAEL (Genesis 25:13-15)")
        print("-" * 50)
        
        ishmael_princes = [
            ('NE', 'Nebajoth', 'Prince Nebajoth', '🏺', 'Firstborn prince of Ishmael'),
            ('KD', 'Kedar', 'Prince Kedar', '🌙', 'Prince of Ishmael, the dark one'),
            ('AD', 'Adbeel', 'Prince Adbeel', '🏹', 'Prince of Ishmael, disciplined by God'),
            ('MI', 'Mibsam', 'Prince Mibsam', '🕊️', 'Prince of Ishmael, sweet smelling'),
            ('MS', 'Mishma', 'Prince Mishma', '🗣️', 'Prince of Ishmael, the hearing one'),
            ('DU', 'Dumah', 'Prince Dumah', '🏕️', 'Prince of Ishmael, the silent one'),
            ('MX', 'Massa', 'Prince Massa', '🏜️', 'Prince of Ishmael, the burden bearer'),
            ('HD', 'Hadad', 'Prince Hadad', '🌊', 'Prince of Ishmael, the honored one'),
            ('TM', 'Tema', 'Prince Tema', '🎪', 'Prince of Ishmael, the desert dweller'),
            ('JE', 'Jetur', 'Prince Jetur', '📦', 'Prince of Ishmael, the enclosed one'),
            ('NP', 'Naphish', 'Prince Naphish', '🐑', 'Prince of Ishmael, the refreshing one'),
            ('KM', 'Kedemah', 'Prince Kedemah', '🏰', 'Prince of Ishmael, the eastern one')
        ]
        
        for nation_code, nation_name, tribe_name, flag_symbol, description in ishmael_princes:
            # Check if exists
            existing = db.query_one("SELECT nation_code FROM biblical_nations WHERE nation_code = ?", (nation_code,))
            if existing:
                # Update existing
                db.execute("""
                    UPDATE biblical_nations 
                    SET nation_name = ?, tribe_name = ?, flag_symbol = ?, description = ?,
                        ancestral_group = 'Ishmael', 
                        ancestral_description = 'The 12 Princes of Ishmael, sons of Abraham (Genesis 25:13-15)',
                        historical_connection = 'Princes who became great nations as promised to Abraham'
                    WHERE nation_code = ?
                """, (nation_name, tribe_name, flag_symbol, description, nation_code))
                print(f"   ✅ Updated {nation_name}")
            else:
                # Insert new
                db.execute("""
                    INSERT INTO biblical_nations (
                        nation_code, nation_name, tribe_name, nation_type, flag_symbol, description,
                        ancestral_group, ancestral_description, historical_connection
                    ) VALUES (?, ?, ?, 'witness', ?, ?, 'Ishmael', 
                             'The 12 Princes of Ishmael, sons of Abraham (Genesis 25:13-15)',
                             'Princes who became great nations as promised to Abraham')
                """, (nation_code, nation_name, tribe_name, flag_symbol, description))
                print(f"   ✅ Added {nation_name}")
        
        print("\n🏺 ADDING HAMITIC NATIONS (Genesis 10:6)")
        print("-" * 50)
        
        hamitic_nations = [
            ('CU', 'Cush', 'House of Cush', '🏺', 'Son of Ham, father of Ethiopia'),
            ('MZ', 'Mizraim', 'House of Mizraim', '🏛️', 'Son of Ham, father of Egypt'),
            ('CA', 'Canaan', 'House of Canaan', '🏔️', 'Son of Ham, father of the Canaanites'),
            ('PU', 'Put', 'House of Put', '🌴', 'Son of Ham, father of Libya')
        ]
        
        for nation_code, nation_name, tribe_name, flag_symbol, description in hamitic_nations:
            # Check if exists
            existing = db.query_one("SELECT nation_code FROM biblical_nations WHERE nation_code = ?", (nation_code,))
            if existing:
                # Update existing
                db.execute("""
                    UPDATE biblical_nations 
                    SET nation_name = ?, tribe_name = ?, flag_symbol = ?, description = ?,
                        ancestral_group = 'Hamitic Nations', 
                        ancestral_description = 'Sons of Ham: Cush, Mizraim, Put, and Canaan (Genesis 10:6)',
                        historical_connection = 'Descendants of Ham who populated Africa and parts of Asia'
                    WHERE nation_code = ?
                """, (nation_name, tribe_name, flag_symbol, description, nation_code))
                print(f"   ✅ Updated {nation_name}")
            else:
                # Insert new
                db.execute("""
                    INSERT INTO biblical_nations (
                        nation_code, nation_name, tribe_name, nation_type, flag_symbol, description,
                        ancestral_group, ancestral_description, historical_connection
                    ) VALUES (?, ?, ?, 'witness', ?, ?, 'Hamitic Nations', 
                             'Sons of Ham: Cush, Mizraim, Put, and Canaan (Genesis 10:6)',
                             'Descendants of Ham who populated Africa and parts of Asia')
                """, (nation_code, nation_name, tribe_name, flag_symbol, description))
                print(f"   ✅ Added {nation_name}")
        
        # Verify the results
        print("\n🎉 VERIFICATION")
        print("-" * 50)
        
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as count
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)
        
        print("📋 Correct Biblical Ancestral Groups:")
        for group in ancestral_groups:
            print(f"   {group['ancestral_group']:20s}: {group['count']} nations")
            
        # Verify we have exactly 12 of each
        edom_count = db.query_one("SELECT COUNT(*) as count FROM biblical_nations WHERE ancestral_group = 'Edom'")['count']
        ishmael_count = db.query_one("SELECT COUNT(*) as count FROM biblical_nations WHERE ancestral_group = 'Ishmael'")['count']
        
        print(f"\n✅ Verification:")
        print(f"   Edom Dukes: {edom_count}/12 {'✅' if edom_count == 12 else '❌'}")
        print(f"   Ishmael Princes: {ishmael_count}/12 {'✅' if ishmael_count == 12 else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_biblical_nations()
    if success:
        print("\n✅ Biblical nations fixed successfully!")
    else:
        print("\n❌ Biblical nations fix failed!")
