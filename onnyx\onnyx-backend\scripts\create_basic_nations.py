#!/usr/bin/env python3
"""
Create Basic Nations for Eden Mode

This script creates the basic nations needed for Eden Mode identity creation
by temporarily disabling foreign key constraints.
"""

import os
import sys
import time
import logging
import sqlite3

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_nations_directly():
    """Create nations directly using SQLite connection"""
    try:
        # Connect directly to the database
        db_path = os.path.join(os.path.dirname(__file__), '..', 'shared', 'db', 'db', 'onnyx.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("Creating nations directly in database...")
        
        # Disable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # 12 Tribes of Israel + System nations
        nations_data = [
            ("J<PERSON>", "Judah", "The tribe of Judah, from which kings come", "system", "active"),
            ("B<PERSON>", "<PERSON>", "The tribe of <PERSON>, the beloved of the Lord", "system", "active"),
            ("LE", "Levi", "The tribe of Levi, the priestly tribe", "system", "active"),
            ("IS", "Issachar", "The tribe of Issachar, strong donkey", "system", "active"),
            ("ZE", "Zebulun", "The tribe of Zebulun, dwelling at the seashore", "system", "active"),
            ("RE", "Reuben", "The tribe of Reuben, the firstborn", "system", "active"),
            ("SI", "Simeon", "The tribe of Simeon, hearing", "system", "active"),
            ("GA", "Gad", "The tribe of Gad, a troop shall overcome", "system", "active"),
            ("AS", "Asher", "The tribe of Asher, happy", "system", "active"),
            ("NA", "Naphtali", "The tribe of Naphtali, wrestling", "system", "active"),
            ("DA", "Dan", "The tribe of Dan, judging", "system", "active"),
            ("EP", "Ephraim", "The tribe of Ephraim, fruitful", "system", "active"),
            ("MA", "Manasseh", "The tribe of Manasseh, causing to forget", "system", "active"),
            ("SY", "System", "System-generated nation for administrative purposes", "system", "active"),
            ("WN", "Witness Nations", "Nations that witness to the covenant", "system", "active")
        ]
        
        current_time = int(time.time())
        
        for nation_id, name, description, founder_id, status in nations_data:
            # Check if nation already exists
            cursor.execute("SELECT nation_id FROM nations WHERE nation_id = ?", (nation_id,))
            existing = cursor.fetchone()
            
            if not existing:
                metadata = f'{{"tribe": "{name}", "type": "israelite", "biblical": true}}'
                
                cursor.execute("""
                    INSERT INTO nations (nation_id, name, description, founder_id, metadata, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    nation_id,
                    name,
                    description,
                    founder_id,
                    metadata,
                    status,
                    current_time,
                    current_time
                ))
                
                logger.info(f"✅ Created nation: {nation_id} - {name}")
            else:
                logger.info(f"✅ Nation already exists: {nation_id} - {name}")
        
        # Commit the changes
        conn.commit()
        
        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Verify the nations were created
        cursor.execute("SELECT COUNT(*) FROM nations")
        count = cursor.fetchone()[0]
        logger.info(f"✅ Total nations in database: {count}")
        
        # Close connection
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating nations: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def test_eden_mode_identity_creation():
    """Test Eden Mode identity creation with nation_id"""
    try:
        from shared.db.db import db
        
        logger.info("Testing Eden Mode identity creation...")
        
        # Test data
        test_identity = {
            'identity_id': 'TEST_EDEN_NATION_123',
            'name': 'Test Eden Nation User',
            'email': '<EMAIL>',
            'public_key': 'test_public_key_eden_nation_123',
            'nation_id': 'JU',  # Judah
            'tribal_affiliation': 'judah',
            'status': 'active',
            'role_class': 'citizen',
            'verification_level': 1,
            'covenant_accepted': True,
            'eden_mode_completed': True,
            'metadata': '{"test": true, "eden_mode": true}',
            'created_at': int(time.time()),
            'updated_at': int(time.time())
        }
        
        # Insert test identity
        columns = ', '.join(test_identity.keys())
        placeholders = ', '.join(['?' for _ in test_identity.keys()])
        values = list(test_identity.values())
        
        db.execute(f"INSERT INTO identities ({columns}) VALUES ({placeholders})", values)
        
        # Verify insertion
        result = db.query_one("SELECT identity_id, name, nation_id, tribal_affiliation FROM identities WHERE identity_id = ?", 
                             (test_identity['identity_id'],))
        
        if result:
            logger.info("✅ Eden Mode identity creation test successful!")
            logger.info(f"   Identity: {result[0] if isinstance(result, tuple) else result['identity_id']}")
            logger.info(f"   Name: {result[1] if isinstance(result, tuple) else result['name']}")
            logger.info(f"   Nation ID: {result[2] if isinstance(result, tuple) else result['nation_id']}")
            logger.info(f"   Tribal Affiliation: {result[3] if isinstance(result, tuple) else result['tribal_affiliation']}")
            
            # Clean up test data
            db.execute("DELETE FROM identities WHERE identity_id = ?", (test_identity['identity_id'],))
            logger.info("✅ Test data cleaned up")
            return True
        else:
            logger.error("❌ Eden Mode identity creation test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing Eden Mode identity creation: {e}")
        return False

def main():
    """Main function"""
    logger.info("🔧 Creating Basic Nations for Eden Mode...")
    logger.info("=" * 60)
    
    try:
        # Step 1: Create nations directly
        if not create_nations_directly():
            logger.error("❌ Failed to create nations")
            return 1
        
        # Step 2: Test Eden Mode identity creation
        if not test_eden_mode_identity_creation():
            logger.error("❌ Eden Mode identity creation test failed")
            return 1
        
        logger.info("=" * 60)
        logger.info("🎉 Basic Nations Creation Complete!")
        logger.info("✅ Eden Mode should now work without foreign key constraint errors")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Script failed: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
