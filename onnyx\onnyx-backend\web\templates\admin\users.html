{% extends "base.html" %}

{% block title %}User Management - ONNYX Admin{% endblock %}

{% block head %}
<style>
    .admin-warning {
        background: linear-gradient(135deg, rgba(255, 165, 0, 0.1), rgba(255, 69, 0, 0.1));
        border: 1px solid rgba(255, 165, 0, 0.3);
    }
    
    .admin-card {
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid rgba(255, 0, 0, 0.3);
        backdrop-filter: blur(10px);
    }
    
    .user-row {
        transition: all 0.3s ease;
    }
    
    .user-row:hover {
        background: rgba(0, 255, 247, 0.05);
        border-color: var(--cyber-cyan);
    }
    
    .danger-button {
        background: linear-gradient(135deg, #dc2626, #991b1b);
        border: 1px solid #dc2626;
        color: white;
        transition: all 0.3s ease;
    }
    
    .danger-button:hover {
        background: linear-gradient(135deg, #991b1b, #7f1d1d);
        box-shadow: 0 0 20px rgba(220, 38, 38, 0.5);
    }
    
    .info-button {
        background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-blue));
        border: 1px solid var(--cyber-cyan);
        color: var(--onyx-black);
        transition: all 0.3s ease;
    }
    
    .info-button:hover {
        box-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
    }
    
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
    }
    
    .modal-content {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        margin: 5% auto;
        padding: 2rem;
        width: 90%;
        max-width: 600px;
        border-radius: 16px;
        position: relative;
    }
    
    .close {
        color: var(--cyber-cyan);
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 15px;
    }
    
    .close:hover {
        color: var(--cyber-purple);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Admin Header -->
        <div class="admin-warning p-4 rounded-xl mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl">👥</div>
                    <div>
                        <h1 class="text-2xl font-orbitron font-bold text-orange-400">User Management</h1>
                        <p class="text-orange-300">Manage all platform users and their permissions</p>
                    </div>
                </div>
                <a href="{{ url_for('admin.dashboard') }}" class="glass-button-secondary px-4 py-2 rounded-lg">
                    ← Back to Admin Dashboard
                </a>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="text-3xl font-orbitron font-bold text-cyber-cyan">{{ total_users }}</div>
                <div class="text-gray-400">Total Users</div>
            </div>
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="text-3xl font-orbitron font-bold text-green-400" id="active-users">-</div>
                <div class="text-gray-400">Active Users</div>
            </div>
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="text-3xl font-orbitron font-bold text-yellow-400" id="admin-users">-</div>
                <div class="text-gray-400">Administrators</div>
            </div>
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="text-3xl font-orbitron font-bold text-cyber-purple" id="sela-owners">-</div>
                <div class="text-gray-400">Sela Owners</div>
            </div>
        </div>

        <!-- User Table -->
        <div class="admin-card rounded-xl overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-orbitron font-bold text-white">All Users</h2>
                    <div class="flex items-center space-x-4">
                        <input type="text" id="search-users" placeholder="Search users..." 
                               class="bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white">
                        <button onclick="refreshUsers()" class="glass-button-primary px-4 py-2 rounded-lg">
                            🔄 Refresh
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-900">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body" class="divide-y divide-gray-700">
                        {% for user in users %}
                        <tr class="user-row border border-transparent">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-r from-cyber-cyan to-cyber-purple flex items-center justify-center text-onyx-black font-bold">
                                            {{ (user.name or user[1] or 'U')[0].upper() }}
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-white">{{ user.name or user[1] or 'Unknown' }}</div>
                                        <div class="text-sm text-gray-400">{{ user.email or user[2] or 'No email' }}</div>
                                        <div class="text-xs text-gray-500">ID: {{ (user.identity_id or user[0])[:16] }}...</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if (user.role_class or user[5]) == 'system_admin' %}bg-red-100 text-red-800
                                    {% elif (user.role_class or user[5]) == 'tribal_elder' %}bg-purple-100 text-purple-800
                                    {% elif (user.role_class or user[5]) == 'business' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ user.role_class or user[5] or 'citizen' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if (user.status or user[4]) == 'active' %}bg-green-100 text-green-800
                                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ user.status or user[4] or 'unknown' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                {{ moment(user.created_at or user[6]).format('MMM DD, YYYY') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="viewUserDetails('{{ user.identity_id or user[0] }}')" 
                                        class="info-button px-3 py-1 rounded text-xs font-medium">
                                    👁️ View
                                </button>
                                {% if (user.role_class or user[5]) != 'system_admin' %}
                                <button onclick="confirmDeleteUser('{{ user.identity_id or user[0] }}', '{{ user.name or user[1] or 'Unknown' }}')" 
                                        class="danger-button px-3 py-1 rounded text-xs font-medium">
                                    🗑️ Delete
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if total_users > per_page %}
            <div class="px-6 py-4 border-t border-gray-700 flex items-center justify-between">
                <div class="text-sm text-gray-400">
                    Showing {{ ((page - 1) * per_page) + 1 }} to {{ min(page * per_page, total_users) }} of {{ total_users }} users
                </div>
                <div class="flex space-x-2">
                    {% if page > 1 %}
                    <a href="{{ url_for('admin.user_management', page=page-1) }}" class="glass-button-secondary px-3 py-1 rounded text-sm">Previous</a>
                    {% endif %}
                    {% if page * per_page < total_users %}
                    <a href="{{ url_for('admin.user_management', page=page+1) }}" class="glass-button-secondary px-3 py-1 rounded text-sm">Next</a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div id="userDetailsModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">User Details</h2>
        <div id="userDetailsContent">
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-cyber-cyan mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading user details...</p>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h2 class="text-2xl font-orbitron font-bold text-red-400 mb-6">⚠️ Confirm User Deletion</h2>
        <div id="deleteConfirmContent">
            <p class="text-gray-300 mb-6">Are you sure you want to permanently delete this user?</p>
            <div class="bg-red-900 border border-red-600 rounded-lg p-4 mb-6">
                <p class="text-red-200 font-semibold">⚠️ WARNING: This action cannot be undone!</p>
                <p class="text-red-300 text-sm mt-2">All user data, tokens, deeds, and associated records will be permanently deleted.</p>
            </div>
            <div class="flex justify-end space-x-4">
                <button onclick="closeModal()" class="glass-button-secondary px-6 py-2 rounded-lg">Cancel</button>
                <button id="confirmDeleteBtn" onclick="executeUserDeletion()" class="danger-button px-6 py-2 rounded-lg">
                    🗑️ Delete Permanently
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentDeleteUserId = null;
let currentDeleteUserName = null;

// Load statistics on page load
document.addEventListener('DOMContentLoaded', function() {
    loadUserStats();
});

async function loadUserStats() {
    try {
        const response = await fetch('/admin/api/system/stats');
        const stats = await response.json();
        
        if (stats.identities) {
            document.getElementById('active-users').textContent = stats.identities.active || 0;
        }
        
        // Count admins from current page or fetch separately
        const adminCount = document.querySelectorAll('span:contains("system_admin")').length;
        document.getElementById('admin-users').textContent = adminCount;
        
        if (stats.selas) {
            document.getElementById('sela-owners').textContent = stats.selas.total || 0;
        }
    } catch (error) {
        console.error('Error loading user stats:', error);
    }
}

async function viewUserDetails(userId) {
    document.getElementById('userDetailsModal').style.display = 'block';
    
    try {
        const response = await fetch(`/admin/api/user/${userId}/details`);
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        const user = data.user;
        const balances = data.balances || [];
        const deeds = data.recent_deeds || [];
        const selas = data.selas || [];
        
        document.getElementById('userDetailsContent').innerHTML = `
            <div class="space-y-6">
                <!-- User Info -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm text-gray-400">Name</label>
                        <div class="text-white font-medium">${user.name || 'Unknown'}</div>
                    </div>
                    <div>
                        <label class="text-sm text-gray-400">Email</label>
                        <div class="text-white font-medium">${user.email || 'No email'}</div>
                    </div>
                    <div>
                        <label class="text-sm text-gray-400">Role</label>
                        <div class="text-white font-medium">${user.role_class || 'citizen'}</div>
                    </div>
                    <div>
                        <label class="text-sm text-gray-400">Status</label>
                        <div class="text-white font-medium">${user.status || 'unknown'}</div>
                    </div>
                    <div>
                        <label class="text-sm text-gray-400">Etzem Score</label>
                        <div class="text-cyber-cyan font-medium">${user.etzem_score || 0}</div>
                    </div>
                    <div>
                        <label class="text-sm text-gray-400">Nation</label>
                        <div class="text-white font-medium">${user.nation_name || 'Unknown'}</div>
                    </div>
                </div>
                
                <!-- Token Balances -->
                <div>
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Token Balances</h3>
                    ${balances.length > 0 ? balances.map(b => `
                        <div class="flex justify-between py-2 border-b border-gray-700">
                            <span class="text-gray-300">${b.token_id}</span>
                            <span class="text-white font-medium">${parseFloat(b.balance).toFixed(2)}</span>
                        </div>
                    `).join('') : '<p class="text-gray-400">No token balances</p>'}
                </div>
                
                <!-- Selas -->
                ${selas.length > 0 ? `
                <div>
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Selas Owned</h3>
                    ${selas.map(s => `
                        <div class="bg-gray-800 rounded-lg p-3 mb-2">
                            <div class="font-medium text-white">${s.name}</div>
                            <div class="text-sm text-gray-400">Tier: ${s.mining_tier} | Power: ${s.mining_power}</div>
                        </div>
                    `).join('')}
                </div>
                ` : ''}
                
                <!-- Recent Deeds -->
                <div>
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Recent Deeds</h3>
                    ${deeds.length > 0 ? deeds.slice(0, 5).map(d => `
                        <div class="flex justify-between py-2 border-b border-gray-700">
                            <div>
                                <div class="text-white font-medium">${d.deed_type}</div>
                                <div class="text-sm text-gray-400">${d.description || 'No description'}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-cyber-cyan font-medium">${parseFloat(d.deed_value || 0).toFixed(1)}</div>
                                <div class="text-xs text-gray-500">${new Date(d.timestamp * 1000).toLocaleDateString()}</div>
                            </div>
                        </div>
                    `).join('') : '<p class="text-gray-400">No recent deeds</p>'}
                </div>
            </div>
        `;
        
    } catch (error) {
        document.getElementById('userDetailsContent').innerHTML = `
            <div class="text-center py-8">
                <div class="text-red-400 text-xl mb-4">❌</div>
                <p class="text-red-300">Error loading user details: ${error.message}</p>
            </div>
        `;
    }
}

function confirmDeleteUser(userId, userName) {
    currentDeleteUserId = userId;
    currentDeleteUserName = userName;
    
    document.getElementById('deleteConfirmContent').innerHTML = `
        <p class="text-gray-300 mb-6">Are you sure you want to permanently delete <strong class="text-white">${userName}</strong>?</p>
        <div class="bg-red-900 border border-red-600 rounded-lg p-4 mb-6">
            <p class="text-red-200 font-semibold">⚠️ WARNING: This action cannot be undone!</p>
            <p class="text-red-300 text-sm mt-2">All user data, tokens, deeds, and associated records will be permanently deleted.</p>
        </div>
        <div class="flex justify-end space-x-4">
            <button onclick="closeModal()" class="glass-button-secondary px-6 py-2 rounded-lg">Cancel</button>
            <button id="confirmDeleteBtn" onclick="executeUserDeletion()" class="danger-button px-6 py-2 rounded-lg">
                🗑️ Delete ${userName} Permanently
            </button>
        </div>
    `;
    
    document.getElementById('deleteConfirmModal').style.display = 'block';
}

async function executeUserDeletion() {
    if (!currentDeleteUserId) return;
    
    const deleteBtn = document.getElementById('confirmDeleteBtn');
    deleteBtn.disabled = true;
    deleteBtn.innerHTML = '⏳ Deleting...';
    
    try {
        const response = await fetch(`/admin/api/user/${currentDeleteUserId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success message
            if (typeof Onnyx !== 'undefined' && Onnyx.utils && Onnyx.utils.showNotification) {
                Onnyx.utils.showNotification(result.message, 'success');
            } else {
                alert(result.message);
            }
            
            // Close modal and refresh page
            closeModal();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            throw new Error(result.error || 'Failed to delete user');
        }
        
    } catch (error) {
        console.error('Error deleting user:', error);
        
        if (typeof Onnyx !== 'undefined' && Onnyx.utils && Onnyx.utils.showNotification) {
            Onnyx.utils.showNotification(`Error: ${error.message}`, 'error');
        } else {
            alert(`Error deleting user: ${error.message}`);
        }
        
        deleteBtn.disabled = false;
        deleteBtn.innerHTML = `🗑️ Delete ${currentDeleteUserName} Permanently`;
    }
}

function closeModal() {
    document.getElementById('userDetailsModal').style.display = 'none';
    document.getElementById('deleteConfirmModal').style.display = 'none';
    currentDeleteUserId = null;
    currentDeleteUserName = null;
}

function refreshUsers() {
    window.location.reload();
}

// Close modal when clicking outside
window.onclick = function(event) {
    const userModal = document.getElementById('userDetailsModal');
    const deleteModal = document.getElementById('deleteConfirmModal');
    
    if (event.target === userModal) {
        closeModal();
    }
    if (event.target === deleteModal) {
        closeModal();
    }
}

// Search functionality
document.getElementById('search-users').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('.user-row');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
