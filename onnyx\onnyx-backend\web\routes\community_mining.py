"""
Community Mining Routes

Handles lite mining for community members (non-Sela operators).
"""

import os
import sys
import json
import time
import logging
import random
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.db.db import db
from shared.models.tokenomics import biblical_tokenomics
from web.auth_decorators import require_auth, get_current_user
from datetime import datetime, timezone

logger = logging.getLogger("onnyx.web.community_mining")

community_mining_bp = Blueprint('community_mining', __name__, url_prefix='/community-mining')

@community_mining_bp.route('/')
@require_auth
def dashboard():
    """Community mining dashboard for regular users."""
    try:
        current_user = get_current_user()
        
        # Check if user is eligible for community mining
        if not _is_eligible_for_community_mining(current_user):
            flash('You are not eligible for community mining. Please complete identity verification.', 'warning')
            return redirect(url_for('dashboard.overview'))
        
        # Get user's mining statistics
        mining_stats = _get_user_mining_stats(current_user['identity_id'])
        
        # Get biblical compliance status
        biblical_status = _get_biblical_mining_status()
        
        # Get community mining leaderboard
        leaderboard = _get_community_leaderboard()
        
        # Get user's current mining session
        current_session = _get_current_mining_session(current_user['identity_id'])
        
        return render_template('community_mining/dashboard.html',
                             current_user=current_user,
                             mining_stats=mining_stats,
                             biblical_status=biblical_status,
                             leaderboard=leaderboard,
                             current_session=current_session)
        
    except Exception as e:
        logger.error(f"Error loading community mining dashboard: {e}")
        flash('Error loading mining dashboard.', 'error')
        return redirect(url_for('dashboard.overview'))

@community_mining_bp.route('/api/start-mining', methods=['POST'])
@require_auth
def start_mining():
    """Start a community mining session."""
    try:
        current_user = get_current_user()
        
        # Check eligibility
        if not _is_eligible_for_community_mining(current_user):
            return jsonify({'success': False, 'error': 'Not eligible for community mining'}), 403
        
        # Check if mining is allowed (biblical compliance)
        if not biblical_tokenomics.is_mining_allowed():
            return jsonify({'success': False, 'error': 'Mining not allowed during Sabbath or feast periods'}), 400
        
        # Check if user already has an active session
        existing_session = _get_current_mining_session(current_user['identity_id'])
        if existing_session:
            return jsonify({'success': False, 'error': 'Mining session already active'}), 400
        
        # Create new mining session
        session_id = _create_mining_session(current_user['identity_id'])
        
        if session_id:
            return jsonify({
                'success': True,
                'message': 'Community mining session started',
                'session_id': session_id
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to start mining session'}), 500
        
    except Exception as e:
        logger.error(f"Error starting community mining: {e}")
        return jsonify({'error': 'Failed to start mining'}), 500

@community_mining_bp.route('/api/mine-block', methods=['POST'])
@require_auth
def mine_block():
    """Attempt to mine a block (lite mining)."""
    try:
        current_user = get_current_user()
        
        # Check if user has active mining session
        session = _get_current_mining_session(current_user['identity_id'])
        if not session:
            return jsonify({'success': False, 'error': 'No active mining session'}), 400
        
        # Check if mining is still allowed
        if not biblical_tokenomics.is_mining_allowed():
            return jsonify({'success': False, 'error': 'Mining not allowed during Sabbath or feast periods'}), 400
        
        # Perform lite mining attempt
        mining_result = _perform_lite_mining(current_user['identity_id'], session)
        
        return jsonify(mining_result)
        
    except Exception as e:
        logger.error(f"Error in community mining: {e}")
        return jsonify({'error': 'Mining attempt failed'}), 500

@community_mining_bp.route('/api/stop-mining', methods=['POST'])
@require_auth
def stop_mining():
    """Stop the current mining session."""
    try:
        current_user = get_current_user()
        
        # End mining session
        result = _end_mining_session(current_user['identity_id'])
        
        if result:
            return jsonify({
                'success': True,
                'message': 'Mining session ended',
                'session_stats': result
            })
        else:
            return jsonify({'success': False, 'error': 'No active mining session'}), 400
        
    except Exception as e:
        logger.error(f"Error stopping community mining: {e}")
        return jsonify({'error': 'Failed to stop mining'}), 500

@community_mining_bp.route('/api/stats')
@require_auth
def get_mining_stats():
    """Get user's mining statistics."""
    try:
        current_user = get_current_user()
        
        stats = _get_user_mining_stats(current_user['identity_id'])
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting mining stats: {e}")
        return jsonify({'error': 'Failed to get stats'}), 500

# Helper Functions

def _is_eligible_for_community_mining(user):
    """Check if user is eligible for community mining."""
    try:
        # Must be verified citizen
        if user['role'] not in ['citizen', 'community_member']:
            return False
        
        # Must not be a Sela operator (they use full mining)
        selas = db.query("SELECT sela_id FROM selas WHERE identity_id = ? AND status = 'active'", (user['identity_id'],))
        if selas:
            return False
        
        # Must have minimum verification level
        if user.get('verification_level', 0) < 1:
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking mining eligibility: {e}")
        return False

def _get_user_mining_stats(identity_id):
    """Get user's mining statistics."""
    try:
        # Get total rewards earned
        total_rewards = db.query_one("""
            SELECT COALESCE(SUM(reward_amount), 0) as total
            FROM community_mining_rewards
            WHERE identity_id = ?
        """, (identity_id,))
        
        # Get mining sessions count
        sessions_count = db.query_one("""
            SELECT COUNT(*) as count
            FROM community_mining_sessions
            WHERE identity_id = ?
        """, (identity_id,))
        
        # Get successful mining attempts
        successful_attempts = db.query_one("""
            SELECT COUNT(*) as count
            FROM community_mining_rewards
            WHERE identity_id = ?
        """, (identity_id,))
        
        # Get current balance
        balance = db.query_one("""
            SELECT COALESCE(balance, 0) as balance
            FROM token_balances
            WHERE identity_id = ? AND token_id = 'ONX'
        """, (identity_id,))
        
        return {
            'total_rewards': total_rewards['total'] if total_rewards else 0,
            'sessions_count': sessions_count['count'] if sessions_count else 0,
            'successful_attempts': successful_attempts['count'] if successful_attempts else 0,
            'current_balance': balance['balance'] if balance else 0,
            'mining_power': 1.0,  # Community members have base mining power
            'efficiency': _calculate_mining_efficiency(identity_id)
        }
        
    except Exception as e:
        logger.error(f"Error getting user mining stats: {e}")
        return {}

def _get_biblical_mining_status():
    """Get current biblical mining compliance status."""
    try:
        return {
            'mining_allowed': biblical_tokenomics.is_mining_allowed(),
            'is_sabbath': biblical_tokenomics.is_sabbath_period(),
            'is_new_moon': biblical_tokenomics.is_new_moon_period(),
            'is_feast_day': biblical_tokenomics.is_feast_day(),
            'next_mining_window': 'Sunday 8:00 PM',  # Calculate properly
            'compliance_message': _get_compliance_message()
        }
        
    except Exception as e:
        logger.error(f"Error getting biblical status: {e}")
        return {'mining_allowed': False, 'compliance_message': 'Status unavailable'}

def _get_compliance_message():
    """Get appropriate compliance message."""
    if biblical_tokenomics.is_sabbath_period():
        return "Mining paused for Sabbath observance (Friday 6 PM - Saturday 8 PM)"
    elif biblical_tokenomics.is_new_moon_period():
        return "Mining paused for New Moon observance"
    elif biblical_tokenomics.is_feast_day():
        return "Mining paused for biblical feast day"
    else:
        return "Mining allowed - covenant compliance active"

def _get_community_leaderboard():
    """Get community mining leaderboard."""
    try:
        leaderboard = db.query("""
            SELECT i.name, i.identity_id, COALESCE(SUM(cmr.reward_amount), 0) as total_rewards,
                   COUNT(cmr.reward_id) as successful_attempts
            FROM identities i
            LEFT JOIN community_mining_rewards cmr ON i.identity_id = cmr.identity_id
            WHERE i.role IN ('citizen', 'community_member')
            GROUP BY i.identity_id, i.name
            ORDER BY total_rewards DESC
            LIMIT 10
        """)
        
        return leaderboard or []
        
    except Exception as e:
        logger.error(f"Error getting leaderboard: {e}")
        return []

def _get_current_mining_session(identity_id):
    """Get user's current active mining session."""
    try:
        session = db.query_one("""
            SELECT * FROM community_mining_sessions
            WHERE identity_id = ? AND status = 'active'
            ORDER BY created_at DESC
            LIMIT 1
        """, (identity_id,))
        
        return session
        
    except Exception as e:
        logger.error(f"Error getting mining session: {e}")
        return None

def _create_mining_session(identity_id):
    """Create a new mining session."""
    try:
        current_time = int(time.time())
        session_id = f"cms_{identity_id[:8]}_{current_time}"
        
        db.execute("""
            INSERT INTO community_mining_sessions 
            (session_id, identity_id, status, created_at, last_activity)
            VALUES (?, ?, 'active', ?, ?)
        """, (session_id, identity_id, current_time, current_time))
        
        return session_id
        
    except Exception as e:
        logger.error(f"Error creating mining session: {e}")
        return None

def _perform_lite_mining(identity_id, session):
    """Perform a lite mining attempt."""
    try:
        current_time = int(time.time())
        
        # Update session activity
        db.execute("""
            UPDATE community_mining_sessions 
            SET last_activity = ?, attempts = attempts + 1
            WHERE session_id = ?
        """, (current_time, session['session_id']))
        
        # Calculate success probability (much lower than full mining)
        base_probability = 0.05  # 5% base chance
        user_bonus = _get_user_mining_bonus(identity_id)
        success_probability = min(base_probability + user_bonus, 0.15)  # Max 15%
        
        # Determine if mining is successful
        if random.random() < success_probability:
            # Successful mining - award small reward
            reward_amount = _calculate_community_reward(identity_id)
            
            # Record reward
            reward_id = f"cmr_{identity_id[:8]}_{current_time}"
            db.execute("""
                INSERT INTO community_mining_rewards
                (reward_id, identity_id, session_id, reward_amount, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (reward_id, identity_id, session['session_id'], reward_amount, current_time))
            
            # Update user balance
            db.execute("""
                INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, last_updated)
                VALUES (?, 'ONX', COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = 'ONX'), 0) + ?, ?)
            """, (identity_id, identity_id, reward_amount, current_time))
            
            # Record deed
            biblical_tokenomics.record_deed(identity_id, "COMMUNITY_MINING", reward_amount, "Community lite mining reward")
            
            return {
                'success': True,
                'message': 'Block mined successfully!',
                'reward': reward_amount,
                'total_attempts': session.get('attempts', 0) + 1
            }
        else:
            # Unsuccessful attempt
            return {
                'success': False,
                'message': 'Mining attempt unsuccessful. Keep trying!',
                'reward': 0,
                'total_attempts': session.get('attempts', 0) + 1
            }
        
    except Exception as e:
        logger.error(f"Error in lite mining: {e}")
        return {'success': False, 'error': 'Mining attempt failed'}

def _get_user_mining_bonus(identity_id):
    """Calculate user's mining bonus based on various factors."""
    try:
        # Get user info
        user = db.query_one("SELECT etzem_score, verification_level FROM identities WHERE identity_id = ?", (identity_id,))
        if not user:
            return 0
        
        bonus = 0
        
        # Etzem score bonus (up to 3% bonus)
        etzem_bonus = min((user['etzem_score'] - 100) / 1000, 0.03)
        bonus += max(etzem_bonus, 0)
        
        # Verification level bonus
        verification_bonus = user['verification_level'] * 0.01
        bonus += verification_bonus
        
        return bonus
        
    except Exception as e:
        logger.error(f"Error calculating mining bonus: {e}")
        return 0

def _calculate_community_reward(identity_id):
    """Calculate reward amount for community mining."""
    try:
        # Base reward for community mining (much smaller than Sela mining)
        base_reward = 0.5  # 0.5 ONX base
        
        # Apply user bonus
        user_bonus = _get_user_mining_bonus(identity_id)
        final_reward = base_reward * (1 + user_bonus)
        
        # Apply biblical compliance bonus if applicable
        if biblical_tokenomics.is_righteous_period():
            final_reward *= 1.05  # 5% bonus for righteous periods
        
        return round(final_reward, 6)
        
    except Exception as e:
        logger.error(f"Error calculating reward: {e}")
        return 0.5

def _end_mining_session(identity_id):
    """End the current mining session."""
    try:
        current_time = int(time.time())
        
        # Get session stats
        session = db.query_one("""
            SELECT cms.*, COUNT(cmr.reward_id) as successful_attempts,
                   COALESCE(SUM(cmr.reward_amount), 0) as total_rewards
            FROM community_mining_sessions cms
            LEFT JOIN community_mining_rewards cmr ON cms.session_id = cmr.session_id
            WHERE cms.identity_id = ? AND cms.status = 'active'
            GROUP BY cms.session_id
        """, (identity_id,))
        
        if not session:
            return None
        
        # Update session status
        db.execute("""
            UPDATE community_mining_sessions 
            SET status = 'completed', ended_at = ?
            WHERE session_id = ?
        """, (current_time, session['session_id']))
        
        return {
            'session_duration': current_time - session['created_at'],
            'total_attempts': session.get('attempts', 0),
            'successful_attempts': session['successful_attempts'],
            'total_rewards': session['total_rewards']
        }
        
    except Exception as e:
        logger.error(f"Error ending mining session: {e}")
        return None

def _calculate_mining_efficiency(identity_id):
    """Calculate user's mining efficiency percentage."""
    try:
        stats = db.query_one("""
            SELECT 
                COALESCE(SUM(cms.attempts), 0) as total_attempts,
                COUNT(cmr.reward_id) as successful_attempts
            FROM community_mining_sessions cms
            LEFT JOIN community_mining_rewards cmr ON cms.session_id = cmr.session_id
            WHERE cms.identity_id = ?
        """, (identity_id,))
        
        if not stats or stats['total_attempts'] == 0:
            return 0
        
        efficiency = (stats['successful_attempts'] / stats['total_attempts']) * 100
        return round(efficiency, 2)
        
    except Exception as e:
        logger.error(f"Error calculating efficiency: {e}")
        return 0
