#!/usr/bin/env python3
"""
Manual ONNYX Flow Test
Tests the endpoints and basic functionality without browser automation
"""

import requests
import json
import time

def test_onnyx_endpoints():
    """Test ONNYX endpoints and basic functionality"""
    base_url = "http://localhost:5000"
    
    print("🔍 Testing ONNYX Endpoints and Flow")
    print("=" * 50)
    
    # Test 1: Homepage
    print("\n1. Testing Homepage")
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Homepage: {response.status_code}")
        if response.status_code == 200:
            if "ONNYX" in response.text:
                print("✅ Homepage contains ONNYX branding")
            else:
                print("❌ Homepage missing ONNYX branding")
        else:
            print(f"❌ Homepage failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Homepage error: {e}")
    
    # Test 2: Login page
    print("\n2. Testing Login Page")
    try:
        response = requests.get(f"{base_url}/auth/login")
        print(f"✅ Login page: {response.status_code}")
        if response.status_code == 200:
            if "Eden Mode" in response.text:
                print("✅ Login page contains Eden Mode link")
            else:
                print("❌ Login page missing Eden Mode link")
        else:
            print(f"❌ Login page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Login page error: {e}")
    
    # Test 3: Eden Mode
    print("\n3. Testing Eden Mode")
    try:
        response = requests.get(f"{base_url}/auth/eden-mode")
        print(f"✅ Eden Mode base: {response.status_code}")
        if response.status_code == 200:
            if "Choose Your Covenant Path" in response.text:
                print("✅ Eden Mode contains path selection")
            else:
                print("❌ Eden Mode missing path selection")
        else:
            print(f"❌ Eden Mode failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Eden Mode error: {e}")
    
    # Test 4: Eden Mode Step 1
    print("\n4. Testing Eden Mode Step 1")
    try:
        response = requests.get(f"{base_url}/auth/eden-mode/step1")
        print(f"✅ Eden Mode Step 1: {response.status_code}")
        if response.status_code == 200:
            if "Begin Israelite Journey" in response.text:
                print("✅ Step 1 contains Israelite button")
            else:
                print("❌ Step 1 missing Israelite button")
            
            if "Begin Witness Journey" in response.text:
                print("✅ Step 1 contains Witness button")
            else:
                print("❌ Step 1 missing Witness button")
        else:
            print(f"❌ Eden Mode Step 1 failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Eden Mode Step 1 error: {e}")
    
    # Test 5: Israelite Step 2
    print("\n5. Testing Israelite Flow")
    try:
        response = requests.get(f"{base_url}/auth/eden-mode/israelite-step2")
        print(f"✅ Israelite Step 2: {response.status_code}")
        if response.status_code == 200:
            if "Twelve Tribes" in response.text or "tribal" in response.text.lower():
                print("✅ Israelite Step 2 contains tribal selection")
            else:
                print("❌ Israelite Step 2 missing tribal selection")
        else:
            print(f"❌ Israelite Step 2 failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Israelite Step 2 error: {e}")
    
    # Test 6: Israelite Step 3
    print("\n6. Testing Israelite Heritage Verification")
    try:
        response = requests.get(f"{base_url}/auth/eden-mode/israelite-step3")
        print(f"✅ Israelite Step 3: {response.status_code}")
        if response.status_code == 200:
            if "Developer Bypass" in response.text:
                print("✅ Israelite Step 3 contains developer bypass")
            else:
                print("❌ Israelite Step 3 missing developer bypass")
            
            if "Heritage Verification" in response.text:
                print("✅ Israelite Step 3 contains heritage verification")
            else:
                print("❌ Israelite Step 3 missing heritage verification")
        else:
            print(f"❌ Israelite Step 3 failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Israelite Step 3 error: {e}")
    
    # Test 7: Witness Nation Flow
    print("\n7. Testing Witness Nation Flow")
    try:
        response = requests.get(f"{base_url}/auth/eden-mode/witness-step2")
        print(f"✅ Witness Step 2: {response.status_code}")
        if response.status_code == 200:
            if "nation" in response.text.lower():
                print("✅ Witness Step 2 contains nation selection")
            else:
                print("❌ Witness Step 2 missing nation selection")
        else:
            print(f"❌ Witness Step 2 failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Witness Step 2 error: {e}")
    
    # Test 8: Database connectivity
    print("\n8. Testing Database Connectivity")
    try:
        # Test a simple endpoint that might use the database
        response = requests.get(f"{base_url}/auth/register")
        print(f"✅ Database connectivity test: {response.status_code}")
    except Exception as e:
        print(f"❌ Database connectivity error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Endpoint testing complete")
    print("\n📋 Manual Testing Instructions:")
    print("1. Open browser and go to http://localhost:5000/auth/login")
    print("2. Click 'Eden Mode - Reclaim Your Legacy'")
    print("3. Click 'Begin Israelite Journey' (should navigate to step 2)")
    print("4. Select any tribe and click 'Proceed'")
    print("5. Click 'Developer Bypass Gate Keeper'")
    print("6. Enter password: 'Israel United In Christ'")
    print("7. Click 'Bypass Gate Keeper Review'")
    print("8. Click 'Continue to Inscription' in the modal")
    print("9. Fill out the form and submit")
    print("\nRepeat for Witness Nation path")

def test_form_submission():
    """Test form submission with mock data"""
    print("\n🔹 Testing Form Submission")
    
    # This would require session handling, cookies, etc.
    # For now, just verify the endpoints exist
    endpoints_to_test = [
        "/auth/eden-mode/israelite-step5",
        "/auth/eden-mode/witness-step4"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"http://localhost:5000{endpoint}")
            print(f"✅ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

if __name__ == "__main__":
    print("ONNYX Platform Manual Testing Suite")
    print("This tests endpoints and provides manual testing instructions")
    print("=" * 70)
    
    test_onnyx_endpoints()
    test_form_submission()
    
    print("\n🎯 Next Steps:")
    print("1. Fix any failed endpoints shown above")
    print("2. Follow the manual testing instructions")
    print("3. Test the complete user journey end-to-end")
    print("4. Verify both accounts are created successfully")
