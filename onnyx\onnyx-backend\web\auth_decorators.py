"""
ONNYX Security Authorization Decorators
Comprehensive Role-Based Access Control (RBAC) system
"""

import json
import logging
import time
from functools import wraps
from flask import session, flash, redirect, url_for, jsonify, request
from shared.db.db import db
from web.secure_auth import SecureAuth

logger = logging.getLogger(__name__)

class UserRole:
    """User role constants"""
    SYSTEM_ADMIN = "system_admin"
    GATE_KEEPER = "gate_keeper"
    TRIBAL_ELDER = "tribal_elder"
    ISRAELITE = "israelite"
    WITNESS_NATION = "witness_nation"
    OBSERVER = "observer"
    CITIZEN = "citizen"

class Permission:
    """Permission constants"""
    MANUAL_ONBOARDING = "manual_onboarding"
    GATE_KEEPER_VOTING = "gate_keeper_voting"
    TRIBAL_GOVERNANCE = "tribal_governance"
    ADMIN_DASHBOARD = "admin_dashboard"
    USER_MANAGEMENT = "user_management"
    SYSTEM_CONFIG = "system_config"
    BLOCKCHAIN_ADMIN = "blockchain_admin"
    MINING_CONTROL = "mining_control"
    VALIDATOR_MANAGEMENT = "validator_management"

def get_current_user():
    """Get current user with role information using secure session validation"""
    try:
        # Validate secure session
        session_data = SecureAuth.validate_session()
        if not session_data:
            return None

        identity_id = session_data['identity_id']

        # Get fresh identity data from database
        identity = db.query_one("""
            SELECT identity_id, name, email, metadata, verification_level,
                   nation_code, tribe_name, created_at
            FROM identities
            WHERE identity_id = ?
        """, (identity_id,))

        if not identity:
            # Identity was deleted - destroy session
            SecureAuth.destroy_session()
            return None

        # Parse metadata to get role information
        metadata = {}
        if identity.get('metadata'):
            try:
                metadata = json.loads(identity['metadata'])
            except (json.JSONDecodeError, TypeError):
                logger.warning(f"Invalid metadata for user {identity['identity_id']}")
                metadata = {}

        # Determine user role based on metadata and other factors
        user_role = determine_user_role(identity, metadata)

        # Verify role integrity (prevent privilege escalation)
        if not verify_role_integrity(identity, metadata, user_role):
            logger.error(f"SECURITY ALERT: Role integrity check failed for user {identity_id}")
            SecureAuth.log_auth_event('ROLE_INTEGRITY_VIOLATION', identity_id, {
                'detected_role': user_role,
                'metadata': metadata
            })
            # Downgrade to observer role for safety
            user_role = UserRole.OBSERVER

        return {
            'identity_id': identity['identity_id'],
            'name': identity['name'],
            'email': identity['email'],
            'role': user_role,
            'metadata': metadata,
            'verification_level': identity.get('verification_level', 0),
            'nation_code': identity.get('nation_code'),
            'tribe_name': identity.get('tribe_name'),
            'is_admin': metadata.get('admin_privileges', False) and user_role == UserRole.SYSTEM_ADMIN,
            'is_genesis': metadata.get('genesis_identity', False) and user_role == UserRole.SYSTEM_ADMIN,
            'is_gate_keeper': user_role == UserRole.GATE_KEEPER,
            'is_tribal_elder': user_role == UserRole.TRIBAL_ELDER,
            'session_valid': True
        }

    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        return None

def determine_user_role(identity, metadata):
    """Determine user role based on identity data and metadata"""
    
    # System Administrator (Genesis Identity)
    if metadata.get('admin_privileges') or metadata.get('genesis_identity'):
        return UserRole.SYSTEM_ADMIN
    
    # Gate Keeper (special role for identity verification)
    if metadata.get('role') == 'gate_keeper' or 'gate_keeper' in metadata.get('badges', []):
        return UserRole.GATE_KEEPER
    
    # Tribal Elder
    if (metadata.get('role') == 'tribal_elder' or 
        metadata.get('tribal_role') == 'Elder' or
        'Tribal_Elder' in metadata.get('badges', [])):
        return UserRole.TRIBAL_ELDER
    
    # Observer (default for unverified or low-tier users) - Check this first
    verification_level = identity.get('verification_level', 0)

    # Handle both numeric and string verification levels
    if isinstance(verification_level, str):
        # String verification levels that indicate unverified status
        unverified_statuses = [
            'Pending_Gate_Keeper_Verification',
            'Pending_Verification',
            'Unverified',
            'Rejected',
            'Suspended'
        ]
        if verification_level in unverified_statuses:
            return UserRole.OBSERVER
        # If it's a string but not in unverified list, treat as verified
        verification_level = 1

    # Numeric verification level check
    if isinstance(verification_level, (int, float)) and verification_level < 1:
        return UserRole.OBSERVER

    # Israelite (covenant member) - Only if verified
    nation_code = identity.get('nation_code', '')
    israelite_codes = ['JU', 'BE', 'LE', 'SI', 'EP', 'MA', 'IS', 'ZE', 'NA', 'GA', 'AS', 'RE', 'ISR']  # Added ISR for Israel
    if nation_code in israelite_codes:
        return UserRole.ISRAELITE

    # Witness Nation (non-Israelite covenant nations) - Only if verified
    if nation_code and nation_code not in israelite_codes:
        return UserRole.WITNESS_NATION

    # Default to citizen
    return UserRole.CITIZEN

def verify_role_integrity(identity, metadata, assigned_role):
    """Verify that the assigned role is legitimate and hasn't been tampered with"""
    try:
        # System Admin verification - must have genesis_identity or admin_privileges
        if assigned_role == UserRole.SYSTEM_ADMIN:
            if not (metadata.get('genesis_identity') or metadata.get('admin_privileges')):
                logger.warning(f"Invalid admin role assignment for {identity['identity_id']}")
                return False

        # Gate Keeper verification - check against known Gate Keeper list
        if assigned_role == UserRole.GATE_KEEPER:
            # Additional verification could be added here
            gate_keeper_record = db.query_one("""
                SELECT * FROM gate_keepers WHERE identity_id = ?
            """, (identity['identity_id'],))
            if not gate_keeper_record:
                logger.warning(f"Invalid Gate Keeper role assignment for {identity['identity_id']}")
                return False

        # Tribal Elder verification
        if assigned_role == UserRole.TRIBAL_ELDER:
            # Verify tribal elder status
            if not (metadata.get('role') == 'tribal_elder' or
                   metadata.get('tribal_role') == 'Elder' or
                   'Tribal_Elder' in metadata.get('badges', [])):
                logger.warning(f"Invalid Tribal Elder role assignment for {identity['identity_id']}")
                return False

        return True

    except Exception as e:
        logger.error(f"Error verifying role integrity: {e}")
        return False  # Fail secure

def get_role_permissions(role):
    """Get permissions for a specific role"""
    permissions = {
        UserRole.SYSTEM_ADMIN: [
            Permission.MANUAL_ONBOARDING,
            Permission.GATE_KEEPER_VOTING,
            Permission.TRIBAL_GOVERNANCE,
            Permission.ADMIN_DASHBOARD,
            Permission.USER_MANAGEMENT,
            Permission.SYSTEM_CONFIG,
            Permission.BLOCKCHAIN_ADMIN,
            Permission.MINING_CONTROL,
            Permission.VALIDATOR_MANAGEMENT
        ],
        UserRole.GATE_KEEPER: [
            Permission.GATE_KEEPER_VOTING,
            Permission.TRIBAL_GOVERNANCE
        ],
        UserRole.TRIBAL_ELDER: [
            Permission.TRIBAL_GOVERNANCE,
            Permission.MINING_CONTROL,
            Permission.VALIDATOR_MANAGEMENT
        ],
        UserRole.ISRAELITE: [
            Permission.MINING_CONTROL  # Can manage their own mining operations
        ],
        UserRole.WITNESS_NATION: [],
        UserRole.OBSERVER: [],
        UserRole.CITIZEN: []
    }
    
    return permissions.get(role, [])

def has_permission(user, permission):
    """Check if user has specific permission"""
    if not user:
        return False
    
    user_permissions = get_role_permissions(user['role'])
    return permission in user_permissions

def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'identity_id' not in session:
            if request.is_json:
                return jsonify({'error': 'Authentication required'}), 401
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = get_current_user()
            if not user:
                if request.is_json:
                    return jsonify({'error': 'Authentication required'}), 401
                flash('Please log in to access this page.', 'warning')
                return redirect(url_for('auth.login'))
            
            if not has_permission(user, permission):
                logger.warning(f"Access denied: User {user['identity_id']} ({user['role']}) attempted to access {permission}")
                if request.is_json:
                    return jsonify({'error': 'Insufficient permissions'}), 403
                flash('You do not have permission to access this page.', 'error')
                return redirect(url_for('dashboard.overview'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(required_role):
    """Decorator to require specific role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = get_current_user()
            if not user:
                if request.is_json:
                    return jsonify({'error': 'Authentication required'}), 401
                flash('Please log in to access this page.', 'warning')
                return redirect(url_for('auth.login'))
            
            if user['role'] != required_role:
                logger.warning(f"Access denied: User {user['identity_id']} ({user['role']}) attempted to access {required_role}-only resource")
                if request.is_json:
                    return jsonify({'error': f'Role {required_role} required'}), 403
                flash(f'This page requires {required_role} privileges.', 'error')
                return redirect(url_for('dashboard.overview'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_admin(f):
    """Decorator to require system administrator privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            if request.is_json:
                return jsonify({'error': 'Authentication required'}), 401
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        if not user['is_admin'] and user['role'] != UserRole.SYSTEM_ADMIN:
            logger.warning(f"SECURITY ALERT: User {user['identity_id']} ({user['role']}) attempted to access admin-only resource")
            if request.is_json:
                return jsonify({'error': 'System administrator privileges required'}), 403
            flash('This page requires system administrator privileges.', 'error')
            return redirect(url_for('dashboard.overview'))

        return f(*args, **kwargs)
    return decorated_function

def require_mining_access(f):
    """Decorator to require mining/validator access privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            if request.is_json:
                return jsonify({'error': 'Authentication required'}), 401
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        # Check if user has mining control permission
        if not has_permission(user, Permission.MINING_CONTROL):
            logger.warning(f"MINING ACCESS DENIED: User {user['identity_id']} ({user['role']}) attempted to access mining controls")
            log_security_event('MINING_ACCESS_DENIED', user['identity_id'], {
                'user_role': user['role'],
                'attempted_resource': request.endpoint,
                'ip_address': request.remote_addr
            })

            if request.is_json:
                return jsonify({
                    'error': 'Mining access restricted',
                    'message': 'Mining controls are only available to Israelites with Selas, Tribal Elders, and System Administrators.'
                }), 403

            flash('Mining controls are only available to Israelites with Selas, Tribal Elders, and System Administrators.', 'error')
            return redirect(url_for('dashboard.overview'))

        # Additional check: Israelites can only manage their own Selas
        if user['role'] == UserRole.ISRAELITE:
            # Check if they have any Selas registered
            user_selas = db.query("SELECT sela_id FROM selas WHERE identity_id = ?", (user['identity_id'],))
            if not user_selas:
                logger.warning(f"MINING ACCESS DENIED: Israelite {user['identity_id']} has no registered Selas")
                if request.is_json:
                    return jsonify({
                        'error': 'No Selas registered',
                        'message': 'You must register a Sela business to access mining controls.'
                    }), 403

                flash('You must register a Sela business to access mining controls.', 'error')
                return redirect(url_for('sela.register'))

        return f(*args, **kwargs)
    return decorated_function

def log_security_event(event_type, user_id, details):
    """Log security events for audit trail"""
    try:
        db.execute("""
            INSERT INTO security_audit_log (event_type, user_id, details, timestamp, ip_address)
            VALUES (?, ?, ?, ?, ?)
        """, (event_type, user_id, json.dumps(details), int(time.time()), request.remote_addr))
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")
