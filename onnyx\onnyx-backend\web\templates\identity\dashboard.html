{% extends "base.html" %}

{% block title %}Covenant Identity Dashboard - {{ identity.name }} | ONNYX Platform{% endblock %}

{% block description %}Comprehensive covenant identity dashboard with biblical protection protocols, verification tiers, and Etzem reputation tracking.{% endblock %}

{% block head %}
<style>
    .covenant-shield {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
        border: 2px solid rgba(0, 212, 255, 0.3);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }

    .covenant-shield::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .tier-badge {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 12px;
        padding: 8px 16px;
        position: relative;
    }

    .tier-badge.tier-0 { border-color: rgba(156, 163, 175, 0.5); }
    .tier-badge.tier-1 { border-color: rgba(34, 197, 94, 0.5); }
    .tier-badge.tier-2 { border-color: rgba(59, 130, 246, 0.5); }
    .tier-badge.tier-3 { border-color: rgba(139, 92, 246, 0.5); }

    .etzem-meter {
        background: linear-gradient(90deg, #ef4444 0%, #f59e0b 25%, #10b981 50%, #3b82f6 75%, #8b5cf6 100%);
        height: 12px;
        border-radius: 6px;
        overflow: hidden;
        position: relative;
    }

    .etzem-progress {
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 6px;
        transition: width 1s ease-out;
        position: relative;
    }

    .etzem-progress::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }

    .zeman-timeline {
        position: relative;
        padding-left: 24px;
    }

    .zeman-timeline::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #00d4ff, #8b5cf6);
    }

    .zeman-item {
        position: relative;
        margin-bottom: 16px;
    }

    .zeman-item::before {
        content: '';
        position: absolute;
        left: -20px;
        top: 8px;
        width: 8px;
        height: 8px;
        background: #00d4ff;
        border-radius: 50%;
        border: 2px solid #1f2937;
    }

    .protection-status {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .protection-basic { background: rgba(156, 163, 175, 0.2); color: #9ca3af; }
    .protection-shielded { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
    .protection-covenant { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
    .protection-elder { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; }

    .nation-emblem {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #00d4ff, #8b5cf6);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .contribution-card {
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        transition: all 0.3s ease;
    }

    .contribution-card:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(0, 212, 255, 0.3);
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Top Section - Covenant Identity Summary -->
        <div class="covenant-shield p-8 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <!-- Left: Identity Core -->
                <div class="flex items-center gap-6">
                    <!-- Nation Emblem -->
                    <div class="nation-emblem">
                        {{ nation_info.flag_symbol or '🛡️' }}
                    </div>

                    <!-- Identity Details -->
                    <div>
                        <div class="flex items-center gap-3 mb-2">
                            <h1 class="text-3xl font-orbitron font-bold text-primary">{{ identity.name }}</h1>
                            <div class="protection-status protection-{{ identity.protection_tier.lower().replace('_', '-') }}">
                                🛡️ {{ identity.protection_tier.replace('_', ' ') }}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-secondary">
                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Nation:</span>
                                <span class="font-medium text-cyber-cyan">{{ identity.nation_name or 'Unknown' }}</span>
                                <span class="text-xs text-tertiary">({{ nation_info.tribe_name or 'Unknown Tribe' }})</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Role Class:</span>
                                <span class="font-medium text-cyber-purple">{{ identity.role_class }}</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Covenant Status:</span>
                                <span class="font-medium {{ 'text-cyber-green' if identity.covenant_accepted else 'text-cyber-red' }}">
                                    {{ '✅ Accepted' if identity.covenant_accepted else '❌ Pending' }}
                                </span>
                            </div>

                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Vault Status:</span>
                                <span class="font-medium {{ 'text-cyber-green' if identity.vault_status == 'Active' else 'text-cyber-yellow' if identity.vault_status == 'Frozen' else 'text-cyber-red' }}">
                                    {{ identity.vault_status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right: Verification Tier -->
                <div class="text-center lg:text-right">
                    <div class="tier-badge tier-{{ identity.verification_level }} mb-4">
                        <div class="text-lg font-orbitron font-bold text-primary">Tier {{ identity.verification_level }}</div>
                        <div class="text-xs text-secondary">{{ tier_names[identity.verification_level] }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ identity.etzem_score }}</div>
                            <div class="text-xs text-tertiary">Etzem Score</div>
                        </div>
                        <div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple">{{ identity.zeman_count }}</div>
                            <div class="text-xs text-tertiary">Zeman Cycles</div>
                        </div>
                        <div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-blue">{{ contributions_count }}</div>
                            <div class="text-xs text-tertiary">Contributions</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Middle Section - Etzem & Zeman Tracking -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Etzem Score Tracking -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">Etzem Reputation</h2>

                <div class="text-center mb-6">
                    <div class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">{{ identity.etzem_score }}</div>
                    <div class="text-sm text-secondary mb-4">Righteous Deed Score</div>

                    <!-- Etzem Meter -->
                    <div class="etzem-meter mb-4">
                        <div class="etzem-progress" style="width: {{ (identity.etzem_score / 100) * 100 }}%"></div>
                    </div>

                    <div class="text-xs text-tertiary">{{ identity.etzem_score }}/100 - {{ etzem_level }}</div>
                </div>

                <!-- Recent Etzem Changes -->
                <div class="space-y-3">
                    <h3 class="font-semibold text-primary mb-3">Recent Changes</h3>
                    {% for change in etzem_history[:5] %}
                    <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                        <div>
                            <div class="font-medium text-primary text-sm">{{ change.change_reason }}</div>
                            <div class="text-xs text-tertiary">{{ change.timestamp|timestamp_to_date }}</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold {{ 'text-cyber-green' if change.change_amount > 0 else 'text-cyber-red' }}">
                                {{ '+' if change.change_amount > 0 else '' }}{{ change.change_amount }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Zeman Timeline -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">Zeman Seasons</h2>

                <div class="zeman-timeline">
                    {% for season in zeman_activities %}
                    <div class="zeman-item">
                        <div class="bg-glass-bg rounded-lg border border-glass-border p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-primary">Season {{ loop.index }}</h3>
                                <span class="px-2 py-1 bg-{{ 'cyber-green' if season.completed else 'cyber-yellow' }} bg-opacity-20 text-{{ 'cyber-green' if season.completed else 'cyber-yellow' }} rounded text-xs">
                                    {{ 'Completed' if season.completed else 'Active' }}
                                </span>
                            </div>

                            <div class="grid grid-cols-3 gap-2 text-sm">
                                <div>
                                    <div class="text-tertiary">Activities</div>
                                    <div class="font-medium text-cyber-cyan">{{ season.activity_count }}</div>
                                </div>
                                <div>
                                    <div class="text-tertiary">Value</div>
                                    <div class="font-medium text-cyber-purple">{{ season.contribution_value|round(1) }}</div>
                                </div>
                                <div>
                                    <div class="text-tertiary">Score</div>
                                    <div class="font-medium text-cyber-blue">{{ season.season_score }}</div>
                                </div>
                            </div>

                            <div class="text-xs text-tertiary mt-2">
                                {{ season.season_start|timestamp_to_date }} - {{ season.season_end|timestamp_to_date }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Bottom Section - Verification & Labor -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Verification Progress -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-6">Verification Progress</h2>

                <!-- Current Tier Status -->
                <div class="bg-glass-bg rounded-lg border border-glass-border p-4 mb-6">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-primary">Current Tier: {{ identity.verification_level }}</h3>
                        <div class="tier-badge tier-{{ identity.verification_level }}">
                            <span class="text-sm font-bold">{{ tier_names[identity.verification_level] }}</span>
                        </div>
                    </div>

                    {% if next_tier_requirements %}
                    <div class="mb-4">
                        <h4 class="font-medium text-cyber-cyan mb-2">Next: {{ next_tier_requirements.name }}</h4>
                        <ul class="space-y-1 text-sm text-secondary">
                            {% for req in next_tier_requirements.requirements %}
                            <li class="flex items-start gap-2">
                                <span class="text-cyber-blue mt-1">•</span>
                                <span>{{ req }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>

                <!-- Tier Progress -->
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                        <span class="font-medium text-primary">Tier 0 - Basic Identity</span>
                        <span class="text-cyber-green">✅ Completed</span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                        <span class="font-medium text-primary">Tier 1 - Proof of Humanity</span>
                        <span class="{{ 'text-cyber-green' if verification_progress.tier_1_completed else 'text-cyber-yellow' }}">
                            {{ '✅ Completed' if verification_progress.tier_1_completed else '🕓 Pending' }}
                        </span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                        <span class="font-medium text-primary">Tier 2 - Proof of Labor</span>
                        <span class="{{ 'text-cyber-green' if verification_progress.tier_2_completed else 'text-cyber-yellow' }}">
                            {{ '✅ Completed' if verification_progress.tier_2_completed else '🕓 Pending' }}
                        </span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                        <span class="font-medium text-primary">Tier 3 - Covenant Holder</span>
                        <span class="{{ 'text-cyber-green' if verification_progress.tier_3_completed else 'text-cyber-yellow' }}">
                            {{ '✅ Completed' if verification_progress.tier_3_completed else '🕓 Pending' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Recent Contributions -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-cyber-green mb-6">Recent Contributions</h2>

                <div class="space-y-4">
                    {% for contribution in recent_contributions %}
                    <div class="contribution-card">
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-cyber-green to-cyber-blue rounded-lg flex items-center justify-center">
                                <span class="text-xl">{{ contribution.icon }}</span>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-primary">{{ contribution.type }}</div>
                                <div class="text-sm text-secondary">{{ contribution.description }}</div>
                                <div class="text-xs text-tertiary">{{ contribution.timestamp|timestamp_to_date }}</div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-cyber-green">+{{ contribution.etzem_value }}</div>
                                <div class="text-xs text-tertiary">Etzem</div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                {% if not recent_contributions %}
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-green to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🌱</span>
                    </div>
                    <h3 class="text-lg font-semibold text-primary mb-2">Start Contributing</h3>
                    <p class="text-secondary mb-4">Begin your journey by participating in the ONNYX community</p>
                    <button class="glass-button-primary px-6 py-3 rounded-lg font-medium">
                        Explore Opportunities
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8">
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Quick Actions</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button class="glass-button-primary p-4 rounded-xl text-center transition-all duration-300 hover:scale-105" onclick="requestProtection()">
                        <div class="text-2xl mb-2">🛡️</div>
                        <div class="font-semibold">Request Protection</div>
                        <div class="text-xs text-secondary">Emergency vault lock</div>
                    </button>

                    <button class="glass-button p-4 rounded-xl text-center transition-all duration-300 hover:scale-105" onclick="upgradeVerification()">
                        <div class="text-2xl mb-2">⬆️</div>
                        <div class="font-semibold">Upgrade Tier</div>
                        <div class="text-xs text-secondary">Increase verification</div>
                    </button>

                    <a href="{{ url_for('auth.covenant_scroll') }}" class="glass-button p-4 rounded-xl text-center transition-all duration-300 hover:scale-105">
                        <div class="text-2xl mb-2">📜</div>
                        <div class="font-semibold">View Covenant</div>
                        <div class="text-xs text-secondary">Digital rights scroll</div>
                    </a>

                    <button class="glass-button p-4 rounded-xl text-center transition-all duration-300 hover:scale-105" onclick="exportIdentity()">
                        <div class="text-2xl mb-2">💾</div>
                        <div class="font-semibold">Export Identity</div>
                        <div class="text-xs text-secondary">Backup credentials</div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Identity dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Animate Etzem progress bar
    const progressBar = document.querySelector('.etzem-progress');
    if (progressBar) {
        const targetWidth = progressBar.style.width;
        progressBar.style.width = '0%';
        setTimeout(() => {
            progressBar.style.width = targetWidth;
        }, 500);
    }
});

function requestProtection() {
    // Placeholder for protection request
    alert('Protection request feature coming soon!');
}

function upgradeVerification() {
    // Placeholder for verification upgrade
    alert('Verification upgrade feature coming soon!');
}

function exportIdentity() {
    // Placeholder for identity export
    alert('Identity export feature coming soon!');
}

// Store identity ID for API calls
window.identityId = '{{ identity.identity_id }}';
</script>
{% endblock %}
