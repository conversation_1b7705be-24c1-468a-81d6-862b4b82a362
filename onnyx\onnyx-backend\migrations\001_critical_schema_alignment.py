#!/usr/bin/env python3
"""
Critical Database-Blockchain Schema Alignment Migration
Addresses the critical risk of DB-blockchain mismatch during full sync or recovery.

This migration adds missing tables for:
1. <PERSON>vel (Jubilee) cycle tracking
2. Sabbatical year enforcement
3. Land ownership and inheritance
4. Debt tracking with biblical forgiveness
5. Tribal inheritance allocations

Author: ONNYX Development Team
Date: 2025-07-17
Priority: CRITICAL - Must be applied before production deployment
"""

import sqlite3
import sys
import os
import logging
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_connection():
    """Get database connection using ONNYX config."""
    try:
        # Add the backend directory to Python path
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        if backend_dir.endswith('migrations'):
            backend_dir = os.path.dirname(backend_dir)
        sys.path.insert(0, backend_dir)

        # Try to import ONNYX config
        try:
            from shared.config.onnyx_config import onnyx_config
            db_path = onnyx_config.db_path
        except ImportError:
            # Fallback to default database path
            db_path = os.path.join(backend_dir, 'data', 'onnyx.db')
            logger.warning(f"Using fallback database path: {db_path}")

        logger.info(f"Connecting to database: {db_path}")

        # Ensure database directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn

    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return None

def create_yovel_cycles_table(conn):
    """Create Yovel (Jubilee) cycle tracking table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS yovel_cycles (
                cycle_id TEXT PRIMARY KEY,
                cycle_number INTEGER NOT NULL,
                start_timestamp INTEGER NOT NULL,
                end_timestamp INTEGER,
                reset_completed BOOLEAN DEFAULT FALSE,
                land_redistribution_completed BOOLEAN DEFAULT FALSE,
                debt_forgiveness_completed BOOLEAN DEFAULT FALSE,
                created_at INTEGER NOT NULL,
                completed_at INTEGER
            )
        """)
        
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_yovel_cycles_number ON yovel_cycles(cycle_number)")
        
        conn.commit()
        logger.info("✅ Created yovel_cycles table")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create yovel_cycles table: {e}")
        return False

def create_sabbatical_years_table(conn):
    """Create Sabbatical (Shmita) year tracking table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sabbatical_years (
                year_id TEXT PRIMARY KEY,
                year_number INTEGER NOT NULL,
                start_timestamp INTEGER NOT NULL,
                end_timestamp INTEGER NOT NULL,
                debt_forgiveness_completed BOOLEAN DEFAULT FALSE,
                land_rest_enforced BOOLEAN DEFAULT TRUE,
                created_at INTEGER NOT NULL
            )
        """)
        
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sabbatical_years_number ON sabbatical_years(year_number)")
        
        conn.commit()
        logger.info("✅ Created sabbatical_years table")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create sabbatical_years table: {e}")
        return False

def create_land_ownership_table(conn):
    """Create land ownership and inheritance tracking table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS land_ownership (
                land_id TEXT PRIMARY KEY,
                owner_identity_id TEXT NOT NULL,
                original_tribal_owner TEXT NOT NULL,
                current_holder_id TEXT,
                land_type TEXT NOT NULL,
                location_description TEXT,
                size_units REAL DEFAULT 0.0,
                acquired_at INTEGER NOT NULL,
                last_jubilee_return INTEGER,
                next_jubilee_return INTEGER,
                can_be_sold BOOLEAN DEFAULT FALSE,
                redemption_price REAL DEFAULT 0.0,
                pledged_as_collateral BOOLEAN DEFAULT FALSE,
                metadata TEXT DEFAULT '{}',
                created_at INTEGER NOT NULL,
                FOREIGN KEY (owner_identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (current_holder_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_land_ownership_owner ON land_ownership(owner_identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_land_ownership_tribal ON land_ownership(original_tribal_owner)",
            "CREATE INDEX IF NOT EXISTS idx_land_ownership_jubilee ON land_ownership(next_jubilee_return)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created land_ownership table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create land_ownership table: {e}")
        return False

def create_land_transactions_table(conn):
    """Create land transactions and transfers table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS land_transactions (
                transaction_id TEXT PRIMARY KEY,
                land_id TEXT NOT NULL,
                from_identity_id TEXT NOT NULL,
                to_identity_id TEXT NOT NULL,
                transaction_type TEXT NOT NULL,
                price_paid REAL DEFAULT 0.0,
                years_remaining_to_jubilee INTEGER,
                biblical_reference TEXT,
                timestamp INTEGER NOT NULL,
                block_height INTEGER,
                FOREIGN KEY (land_id) REFERENCES land_ownership(land_id),
                FOREIGN KEY (from_identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (to_identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_land_transactions_land ON land_transactions(land_id)",
            "CREATE INDEX IF NOT EXISTS idx_land_transactions_timestamp ON land_transactions(timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created land_transactions table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create land_transactions table: {e}")
        return False

def create_debt_records_table(conn):
    """Create debt tracking table with biblical forgiveness."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS debt_records (
                debt_id TEXT PRIMARY KEY,
                creditor_id TEXT NOT NULL,
                debtor_id TEXT NOT NULL,
                original_amount REAL NOT NULL,
                current_balance REAL NOT NULL,
                interest_rate REAL DEFAULT 0.0,
                created_at INTEGER NOT NULL,
                due_date INTEGER,
                forgiven_at INTEGER,
                forgiveness_type TEXT,
                status TEXT DEFAULT 'ACTIVE',
                biblical_compliance BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (creditor_id) REFERENCES identities(identity_id),
                FOREIGN KEY (debtor_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_debt_records_creditor ON debt_records(creditor_id)",
            "CREATE INDEX IF NOT EXISTS idx_debt_records_debtor ON debt_records(debtor_id)",
            "CREATE INDEX IF NOT EXISTS idx_debt_records_status ON debt_records(status)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created debt_records table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create debt_records table: {e}")
        return False

def create_tribal_inheritance_table(conn):
    """Create tribal inheritance allocations table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tribal_inheritance (
                inheritance_id TEXT PRIMARY KEY,
                tribe_code TEXT NOT NULL,
                identity_id TEXT NOT NULL,
                inheritance_type TEXT NOT NULL,
                allocation_size REAL DEFAULT 0.0,
                location_description TEXT,
                inherited_at INTEGER NOT NULL,
                can_be_transferred BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_tribal_inheritance_tribe ON tribal_inheritance(tribe_code)",
            "CREATE INDEX IF NOT EXISTS idx_tribal_inheritance_identity ON tribal_inheritance(identity_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created tribal_inheritance table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create tribal_inheritance table: {e}")
        return False

def verify_schema_alignment(conn):
    """Verify that all critical tables exist and are properly structured."""
    try:
        cursor = conn.cursor()
        
        required_tables = [
            'yovel_cycles',
            'sabbatical_years', 
            'land_ownership',
            'land_transactions',
            'debt_records',
            'tribal_inheritance',
            'jubilee_pools',
            'dormant_accounts',
            'deeds_ledger'
        ]
        
        missing_tables = []
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                missing_tables.append(table)
        
        if missing_tables:
            logger.error(f"❌ Missing critical tables: {missing_tables}")
            return False
        
        logger.info("✅ All critical tables present - schema alignment verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Schema verification failed: {e}")
        return False

def run_migration():
    """Run the complete critical schema alignment migration."""
    logger.info("🔧 Starting Critical Database-Blockchain Schema Alignment Migration")
    logger.info("=" * 80)
    
    # Get database connection
    conn = get_db_connection()
    if not conn:
        logger.error("❌ Cannot proceed without database connection")
        return False
    
    try:
        # Create all missing tables
        migration_steps = [
            ("Yovel Cycles", create_yovel_cycles_table),
            ("Sabbatical Years", create_sabbatical_years_table),
            ("Land Ownership", create_land_ownership_table),
            ("Land Transactions", create_land_transactions_table),
            ("Debt Records", create_debt_records_table),
            ("Tribal Inheritance", create_tribal_inheritance_table)
        ]
        
        success_count = 0
        
        for step_name, step_function in migration_steps:
            logger.info(f"📋 Creating {step_name} table...")
            if step_function(conn):
                success_count += 1
            else:
                logger.error(f"❌ Failed to create {step_name} table")
        
        # Verify schema alignment
        logger.info("🔍 Verifying schema alignment...")
        schema_aligned = verify_schema_alignment(conn)
        
        # Report results
        logger.info("\n📊 Migration Results:")
        logger.info("=" * 40)
        logger.info(f"✅ Tables created successfully: {success_count}/{len(migration_steps)}")
        logger.info(f"✅ Schema alignment verified: {'Yes' if schema_aligned else 'No'}")
        
        overall_success = success_count == len(migration_steps) and schema_aligned
        
        if overall_success:
            logger.info("\n🎉 SUCCESS: Critical schema alignment migration completed!")
            logger.info("   - All Jubilee/Sabbatical tracking tables created")
            logger.info("   - Land ownership and inheritance system implemented")
            logger.info("   - Debt tracking with biblical forgiveness added")
            logger.info("   - Database-blockchain sync risk eliminated")
        else:
            logger.error("\n⚠️ Migration completed with issues - manual review required")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Migration failed with exception: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
