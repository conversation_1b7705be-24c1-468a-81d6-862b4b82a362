"""
Onnyx Block Signer Module

This module provides utilities for signing and verifying blocks.
"""

import os
import json
import hashlib
import base64
import logging
from typing import Dict, Any, Optional, Tuple

from ecdsa import Signing<PERSON><PERSON>, Veri<PERSON><PERSON><PERSON>, SECP256k1
from identity.registry import IdentityRegistry

# Set up logging
logger = logging.getLogger("onnyx.node.block_signer")

class BlockSigner:
    """
    BlockSigner provides utilities for signing and verifying blocks.
    """

    def __init__(self, identity_registry_path: str = "data/identities.json"):
        """
        Initialize the BlockSigner.

        Args:
            identity_registry_path: Path to the identity registry JSON file
        """
        self.identity_registry = IdentityRegistry(identity_registry_path)

    def hash_block_content(self, block: Dict[str, Any], exclude_fields: list = None) -> str:
        """
        Hash the content of a block, excluding specified fields.

        Args:
            block: The block to hash
            exclude_fields: Fields to exclude from the hash (default: ["signature", "hash"])

        Returns:
            The hash of the block content
        """
        if exclude_fields is None:
            exclude_fields = ["signature", "hash", "signed_by"]

        # Create a copy of the block without the excluded fields
        block_copy = {k: v for k, v in block.items() if k not in exclude_fields}

        # Convert to string and hash
        block_string = json.dumps(block_copy, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()

    def sign_block(self, block: Dict[str, Any], sela_id: str, private_key_pem: str) -> Dict[str, Any]:
        """
        Sign a block with a Sela's private key.

        Args:
            block: The block to sign
            sela_id: The Sela ID
            private_key_pem: The private key in PEM format

        Returns:
            The signed block
        """
        # Create a copy of the block
        signed_block = block.copy()

        # Hash the block content
        block_hash = self.hash_block_content(block)

        # Sign the hash
        try:
            sk = SigningKey.from_pem(private_key_pem)
            signature = sk.sign(block_hash.encode())
            signature_b64 = base64.b64encode(signature).decode()

            # Add the signature and signer to the block
            signed_block["signed_by"] = sela_id
            signed_block["signature"] = signature_b64

            logger.info(f"Block {block.get('index')} signed by Sela {sela_id}")
            return signed_block
        except Exception as e:
            logger.error(f"Error signing block: {str(e)}")
            raise

    def verify_block_signature(self, block: Dict[str, Any]) -> bool:
        """
        Verify the signature of a block.

        Args:
            block: The block to verify

        Returns:
            True if the signature is valid, False otherwise
        """
        # Check if the block has a signature and signer
        if "signature" not in block or "signed_by" not in block:
            logger.warning("Block missing signature or signer")
            return False

        # Get the signer's identity
        sela_id = block["signed_by"]
        
        # Find the founder of the Sela
        founder_id = None
        for identity_id, identity in self.identity_registry.identities.items():
            if "founded_selas" in identity and sela_id in identity.get("founded_selas", []):
                founder_id = identity_id
                break
        
        if not founder_id:
            logger.warning(f"Sela {sela_id} not found in identity registry")
            return False
        
        # Get the founder's public key
        identity = self.identity_registry.get_identity(founder_id)
        if not identity:
            logger.warning(f"Identity {founder_id} not found in identity registry")
            return False
        
        public_key = identity.get("public_key")
        if not public_key:
            logger.warning(f"Identity {founder_id} has no public key")
            return False

        # Hash the block content
        block_hash = self.hash_block_content(block)

        # Verify the signature
        try:
            # Convert the public key from hex to VerifyingKey
            vk = VerifyingKey.from_string(bytes.fromhex(public_key), curve=SECP256k1)
            
            # Decode the signature
            signature = base64.b64decode(block["signature"])
            
            # Verify the signature
            result = vk.verify(signature, block_hash.encode())
            
            if result:
                logger.info(f"Block {block.get('index')} signature verified for Sela {sela_id}")
            else:
                logger.warning(f"Block {block.get('index')} has invalid signature")
            
            return result
        except Exception as e:
            logger.error(f"Error verifying block signature: {str(e)}")
            return False

    def load_private_key(self, key_path: str) -> str:
        """
        Load a private key from a file.

        Args:
            key_path: Path to the private key file

        Returns:
            The private key in PEM format

        Raises:
            FileNotFoundError: If the key file is not found
        """
        try:
            with open(key_path, "r") as f:
                return f.read()
        except FileNotFoundError:
            logger.error(f"Private key file not found: {key_path}")
            raise
        except Exception as e:
            logger.error(f"Error loading private key: {str(e)}")
            raise

# Create a global instance of the BlockSigner
block_signer = BlockSigner()
