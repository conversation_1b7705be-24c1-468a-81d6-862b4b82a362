{% extends "base.html" %}

{% block title %}Sela Dashboard - {{ sela.name }} | ONNYX Platform{% endblock %}

{% block description %}Comprehensive dashboard for {{ sela.name }} - biblical tokenomics, covenant economics, and on-chain business verification.{% endblock %}

{% block head %}
<style>
    .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
        animation: pulse 2s infinite;
    }

    .status-active { background: #10b981; }
    .status-suspended { background: #ef4444; }
    .status-pending { background: #f59e0b; }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .verification-item {
        transition: all 0.3s ease;
    }

    .verification-item:hover {
        transform: translateX(4px);
    }

    .etzem-meter {
        background: linear-gradient(90deg, #ef4444 0%, #f59e0b 25%, #10b981 50%, #3b82f6 75%, #8b5cf6 100%);
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
    }

    .etzem-progress {
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transition: width 0.5s ease;
    }

    .covenant-badge {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
        border: 1px solid rgba(139, 92, 246, 0.3);
        border-radius: 12px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .nation-flag {
        width: 24px;
        height: 16px;
        border-radius: 2px;
        object-fit: cover;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .hash-display {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        background: rgba(0, 212, 255, 0.1);
        padding: 4px 8px;
        border-radius: 6px;
        border: 1px solid rgba(0, 212, 255, 0.2);
    }

    .participant-card {
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        transition: all 0.3s ease;
    }

    .participant-card:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(0, 212, 255, 0.3);
        transform: translateY(-2px);
    }

    .tab-button {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .tab-button.active {
        background: rgba(0, 212, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.3);
        color: #00d4ff;
    }

    .tab-button:not(.active):hover {
        background: rgba(255, 255, 255, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- 1. Sela Information Card (Primary Header Section) -->
        <div class="glass-card-enhanced p-8 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <!-- Left: Sela Identity -->
                <div class="flex items-center gap-6">
                    <!-- Sela Avatar/Logo -->
                    <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl flex items-center justify-center flex-shrink-0">
                        <span class="text-3xl">🏢</span>
                    </div>

                    <!-- Sela Details -->
                    <div>
                        <div class="flex items-center gap-3 mb-2">
                            <h1 class="text-3xl font-orbitron font-bold text-primary">{{ sela.name }}</h1>
                            <div class="flex items-center">
                                <span class="status-dot status-{{ sela.status }}"></span>
                                <span class="text-sm font-medium text-{{ 'cyber-green' if sela.status == 'active' else 'cyber-red' if sela.status == 'suspended' else 'cyber-yellow' }}">
                                    {{ sela.status.title() }}
                                </span>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-secondary">
                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Registration ID:</span>
                                <a href="{{ url_for('explorer.transaction', tx_id=sela.registration_tx) }}"
                                   class="hash-display hover:bg-cyber-cyan hover:bg-opacity-20 transition-colors">
                                    {{ sela.sela_id[:12] }}...{{ sela.sela_id[-8:] }}
                                </a>
                            </div>

                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Genesis:</span>
                                <span class="font-medium">{{ sela.created_at|timestamp_to_date }}</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Nation:</span>
                                <img src="{{ url_for('static', filename='images/flags/' + (sela.nation_code|lower) + '.png') }}"
                                     alt="{{ sela.nation_name }}" class="nation-flag">
                                <span class="font-medium">{{ sela.nation_name or 'Unverified' }}</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <span class="text-tertiary">Category:</span>
                                <span class="px-3 py-1 bg-cyber-blue bg-opacity-20 text-cyber-blue rounded-full text-xs font-medium">
                                    {{ sela.category.title() }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right: Covenant Class & Quick Stats -->
                <div class="text-center lg:text-right">
                    <div class="covenant-badge mb-4">
                        <span class="text-cyber-purple">{{ sela.covenant_class or 'Harashim' }}</span>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ sela.onx_balance|round(1) }}</div>
                            <div class="text-xs text-tertiary">ONX Balance</div>
                        </div>
                        <div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple">{{ sela.blocks_mined or 0 }}</div>
                            <div class="text-xs text-tertiary">Blocks Mined</div>
                        </div>
                        <div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-blue">{{ sela.trust_score|round(1) }}</div>
                            <div class="text-xs text-tertiary">Etzem Score</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. Verification Status Panel -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">Verification Status</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Proof of Identity -->
                <div class="verification-item flex items-center gap-3 p-4 bg-glass-bg rounded-lg border border-glass-border">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ 'bg-cyber-green' if verification.identity_verified else 'bg-cyber-red' if verification.identity_failed else 'bg-cyber-yellow' }}">
                        {% if verification.identity_verified %}
                            <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        {% elif verification.identity_failed %}
                            <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        {% else %}
                            <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        {% endif %}
                    </div>
                    <div>
                        <div class="font-semibold text-primary">Proof of Identity</div>
                        <div class="text-xs text-secondary">Cryptographic signature verification</div>
                    </div>
                </div>

                <!-- Nation Affiliation -->
                <div class="verification-item flex items-center gap-3 p-4 bg-glass-bg rounded-lg border border-glass-border">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ 'bg-cyber-green' if verification.nation_verified else 'bg-cyber-red' if verification.nation_failed else 'bg-cyber-yellow' }}">
                        {% if verification.nation_verified %}✅{% elif verification.nation_failed %}❌{% else %}🕓{% endif %}
                    </div>
                    <div>
                        <div class="font-semibold text-primary">Nation Affiliation</div>
                        <div class="text-xs text-secondary">Verified through nation validator</div>
                    </div>
                </div>

                <!-- Activity Proof -->
                <div class="verification-item flex items-center gap-3 p-4 bg-glass-bg rounded-lg border border-glass-border">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ 'bg-cyber-green' if verification.activity_verified else 'bg-cyber-red' if verification.activity_failed else 'bg-cyber-yellow' }}">
                        {% if verification.activity_verified %}✅{% elif verification.activity_failed %}❌{% else %}🕓{% endif %}
                    </div>
                    <div>
                        <div class="font-semibold text-primary">Activity Proof</div>
                        <div class="text-xs text-secondary">{{ verification.recent_transactions or 0 }} transactions (30 days)</div>
                    </div>
                </div>

                <!-- Yovel Compliance -->
                <div class="verification-item flex items-center gap-3 p-4 bg-glass-bg rounded-lg border border-glass-border">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ 'bg-cyber-green' if verification.yovel_compliant else 'bg-cyber-red' if verification.yovel_failed else 'bg-cyber-yellow' }}">
                        {% if verification.yovel_compliant %}✅{% elif verification.yovel_failed %}❌{% else %}🕓{% endif %}
                    </div>
                    <div>
                        <div class="font-semibold text-primary">Yovel Compliance</div>
                        <div class="text-xs text-secondary">Biblical jubilee token limits</div>
                    </div>
                </div>

                <!-- Etzem Growth -->
                <div class="verification-item flex items-center gap-3 p-4 bg-glass-bg rounded-lg border border-glass-border">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ 'bg-cyber-green' if verification.etzem_growing else 'bg-cyber-red' if verification.etzem_declining else 'bg-cyber-yellow' }}">
                        {% if verification.etzem_growing %}✅{% elif verification.etzem_declining %}❌{% else %}🕓{% endif %}
                    </div>
                    <div>
                        <div class="font-semibold text-primary">Etzem Growth</div>
                        <div class="text-xs text-secondary">Contribution-based rewards active</div>
                    </div>
                </div>
            </div>

            <!-- Overall Verification Score -->
            <div class="mt-6 p-4 bg-glass-bg rounded-lg border border-glass-border">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-semibold text-primary">Overall Verification</span>
                    <span class="text-cyber-cyan font-bold">{{ verification.overall_score }}%</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div class="bg-gradient-to-r from-cyber-cyan to-cyber-purple h-2 rounded-full transition-all duration-500"
                         style="width: {{ verification.overall_score }}%"></div>
                </div>
            </div>
        </div>

        <!-- 3. On-Chain Accounting Dashboard -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">On-Chain Accounting</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- ONNYX Tax ID -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">🆔</span>
                    </div>
                    <div class="font-orbitron font-bold text-cyber-purple mb-1">{{ accounting.tax_id or 'Pending' }}</div>
                    <div class="text-xs text-tertiary">ONNYX Tax ID</div>
                </div>

                <!-- Vault Address -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-cyber-green rounded-xl flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">🏦</span>
                    </div>
                    <div class="hash-display mb-1">{{ accounting.vault_address[:8] }}...{{ accounting.vault_address[-6:] }}</div>
                    <div class="text-xs text-tertiary">Vault Address</div>
                </div>

                <!-- Mining Rewards -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-green to-cyber-cyan rounded-xl flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">⛏️</span>
                    </div>
                    <div class="font-orbitron font-bold text-cyber-green mb-1">{{ accounting.mining_rewards|round(2) }}</div>
                    <div class="text-xs text-tertiary">Mining Rewards</div>
                </div>

                <!-- Mikvah Tokens -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">💧</span>
                    </div>
                    <div class="font-orbitron font-bold text-cyber-cyan mb-1">{{ accounting.mikvah_tokens|round(2) }}</div>
                    <div class="text-xs text-tertiary">Mikvah Accruals</div>
                </div>
            </div>

            <!-- Year-End Reports -->
            <div class="mt-6 p-4 bg-glass-bg rounded-lg border border-glass-border">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-semibold text-primary mb-1">Year-End Reports</h3>
                        <p class="text-sm text-secondary">Auto-generated financial summaries</p>
                    </div>
                    <div class="flex gap-2">
                        <button class="glass-button px-4 py-2 rounded-lg text-sm font-medium">
                            📊 2024 Report
                        </button>
                        <button class="glass-button px-4 py-2 rounded-lg text-sm font-medium">
                            📈 Analytics
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. Sela Participants & Structure (Tabbed Interface) -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-6">Sela Structure & Participants</h2>

            <!-- Tab Navigation -->
            <div class="flex flex-wrap gap-2 mb-6 border-b border-glass-border pb-4">
                <button class="tab-button active" data-tab="founders">
                    👑 Founders ({{ participants.founders|length }})
                </button>
                <button class="tab-button" data-tab="members">
                    👥 Members ({{ participants.members|length }})
                </button>
                <button class="tab-button" data-tab="labor-management">
                    🪙 Labor Management
                </button>
                <button class="tab-button" data-tab="service-logs">
                    📋 Service Logs ({{ service_logs|length }})
                </button>
                <button class="tab-button" data-tab="sub-selas">
                    🏗️ Sub-Selas ({{ sub_selas|length }})
                </button>
            </div>

            <!-- Tab Content -->
            <div id="tab-content">
                <!-- Founders Tab -->
                <div id="founders-tab" class="tab-content">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {% for founder in participants.founders %}
                        <div class="participant-card">
                            <div class="flex items-center gap-3 mb-3">
                                <img src="{{ url_for('static', filename='images/flags/' + (founder.nation_code|lower) + '.png') }}"
                                     alt="{{ founder.nation_name }}" class="nation-flag">
                                <div>
                                    <div class="font-semibold text-primary">{{ founder.name }}</div>
                                    <div class="text-xs text-secondary">{{ founder.role }}</div>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3 text-sm">
                                <div>
                                    <div class="text-tertiary">Contributions</div>
                                    <div class="font-medium text-cyber-cyan">{{ founder.contribution_count }}</div>
                                </div>
                                <div>
                                    <div class="text-tertiary">Etzem Score</div>
                                    <div class="font-medium text-cyber-purple">{{ founder.etzem_score|round(1) }}</div>
                                </div>
                            </div>

                            <div class="mt-3 flex items-center justify-between">
                                <span class="px-2 py-1 bg-cyber-green bg-opacity-20 text-cyber-green rounded text-xs">
                                    ✅ Verified
                                </span>
                                <span class="text-xs text-tertiary">Since {{ founder.joined_date|timestamp_to_date }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Members Tab -->
                <div id="members-tab" class="tab-content hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {% for member in participants.members %}
                        <div class="participant-card">
                            <div class="flex items-center gap-3 mb-3">
                                <img src="{{ url_for('static', filename='images/flags/' + (member.nation_code|lower) + '.png') }}"
                                     alt="{{ member.nation_name }}" class="nation-flag">
                                <div>
                                    <div class="font-semibold text-primary">{{ member.name }}</div>
                                    <div class="text-xs text-secondary">{{ member.role }}</div>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3 text-sm">
                                <div>
                                    <div class="text-tertiary">Contributions</div>
                                    <div class="font-medium text-cyber-cyan">{{ member.contribution_count }}</div>
                                </div>
                                <div>
                                    <div class="text-tertiary">Etzem Score</div>
                                    <div class="font-medium text-cyber-purple">{{ member.etzem_score|round(1) }}</div>
                                </div>
                            </div>

                            <div class="mt-3 flex items-center justify-between">
                                <span class="px-2 py-1 bg-{{ 'cyber-green' if member.verified else 'cyber-yellow' }} bg-opacity-20 text-{{ 'cyber-green' if member.verified else 'cyber-yellow' }} rounded text-xs">
                                    {{ '✅ Verified' if member.verified else '🕓 Pending' }}
                                </span>
                                <span class="text-xs text-tertiary">Since {{ member.joined_date|timestamp_to_date }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Labor Management Tab -->
                <div id="labor-management-tab" class="tab-content hidden">
                    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-6">
                        <!-- Team Labor Overview -->
                        <div class="glass-panel p-6">
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Team Labor Overview</h3>

                            <!-- Labor Statistics -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyber-blue mb-1" id="teamLaborCount">0</div>
                                    <div class="text-xs text-tertiary">Total Labor</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyber-purple mb-1" id="teamMikvahTokens">0.00</div>
                                    <div class="text-xs text-tertiary">Mikvah Earned</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyber-cyan mb-1" id="teamValueCreated">0.00</div>
                                    <div class="text-xs text-tertiary">Value Created</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyber-green mb-1" id="teamVerificationRate">0%</div>
                                    <div class="text-xs text-tertiary">Verification Rate</div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="space-y-3">
                                <button onclick="showLaborLogModal()" class="w-full glass-button-primary py-2 px-4 rounded-lg text-sm">
                                    📝 Log Team Labor
                                </button>
                                <button onclick="loadPendingTeamVerifications()" class="w-full glass-button py-2 px-4 rounded-lg text-sm">
                                    ✅ Verify Team Labor
                                </button>
                                <button onclick="generateLaborReport()" class="w-full glass-button py-2 px-4 rounded-lg text-sm">
                                    📊 Generate Report
                                </button>
                            </div>
                        </div>

                        <!-- Recent Team Labor -->
                        <div class="glass-panel p-6">
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Recent Team Labor</h3>

                            <div id="recentTeamLabor" class="space-y-3">
                                <div class="text-center text-secondary py-4">
                                    Loading team labor...
                                </div>
                            </div>
                        </div>

                        <!-- Token Distribution -->
                        <div class="glass-panel p-6">
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Token Distribution</h3>

                            <!-- Distribution Pool -->
                            <div class="bg-glass-bg rounded-lg border border-glass-border p-4 mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-secondary">Distribution Pool</span>
                                    <span class="text-cyber-cyan font-bold" id="distributionPool">0.00</span>
                                </div>
                                <div class="progress-bar">
                                    <div id="distributionProgress" class="progress-fill" style="width: 0%"></div>
                                </div>
                                <div class="text-xs text-tertiary mt-1">Ready for distribution</div>
                            </div>

                            <!-- Distribution Actions -->
                            <div class="space-y-2">
                                <button onclick="distributeTokens()" class="w-full glass-button-primary py-2 px-4 rounded-lg text-sm">
                                    💰 Distribute Tokens
                                </button>
                                <button onclick="showDistributionHistory()" class="w-full glass-button py-2 px-4 rounded-lg text-sm">
                                    📈 Distribution History
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Team Member Labor Details -->
                    <div class="glass-panel p-6">
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Team Member Labor Details</h3>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-glass-border">
                                        <th class="text-left py-3 px-4 text-sm font-medium text-secondary">Member</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-secondary">Labor Count</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-secondary">Value Created</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-secondary">Tokens Earned</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-secondary">Verification Rate</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-secondary">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="teamLaborTable">
                                    <tr>
                                        <td colspan="6" class="text-center py-8 text-secondary">
                                            Loading team member data...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Service Logs Tab -->
                <div id="service-logs-tab" class="tab-content hidden">
                    <div class="space-y-4">
                        {% for log in service_logs[:10] %}
                        <div class="flex items-center justify-between p-4 bg-glass-bg rounded-lg border border-glass-border">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-cyber-blue to-cyber-purple rounded-lg flex items-center justify-center">
                                    <span class="text-sm">📋</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-primary">{{ log.service_type }}</div>
                                    <div class="text-sm text-secondary">{{ log.description }}</div>
                                </div>
                            </div>

                            <div class="text-right">
                                <div class="font-medium text-cyber-cyan">{{ log.amount }} ONX</div>
                                <div class="text-xs text-tertiary">{{ log.timestamp|timestamp_to_date }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Sub-Selas Tab -->
                <div id="sub-selas-tab" class="tab-content hidden">
                    {% if sub_selas %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for sub_sela in sub_selas %}
                        <div class="participant-card">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-blue to-cyber-green rounded-lg flex items-center justify-center">
                                    <span class="text-lg">🏗️</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-primary">{{ sub_sela.name }}</div>
                                    <div class="text-xs text-secondary">{{ sub_sela.category }}</div>
                                </div>
                            </div>

                            <div class="grid grid-cols-3 gap-2 text-sm text-center">
                                <div>
                                    <div class="font-medium text-cyber-cyan">{{ sub_sela.members_count }}</div>
                                    <div class="text-xs text-tertiary">Members</div>
                                </div>
                                <div>
                                    <div class="font-medium text-cyber-purple">{{ sub_sela.onx_balance|round(1) }}</div>
                                    <div class="text-xs text-tertiary">ONX</div>
                                </div>
                                <div>
                                    <div class="font-medium text-cyber-blue">{{ sub_sela.trust_score|round(1) }}</div>
                                    <div class="text-xs text-tertiary">Trust</div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-cyber-purple rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🏗️</span>
                        </div>
                        <h3 class="text-lg font-semibold text-primary mb-2">No Sub-Selas Yet</h3>
                        <p class="text-secondary mb-4">Expand your business network by creating specialized units</p>
                        <button class="glass-button-primary px-6 py-3 rounded-lg font-medium">
                            Create Sub-Sela
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 5. Governance & Reputation Section -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-green mb-6">Governance & Reputation</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left: Governance Participation -->
                <div>
                    <h3 class="text-xl font-semibold text-primary mb-4">Community Participation</h3>

                    <!-- Submitted Scrolls -->
                    <div class="bg-glass-bg rounded-lg border border-glass-border p-4 mb-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-cyber-cyan">Submitted Scrolls</h4>
                            <span class="text-sm text-secondary">{{ governance.submitted_scrolls|length }} proposals</span>
                        </div>

                        {% if governance.submitted_scrolls %}
                        <div class="space-y-2">
                            {% for scroll in governance.submitted_scrolls[:3] %}
                            <div class="flex items-center justify-between p-2 bg-glass-bg rounded border border-glass-border">
                                <div>
                                    <div class="font-medium text-primary text-sm">{{ scroll.title }}</div>
                                    <div class="text-xs text-tertiary">{{ scroll.category }} • {{ scroll.created_at|timestamp_to_date }}</div>
                                </div>
                                <span class="px-2 py-1 bg-{{ 'cyber-green' if scroll.status == 'approved' else 'cyber-yellow' if scroll.status == 'pending' else 'cyber-red' }} bg-opacity-20 text-{{ 'cyber-green' if scroll.status == 'approved' else 'cyber-yellow' if scroll.status == 'pending' else 'cyber-red' }} rounded text-xs">
                                    {{ scroll.status.title() }}
                                </span>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <span class="text-sm text-secondary">No proposals submitted yet</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Active Contracts -->
                    <div class="bg-glass-bg rounded-lg border border-glass-border p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-cyber-purple">Active Contracts</h4>
                            <span class="text-sm text-secondary">{{ governance.active_contracts|length }} ongoing</span>
                        </div>

                        {% if governance.active_contracts %}
                        <div class="space-y-2">
                            {% for contract in governance.active_contracts[:3] %}
                            <div class="flex items-center justify-between p-2 bg-glass-bg rounded border border-glass-border">
                                <div>
                                    <div class="font-medium text-primary text-sm">{{ contract.service_type }}</div>
                                    <div class="text-xs text-tertiary">{{ contract.client_name }} • {{ contract.value }} ONX</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-xs text-cyber-purple font-medium">{{ contract.progress }}%</div>
                                    <div class="w-16 bg-gray-700 rounded-full h-1">
                                        <div class="bg-cyber-purple h-1 rounded-full" style="width: {{ contract.progress }}%"></div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <span class="text-sm text-secondary">No active contracts</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Right: Reputation & Escrow -->
                <div>
                    <!-- Sela Reputation Score -->
                    <div class="bg-glass-bg rounded-lg border border-glass-border p-6 mb-4">
                        <h4 class="font-semibold text-cyber-blue mb-4">Sela Reputation Score</h4>

                        <div class="text-center mb-4">
                            <div class="text-4xl font-orbitron font-bold text-cyber-blue mb-2">{{ reputation.composite_score|round(1) }}</div>
                            <div class="text-sm text-secondary">Composite Etzem Rating</div>
                        </div>

                        <!-- Reputation Meter -->
                        <div class="etzem-meter mb-4">
                            <div class="etzem-progress" style="width: {{ (reputation.composite_score / 10) * 100 }}%"></div>
                        </div>

                        <!-- Reputation Breakdown -->
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="font-medium text-cyber-cyan">{{ reputation.service_quality|round(1) }}</div>
                                <div class="text-xs text-tertiary">Service Quality</div>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-cyber-purple">{{ reputation.reliability|round(1) }}</div>
                                <div class="text-xs text-tertiary">Reliability</div>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-cyber-blue">{{ reputation.community_impact|round(1) }}</div>
                                <div class="text-xs text-tertiary">Community Impact</div>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-cyber-green">{{ reputation.biblical_compliance|round(1) }}</div>
                                <div class="text-xs text-tertiary">Biblical Compliance</div>
                            </div>
                        </div>
                    </div>

                    <!-- Escrow History -->
                    <div class="bg-glass-bg rounded-lg border border-glass-border p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-cyber-green">Escrow History</h4>
                            <span class="text-sm text-secondary">{{ escrow.pending_count }} pending</span>
                        </div>

                        {% if escrow.recent_transactions %}
                        <div class="space-y-2">
                            {% for transaction in escrow.recent_transactions[:3] %}
                            <div class="flex items-center justify-between p-2 bg-glass-bg rounded border border-glass-border">
                                <div>
                                    <div class="font-medium text-primary text-sm">{{ transaction.amount }} ONX</div>
                                    <div class="text-xs text-tertiary">{{ transaction.service_type }}</div>
                                </div>
                                <div class="text-right">
                                    <span class="px-2 py-1 bg-{{ 'cyber-green' if transaction.status == 'released' else 'cyber-yellow' if transaction.status == 'pending' else 'cyber-blue' }} bg-opacity-20 text-{{ 'cyber-green' if transaction.status == 'released' else 'cyber-yellow' if transaction.status == 'pending' else 'cyber-blue' }} rounded text-xs">
                                        {{ transaction.status.title() }}
                                    </span>
                                    <div class="text-xs text-tertiary mt-1">{{ transaction.timeline }}</div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <span class="text-sm text-secondary">No escrow transactions</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Panel -->
        <div class="glass-card p-6">
            <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Quick Actions</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button class="glass-button-primary p-4 rounded-xl text-center transition-all duration-300 hover:scale-105">
                    <div class="text-2xl mb-2">⛏️</div>
                    <div class="font-semibold">Mine Block</div>
                    <div class="text-xs text-secondary">Validate transactions</div>
                </button>

                <button class="glass-button p-4 rounded-xl text-center transition-all duration-300 hover:scale-105">
                    <div class="text-2xl mb-2">📜</div>
                    <div class="font-semibold">Submit Scroll</div>
                    <div class="text-xs text-secondary">Governance proposal</div>
                </button>

                <button class="glass-button p-4 rounded-xl text-center transition-all duration-300 hover:scale-105">
                    <div class="text-2xl mb-2">🤝</div>
                    <div class="font-semibold">New Contract</div>
                    <div class="text-xs text-secondary">Service agreement</div>
                </button>

                <button class="glass-button p-4 rounded-xl text-center transition-all duration-300 hover:scale-105">
                    <div class="text-2xl mb-2">📊</div>
                    <div class="font-semibold">View Analytics</div>
                    <div class="text-xs text-secondary">Performance metrics</div>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Labor Log Modal -->
<div id="laborLogModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="glass-card p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Log Team Labor</h3>

        <form id="teamLaborForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-secondary mb-2">Team Member</label>
                <select id="teamMember" class="glass-input w-full">
                    <option value="">Select team member...</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-secondary mb-2">Labor Type</label>
                <select id="teamLaborType" class="glass-input w-full">
                    <option value="service">Service</option>
                    <option value="product">Product</option>
                    <option value="teaching">Teaching</option>
                    <option value="healing">Healing</option>
                    <option value="farming">Farming</option>
                    <option value="governance">Governance</option>
                    <option value="community">Community</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-secondary mb-2">Description</label>
                <textarea id="teamLaborDescription" rows="3" class="glass-input w-full" placeholder="Describe the labor contribution..."></textarea>
            </div>
            <div>
                <label class="block text-sm font-medium text-secondary mb-2">Estimated Value</label>
                <input type="number" id="teamLaborValue" min="1" max="2000" class="glass-input w-full" placeholder="0.00">
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeLaborLogModal()" class="flex-1 glass-button py-2 px-4 rounded-lg">
                    Cancel
                </button>
                <button type="submit" class="flex-1 glass-button-primary py-2 px-4 rounded-lg">
                    Log Labor
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Labor Management Functions
const selaId = '{{ sela.sela_id }}';

// Show labor log modal
function showLaborLogModal() {
    loadTeamMembers();
    document.getElementById('laborLogModal').classList.remove('hidden');
}

// Close labor log modal
function closeLaborLogModal() {
    document.getElementById('laborLogModal').classList.add('hidden');
    document.getElementById('teamLaborForm').reset();
}

// Load team members for selection
async function loadTeamMembers() {
    try {
        const response = await fetch(`/api/sela/${selaId}/members`);
        const data = await response.json();

        const select = document.getElementById('teamMember');
        select.innerHTML = '<option value="">Select team member...</option>';

        if (data.members) {
            data.members.forEach(member => {
                const option = document.createElement('option');
                option.value = member.identity_id;
                option.textContent = member.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading team members:', error);
    }
}

// Submit team labor
document.getElementById('teamLaborForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const laborData = {
        identity_id: document.getElementById('teamMember').value,
        sela_id: selaId,
        labor_type: document.getElementById('teamLaborType').value,
        description: document.getElementById('teamLaborDescription').value,
        value_estimate: parseFloat(document.getElementById('teamLaborValue').value) || 0
    };

    if (!laborData.identity_id) {
        alert('Please select a team member.');
        return;
    }

    if (!laborData.description || laborData.description.length < 10) {
        alert('Please provide a description of at least 10 characters.');
        return;
    }

    if (laborData.value_estimate <= 0) {
        alert('Please provide a valid estimated value.');
        return;
    }

    try {
        const response = await fetch('/api/labor/log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(laborData)
        });

        const result = await response.json();

        if (result.success) {
            alert(`Team labor logged successfully! Potential reward: ${result.potential_mikvah_tokens} tokens.`);
            closeLaborLogModal();
            loadTeamLaborData();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        console.error('Error logging team labor:', error);
        alert('Failed to log team labor. Please try again.');
    }
});

// Load team labor data
async function loadTeamLaborData() {
    try {
        const response = await fetch(`/api/sela/${selaId}/labor-statistics`);
        const data = await response.json();

        if (data.team_statistics) {
            updateTeamLaborDisplay(data.team_statistics);
        }

        if (data.recent_labor) {
            displayRecentTeamLabor(data.recent_labor);
        }

        if (data.member_details) {
            displayTeamMemberTable(data.member_details);
        }
    } catch (error) {
        console.error('Error loading team labor data:', error);
    }
}

// Update team labor display
function updateTeamLaborDisplay(stats) {
    document.getElementById('teamLaborCount').textContent = stats.total_labor_count || 0;
    document.getElementById('teamMikvahTokens').textContent = (stats.total_mikvah_earned || 0).toFixed(2);
    document.getElementById('teamValueCreated').textContent = (stats.total_value_created || 0).toFixed(2);
    document.getElementById('teamVerificationRate').textContent = `${(stats.verification_rate || 0).toFixed(1)}%`;

    // Update distribution pool
    document.getElementById('distributionPool').textContent = (stats.pending_distribution || 0).toFixed(2);
    const distributionProgress = Math.min((stats.pending_distribution || 0) / 100 * 100, 100);
    document.getElementById('distributionProgress').style.width = `${distributionProgress}%`;
}

// Display recent team labor
function displayRecentTeamLabor(laborRecords) {
    const container = document.getElementById('recentTeamLabor');

    if (laborRecords.length === 0) {
        container.innerHTML = '<div class="text-center text-secondary py-4">No recent team labor</div>';
        return;
    }

    container.innerHTML = laborRecords.map(labor => `
        <div class="flex justify-between items-center p-3 glass-panel rounded-lg">
            <div>
                <div class="text-sm font-medium text-primary">${labor.member_name}</div>
                <div class="text-xs text-secondary">${labor.labor_type} - ${labor.description.substring(0, 30)}...</div>
            </div>
            <div class="text-right">
                <div class="text-sm font-medium ${getStatusColor(labor.verification_status)}">${labor.verification_status}</div>
                <div class="text-xs text-secondary">${labor.value_estimate} value</div>
            </div>
        </div>
    `).join('');
}

// Display team member table
function displayTeamMemberTable(memberDetails) {
    const tbody = document.getElementById('teamLaborTable');

    if (memberDetails.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-8 text-secondary">
                    No team member data available
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = memberDetails.map(member => `
        <tr class="border-b border-glass-border hover:bg-glass-bg">
            <td class="py-3 px-4">
                <div class="flex items-center gap-2">
                    <img src="/static/images/flags/${(member.nation_code || 'us').toLowerCase()}.png"
                         alt="${member.nation_name}" class="nation-flag">
                    <div>
                        <div class="font-medium text-primary">${member.name}</div>
                        <div class="text-xs text-secondary">${member.role || 'Team Member'}</div>
                    </div>
                </div>
            </td>
            <td class="py-3 px-4 text-cyber-blue font-medium">${member.labor_count || 0}</td>
            <td class="py-3 px-4 text-cyber-cyan font-medium">${(member.value_created || 0).toFixed(2)}</td>
            <td class="py-3 px-4 text-cyber-purple font-medium">${(member.tokens_earned || 0).toFixed(2)}</td>
            <td class="py-3 px-4">
                <span class="text-cyber-green font-medium">${(member.verification_rate || 0).toFixed(1)}%</span>
            </td>
            <td class="py-3 px-4">
                <div class="flex gap-1">
                    <button onclick="viewMemberLabor('${member.identity_id}')" class="glass-button px-2 py-1 rounded text-xs">
                        👁️ View
                    </button>
                    <button onclick="distributeMemberTokens('${member.identity_id}')" class="glass-button px-2 py-1 rounded text-xs">
                        💰 Pay
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Load pending team verifications
async function loadPendingTeamVerifications() {
    try {
        const response = await fetch(`/api/sela/${selaId}/pending-verifications`);
        const data = await response.json();

        if (data.pending_labor && data.pending_labor.length > 0) {
            alert(`Found ${data.pending_labor.length} pending verifications. Opening verification interface...`);
            // Could open a modal or redirect to verification page
        } else {
            alert('No pending team verifications found.');
        }
    } catch (error) {
        console.error('Error loading pending verifications:', error);
        alert('Failed to load pending verifications.');
    }
}

// Generate labor report
async function generateLaborReport() {
    try {
        const response = await fetch(`/api/sela/${selaId}/labor-report`, {
            method: 'POST'
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sela-labor-report-${selaId}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            alert('Failed to generate labor report.');
        }
    } catch (error) {
        console.error('Error generating report:', error);
        alert('Failed to generate labor report.');
    }
}

// Distribute tokens
async function distributeTokens() {
    if (confirm('Distribute pending tokens to all team members based on their contributions?')) {
        try {
            const response = await fetch(`/api/sela/${selaId}/distribute-tokens`, {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                alert(`Successfully distributed ${result.total_distributed} tokens to ${result.member_count} team members.`);
                loadTeamLaborData();
            } else {
                alert(`Error: ${result.error}`);
            }
        } catch (error) {
            console.error('Error distributing tokens:', error);
            alert('Failed to distribute tokens.');
        }
    }
}

// View member labor details
function viewMemberLabor(identityId) {
    window.open(`/api/labor/dashboard?member=${identityId}`, '_blank');
}

// Distribute tokens to specific member
async function distributeMemberTokens(identityId) {
    const amount = prompt('Enter token amount to distribute:');
    if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
        try {
            const response = await fetch(`/api/sela/${selaId}/distribute-member-tokens`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    identity_id: identityId,
                    amount: parseFloat(amount)
                })
            });

            const result = await response.json();

            if (result.success) {
                alert(`Successfully distributed ${amount} tokens.`);
                loadTeamLaborData();
            } else {
                alert(`Error: ${result.error}`);
            }
        } catch (error) {
            console.error('Error distributing member tokens:', error);
            alert('Failed to distribute tokens.');
        }
    }
}

// Show distribution history
function showDistributionHistory() {
    alert('Distribution history feature coming soon!');
}

// Helper function for status colors
function getStatusColor(status) {
    switch (status) {
        case 'verified': return 'text-green-400';
        case 'pending': return 'text-yellow-400';
        case 'disputed': return 'text-orange-400';
        case 'rejected': return 'text-red-400';
        default: return 'text-secondary';
    }
}

// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabName = button.dataset.tab;

            // Remove active class from all buttons
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // Hide all tab contents
            tabContents.forEach(content => content.classList.add('hidden'));

            // Activate clicked button
            button.classList.add('active');

            // Show corresponding tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.remove('hidden');
            }
        });
    });

    // Real-time verification status updates
    function updateVerificationStatus() {
        fetch(`/api/sela/${window.selaId}/verification`)
            .then(response => response.json())
            .then(data => {
                // Update verification indicators
                Object.keys(data).forEach(key => {
                    const element = document.querySelector(`[data-verification="${key}"]`);
                    if (element) {
                        element.className = `w-8 h-8 rounded-full flex items-center justify-center ${
                            data[key] ? 'bg-cyber-green' : 'bg-cyber-red'
                        }`;
                    }
                });
            })
            .catch(error => console.log('Verification update failed:', error));
    }

    // Update every 30 seconds
    setInterval(updateVerificationStatus, 30000);

    // Etzem score animation
    function animateEtzemScore() {
        const progressBar = document.querySelector('.etzem-progress');
        if (progressBar) {
            const targetWidth = progressBar.style.width;
            progressBar.style.width = '0%';
            setTimeout(() => {
                progressBar.style.width = targetWidth;
            }, 500);
        }
    }

    animateEtzemScore();

    // Initialize labor management data
    loadTeamLaborData();
});

// Store sela ID for API calls
window.selaId = '{{ sela.sela_id }}';
</script>

<!-- Sela Dashboard JavaScript -->
<script src="{{ url_for('static', filename='js/sela-dashboard.js') }}"></script>
{% endblock %}
