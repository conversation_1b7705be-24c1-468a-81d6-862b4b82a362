{% extends "base.html" %}

{% block title %}Tribal Elder Management - Admin Panel | ONNYX{% endblock %}

{% block head %}
<style>
.admin-warning {
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.1), rgba(255, 69, 0, 0.1));
    border: 1px solid rgba(255, 165, 0, 0.3);
}

.tribal-elder-card {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    border: 1px solid rgba(0, 255, 247, 0.2);
    transition: all 0.3s ease;
}

.tribal-elder-card:hover {
    border-color: rgba(0, 255, 247, 0.4);
    transform: translateY(-2px);
}

.tribe-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.governance-role {
    background: rgba(154, 0, 255, 0.2);
    color: var(--cyber-purple);
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
}

.voting-weight {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: bold;
}

.empty-tribe {
    background: rgba(255, 69, 0, 0.1);
    border: 1px dashed rgba(255, 69, 0, 0.3);
    color: rgba(255, 69, 0, 0.8);
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background: linear-gradient(135deg, var(--dark-900), var(--dark-800));
    margin: 5% auto;
    padding: 2rem;
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    color: var(--text-secondary);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--cyber-cyan);
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Admin Header -->
        <div class="admin-warning p-4 rounded-xl mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl">👑</div>
                    <div>
                        <h1 class="text-2xl font-orbitron font-bold text-orange-400">Tribal Elder Management</h1>
                        <p class="text-orange-300">Manage the Council of Twelve Tribes - Biblical Governance System</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button onclick="openCreateElderModal()" class="glass-button-primary px-4 py-2 rounded-lg">
                        + Add Tribal Elder
                    </button>
                    <a href="{{ url_for('admin.dashboard') }}" class="glass-button-secondary px-4 py-2 rounded-lg">
                        ← Back to Admin Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Council Overview -->
        <div class="glass-card p-6 rounded-xl mb-8">
            <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Council of Twelve Tribes Overview</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-cyber-cyan">{{ elders|length }}</div>
                    <div class="text-sm text-secondary">Active Elders</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-cyber-purple">{{ 12 - elders|length }}</div>
                    <div class="text-sm text-secondary">Vacant Positions</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-cyber-green">{{ elders|selectattr('metadata_parsed.voting_weight', 'equalto', 2)|list|length }}</div>
                    <div class="text-sm text-secondary">High Authority</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-400">{{ elders|map(attribute='metadata_parsed.voting_weight')|sum or 0 }}</div>
                    <div class="text-sm text-secondary">Total Voting Weight</div>
                </div>
            </div>
        </div>

        <!-- Twelve Tribes Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {% for tribe in twelve_tribes %}
            {% set elder = elders|selectattr('nation_code', 'equalto', tribe.code)|first %}
            <div class="tribal-elder-card p-6 rounded-xl {% if not elder %}empty-tribe{% endif %}">
                <div class="flex items-center justify-between mb-4">
                    <div class="tribe-badge">{{ tribe.code }}</div>
                    <div class="voting-weight">Weight: {{ tribe.voting_weight }}</div>
                </div>
                
                <h3 class="text-lg font-orbitron font-bold text-white mb-2">Tribe of {{ tribe.name }}</h3>
                <p class="text-sm text-secondary mb-4">{{ tribe.role }}</p>
                
                {% if elder %}
                <div class="space-y-2">
                    <div class="font-semibold text-cyber-cyan">{{ elder.name }}</div>
                    <div class="text-sm text-secondary">{{ elder.email }}</div>
                    <div class="governance-role">{{ elder.metadata_parsed.governance_role or 'Elder' }}</div>
                    <div class="text-xs text-secondary">
                        Created: {{ moment(elder.created_at).format('%b %d, %Y') }}
                    </div>
                    <div class="flex space-x-2 mt-4">
                        <button onclick="viewElderDetails('{{ elder.identity_id }}')" 
                                class="px-3 py-1 bg-cyber-cyan text-onyx-black rounded text-xs hover:bg-cyan-400 transition-colors">
                            View Details
                        </button>
                        <button onclick="confirmRemoveElder('{{ elder.identity_id }}', '{{ elder.name }}')" 
                                class="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">
                            Remove
                        </button>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <div class="text-4xl mb-2">👑</div>
                    <div class="text-sm text-secondary mb-4">No Elder Appointed</div>
                    <button onclick="openCreateElderModal('{{ tribe.code }}')" 
                            class="px-4 py-2 bg-cyber-green text-onyx-black rounded hover:bg-green-500 transition-colors">
                        Appoint Elder
                    </button>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Create Elder Modal -->
<div id="createElderModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeCreateElderModal()">&times;</span>
        <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">Appoint Tribal Elder</h2>
        
        <form id="createElderForm" onsubmit="createElder(event)">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Tribe</label>
                    <select id="tribeSelect" name="tribe_code" required class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                        <option value="">Select Tribe</option>
                        {% for tribe in twelve_tribes %}
                        {% set elder = elders|selectattr('nation_code', 'equalto', tribe.code)|first %}
                        {% if not elder %}
                        <option value="{{ tribe.code }}">{{ tribe.name }} ({{ tribe.role }})</option>
                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Elder Name</label>
                    <input type="text" id="elderName" name="name" required 
                           class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white"
                           placeholder="Enter the elder's full name">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Email Address</label>
                    <input type="email" id="elderEmail" name="email" required 
                           class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white"
                           placeholder="<EMAIL>">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-secondary mb-2">Governance Role</label>
                    <select id="governanceRole" name="governance_role" required class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                        <option value="">Select Role</option>
                        <option value="High Priest">High Priest</option>
                        <option value="Judge">Judge</option>
                        <option value="Prophet">Prophet</option>
                        <option value="Teacher">Teacher</option>
                        <option value="Guardian">Guardian</option>
                        <option value="Scribe">Scribe</option>
                        <option value="Elder">Elder</option>
                    </select>
                </div>
                
                <div class="flex space-x-4 pt-4">
                    <button type="submit" class="flex-1 glass-button-primary py-3 rounded-lg">
                        Appoint Elder
                    </button>
                    <button type="button" onclick="closeCreateElderModal()" class="flex-1 glass-button-secondary py-3 rounded-lg">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function openCreateElderModal(tribeCode = '') {
    const modal = document.getElementById('createElderModal');
    const tribeSelect = document.getElementById('tribeSelect');
    
    if (tribeCode) {
        tribeSelect.value = tribeCode;
    }
    
    modal.style.display = 'block';
}

function closeCreateElderModal() {
    const modal = document.getElementById('createElderModal');
    modal.style.display = 'none';
    document.getElementById('createElderForm').reset();
}

async function createElder(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        tribe_code: formData.get('tribe_code'),
        governance_role: formData.get('governance_role')
    };
    
    try {
        const response = await fetch('/admin/api/tribal-elders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message);
            closeCreateElderModal();
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        alert(`Error creating elder: ${error.message}`);
    }
}

function viewElderDetails(elderId) {
    // TODO: Implement elder details view
    alert('Elder details view coming soon!');
}

function confirmRemoveElder(elderId, elderName) {
    if (confirm(`Are you sure you want to remove ${elderName} as tribal elder? This action cannot be undone.`)) {
        removeElder(elderId);
    }
}

async function removeElder(elderId) {
    try {
        const response = await fetch(`/admin/api/tribal-elders/${elderId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        alert(`Error removing elder: ${error.message}`);
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('createElderModal');
    if (event.target == modal) {
        closeCreateElderModal();
    }
}
</script>
{% endblock %}
