#!/usr/bin/env python3
"""
ONNYX Platform Startup Script

This script starts the complete ONNYX platform:
- Flask web application
- P2P blockchain network
- Mining network
- Database initialization

Usage:
    python start_onnyx.py
"""

import os
import sys
import logging
import asyncio
import threading
import time
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging with UTF-8 encoding for Windows compatibility
import sys

# Create handlers with proper encoding
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)

file_handler = logging.FileHandler('onnyx_startup.log', encoding='utf-8')
file_handler.setLevel(logging.INFO)

# Set formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    handlers=[console_handler, file_handler]
)
logger = logging.getLogger("onnyx.startup")

def initialize_database():
    """Initialize the database if needed."""
    try:
        from init_db import check_tables, init_database
        logger.info("[DATABASE] Checking database...")

        if not check_tables():
            logger.info("[DATABASE] Initializing database tables...")
            init_database()
            logger.info("[DATABASE] Database initialized successfully")
        else:
            logger.info("[DATABASE] Database tables already exist")
        return True
    except Exception as e:
        logger.error(f"[DATABASE] Database initialization failed: {e}")
        return False

def start_blockchain_network():
    """Start the blockchain network in a background thread."""
    def run_blockchain():
        try:
            logger.info("[BLOCKCHAIN] Starting blockchain network...")

            # Import blockchain components
            from scripts.deploy_p2p_network import NetworkDeployment
            from scripts.deploy_p2p_mining import P2PMiningDeployment

            async def deploy_blockchain():
                # Start P2P network
                logger.info("[P2P] Deploying P2P network...")
                network = NetworkDeployment()
                network_success = await network.deploy_network()

                if network_success:
                    logger.info("[P2P] P2P network deployed successfully")

                    # Start mining network
                    logger.info("[MINING] Deploying mining network...")
                    mining = P2PMiningDeployment()
                    mining_success = await mining.deploy_mining_network()

                    if mining_success:
                        logger.info("[MINING] Mining network deployed successfully")
                    else:
                        logger.warning("[MINING] Mining network deployment failed")

                    # Keep running
                    logger.info("[BLOCKCHAIN] Blockchain network is running...")
                    while True:
                        await asyncio.sleep(60)
                else:
                    logger.error("[P2P] P2P network deployment failed")
            
            # Run blockchain in new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(deploy_blockchain())
            
        except Exception as e:
            logger.error(f"Blockchain startup error: {e}")
    
    # Start blockchain in background thread
    blockchain_thread = threading.Thread(target=run_blockchain, daemon=True)
    blockchain_thread.start()
    logger.info("[BLOCKCHAIN] Blockchain network startup initiated")

    # Give blockchain time to start
    time.sleep(3)

def start_web_application():
    """Start the Flask web application."""
    try:
        logger.info("[WEB] Starting web application...")
        from web.app import create_app

        app = create_app()

        # Set blockchain status flags
        app.blockchain_running = True
        app.mining_running = True

        logger.info("[WEB] ONNYX Platform is ready!")
        logger.info("[WEB] Web interface: http://localhost:5000")
        logger.info("[WEB] Admin access: Create account via Eden Mode")
        logger.info("[WEB] Press Ctrl+C to stop")

        # Start Flask app
        app.run(
            debug=False,  # Set to False for production
            host='0.0.0.0',
            port=5000,
            threaded=True
        )

    except Exception as e:
        logger.error(f"[WEB] Web application startup failed: {e}")
        return False

def main():
    """Main startup sequence."""
    logger.info("[STARTUP] STARTING ONNYX PLATFORM")
    logger.info("=" * 50)

    # Step 1: Initialize database
    if not initialize_database():
        logger.error("[ERROR] Startup failed: Database initialization error")
        return 1

    # Step 2: Start blockchain network
    start_blockchain_network()

    # Step 3: Start web application
    try:
        start_web_application()
    except KeyboardInterrupt:
        logger.info("[SHUTDOWN] Shutdown requested by user")
    except Exception as e:
        logger.error(f"[ERROR] Startup error: {e}")
        return 1

    logger.info("[SHUTDOWN] ONNYX Platform stopped")
    return 0

if __name__ == '__main__':
    sys.exit(main())
