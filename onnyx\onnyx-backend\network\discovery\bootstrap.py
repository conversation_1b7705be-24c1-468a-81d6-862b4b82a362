"""
ONNYX Bootstrap Node Discovery
DHT-based peer discovery system for the ONNYX covenant blockchain network
"""

import asyncio
import json
import time
import hashlib
import logging
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

@dataclass
class BootstrapPeer:
    peer_id: str
    address: str
    port: int
    node_type: str
    tribal_code: Optional[str]
    last_seen: int
    reputation: int = 100
    covenant_tier: int = 0

    def to_dict(self):
        return asdict(self)

class BootstrapNode:
    """
    Bootstrap node for ONNYX covenant network peer discovery
    Implements a simplified DHT for biblical blockchain network
    """

    def __init__(self, node_id: str, port: int = 8766):
        self.node_id = node_id
        self.port = port
        self.peers: Dict[str, BootstrapPeer] = {}
        self.tribal_elders: Dict[str, BootstrapPeer] = {}
        self.running = False

        # Biblical network parameters
        self.max_peers_per_tribe = 12  # 12 members per tribe
        self.peer_timeout = 300  # 5 minutes
        self.cleanup_interval = 60  # 1 minute

        # Genesis bootstrap peers (tribal elders)
        self.genesis_elders = [
            ("JU", "127.0.0.1", 8767),  # <PERSON>
            ("LE", "127.0.0.1", 8768),  # Levi
            ("EP", "127.0.0.1", 8769),  # Ephraim
            ("BE", "127.0.0.1", 8770),  # Benjamin
            ("SI", "127.0.0.1", 8771),  # Simeon
            ("MA", "127.0.0.1", 8772),  # Manasseh
            ("IS", "127.0.0.1", 8773),  # Issachar
            ("ZE", "127.0.0.1", 8774),  # Zebulun
            ("NA", "127.0.0.1", 8775),  # Naphtali
            ("GA", "127.0.0.1", 8776),  # Gad
            ("AS", "127.0.0.1", 8777),  # Asher
            ("RE", "127.0.0.1", 8778),  # Reuben
        ]

    async def start(self):
        """Start the bootstrap node service"""
        try:
            import websockets

            self.server = await websockets.serve(
                self._handle_discovery_request,
                "0.0.0.0",
                self.port
            )

            self.running = True
            logger.info(f"Bootstrap node started on port {self.port}")

            # Start background tasks
            asyncio.create_task(self._peer_cleanup_loop())
            asyncio.create_task(self._tribal_elder_monitoring())

            # Note: Genesis elders will be registered as they come online
            # No longer pre-populating to avoid connection attempts to non-existent nodes
            logger.info("Bootstrap node ready - tribal elders will register as they start")

        except Exception as e:
            logger.error(f"Failed to start bootstrap node: {e}")
            raise

    async def stop(self):
        """Stop the bootstrap node service"""
        self.running = False
        if hasattr(self, 'server'):
            self.server.close()
            await self.server.wait_closed()
        logger.info("Bootstrap node stopped")

    async def _handle_discovery_request(self, websocket, path=None):
        """Handle peer discovery requests"""
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    request_type = data.get('type')

                    if request_type == 'register_peer':
                        response = await self._handle_peer_registration(data)
                    elif request_type == 'discover_peers':
                        response = await self._handle_peer_discovery(data)
                    elif request_type == 'update_status':
                        response = await self._handle_status_update(data)
                    elif request_type == 'get_tribal_elders':
                        response = await self._handle_tribal_elder_request(data)
                    else:
                        response = {"error": f"Unknown request type: {request_type}"}

                    await websocket.send(json.dumps(response))

                except json.JSONDecodeError:
                    await websocket.send(json.dumps({"error": "Invalid JSON"}))
                except Exception as e:
                    logger.error(f"Error handling discovery request: {e}")
                    await websocket.send(json.dumps({"error": str(e)}))

        except Exception as e:
            logger.error(f"Discovery request handler error: {e}")

    async def _handle_peer_registration(self, data: dict) -> dict:
        """Handle peer registration requests"""
        try:
            peer_info = data.get('peer_info', {})
            peer_id = peer_info.get('peer_id')

            if not peer_id:
                return {"error": "Missing peer_id"}

            # Create bootstrap peer record
            bootstrap_peer = BootstrapPeer(
                peer_id=peer_id,
                address=peer_info.get('address', ''),
                port=peer_info.get('port', 8765),
                node_type=peer_info.get('node_type', 'full_node'),
                tribal_code=peer_info.get('tribal_code'),
                last_seen=int(time.time()),
                reputation=peer_info.get('reputation', 100),
                covenant_tier=peer_info.get('covenant_tier', 0)
            )

            # Store peer
            self.peers[peer_id] = bootstrap_peer

            # If tribal elder, store separately
            if bootstrap_peer.node_type == 'tribal_elder' and bootstrap_peer.tribal_code:
                self.tribal_elders[bootstrap_peer.tribal_code] = bootstrap_peer

            logger.info(f"Registered peer {peer_id} ({bootstrap_peer.node_type})")

            return {
                "success": True,
                "peer_id": peer_id,
                "registered_at": bootstrap_peer.last_seen,
                "network_stats": self._get_network_stats()
            }

        except Exception as e:
            logger.error(f"Peer registration error: {e}")
            return {"error": str(e)}

    async def _handle_peer_discovery(self, data: dict) -> dict:
        """Handle peer discovery requests"""
        try:
            requester_id = data.get('requester_id')
            node_type = data.get('node_type', 'full_node')
            tribal_preference = data.get('tribal_preference')
            max_peers = data.get('max_peers', 10)

            # Get suitable peers
            suitable_peers = self._find_suitable_peers(
                requester_id, node_type, tribal_preference, max_peers
            )

            return {
                "success": True,
                "peers": [peer.to_dict() for peer in suitable_peers],
                "total_network_peers": len(self.peers),
                "tribal_elders_online": len(self.tribal_elders)
            }

        except Exception as e:
            logger.error(f"Peer discovery error: {e}")
            return {"error": str(e)}

    async def _handle_status_update(self, data: dict) -> dict:
        """Handle peer status updates"""
        try:
            peer_id = data.get('peer_id')

            if peer_id in self.peers:
                self.peers[peer_id].last_seen = int(time.time())
                self.peers[peer_id].reputation = data.get('reputation', self.peers[peer_id].reputation)

                return {"success": True, "updated_at": self.peers[peer_id].last_seen}
            else:
                return {"error": "Peer not found"}

        except Exception as e:
            logger.error(f"Status update error: {e}")
            return {"error": str(e)}

    async def _handle_tribal_elder_request(self, data: dict) -> dict:
        """Handle requests for tribal elder information"""
        try:
            tribal_code = data.get('tribal_code')

            if tribal_code:
                # Return specific tribal elder
                elder = self.tribal_elders.get(tribal_code)
                if elder:
                    return {"success": True, "elder": elder.to_dict()}
                else:
                    return {"error": f"Tribal elder {tribal_code} not found"}
            else:
                # Return all tribal elders
                return {
                    "success": True,
                    "elders": {code: elder.to_dict() for code, elder in self.tribal_elders.items()},
                    "total_elders": len(self.tribal_elders)
                }

        except Exception as e:
            logger.error(f"Tribal elder request error: {e}")
            return {"error": str(e)}

    def _find_suitable_peers(self, requester_id: str, node_type: str,
                           tribal_preference: Optional[str], max_peers: int) -> List[BootstrapPeer]:
        """Find suitable peers for connection"""
        current_time = int(time.time())
        suitable_peers = []

        # Filter active peers
        active_peers = [
            peer for peer in self.peers.values()
            if peer.peer_id != requester_id and
            current_time - peer.last_seen < self.peer_timeout
        ]

        # Prioritize tribal elders for governance nodes
        if node_type in ['tribal_elder', 'community_light']:
            tribal_elders = [peer for peer in active_peers if peer.node_type == 'tribal_elder']
            suitable_peers.extend(tribal_elders[:max_peers // 2])

        # Add peers from preferred tribe
        if tribal_preference:
            tribal_peers = [
                peer for peer in active_peers
                if peer.tribal_code == tribal_preference and peer not in suitable_peers
            ]
            suitable_peers.extend(tribal_peers[:max_peers // 4])

        # Fill remaining slots with diverse peers
        remaining_slots = max_peers - len(suitable_peers)
        other_peers = [peer for peer in active_peers if peer not in suitable_peers]

        # Sort by reputation and diversity
        other_peers.sort(key=lambda p: (-p.reputation, p.tribal_code or ''))
        suitable_peers.extend(other_peers[:remaining_slots])

        return suitable_peers[:max_peers]

    async def _initialize_genesis_elders(self):
        """Initialize genesis tribal elders as bootstrap peers"""
        # No longer pre-populating elders to avoid connection attempts to non-existent nodes
        # Elders will register themselves when they come online
        logger.info("Genesis elders will register as they start up")

    async def _peer_cleanup_loop(self):
        """Remove stale peers periodically"""
        while self.running:
            try:
                current_time = int(time.time())
                stale_peers = []

                for peer_id, peer in self.peers.items():
                    if current_time - peer.last_seen > self.peer_timeout:
                        stale_peers.append(peer_id)

                for peer_id in stale_peers:
                    peer = self.peers[peer_id]

                    # Remove from tribal elders if applicable
                    if peer.tribal_code and peer.tribal_code in self.tribal_elders:
                        if self.tribal_elders[peer.tribal_code].peer_id == peer_id:
                            del self.tribal_elders[peer.tribal_code]

                    del self.peers[peer_id]
                    logger.info(f"Removed stale peer {peer_id}")

                await asyncio.sleep(self.cleanup_interval)

            except Exception as e:
                logger.error(f"Peer cleanup error: {e}")
                await asyncio.sleep(self.cleanup_interval)

    async def _tribal_elder_monitoring(self):
        """Monitor tribal elder availability"""
        while self.running:
            try:
                missing_tribes = []

                # Check which tribes are missing elders
                all_tribes = {"JU", "LE", "EP", "BE", "SI", "MA", "IS", "ZE", "NA", "GA", "AS", "RE"}
                active_tribes = set(self.tribal_elders.keys())
                missing_tribes = all_tribes - active_tribes

                if missing_tribes:
                    logger.warning(f"Missing tribal elders: {missing_tribes}")

                # Check elder health
                current_time = int(time.time())
                unhealthy_elders = []

                for tribal_code, elder in self.tribal_elders.items():
                    if current_time - elder.last_seen > self.peer_timeout // 2:
                        unhealthy_elders.append(tribal_code)

                if unhealthy_elders:
                    logger.warning(f"Unhealthy tribal elders: {unhealthy_elders}")

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Tribal elder monitoring error: {e}")
                await asyncio.sleep(60)

    def _get_network_stats(self) -> dict:
        """Get current network statistics"""
        current_time = int(time.time())
        active_peers = [
            peer for peer in self.peers.values()
            if current_time - peer.last_seen < self.peer_timeout
        ]

        node_types = {}
        tribal_distribution = {}

        for peer in active_peers:
            # Count by node type
            node_types[peer.node_type] = node_types.get(peer.node_type, 0) + 1

            # Count by tribe
            if peer.tribal_code:
                tribal_distribution[peer.tribal_code] = tribal_distribution.get(peer.tribal_code, 0) + 1

        return {
            "total_peers": len(active_peers),
            "tribal_elders": len(self.tribal_elders),
            "node_types": node_types,
            "tribal_distribution": tribal_distribution,
            "network_health": "healthy" if len(self.tribal_elders) >= 8 else "degraded"
        }

    # Public API Methods
    def get_network_status(self) -> dict:
        """Get comprehensive network status"""
        return {
            "bootstrap_node_id": self.node_id,
            "uptime": int(time.time()),
            "stats": self._get_network_stats(),
            "tribal_elders": {code: elder.to_dict() for code, elder in self.tribal_elders.items()},
            "total_registered_peers": len(self.peers)
        }

    async def register_peer_direct(self, peer_info: dict) -> bool:
        """Direct peer registration (for testing)"""
        try:
            result = await self._handle_peer_registration({"peer_info": peer_info})
            return result.get("success", False)
        except Exception as e:
            logger.error(f"Direct peer registration error: {e}")
            return False

    async def discover_peers_direct(self, requester_id: str, node_type: str = "full_node",
                                  max_peers: int = 10) -> List[dict]:
        """Direct peer discovery (for testing)"""
        try:
            result = await self._handle_peer_discovery({
                "requester_id": requester_id,
                "node_type": node_type,
                "max_peers": max_peers
            })
            return result.get("peers", [])
        except Exception as e:
            logger.error(f"Direct peer discovery error: {e}")
            return []

class DiscoveryClient:
    """Client for connecting to bootstrap nodes"""

    def __init__(self, bootstrap_addresses: List[Tuple[str, int]]):
        self.bootstrap_addresses = bootstrap_addresses
        self.discovered_peers: Dict[str, dict] = {}

    async def register_with_bootstrap(self, peer_info: dict) -> bool:
        """Register with bootstrap nodes"""
        success_count = 0

        for address, port in self.bootstrap_addresses:
            try:
                import websockets
                uri = f"ws://{address}:{port}"

                async with websockets.connect(uri) as websocket:
                    request = {
                        "type": "register_peer",
                        "peer_info": peer_info
                    }

                    await websocket.send(json.dumps(request))
                    response = json.loads(await websocket.recv())

                    if response.get("success"):
                        success_count += 1
                        logger.info(f"Registered with bootstrap node {address}:{port}")
                    else:
                        logger.warning(f"Registration failed with {address}:{port}: {response.get('error')}")

            except Exception as e:
                logger.error(f"Failed to register with bootstrap {address}:{port}: {e}")

        return success_count > 0

    async def discover_peers(self, requester_id: str, node_type: str = "full_node",
                           max_peers: int = 10) -> List[dict]:
        """Discover peers from bootstrap nodes"""
        all_peers = []

        for address, port in self.bootstrap_addresses:
            try:
                import websockets
                uri = f"ws://{address}:{port}"

                async with websockets.connect(uri) as websocket:
                    request = {
                        "type": "discover_peers",
                        "requester_id": requester_id,
                        "node_type": node_type,
                        "max_peers": max_peers
                    }

                    await websocket.send(json.dumps(request))
                    response = json.loads(await websocket.recv())

                    if response.get("success"):
                        peers = response.get("peers", [])
                        all_peers.extend(peers)
                        logger.info(f"Discovered {len(peers)} peers from {address}:{port}")
                    else:
                        logger.warning(f"Discovery failed with {address}:{port}: {response.get('error')}")

            except Exception as e:
                logger.error(f"Failed to discover from bootstrap {address}:{port}: {e}")

        # Remove duplicates and return
        unique_peers = {}
        for peer in all_peers:
            unique_peers[peer['peer_id']] = peer

        return list(unique_peers.values())[:max_peers]

    async def get_tribal_elders(self) -> Dict[str, dict]:
        """Get tribal elder information from bootstrap nodes"""
        for address, port in self.bootstrap_addresses:
            try:
                import websockets
                uri = f"ws://{address}:{port}"

                async with websockets.connect(uri) as websocket:
                    request = {"type": "get_tribal_elders"}

                    await websocket.send(json.dumps(request))
                    response = json.loads(await websocket.recv())

                    if response.get("success"):
                        return response.get("elders", {})

            except Exception as e:
                logger.error(f"Failed to get tribal elders from {address}:{port}: {e}")

        return {}
