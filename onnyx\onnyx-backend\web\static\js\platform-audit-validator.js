/**
 * ONNYX Platform Audit Validator
 * Comprehensive testing suite for production readiness
 */

class PlatformAuditValidator {
    constructor() {
        this.results = {
            homepage: {},
            navigation: {},
            authentication: {},
            userJourney: {},
            performance: {},
            accessibility: {},
            conversion: {}
        };
        
        this.init();
    }

    init() {
        console.log('🔍 ONNYX Platform Audit Starting...');
        
        this.validateHomepage();
        this.validateNavigation();
        this.validateAuthentication();
        this.validateUserJourney();
        this.validatePerformance();
        this.validateAccessibility();
        this.validateConversion();
        this.generateAuditReport();
    }

    /**
     * Validate homepage value proposition and messaging
     */
    validateHomepage() {
        console.log('🏠 Validating Homepage...');
        
        // Check for key value proposition elements
        const valueProposition = document.querySelector('h2:contains("Reclaim Your Digital Identity")');
        this.results.homepage.valueProposition = !!valueProposition;
        
        // Check for value proposition cards
        const valueCards = document.querySelectorAll('.glass-card h3');
        this.results.homepage.valueCards = valueCards.length >= 3;
        
        // Check for user journey section
        const userJourney = document.querySelector('h2:contains("Your Path to Digital Freedom")');
        this.results.homepage.userJourney = !!userJourney;
        
        // Check for social proof
        const socialProof = document.querySelector('.text-cyber-cyan:contains("+")');
        this.results.homepage.socialProof = !!socialProof;
        
        // Check for clear CTAs
        const ctaButtons = document.querySelectorAll('.glass-button-primary');
        this.results.homepage.clearCTAs = ctaButtons.length >= 2;
        
        console.log('✅ Homepage validation complete');
    }

    /**
     * Validate navigation functionality
     */
    validateNavigation() {
        console.log('🧭 Validating Navigation...');
        
        // Check for floating auth orb
        const floatingOrb = document.querySelector('.floating-auth-orb');
        this.results.navigation.floatingOrb = !!floatingOrb;
        
        // Check for active navigation states
        const activeNavItems = document.querySelectorAll('.nav-link.active');
        this.results.navigation.activeStates = activeNavItems.length > 0;
        
        // Check for mobile menu functionality
        const mobileMenu = document.querySelector('#mobile-menu-overlay');
        this.results.navigation.mobileMenu = !!mobileMenu;
        
        // Check for sticky navigation
        const navbar = document.querySelector('.onnyx-navbar');
        const isSticky = navbar && getComputedStyle(navbar).position === 'sticky';
        this.results.navigation.stickyNav = isSticky;
        
        console.log('✅ Navigation validation complete');
    }

    /**
     * Validate authentication flow
     */
    validateAuthentication() {
        console.log('🔐 Validating Authentication...');
        
        // Check for floating orb menu options
        const orbOptions = document.querySelectorAll('.orb-option');
        this.results.authentication.orbOptions = orbOptions.length >= 2;
        
        // Check for clear registration paths
        const registrationPaths = document.querySelectorAll('a[href*="register"]');
        this.results.authentication.registrationPaths = registrationPaths.length >= 2;
        
        // Check for security messaging
        const securityBadge = document.querySelector('.orb-security-badge');
        this.results.authentication.securityMessaging = !!securityBadge;
        
        console.log('✅ Authentication validation complete');
    }

    /**
     * Validate user journey and onboarding
     */
    validateUserJourney() {
        console.log('🛤️ Validating User Journey...');
        
        // Check for step-by-step guidance
        const journeySteps = document.querySelectorAll('.glass-card .absolute');
        this.results.userJourney.stepByStep = journeySteps.length >= 3;
        
        // Check for progressive disclosure
        const conditionalContent = document.querySelectorAll('[class*="current_user"]');
        this.results.userJourney.progressiveDisclosure = conditionalContent.length > 0;
        
        // Check for clear next steps
        const nextStepButtons = document.querySelectorAll('.glass-button-primary, .glass-button');
        this.results.userJourney.clearNextSteps = nextStepButtons.length >= 3;
        
        console.log('✅ User Journey validation complete');
    }

    /**
     * Validate performance metrics
     */
    validatePerformance() {
        console.log('⚡ Validating Performance...');
        
        // Check page load time
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        this.results.performance.loadTime = loadTime;
        this.results.performance.fastLoad = loadTime < 3000; // Under 3 seconds
        
        // Check for optimized images
        const images = document.querySelectorAll('img');
        let optimizedImages = 0;
        images.forEach(img => {
            if (img.loading === 'lazy' || img.style.filter) {
                optimizedImages++;
            }
        });
        this.results.performance.optimizedImages = optimizedImages / images.length;
        
        // Check for CSS animations performance
        const animatedElements = document.querySelectorAll('[class*="animate-"], [class*="transition-"]');
        this.results.performance.animations = animatedElements.length;
        
        console.log('✅ Performance validation complete');
    }

    /**
     * Validate accessibility compliance
     */
    validateAccessibility() {
        console.log('♿ Validating Accessibility...');
        
        // Check for ARIA labels
        const ariaLabels = document.querySelectorAll('[aria-label]');
        this.results.accessibility.ariaLabels = ariaLabels.length;
        
        // Check for semantic HTML
        const semanticElements = document.querySelectorAll('nav, main, section, article, header, footer');
        this.results.accessibility.semanticHTML = semanticElements.length >= 3;
        
        // Check for focus indicators
        const focusableElements = document.querySelectorAll('a, button, input, select, textarea');
        this.results.accessibility.focusableElements = focusableElements.length;
        
        // Check for reduced motion support
        const reducedMotionCSS = this.checkForReducedMotionCSS();
        this.results.accessibility.reducedMotion = reducedMotionCSS;
        
        console.log('✅ Accessibility validation complete');
    }

    /**
     * Validate conversion optimization
     */
    validateConversion() {
        console.log('📈 Validating Conversion...');
        
        // Check for multiple CTAs
        const ctaButtons = document.querySelectorAll('.glass-button-primary, .glass-button');
        this.results.conversion.multipleCTAs = ctaButtons.length >= 5;
        
        // Check for urgency/scarcity messaging
        const urgencyText = document.body.textContent.toLowerCase();
        const hasUrgency = urgencyText.includes('limited') || urgencyText.includes('early') || urgencyText.includes('exclusive');
        this.results.conversion.urgencyMessaging = hasUrgency;
        
        // Check for benefit-focused copy
        const benefitKeywords = ['own', 'control', 'earn', 'secure', 'verified', 'transparent'];
        let benefitCount = 0;
        benefitKeywords.forEach(keyword => {
            if (urgencyText.includes(keyword)) benefitCount++;
        });
        this.results.conversion.benefitFocused = benefitCount >= 4;
        
        // Check for social proof elements
        const socialProofElements = document.querySelectorAll('[class*="stat"], .text-cyber-cyan:contains("+")');
        this.results.conversion.socialProof = socialProofElements.length >= 4;
        
        console.log('✅ Conversion validation complete');
    }

    /**
     * Check for reduced motion CSS support
     */
    checkForReducedMotionCSS() {
        const stylesheets = Array.from(document.styleSheets);
        for (let sheet of stylesheets) {
            try {
                const rules = Array.from(sheet.cssRules || sheet.rules || []);
                for (let rule of rules) {
                    if (rule.media && rule.media.mediaText.includes('prefers-reduced-motion')) {
                        return true;
                    }
                }
            } catch (e) {
                // Cross-origin stylesheet, skip
            }
        }
        return false;
    }

    /**
     * Generate comprehensive audit report
     */
    generateAuditReport() {
        console.log('\n📊 ONNYX Platform Audit Report');
        console.log('=====================================');
        
        // Homepage Report
        console.log('\n🏠 Homepage:');
        console.log(`  ✅ Value Proposition: ${this.results.homepage.valueProposition ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Value Cards: ${this.results.homepage.valueCards ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ User Journey: ${this.results.homepage.userJourney ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Social Proof: ${this.results.homepage.socialProof ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Clear CTAs: ${this.results.homepage.clearCTAs ? 'PASS' : 'FAIL'}`);
        
        // Navigation Report
        console.log('\n🧭 Navigation:');
        console.log(`  ✅ Floating Orb: ${this.results.navigation.floatingOrb ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Active States: ${this.results.navigation.activeStates ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Mobile Menu: ${this.results.navigation.mobileMenu ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Sticky Nav: ${this.results.navigation.stickyNav ? 'PASS' : 'FAIL'}`);
        
        // Authentication Report
        console.log('\n🔐 Authentication:');
        console.log(`  ✅ Orb Options: ${this.results.authentication.orbOptions ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Registration Paths: ${this.results.authentication.registrationPaths ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Security Messaging: ${this.results.authentication.securityMessaging ? 'PASS' : 'FAIL'}`);
        
        // User Journey Report
        console.log('\n🛤️ User Journey:');
        console.log(`  ✅ Step-by-Step: ${this.results.userJourney.stepByStep ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Progressive Disclosure: ${this.results.userJourney.progressiveDisclosure ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Clear Next Steps: ${this.results.userJourney.clearNextSteps ? 'PASS' : 'FAIL'}`);
        
        // Performance Report
        console.log('\n⚡ Performance:');
        console.log(`  ✅ Load Time: ${this.results.performance.loadTime}ms (${this.results.performance.fastLoad ? 'PASS' : 'FAIL'})`);
        console.log(`  ✅ Optimized Images: ${(this.results.performance.optimizedImages * 100).toFixed(1)}%`);
        console.log(`  ✅ Animations: ${this.results.performance.animations} elements`);
        
        // Accessibility Report
        console.log('\n♿ Accessibility:');
        console.log(`  ✅ ARIA Labels: ${this.results.accessibility.ariaLabels} elements`);
        console.log(`  ✅ Semantic HTML: ${this.results.accessibility.semanticHTML ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Focusable Elements: ${this.results.accessibility.focusableElements} elements`);
        console.log(`  ✅ Reduced Motion: ${this.results.accessibility.reducedMotion ? 'PASS' : 'FAIL'}`);
        
        // Conversion Report
        console.log('\n📈 Conversion:');
        console.log(`  ✅ Multiple CTAs: ${this.results.conversion.multipleCTAs ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Urgency Messaging: ${this.results.conversion.urgencyMessaging ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Benefit-Focused: ${this.results.conversion.benefitFocused ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Social Proof: ${this.results.conversion.socialProof ? 'PASS' : 'FAIL'}`);
        
        // Overall Score
        const overallScore = this.calculateOverallScore();
        console.log(`\n🎯 Overall Production Readiness: ${overallScore.toFixed(1)}%`);
        
        if (overallScore >= 90) {
            console.log('🚀 EXCELLENT! Ready for production launch.');
        } else if (overallScore >= 80) {
            console.log('✅ GOOD! Minor optimizations recommended.');
        } else if (overallScore >= 70) {
            console.log('⚠️  NEEDS IMPROVEMENT! Address failing areas.');
        } else {
            console.log('❌ NOT READY! Significant work required.');
        }
        
        console.log('\n=====================================');
    }

    /**
     * Calculate overall production readiness score
     */
    calculateOverallScore() {
        const scores = [];
        
        // Homepage score (25%)
        const homepageScore = Object.values(this.results.homepage)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 5 * 100;
        scores.push(homepageScore * 0.25);
        
        // Navigation score (20%)
        const navScore = Object.values(this.results.navigation)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 4 * 100;
        scores.push(navScore * 0.20);
        
        // Authentication score (15%)
        const authScore = Object.values(this.results.authentication)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 3 * 100;
        scores.push(authScore * 0.15);
        
        // User Journey score (15%)
        const journeyScore = Object.values(this.results.userJourney)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 3 * 100;
        scores.push(journeyScore * 0.15);
        
        // Performance score (10%)
        const perfScore = (this.results.performance.fastLoad ? 50 : 0) +
                         (this.results.performance.optimizedImages * 50);
        scores.push(perfScore * 0.10);
        
        // Accessibility score (10%)
        const accessScore = (this.results.accessibility.semanticHTML ? 50 : 0) +
                           (this.results.accessibility.reducedMotion ? 50 : 0);
        scores.push(accessScore * 0.10);
        
        // Conversion score (5%)
        const convScore = Object.values(this.results.conversion)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 4 * 100;
        scores.push(convScore * 0.05);
        
        return scores.reduce((acc, score) => acc + score, 0);
    }
}

// Run audit when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Only run when explicitly requested or in development
    if (window.location.search.includes('audit=true') || 
        document.body.dataset.environment === 'development') {
        new PlatformAuditValidator();
    }
});

// Export for manual testing
window.PlatformAuditValidator = PlatformAuditValidator;
