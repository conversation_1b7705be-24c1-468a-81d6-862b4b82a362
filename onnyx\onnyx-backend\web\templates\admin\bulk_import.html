{% extends "base.html" %}

{% block title %}Bulk Import - Admin Panel | ONNYX{% endblock %}

{% block head %}
<style>
.admin-warning {
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.1), rgba(255, 69, 0, 0.1));
    border: 1px solid rgba(255, 165, 0, 0.3);
}

.import-card {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    border: 1px solid rgba(0, 255, 247, 0.2);
    transition: all 0.3s ease;
}

.import-card:hover {
    border-color: rgba(0, 255, 247, 0.4);
    transform: translateY(-2px);
}

.format-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.drop-zone {
    border: 2px dashed rgba(0, 255, 247, 0.3);
    background: rgba(0, 255, 247, 0.05);
    transition: all 0.3s ease;
}

.drop-zone.dragover {
    border-color: var(--cyber-cyan);
    background: rgba(0, 255, 247, 0.1);
}

.progress-bar {
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    height: 4px;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.sample-data {
    background: var(--dark-800);
    border: 1px solid var(--glass-border);
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

.error-list {
    background: rgba(255, 69, 0, 0.1);
    border: 1px solid rgba(255, 69, 0, 0.3);
    border-radius: 0.5rem;
    padding: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.success-message {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 0.5rem;
    padding: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Admin Header -->
        <div class="admin-warning p-4 rounded-xl mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl">📊</div>
                    <div>
                        <h1 class="text-2xl font-orbitron font-bold text-orange-400">Bulk Import System</h1>
                        <p class="text-orange-300">Import multiple records from CSV or JSON files</p>
                    </div>
                </div>
                <a href="{{ url_for('admin.dashboard') }}" class="glass-button-secondary px-4 py-2 rounded-lg">
                    ← Back to Admin Dashboard
                </a>
            </div>
        </div>

        <!-- Import Form -->
        <div class="import-card p-6 rounded-xl mb-8">
            <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">Import Data</h2>
            
            <form id="importForm" enctype="multipart/form-data">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Import Type -->
                    <div>
                        <label class="block text-sm font-medium text-secondary mb-2">Import Type</label>
                        <select id="importType" name="import_type" required class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                            <option value="">Select Import Type</option>
                            <option value="identities">Identities (Users)</option>
                            <option value="selas">Selas (Businesses)</option>
                            <option value="tribal_elders">Tribal Elders</option>
                        </select>
                    </div>
                    
                    <!-- Data Format -->
                    <div>
                        <label class="block text-sm font-medium text-secondary mb-2">Data Format</label>
                        <select id="dataFormat" name="data_format" required class="w-full p-3 bg-dark-800 border border-glass-border rounded-lg text-white">
                            <option value="">Select Format</option>
                            <option value="csv">CSV (Comma Separated Values)</option>
                            <option value="json">JSON (JavaScript Object Notation)</option>
                        </select>
                    </div>
                </div>
                
                <!-- File Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-secondary mb-2">Upload File</label>
                    <div id="dropZone" class="drop-zone p-8 rounded-lg text-center cursor-pointer">
                        <div class="text-4xl mb-4">📁</div>
                        <p class="text-lg text-cyber-cyan mb-2">Drop your file here or click to browse</p>
                        <p class="text-sm text-secondary">Supports CSV and JSON files up to 10MB</p>
                        <input type="file" id="fileInput" name="file" accept=".csv,.json" class="hidden">
                    </div>
                    <div id="fileInfo" class="mt-2 text-sm text-secondary hidden"></div>
                </div>
                
                <!-- Progress Bar -->
                <div id="progressContainer" class="mb-6 hidden">
                    <div class="flex justify-between text-sm text-secondary mb-2">
                        <span>Importing...</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="w-full bg-dark-700 rounded-full h-2">
                        <div id="progressBar" class="progress-bar w-0"></div>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="flex space-x-4">
                    <button type="submit" id="submitBtn" class="flex-1 glass-button-primary py-3 rounded-lg" disabled>
                        Import Data
                    </button>
                    <button type="button" onclick="resetForm()" class="glass-button-secondary px-6 py-3 rounded-lg">
                        Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- Sample Data Templates -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Identities Sample -->
            <div class="import-card p-6 rounded-xl">
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">Identities Sample</h3>
                <div class="format-badge mb-4">CSV Format</div>
                <div class="sample-data">
name,email,role,nation_code,nation_name
John Doe,<EMAIL>,citizen,ISR,Israel
Jane Smith,<EMAIL>,validator,JU,Judah
                </div>
                <div class="format-badge mt-4 mb-4">JSON Format</div>
                <div class="sample-data">
[
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "citizen",
    "nation_code": "ISR",
    "nation_name": "Israel"
  }
]
                </div>
            </div>
            
            <!-- Selas Sample -->
            <div class="import-card p-6 rounded-xl">
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">Selas Sample</h3>
                <div class="format-badge mb-4">CSV Format</div>
                <div class="sample-data">
name,identity_id,category,description
Tech Solutions,ONX123456,technology,Software development
Food Market,ONX789012,retail,Organic food store
                </div>
                <div class="format-badge mt-4 mb-4">JSON Format</div>
                <div class="sample-data">
[
  {
    "name": "Tech Solutions",
    "identity_id": "ONX123456",
    "category": "technology",
    "description": "Software development"
  }
]
                </div>
            </div>
            
            <!-- Tribal Elders Sample -->
            <div class="import-card p-6 rounded-xl">
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">Tribal Elders Sample</h3>
                <div class="format-badge mb-4">CSV Format</div>
                <div class="sample-data">
name,email,tribe_code,governance_role
Elder Benjamin,<EMAIL>,BE,Judge
Elder Judah,<EMAIL>,JU,High Priest
                </div>
                <div class="format-badge mt-4 mb-4">JSON Format</div>
                <div class="sample-data">
[
  {
    "name": "Elder Benjamin",
    "email": "<EMAIL>",
    "tribe_code": "BE",
    "governance_role": "Judge"
  }
]
                </div>
            </div>
        </div>

        <!-- Results -->
        <div id="resultsContainer" class="hidden">
            <div id="successMessage" class="success-message mb-6 hidden">
                <h3 class="text-lg font-bold text-green-400 mb-2">Import Successful!</h3>
                <p id="successText"></p>
            </div>
            
            <div id="errorContainer" class="error-list hidden">
                <h3 class="text-lg font-bold text-red-400 mb-2">Import Errors</h3>
                <ul id="errorList" class="list-disc list-inside space-y-1"></ul>
            </div>
        </div>
    </div>
</div>

<script>
// File upload handling
const dropZone = document.getElementById('dropZone');
const fileInput = document.getElementById('fileInput');
const fileInfo = document.getElementById('fileInfo');
const submitBtn = document.getElementById('submitBtn');
const importType = document.getElementById('importType');
const dataFormat = document.getElementById('dataFormat');

// Drop zone events
dropZone.addEventListener('click', () => fileInput.click());
dropZone.addEventListener('dragover', (e) => {
    e.preventDefault();
    dropZone.classList.add('dragover');
});
dropZone.addEventListener('dragleave', () => {
    dropZone.classList.remove('dragover');
});
dropZone.addEventListener('drop', (e) => {
    e.preventDefault();
    dropZone.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
    }
});

fileInput.addEventListener('change', handleFileSelect);

function handleFileSelect() {
    const file = fileInput.files[0];
    if (file) {
        fileInfo.textContent = `Selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
        fileInfo.classList.remove('hidden');
        checkFormValid();
    }
}

function checkFormValid() {
    const hasFile = fileInput.files.length > 0;
    const hasType = importType.value !== '';
    const hasFormat = dataFormat.value !== '';
    
    submitBtn.disabled = !(hasFile && hasType && hasFormat);
}

importType.addEventListener('change', checkFormValid);
dataFormat.addEventListener('change', checkFormValid);

// Form submission
document.getElementById('importForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    // Show progress
    document.getElementById('progressContainer').classList.remove('hidden');
    document.getElementById('resultsContainer').classList.add('hidden');
    submitBtn.disabled = true;
    
    try {
        const response = await fetch('/admin/api/bulk-import', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        // Hide progress
        document.getElementById('progressContainer').classList.add('hidden');
        
        // Show results
        document.getElementById('resultsContainer').classList.remove('hidden');
        
        if (result.success) {
            document.getElementById('successMessage').classList.remove('hidden');
            document.getElementById('successText').textContent = result.message;
            
            if (result.errors && result.errors.length > 0) {
                document.getElementById('errorContainer').classList.remove('hidden');
                const errorList = document.getElementById('errorList');
                errorList.innerHTML = '';
                result.errors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });
            }
        } else {
            document.getElementById('errorContainer').classList.remove('hidden');
            const errorList = document.getElementById('errorList');
            errorList.innerHTML = `<li>${result.error}</li>`;
        }
        
    } catch (error) {
        document.getElementById('progressContainer').classList.add('hidden');
        document.getElementById('resultsContainer').classList.remove('hidden');
        document.getElementById('errorContainer').classList.remove('hidden');
        const errorList = document.getElementById('errorList');
        errorList.innerHTML = `<li>Network error: ${error.message}</li>`;
    }
    
    submitBtn.disabled = false;
});

function resetForm() {
    document.getElementById('importForm').reset();
    fileInfo.classList.add('hidden');
    document.getElementById('progressContainer').classList.add('hidden');
    document.getElementById('resultsContainer').classList.add('hidden');
    document.getElementById('successMessage').classList.add('hidden');
    document.getElementById('errorContainer').classList.add('hidden');
    submitBtn.disabled = true;
}
</script>
{% endblock %}
