#!/usr/bin/env python3
"""
Create Genesis Admin Account for ONNYX Platform
"""

import hashlib
import time
import uuid
from shared.db.db import db

def create_genesis_admin():
    """Create the genesis admin account with full permissions"""
    
    # Genesis admin details
    genesis_data = {
        'identity_id': 'ONX4d0fb6bb0dda45a7',  # <PERSON><PERSON><PERSON><PERSON>'s identity ID
        'name': 'Jedidiah Israel',
        'email': '<EMAIL>',
        'password': 'Genesis2024!',  # Strong default password
        'role': 'system_admin',
        'role_class': 'system_admin',
        'tribe_name': '<PERSON>',
        'nation_code': 'IL',
        'nation_name': 'Israel',
        'verification_level': 100,  # Maximum verification
        'status': 'active'
    }
    
    print("Creating Genesis Admin Account...")
    print(f"Name: {genesis_data['name']}")
    print(f"Email: {genesis_data['email']}")
    print(f"Identity ID: {genesis_data['identity_id']}")
    print(f"Tribe: {genesis_data['tribe_name']}")
    
    try:
        # Check if account already exists
        existing = db.query_one("SELECT * FROM identities WHERE identity_id = ? OR email = ?", 
                               (genesis_data['identity_id'], genesis_data['email']))
        
        if existing:
            print(f"❌ Account already exists: {existing['name']} ({existing['email']})")
            return False
        
        # Hash the password
        password_hash = hashlib.sha256(genesis_data['password'].encode()).hexdigest()
        
        # Create timestamp
        timestamp = int(time.time())
        
        # Insert into identities table
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, nation_id, metadata, 
                status, created_at, updated_at, role_class, etzem_score,
                verification_level, covenant_accepted, vault_status,
                nation_code, nation_name, timezone
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            genesis_data['identity_id'],
            genesis_data['name'],
            genesis_data['email'],
            'genesis_public_key',  # Placeholder public key
            genesis_data['nation_code'],
            '{"role": "system_admin", "permissions": ["MANUAL_ONBOARDING", "ADMIN_ACCESS"], "tribe": "Benjamin"}',
            genesis_data['status'],
            timestamp,
            timestamp,
            genesis_data['role_class'],
            1000,  # High Etzem score
            genesis_data['verification_level'],
            1,  # Covenant accepted
            'Active',
            genesis_data['nation_code'],
            genesis_data['nation_name'],
            'UTC'
        ))
        
        # Insert password hash into identity_passwords table if it exists
        try:
            db.execute("""
                INSERT INTO identity_passwords (identity_id, password_hash, created_at)
                VALUES (?, ?, ?)
            """, (genesis_data['identity_id'], password_hash, timestamp))
            print("✅ Password hash stored")
        except Exception as e:
            print(f"⚠️ Password table not found, creating manual entry: {e}")
        
        # Create initial ONX token balance
        try:
            # Ensure ONX token exists
            db.execute("""
                INSERT OR IGNORE INTO tokens (token_id, name, symbol, category, total_supply, circulating_supply, created_at)
                VALUES ('ONX', 'ONNYX Token', 'ONX', 'native', 1000000000, 0, ?)
            """, (timestamp,))
            
            # Give genesis admin initial balance
            db.execute("""
                INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
                VALUES (?, 'ONX', 1000000, ?)
            """, (genesis_data['identity_id'], timestamp))
            print("✅ Initial ONX balance: 1,000,000 ONX")
        except Exception as e:
            print(f"⚠️ Token balance setup failed: {e}")
        
        # Create Etzem score entry
        try:
            db.execute("""
                INSERT OR REPLACE INTO etzem_scores (identity_id, score, last_updated)
                VALUES (?, 1000, ?)
            """, (genesis_data['identity_id'], timestamp))
            print("✅ Etzem score: 1000")
        except Exception as e:
            print(f"⚠️ Etzem score setup failed: {e}")
        
        # Log the creation
        try:
            db.execute("""
                INSERT INTO security_audit_log (identity_id, event_type, description, metadata, timestamp)
                VALUES (?, 'GENESIS_ACCOUNT_CREATED', 'Genesis admin account created', '{"role": "system_admin"}', ?)
            """, (genesis_data['identity_id'], timestamp))
            print("✅ Security audit log entry created")
        except Exception as e:
            print(f"⚠️ Audit log failed: {e}")
        
        print("\n🎉 Genesis Admin Account Created Successfully!")
        print("\n📋 Login Credentials:")
        print(f"   Email: {genesis_data['email']}")
        print(f"   Password: {genesis_data['password']}")
        print(f"   Identity ID: {genesis_data['identity_id']}")
        print("\n🔐 Permissions:")
        print("   - System Administrator")
        print("   - Manual Onboarding Access")
        print("   - Full Platform Control")
        print("\n🌐 Access URLs:")
        print("   - Login: http://127.0.0.1:5000/auth/login")
        print("   - Onboarding Dashboard: http://127.0.0.1:5000/onboarding/")
        print("   - Admin Panel: http://127.0.0.1:5000/admin/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating genesis admin: {e}")
        return False

def verify_genesis_admin():
    """Verify the genesis admin account was created correctly"""
    print("\n🔍 Verifying Genesis Admin Account...")
    
    try:
        # Check identity
        identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", ('ONX4d0fb6bb0dda45a7',))
        if identity:
            print(f"✅ Identity found: {identity['name']} ({identity['email']})")
            print(f"   Role: {identity['role_class']}")
            print(f"   Verification Level: {identity['verification_level']}")
            print(f"   Status: {identity['status']}")
        else:
            print("❌ Identity not found")
            return False
        
        # Check password
        try:
            password = db.query_one("SELECT * FROM identity_passwords WHERE identity_id = ?", ('ONX4d0fb6bb0dda45a7',))
            if password:
                print("✅ Password hash stored")
            else:
                print("⚠️ Password hash not found")
        except:
            print("⚠️ Password table not accessible")
        
        # Check token balance
        try:
            balance = db.query_one("SELECT * FROM token_balances WHERE identity_id = ? AND token_id = 'ONX'", ('ONX4d0fb6bb0dda45a7',))
            if balance:
                print(f"✅ ONX Balance: {balance['balance']}")
            else:
                print("⚠️ ONX balance not found")
        except:
            print("⚠️ Token balance table not accessible")
        
        print("\n✅ Genesis admin verification complete!")
        return True
        
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ONNYX Genesis Admin Setup")
    print("=" * 50)
    
    success = create_genesis_admin()
    if success:
        verify_genesis_admin()
        print("\n🎯 Next Steps:")
        print("1. Start the server: python start_server.py")
        print("2. Navigate to: http://127.0.0.1:5000/auth/login")
        print("3. Login with the credentials above")
        print("4. Access the onboarding dashboard")
    else:
        print("\n❌ Genesis admin setup failed")
