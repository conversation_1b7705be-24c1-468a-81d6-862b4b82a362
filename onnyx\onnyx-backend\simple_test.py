#!/usr/bin/env python3
"""
Simple ONNYX User Flow Test
Tests the basic user flows to identify issues
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def test_onnyx_flows():
    print("🔍 Testing ONNYX User Flows")
    
    # Check server
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print(f"✅ Server responding: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return
    
    # Setup browser
    options = Options()
    options.add_argument("--start-maximized")
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        print("✅ Browser launched")
    except Exception as e:
        print(f"❌ Browser setup failed: {e}")
        return
    
    try:
        # Test 1: Navigate to Eden Mode
        print("\n🔹 Test 1: Navigation to Eden Mode")
        driver.get("http://localhost:5000/auth/eden-mode")
        time.sleep(3)
        
        page_title = driver.title
        page_source = driver.page_source
        current_url = driver.current_url
        
        print(f"Current URL: {current_url}")
        print(f"Page Title: {page_title}")
        
        if "eden-mode" in current_url:
            print("✅ Successfully reached Eden Mode")
        else:
            print("❌ Not on Eden Mode page")
        
        # Check for key elements
        print("\n🔹 Test 2: Looking for key elements")
        
        # Look for Israelite button
        israelite_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Israelite') or contains(@onclick, 'israelite')]")
        print(f"Found {len(israelite_buttons)} Israelite button(s)")
        
        # Look for Witness button
        witness_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Witness') or contains(@onclick, 'witness')]")
        print(f"Found {len(witness_buttons)} Witness button(s)")
        
        # Test 3: Try Israelite flow
        if israelite_buttons:
            print("\n🔹 Test 3: Testing Israelite flow")
            try:
                israelite_buttons[0].click()
                time.sleep(3)
                
                new_url = driver.current_url
                print(f"After clicking Israelite: {new_url}")
                
                if "israelite" in new_url:
                    print("✅ Israelite flow navigation works")
                    
                    # Look for tribe selection
                    tribe_elements = driver.find_elements(By.XPATH, "//button[contains(@onclick, 'selectTribe') or contains(text(), 'tribe')]")
                    print(f"Found {len(tribe_elements)} tribe selection elements")
                    
                    if tribe_elements:
                        print("✅ Tribe selection elements found")
                    else:
                        print("❌ No tribe selection found")
                        
                else:
                    print("❌ Israelite flow navigation failed")
                    
            except Exception as e:
                print(f"❌ Error in Israelite flow: {e}")
        
        # Test 4: Check for errors in console
        print("\n🔹 Test 4: Checking for JavaScript errors")
        try:
            logs = driver.get_log('browser')
            errors = [log for log in logs if log['level'] == 'SEVERE']
            if errors:
                print(f"❌ Found {len(errors)} JavaScript errors:")
                for error in errors[:3]:  # Show first 3 errors
                    print(f"  - {error['message']}")
            else:
                print("✅ No severe JavaScript errors found")
        except:
            print("⚠️ Could not check browser logs")
        
        # Test 5: Check page responsiveness
        print("\n🔹 Test 5: Testing page responsiveness")
        try:
            # Test mobile viewport
            driver.set_window_size(375, 667)  # iPhone size
            time.sleep(1)
            
            # Check if elements are still visible
            israelite_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Israelite')]")
            if israelite_buttons and israelite_buttons[0].is_displayed():
                print("✅ Mobile responsiveness: elements visible")
            else:
                print("❌ Mobile responsiveness: elements not visible")
            
            # Reset to desktop
            driver.maximize_window()
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Responsiveness test failed: {e}")
        
        print("\n🔹 Test 6: Element accessibility")
        # Check if buttons are actually clickable
        try:
            driver.get("http://localhost:5000/auth/eden-mode")
            time.sleep(2)
            
            israelite_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Israelite')]")
            if israelite_buttons:
                button = israelite_buttons[0]
                if button.is_enabled() and button.is_displayed():
                    print("✅ Israelite button is clickable")
                else:
                    print("❌ Israelite button is not properly accessible")
            
        except Exception as e:
            print(f"❌ Accessibility test failed: {e}")
        
        # Keep browser open for manual inspection
        print("\n✋ Browser will stay open for manual inspection...")
        print("Press Enter to close and continue...")
        input()
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    finally:
        driver.quit()
        print("🔚 Browser closed")

if __name__ == "__main__":
    test_onnyx_flows()
