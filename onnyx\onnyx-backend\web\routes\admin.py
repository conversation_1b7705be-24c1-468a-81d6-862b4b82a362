"""
Admin Panel Routes

Administrative interface for system management and oversight.
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from web.auth_decorators import require_admin, require_auth, get_current_user
from shared.db.db import db
import json
import time
import logging
import uuid
import hashlib

logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def log_security_event(identity_id, event_type, description, metadata=None):
    """Log security events for audit purposes"""
    try:
        db.execute("""
            INSERT INTO security_audit_log (identity_id, event_type, description, metadata, timestamp)
            VALUES (?, ?, ?, ?, ?)
        """, (identity_id, event_type, description, json.dumps(metadata or {}), int(time.time())))
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@require_admin
def dashboard():
    """Admin dashboard overview"""
    try:
        current_user = get_current_user()
        
        # Get system statistics
        stats = {
            'total_identities': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
            'active_identities': db.query_one("SELECT COUNT(*) as count FROM identities WHERE status = 'active'")['count'],
            'pending_verification': db.query_one("SELECT COUNT(*) as count FROM identities WHERE verification_level LIKE '%Pending%'")['count'],
            'total_selas': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
            'total_tokens': db.query_one("SELECT COUNT(*) as count FROM tokens")['count'],
            'total_pools': db.query_one("SELECT COUNT(*) as count FROM jubilee_pools")['count'],
            'total_deeds': db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")['count'],
            'system_admins': db.query_one("SELECT COUNT(*) as count FROM identities WHERE role_class = 'system_admin'")['count']
        }
        
        # Get recent activities
        recent_identities = db.query("""
            SELECT identity_id, name, created_at, verification_level, status
            FROM identities 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        recent_deeds = db.query("""
            SELECT d.deed_id, d.deed_type, d.description, d.timestamp, i.name as identity_name
            FROM deeds_ledger d
            JOIN identities i ON d.identity_id = i.identity_id
            ORDER BY d.timestamp DESC
            LIMIT 10
        """)
        
        return render_template('admin/dashboard.html',
                             stats=stats,
                             recent_identities=recent_identities,
                             recent_deeds=recent_deeds,
                             current_user=current_user)
        
    except Exception as e:
        logger.error(f"Admin dashboard error: {e}")
        flash('Error loading admin dashboard', 'error')
        return redirect(url_for('dashboard.overview'))

@admin_bp.route('/users')
@require_admin
def user_management():
    """User management interface"""
    try:
        # Get all users with pagination
        page = request.args.get('page', 1, type=int)
        per_page = 20
        offset = (page - 1) * per_page
        
        users = db.query("""
            SELECT identity_id, name, email, verification_level, status, role_class, created_at
            FROM identities 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        """, (per_page, offset))
        
        total_users = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
        
        return render_template('admin/users.html',
                             users=users,
                             page=page,
                             per_page=per_page,
                             total_users=total_users)
        
    except Exception as e:
        logger.error(f"User management error: {e}")
        flash('Error loading user management', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/system')
@require_admin
def system_management():
    """System configuration and management"""
    try:
        # Get system configuration
        system_info = {
            'database_tables': len(db.query("SELECT name FROM sqlite_master WHERE type='table'")),
            'total_records': sum([
                db.query_one(f"SELECT COUNT(*) as count FROM {table}")['count']
                for table in ['identities', 'selas', 'tokens', 'jubilee_pools', 'deeds_ledger']
            ]),
            'last_backup': 'Not configured',  # TODO: Implement backup system
            'system_version': '1.0.0-beta',
            'uptime': 'Unknown'  # TODO: Track system uptime
        }
        
        return render_template('admin/system.html',
                             system_info=system_info)
        
    except Exception as e:
        logger.error(f"System management error: {e}")
        flash('Error loading system management', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/user/<identity_id>/update', methods=['POST'])
@require_admin
def update_user(identity_id):
    """Update user information"""
    try:
        data = request.get_json()
        
        # Validate input
        allowed_fields = ['verification_level', 'status', 'role_class']
        updates = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not updates:
            return jsonify({'error': 'No valid fields to update'}), 400
        
        # Build update query
        set_clause = ', '.join([f"{k} = ?" for k in updates.keys()])
        values = list(updates.values()) + [identity_id]
        
        db.execute(f"UPDATE identities SET {set_clause}, updated_at = ? WHERE identity_id = ?", 
                  values + [int(time.time())])
        
        # Log the admin action
        current_user = get_current_user()
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_UPDATE_{identity_id}_{int(time.time())}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Updated user {identity_id}",
            int(time.time()),
            json.dumps({'action': 'user_update', 'target': identity_id, 'changes': updates})
        ))
        
        return jsonify({'success': True, 'message': 'User updated successfully'})
        
    except Exception as e:
        logger.error(f"User update error: {e}")
        return jsonify({'error': 'Failed to update user'}), 500

@admin_bp.route('/api/users/<identity_id>', methods=['DELETE'])
@require_admin
def delete_user(identity_id):
    """Delete a user and all associated data"""
    try:
        current_user = get_current_user()

        # Prevent self-deletion
        if identity_id == current_user['identity_id']:
            return jsonify({'success': False, 'error': 'Cannot delete your own account'}), 403

        # Get user info before deletion for logging
        user_info = db.query_one("SELECT name, email, role_class FROM identities WHERE identity_id = ?", (identity_id,))
        if not user_info:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Prevent deletion of other system admins (safety measure)
        if user_info['role_class'] == 'system_admin':
            return jsonify({'success': False, 'error': 'Cannot delete system administrators'}), 403

        # Log the admin action BEFORE deletion
        current_time = int(time.time())
        try:
            db.execute("""
                INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                f"ADMIN_DELETE_{identity_id}_{current_time}",
                current_user['identity_id'],
                'ADMIN_ACTION',
                f"Deleted user {user_info['name']} ({user_info['email']})",
                current_time,
                json.dumps({
                    'action': 'user_deletion',
                    'target': identity_id,
                    'target_name': user_info['name'],
                    'target_email': user_info['email'],
                    'target_role': user_info['role_class']
                })
            ))
        except Exception as e:
            logger.warning(f"Could not log admin action: {e}")

        # Begin transaction to delete all related data
        try:
            # Delete from related tables first (foreign key constraints)
            # Note: We exclude deeds_ledger from bulk deletion to preserve admin action logs
            tables_to_clean = [
                'identity_passwords',
                'token_balances',
                'mining_rewards',
                'labor_records',
                'sela_relationships',
                'secure_sessions',
                'auth_attempts'
            ]

            for table in tables_to_clean:
                try:
                    db.execute(f"DELETE FROM {table} WHERE identity_id = ?", (identity_id,))
                    logger.debug(f"Deleted from {table} for user {identity_id}")
                except Exception as e:
                    logger.warning(f"Could not delete from {table}: {e}")

            # Delete user's own deeds but preserve admin action logs
            try:
                db.execute("""
                    DELETE FROM deeds_ledger
                    WHERE identity_id = ? AND deed_type != 'ADMIN_ACTION'
                """, (identity_id,))
                logger.debug(f"Deleted user deeds for {identity_id}")
            except Exception as e:
                logger.warning(f"Could not delete user deeds: {e}")

            # Delete from selas table if user owns any
            try:
                db.execute("DELETE FROM selas WHERE identity_id = ?", (identity_id,))
                logger.debug(f"Deleted selas for user {identity_id}")
            except Exception as e:
                logger.warning(f"Could not delete selas: {e}")

            # Finally delete the identity
            db.execute("DELETE FROM identities WHERE identity_id = ?", (identity_id,))
            logger.debug(f"Deleted identity {identity_id}")

            logger.info(f"Admin {current_user.get('email', current_user['identity_id'])} successfully deleted user {user_info['email']} ({identity_id})")
            return jsonify({
                'success': True,
                'message': f"User {user_info['name']} has been permanently deleted"
            })

        except Exception as e:
            logger.error(f"Error during user deletion transaction: {e}")
            logger.error(f"Full error details: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'Failed to delete user: {str(e)}'
            }), 500

    except Exception as e:
        logger.error(f"User deletion error: {e}")
        return jsonify({'error': 'Failed to delete user'}), 500

@admin_bp.route('/api/user/<identity_id>/details')
@require_admin
def get_user_details(identity_id):
    """Get detailed user information"""
    try:
        # Get user details
        user = db.query_one("""
            SELECT identity_id, name, email, role_class, tribal_affiliation,
                   status, verification_level, created_at, updated_at, etzem_score,
                   nation_code, nation_name, covenant_accepted, vault_status
            FROM identities
            WHERE identity_id = ?
        """, (identity_id,))

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get user's token balances
        balances = db.query("""
            SELECT token_id, balance, last_updated
            FROM token_balances
            WHERE identity_id = ?
        """, (identity_id,))

        # Get user's recent deeds
        deeds = db.query("""
            SELECT deed_type, description, timestamp, deed_value
            FROM deeds_ledger
            WHERE identity_id = ?
            ORDER BY timestamp DESC
            LIMIT 10
        """, (identity_id,))

        # Get user's selas
        selas = db.query("""
            SELECT sela_id, name, status, mining_tier, mining_power
            FROM selas
            WHERE identity_id = ?
        """, (identity_id,))

        return jsonify({
            'user': dict(user),
            'balances': [dict(b) for b in balances],
            'recent_deeds': [dict(d) for d in deeds],
            'selas': [dict(s) for s in selas]
        })

    except Exception as e:
        logger.error(f"Error getting user details: {e}")
        return jsonify({'error': 'Failed to get user details'}), 500

@admin_bp.route('/api/system/stats')
@require_admin
def system_stats():
    """Get real-time system statistics"""
    try:
        stats = {
            'identities': {
                'total': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
                'active': db.query_one("SELECT COUNT(*) as count FROM identities WHERE status = 'active'")['count'],
                'pending': db.query_one("SELECT COUNT(*) as count FROM identities WHERE verification_level LIKE '%Pending%'")['count']
            },
            'selas': {
                'total': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
                'active': db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count']
            },
            'tokenomics': {
                'pools': db.query_one("SELECT COUNT(*) as count FROM jubilee_pools")['count'],
                'deeds': db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")['count'],
                'tokens': db.query_one("SELECT COUNT(*) as count FROM tokens")['count']
            }
        }
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"System stats error: {e}")
        return jsonify({'error': 'Failed to get system stats'}), 500

@admin_bp.route('/tribal-elders')
@require_admin
def tribal_elders():
    """Tribal Elder management page. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()

        # Get all tribal elders
        elders = db.query("""
            SELECT identity_id, name, email, role, role_class, nation_code, nation_name,
                   tribal_affiliation, verification_level, created_at, status, metadata
            FROM identities
            WHERE role = 'tribal_elder' OR role_class = 'Tribal_Elder'
            ORDER BY nation_code, created_at DESC
        """)

        # Parse metadata for each elder
        for elder in elders:
            try:
                elder['metadata_parsed'] = json.loads(elder['metadata']) if elder['metadata'] else {}
            except:
                elder['metadata_parsed'] = {}

        # Get the 12 tribes for reference
        twelve_tribes = [
            {'code': 'JU', 'name': 'Judah', 'voting_weight': 2, 'role': 'Royal Tribe'},
            {'code': 'LE', 'name': 'Levi', 'voting_weight': 2, 'role': 'Priestly Tribe'},
            {'code': 'EP', 'name': 'Ephraim', 'voting_weight': 2, 'role': 'Leading Tribe'},
            {'code': 'BE', 'name': 'Benjamin', 'voting_weight': 1, 'role': 'Warrior Tribe'},
            {'code': 'SI', 'name': 'Simeon', 'voting_weight': 1, 'role': 'Scattered Tribe'},
            {'code': 'MA', 'name': 'Manasseh', 'voting_weight': 1, 'role': 'Fruitful Tribe'},
            {'code': 'IS', 'name': 'Issachar', 'voting_weight': 1, 'role': 'Wise Tribe'},
            {'code': 'ZE', 'name': 'Zebulun', 'voting_weight': 1, 'role': 'Merchant Tribe'},
            {'code': 'NA', 'name': 'Naphtali', 'voting_weight': 1, 'role': 'Swift Tribe'},
            {'code': 'GA', 'name': 'Gad', 'voting_weight': 1, 'role': 'Warrior Tribe'},
            {'code': 'AS', 'name': 'Asher', 'voting_weight': 1, 'role': 'Blessed Tribe'},
            {'code': 'RE', 'name': 'Reuben', 'voting_weight': 1, 'role': 'Firstborn Tribe'}
        ]

        return render_template('admin/tribal_elders.html',
                             elders=elders,
                             twelve_tribes=twelve_tribes,
                             current_user=current_user)

    except Exception as e:
        logger.error(f"Error loading tribal elders: {e}")
        flash('Error loading tribal elders.', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/selas')
@require_admin
def sela_management():
    """Sela management page for admins. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()

        # Get all Selas with owner information
        selas = db.query("""
            SELECT s.*, i.name as owner_name, i.email as owner_email, i.nation_code, i.nation_name
            FROM selas s
            LEFT JOIN identities i ON s.identity_id = i.identity_id
            ORDER BY s.created_at DESC
        """)

        # Parse metadata for each Sela
        for sela in selas:
            try:
                sela['metadata_parsed'] = json.loads(sela['metadata']) if sela['metadata'] else {}
            except:
                sela['metadata_parsed'] = {}

        # Get Sela statistics
        stats = {
            'total_selas': len(selas),
            'active_selas': len([s for s in selas if s['status'] == 'active']),
            'pending_selas': len([s for s in selas if s['status'] == 'pending']),
            'inactive_selas': len([s for s in selas if s['status'] in ['inactive', 'suspended']]),
            'categories': {}
        }

        # Count by category
        for sela in selas:
            category = sela['category'] or 'uncategorized'
            stats['categories'][category] = stats['categories'].get(category, 0) + 1

        return render_template('admin/sela_management.html',
                             selas=selas,
                             stats=stats,
                             current_user=current_user)

    except Exception as e:
        logger.error(f"Error loading Sela management: {e}")
        flash('Error loading Sela management.', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/validators/<sela_id>', methods=['DELETE'])
@require_admin
def delete_validator(sela_id):
    """Delete a validator (Sela) and all associated data. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        # Get validator details for logging
        validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
        if not validator:
            return jsonify({'success': False, 'error': 'Validator not found'}), 404

        # Begin transaction for safe deletion
        db.execute("BEGIN TRANSACTION")

        try:
            # Delete associated mining rewards
            db.execute("DELETE FROM mining_rewards WHERE identity_id = ?", (validator['identity_id'],))

            # Delete the Sela itself
            db.execute("DELETE FROM selas WHERE sela_id = ?", (sela_id,))

            # Commit transaction
            db.execute("COMMIT")

            # Log the deletion
            log_security_event(
                current_user['identity_id'],
                'VALIDATOR_DELETION',
                f"Deleted validator {validator['name']} (ID: {sela_id})",
                {'validator_name': validator['name'], 'sela_id': sela_id}
            )

            logger.info(f"Admin {current_user['name']} deleted validator {validator['name']} (ID: {sela_id})")

            return jsonify({
                'success': True,
                'message': f"Validator {validator['name']} successfully deleted"
            })

        except Exception as e:
            # Rollback on error
            db.execute("ROLLBACK")
            raise e

    except Exception as e:
        logger.error(f"Error deleting validator {sela_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/selas/<sela_id>/status', methods=['PUT'])
@require_admin
def update_sela_status(sela_id):
    """Update Sela status. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        data = request.get_json()
        new_status = data.get('status')

        if new_status not in ['active', 'pending', 'inactive', 'suspended', 'cancelled']:
            return jsonify({'success': False, 'error': 'Invalid status'}), 400

        # Get Sela details
        sela = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
        if not sela:
            return jsonify({'success': False, 'error': 'Sela not found'}), 404

        # Update status
        current_time = int(time.time())
        db.execute("""
            UPDATE selas
            SET status = ?, updated_at = ?
            WHERE sela_id = ?
        """, (new_status, current_time, sela_id))

        # Log the admin action
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_SELA_STATUS_{sela_id}_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Changed Sela {sela['name']} status to {new_status}",
            current_time,
            json.dumps({'action': 'update_sela_status', 'sela_id': sela_id, 'old_status': sela['status'], 'new_status': new_status})
        ))

        logger.info(f"Admin {current_user['identity_id']} changed Sela {sela_id} status to {new_status}")

        return jsonify({
            'success': True,
            'message': f'Sela status updated to {new_status}',
            'new_status': new_status
        })

    except Exception as e:
        logger.error(f"Error updating Sela status: {e}")
        return jsonify({'error': 'Failed to update Sela status'}), 500

@admin_bp.route('/api/selas/<sela_id>/configure', methods=['PUT'])
@require_admin
def configure_sela(sela_id):
    """Configure Sela settings. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        data = request.get_json()

        # Get current Sela
        sela = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
        if not sela:
            return jsonify({'success': False, 'error': 'Sela not found'}), 404

        # Parse current metadata
        try:
            metadata = json.loads(sela['metadata']) if sela['metadata'] else {}
        except:
            metadata = {}

        # Update configuration
        current_time = int(time.time())

        # Update basic fields if provided
        updates = []
        params = []

        if 'name' in data:
            updates.append("name = ?")
            params.append(data['name'])

        if 'category' in data:
            updates.append("category = ?")
            params.append(data['category'])

        if 'description' in data:
            updates.append("description = ?")
            params.append(data['description'])

        # Update metadata with admin configuration
        if 'admin_config' in data:
            metadata['admin_config'] = data['admin_config']
            metadata['admin_configured_by'] = current_user['identity_id']
            metadata['admin_configured_at'] = current_time

        # Update mining configuration if provided
        if 'mining_config' in data:
            mining_config = data['mining_config']
            if 'mining_tier' in mining_config:
                updates.append("mining_tier = ?")
                params.append(mining_config['mining_tier'])
            if 'mining_power' in mining_config:
                updates.append("mining_power = ?")
                params.append(float(mining_config['mining_power']))

        # Update metadata
        updates.append("metadata = ?")
        params.append(json.dumps(metadata))

        updates.append("updated_at = ?")
        params.append(current_time)

        params.append(sela_id)

        # Execute update
        if updates:
            query = f"UPDATE selas SET {', '.join(updates)} WHERE sela_id = ?"
            db.execute(query, params)

        # Log the admin action
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_SELA_CONFIG_{sela_id}_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Configured Sela {sela['name']}",
            current_time,
            json.dumps({'action': 'configure_sela', 'sela_id': sela_id, 'changes': data})
        ))

        logger.info(f"Admin {current_user['identity_id']} configured Sela {sela_id}")

        return jsonify({
            'success': True,
            'message': 'Sela configuration updated successfully'
        })

    except Exception as e:
        logger.error(f"Error configuring Sela: {e}")
        return jsonify({'error': 'Failed to configure Sela'}), 500

@admin_bp.route('/api/selas/<sela_id>/details', methods=['GET'])
@require_admin
def get_sela_details(sela_id):
    """Get Sela details for configuration. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        # Get Sela details
        sela = db.query_one("""
            SELECT s.*, i.name as owner_name, i.email as owner_email
            FROM selas s
            LEFT JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.sela_id = ?
        """, (sela_id,))

        if not sela:
            return jsonify({'success': False, 'error': 'Sela not found'}), 404

        # Parse metadata
        try:
            sela['metadata_parsed'] = json.loads(sela['metadata']) if sela['metadata'] else {}
        except:
            sela['metadata_parsed'] = {}

        return jsonify(dict(sela))

    except Exception as e:
        logger.error(f"Error getting Sela details: {e}")
        return jsonify({'error': 'Failed to get Sela details'}), 500

@admin_bp.route('/api/selas/generate-pages', methods=['POST'])
@require_admin
def generate_sela_pages():
    """Generate default web pages for all Selas. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        # Get all active Selas
        selas = db.query("""
            SELECT sela_id, name, status, metadata
            FROM selas
            WHERE status = 'active'
        """)

        updated_count = 0
        current_time = int(time.time())

        for sela in selas:
            try:
                # Parse existing metadata
                metadata = json.loads(sela['metadata']) if sela['metadata'] else {}

                # Add default web page configuration
                if 'web_page' not in metadata:
                    metadata['web_page'] = {
                        'enabled': True,
                        'generated_by_admin': current_user['identity_id'],
                        'generated_at': current_time,
                        'public_url': f"/sela/{sela['sela_id']}",
                        'seo_optimized': True,
                        'customer_facing': True,
                        'last_updated': current_time
                    }

                    # Update the Sela with new metadata
                    db.execute("""
                        UPDATE selas
                        SET metadata = ?, updated_at = ?
                        WHERE sela_id = ?
                    """, (json.dumps(metadata), current_time, sela['sela_id']))

                    updated_count += 1

            except Exception as e:
                logger.warning(f"Error updating Sela {sela['sela_id']}: {e}")

        # Log the admin action
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_GENERATE_PAGES_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Generated default web pages for {updated_count} Selas",
            current_time,
            json.dumps({'action': 'generate_sela_pages', 'count': updated_count})
        ))

        logger.info(f"Admin {current_user['identity_id']} generated pages for {updated_count} Selas")

        return jsonify({
            'success': True,
            'message': f'Successfully generated default web pages for {updated_count} Selas',
            'updated_count': updated_count,
            'total_selas': len(selas)
        })

    except Exception as e:
        logger.error(f"Error generating Sela pages: {e}")
        return jsonify({'error': 'Failed to generate Sela pages'}), 500

@admin_bp.route('/api/tribal-elders', methods=['POST'])
@require_admin
def create_tribal_elder():
    """Create a new tribal elder. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        data = request.get_json()

        # Validation
        required_fields = ['name', 'email', 'tribe_code', 'governance_role']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'{field} is required'}), 400

        # Check if tribal elder already exists for this tribe
        existing_elder = db.query_one("""
            SELECT identity_id FROM identities
            WHERE (role = 'tribal_elder' OR role_class = 'Tribal_Elder')
            AND nation_code = ?
        """, (data['tribe_code'],))

        if existing_elder:
            return jsonify({'success': False, 'error': f'Tribal elder already exists for tribe {data["tribe_code"]}'}), 400

        # Generate identity
        identity_id = f"elder_{data['tribe_code'].lower()}_{uuid.uuid4().hex[:8]}"
        current_time = int(time.time())

        # Get tribe information
        tribe_info = {
            'JU': {'name': 'Judah', 'voting_weight': 2},
            'LE': {'name': 'Levi', 'voting_weight': 2},
            'EP': {'name': 'Ephraim', 'voting_weight': 2},
            'BE': {'name': 'Benjamin', 'voting_weight': 1},
            'SI': {'name': 'Simeon', 'voting_weight': 1},
            'MA': {'name': 'Manasseh', 'voting_weight': 1},
            'IS': {'name': 'Issachar', 'voting_weight': 1},
            'ZE': {'name': 'Zebulun', 'voting_weight': 1},
            'NA': {'name': 'Naphtali', 'voting_weight': 1},
            'GA': {'name': 'Gad', 'voting_weight': 1},
            'AS': {'name': 'Asher', 'voting_weight': 1},
            'RE': {'name': 'Reuben', 'voting_weight': 1}
        }.get(data['tribe_code'], {'name': data['tribe_code'], 'voting_weight': 1})

        # Create metadata
        metadata = {
            "tribe_code": data['tribe_code'],
            "tribe_name": tribe_info['name'],
            "tribal_role": "Elder",
            "governance_role": data['governance_role'],
            "council_member": True,
            "voting_weight": tribe_info['voting_weight'],
            "created_by_admin": current_user['identity_id'],
            "creation_timestamp": current_time,
            "badges": ["Tribal_Elder", "Council_Member", f"{tribe_info['name']}_Elder"],
            "reputation": 1000,
            "covenant_comprehension_score": 5,
            "onboarding_completed": True
        }

        # Generate public key
        public_key = hashlib.sha256(f"{identity_id}:{data['tribe_code']}:{current_time}".encode()).hexdigest()

        # Insert tribal elder
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, nation_code, nation_name,
                tribal_affiliation, role, role_class, verification_level,
                etzem_score, status, created_at, updated_at, metadata,
                covenant_accepted, vault_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            data['name'],
            data['email'],
            public_key,
            data['tribe_code'],
            tribe_info['name'],
            data['tribe_code'],
            'tribal_elder',
            'Tribal_Elder',
            3,  # Highest verification level
            1000,  # High Etzem score
            'active',
            current_time,
            current_time,
            json.dumps(metadata),
            True,  # Covenant accepted
            'active'
        ))

        # Log the admin action
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_CREATE_ELDER_{identity_id}_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Created tribal elder {data['name']} for tribe {data['tribe_code']}",
            current_time,
            json.dumps({'action': 'create_tribal_elder', 'elder_id': identity_id, 'tribe': data['tribe_code']})
        ))

        logger.info(f"Tribal elder created: {identity_id} for tribe {data['tribe_code']} by admin {current_user['identity_id']}")

        return jsonify({
            'success': True,
            'message': f'Tribal elder {data["name"]} created successfully for tribe {tribe_info["name"]}',
            'elder_id': identity_id
        })

    except Exception as e:
        logger.error(f"Tribal elder creation error: {e}")
        return jsonify({'error': 'Failed to create tribal elder'}), 500

@admin_bp.route('/bulk-import')
@require_admin
def bulk_import():
    """Bulk import page for CSV and JSON data. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()

        return render_template('admin/bulk_import.html', current_user=current_user)

    except Exception as e:
        logger.error(f"Error loading bulk import: {e}")
        flash('Error loading bulk import.', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/bulk-import', methods=['POST'])
@require_admin
def process_bulk_import():
    """Process bulk import of data. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        # Get form data
        import_type = request.form.get('import_type')
        data_format = request.form.get('data_format')

        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Read file content
        try:
            file_content = file.read().decode('utf-8')
        except Exception as e:
            return jsonify({'success': False, 'error': f'Error reading file: {str(e)}'}), 400

        # Parse data based on format
        if data_format == 'csv':
            import csv
            from io import StringIO

            csv_reader = csv.DictReader(StringIO(file_content))
            data_rows = list(csv_reader)
        elif data_format == 'json':
            data_rows = json.loads(file_content)
            if not isinstance(data_rows, list):
                return jsonify({'success': False, 'error': 'JSON must be an array of objects'}), 400
        else:
            return jsonify({'success': False, 'error': 'Unsupported data format'}), 400

        # Process based on import type
        if import_type == 'identities':
            result = _import_identities(data_rows, current_user)
        elif import_type == 'selas':
            result = _import_selas(data_rows, current_user)
        elif import_type == 'tribal_elders':
            result = _import_tribal_elders(data_rows, current_user)
        else:
            return jsonify({'success': False, 'error': 'Unsupported import type'}), 400

        return jsonify(result)

    except Exception as e:
        logger.error(f"Bulk import error: {e}")
        return jsonify({'error': 'Failed to process bulk import'}), 500

def _import_identities(data_rows, current_user):
    """Import identities from data rows"""
    try:
        imported_count = 0
        errors = []
        current_time = int(time.time())

        for i, row in enumerate(data_rows):
            try:
                # Required fields
                required_fields = ['name', 'email']
                for field in required_fields:
                    if not row.get(field):
                        errors.append(f"Row {i+1}: Missing required field '{field}'")
                        continue

                # Generate identity ID
                identity_id = f"import_{uuid.uuid4().hex[:8]}"

                # Generate public key
                public_key = hashlib.sha256(f"{identity_id}:{row['email']}:{current_time}".encode()).hexdigest()

                # Create metadata
                metadata = {
                    "imported_by_admin": current_user['identity_id'],
                    "import_timestamp": current_time,
                    "import_source": "bulk_import",
                    "original_data": row
                }

                # Insert identity
                db.execute("""
                    INSERT INTO identities (
                        identity_id, name, email, public_key, role, role_class,
                        nation_code, nation_name, tribal_affiliation,
                        verification_level, etzem_score, status, created_at, updated_at,
                        metadata, covenant_accepted, vault_status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    identity_id,
                    row['name'],
                    row['email'],
                    public_key,
                    row.get('role', 'citizen'),
                    row.get('role_class', 'Community_Member'),
                    row.get('nation_code', 'ISR'),
                    row.get('nation_name', 'Israel'),
                    row.get('tribal_affiliation'),
                    int(row.get('verification_level', 1)),
                    float(row.get('etzem_score', 50)),
                    row.get('status', 'active'),
                    current_time,
                    current_time,
                    json.dumps(metadata),
                    bool(row.get('covenant_accepted', True)),
                    row.get('vault_status', 'active')
                ))

                imported_count += 1

            except Exception as e:
                errors.append(f"Row {i+1}: {str(e)}")

        # Log the import
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_BULK_IMPORT_IDENTITIES_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Bulk imported {imported_count} identities",
            current_time,
            json.dumps({'action': 'bulk_import_identities', 'count': imported_count, 'errors': len(errors)})
        ))

        return {
            'success': True,
            'message': f'Successfully imported {imported_count} identities',
            'imported_count': imported_count,
            'errors': errors
        }

    except Exception as e:
        logger.error(f"Identity import error: {e}")
        return {'success': False, 'error': str(e)}

def _import_selas(data_rows, current_user):
    """Import Selas from data rows"""
    try:
        imported_count = 0
        errors = []
        current_time = int(time.time())

        for i, row in enumerate(data_rows):
            try:
                # Required fields
                required_fields = ['name', 'identity_id', 'category']
                for field in required_fields:
                    if not row.get(field):
                        errors.append(f"Row {i+1}: Missing required field '{field}'")
                        continue

                # Check if identity exists
                identity = db.query_one("SELECT identity_id FROM identities WHERE identity_id = ?", (row['identity_id'],))
                if not identity:
                    errors.append(f"Row {i+1}: Identity {row['identity_id']} not found")
                    continue

                # Generate Sela ID
                sela_id = f"SELA_{row['identity_id'][:8]}_{current_time}_{i}"

                # Create metadata
                metadata = {
                    "imported_by_admin": current_user['identity_id'],
                    "import_timestamp": current_time,
                    "import_source": "bulk_import",
                    "original_data": row,
                    "creation_source": "bulk_import",
                    "business_category": row['category'],
                    "covenant_compliant": True
                }

                # Insert Sela
                db.execute("""
                    INSERT INTO selas (
                        sela_id, identity_id, name, category, description,
                        status, created_at, metadata, address, stake_amount, stake_token_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    sela_id,
                    row['identity_id'],
                    row['name'],
                    row['category'],
                    row.get('description', ''),
                    row.get('status', 'active'),
                    current_time,
                    json.dumps(metadata),
                    row.get('address', ''),
                    float(row.get('stake_amount', 1000)),
                    row.get('stake_token_id', 'ONX')
                ))

                imported_count += 1

            except Exception as e:
                errors.append(f"Row {i+1}: {str(e)}")

        # Log the import
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_BULK_IMPORT_SELAS_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Bulk imported {imported_count} Selas",
            current_time,
            json.dumps({'action': 'bulk_import_selas', 'count': imported_count, 'errors': len(errors)})
        ))

        return {
            'success': True,
            'message': f'Successfully imported {imported_count} Selas',
            'imported_count': imported_count,
            'errors': errors
        }

    except Exception as e:
        logger.error(f"Sela import error: {e}")
        return {'success': False, 'error': str(e)}

def _import_tribal_elders(data_rows, current_user):
    """Import tribal elders from data rows"""
    try:
        imported_count = 0
        errors = []
        current_time = int(time.time())

        for i, row in enumerate(data_rows):
            try:
                # Required fields
                required_fields = ['name', 'email', 'tribe_code']
                for field in required_fields:
                    if not row.get(field):
                        errors.append(f"Row {i+1}: Missing required field '{field}'")
                        continue

                # Check if tribal elder already exists for this tribe
                existing_elder = db.query_one("""
                    SELECT identity_id FROM identities
                    WHERE (role = 'tribal_elder' OR role_class = 'Tribal_Elder')
                    AND nation_code = ?
                """, (row['tribe_code'],))

                if existing_elder:
                    errors.append(f"Row {i+1}: Tribal elder already exists for tribe {row['tribe_code']}")
                    continue

                # Generate identity
                identity_id = f"elder_{row['tribe_code'].lower()}_{uuid.uuid4().hex[:8]}"

                # Get tribe information
                tribe_info = {
                    'JU': {'name': 'Judah', 'voting_weight': 2},
                    'LE': {'name': 'Levi', 'voting_weight': 2},
                    'EP': {'name': 'Ephraim', 'voting_weight': 2},
                    'BE': {'name': 'Benjamin', 'voting_weight': 1},
                    'SI': {'name': 'Simeon', 'voting_weight': 1},
                    'MA': {'name': 'Manasseh', 'voting_weight': 1},
                    'IS': {'name': 'Issachar', 'voting_weight': 1},
                    'ZE': {'name': 'Zebulun', 'voting_weight': 1},
                    'NA': {'name': 'Naphtali', 'voting_weight': 1},
                    'GA': {'name': 'Gad', 'voting_weight': 1},
                    'AS': {'name': 'Asher', 'voting_weight': 1},
                    'RE': {'name': 'Reuben', 'voting_weight': 1}
                }.get(row['tribe_code'], {'name': row['tribe_code'], 'voting_weight': 1})

                # Create metadata
                metadata = {
                    "tribe_code": row['tribe_code'],
                    "tribe_name": tribe_info['name'],
                    "tribal_role": "Elder",
                    "governance_role": row.get('governance_role', 'Elder'),
                    "council_member": True,
                    "voting_weight": tribe_info['voting_weight'],
                    "imported_by_admin": current_user['identity_id'],
                    "import_timestamp": current_time,
                    "badges": ["Tribal_Elder", "Council_Member", f"{tribe_info['name']}_Elder"],
                    "reputation": 1000,
                    "covenant_comprehension_score": 5,
                    "onboarding_completed": True
                }

                # Generate public key
                public_key = hashlib.sha256(f"{identity_id}:{row['tribe_code']}:{current_time}".encode()).hexdigest()

                # Insert tribal elder
                db.execute("""
                    INSERT INTO identities (
                        identity_id, name, email, public_key, nation_code, nation_name,
                        tribal_affiliation, role, role_class, verification_level,
                        etzem_score, status, created_at, updated_at, metadata,
                        covenant_accepted, vault_status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    identity_id,
                    row['name'],
                    row['email'],
                    public_key,
                    row['tribe_code'],
                    tribe_info['name'],
                    row['tribe_code'],
                    'tribal_elder',
                    'Tribal_Elder',
                    3,  # Highest verification level
                    1000,  # High Etzem score
                    'active',
                    current_time,
                    current_time,
                    json.dumps(metadata),
                    True,  # Covenant accepted
                    'active'
                ))

                imported_count += 1

            except Exception as e:
                errors.append(f"Row {i+1}: {str(e)}")

        # Log the import
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_BULK_IMPORT_ELDERS_{current_time}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Bulk imported {imported_count} tribal elders",
            current_time,
            json.dumps({'action': 'bulk_import_tribal_elders', 'count': imported_count, 'errors': len(errors)})
        ))

        return {
            'success': True,
            'message': f'Successfully imported {imported_count} tribal elders',
            'imported_count': imported_count,
            'errors': errors
        }

    except Exception as e:
        logger.error(f"Tribal elder import error: {e}")
        return {'success': False, 'error': str(e)}


