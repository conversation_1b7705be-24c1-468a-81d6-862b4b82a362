#!/usr/bin/env python3
"""
CIPP Migration Script
Migrates existing identities to the Covenant Identity Protection Protocol
"""

import os
import sys
import time
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.cipp_migration")

def migrate_existing_identities():
    """Migrate existing identities to CIPP format."""
    try:
        logger.info("🛡️ Starting CIPP migration for existing identities...")
        
        # Get all existing identities
        existing_identities = db.query("SELECT identity_id, name, email FROM identities")
        
        if not existing_identities:
            logger.info("No existing identities found. Migration complete.")
            return
        
        logger.info(f"Found {len(existing_identities)} existing identities to migrate")
        
        current_time = int(time.time())
        migrated_count = 0
        
        for identity in existing_identities:
            identity_id = identity['identity_id']
            
            try:
                # Check if already migrated (has CIPP fields)
                existing_cipp = db.query_one("""
                    SELECT nation_of_origin FROM identities 
                    WHERE identity_id = ? AND nation_of_origin IS NOT NULL
                """, (identity_id,))
                
                if existing_cipp:
                    logger.info(f"Identity {identity_id} already migrated, skipping...")
                    continue
                
                # Update identity with CIPP defaults
                db.execute("""
                    UPDATE identities SET
                        nation_of_origin = ?,
                        nation_code = ?,
                        nation_name = ?,
                        role_class = ?,
                        etzem_score = ?,
                        zeman_count = ?,
                        protection_tier = ?,
                        verification_level = ?,
                        covenant_accepted = ?,
                        vault_status = ?,
                        last_activity_season = ?
                    WHERE identity_id = ?
                """, ('JU', 'JU', 'Judah', 'Citizen', 10, 0, 'Basic', 0, 1, 'Active', current_time, identity_id))
                
                # Create verification progress record
                existing_progress = db.query_one("""
                    SELECT identity_id FROM verification_progress WHERE identity_id = ?
                """, (identity_id,))
                
                if not existing_progress:
                    db.execute("""
                        INSERT INTO verification_progress 
                        (identity_id, tier_0_completed, last_updated)
                        VALUES (?, ?, ?)
                    """, (identity_id, True, current_time))
                
                # Create covenant acceptance record
                existing_covenant = db.query_one("""
                    SELECT identity_id FROM covenant_acceptances WHERE identity_id = ?
                """, (identity_id,))
                
                if not existing_covenant:
                    db.execute("""
                        INSERT INTO covenant_acceptances 
                        (identity_id, scroll_version, accepted_at, ip_address, user_agent)
                        VALUES (?, ?, ?, ?, ?)
                    """, (identity_id, 'v1.0', current_time, 'migration_script', 'CIPP_Migration_v1.0'))
                
                # Create initial Etzem score history
                existing_etzem = db.query_one("""
                    SELECT identity_id FROM etzem_history WHERE identity_id = ?
                """, (identity_id,))
                
                if not existing_etzem:
                    db.execute("""
                        INSERT INTO etzem_history 
                        (identity_id, old_score, new_score, change_reason, change_amount, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (identity_id, 0, 10, 'CIPP migration bonus', 10, current_time))
                
                # Create initial Zeman activity period
                existing_zeman = db.query_one("""
                    SELECT identity_id FROM zeman_activities WHERE identity_id = ?
                """, (identity_id,))
                
                if not existing_zeman:
                    db.execute("""
                        INSERT INTO zeman_activities 
                        (identity_id, season_start, season_end, activity_count)
                        VALUES (?, ?, ?, ?)
                    """, (identity_id, current_time, current_time + (90 * 24 * 3600), 1))
                
                migrated_count += 1
                logger.info(f"✅ Migrated identity: {identity['name']} ({identity_id})")
                
            except Exception as e:
                logger.error(f"❌ Failed to migrate identity {identity_id}: {e}")
                continue
        
        logger.info(f"🎉 CIPP migration completed! Migrated {migrated_count} identities")
        
    except Exception as e:
        logger.error(f"❌ CIPP migration failed: {e}")
        raise

def verify_biblical_nations():
    """Verify biblical nations table is populated."""
    try:
        logger.info("🏛️ Verifying biblical nations table...")
        
        nations_count = db.query_one("SELECT COUNT(*) as count FROM biblical_nations")['count']
        
        if nations_count == 0:
            logger.info("Biblical nations table is empty, populating...")
            
            # Insert biblical nations (this should already be done by schema, but just in case)
            nations_data = [
                ('JU', 'Judah', 'Tribe of Judah', 'The royal tribe, keepers of the scepter', '🦁'),
                ('BE', 'Benjamin', 'Tribe of Benjamin', 'The beloved tribe, warriors and protectors', '🐺'),
                ('EP', 'Ephraim', 'Tribe of Ephraim', 'The fruitful tribe, leaders of the northern kingdom', '🌾'),
                ('MA', 'Manasseh', 'Tribe of Manasseh', 'The forgetful tribe, blessed with abundance', '🌳'),
                ('LE', 'Levi', 'Tribe of Levi', 'The priestly tribe, servants of the sanctuary', '⚖️'),
                ('IS', 'Issachar', 'Tribe of Issachar', 'The wise tribe, discerners of times and seasons', '📚'),
                ('ZE', 'Zebulun', 'Tribe of Zebulun', 'The merchant tribe, dwellers by the sea', '⛵'),
                ('RU', 'Reuben', 'Tribe of Reuben', 'The firstborn tribe, unstable as water', '💧'),
                ('GA', 'Gad', 'Tribe of Gad', 'The warrior tribe, overcomers of adversity', '⚔️'),
                ('AS', 'Asher', 'Tribe of Asher', 'The blessed tribe, providers of royal dainties', '🍯'),
                ('NA', 'Naphtali', 'Tribe of Naphtali', 'The swift tribe, messengers of good tidings', '🦌'),
                ('DA', 'Dan', 'Tribe of Dan', 'The judging tribe, serpents by the way', '🐍'),
                ('SI', 'Simeon', 'Tribe of Simeon', 'The hearing tribe, scattered among brothers', '👂')
            ]
            
            for nation_data in nations_data:
                db.execute("""
                    INSERT OR IGNORE INTO biblical_nations 
                    (nation_code, nation_name, tribe_name, description, flag_symbol, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (*nation_data, int(time.time())))
            
            logger.info(f"✅ Populated {len(nations_data)} biblical nations")
        else:
            logger.info(f"✅ Biblical nations table already populated with {nations_count} entries")
            
    except Exception as e:
        logger.error(f"❌ Failed to verify biblical nations: {e}")
        raise

def create_missing_tables():
    """Create any missing CIPP tables."""
    try:
        logger.info("🔧 Checking for missing CIPP tables...")
        
        # Check if CIPP tables exist
        tables_to_check = [
            'biblical_nations',
            'verification_progress', 
            'protection_requests',
            'covenant_acceptances',
            'etzem_history',
            'zeman_activities',
            'vault_events'
        ]
        
        missing_tables = []
        for table in tables_to_check:
            if not db.table_exists(table):
                missing_tables.append(table)
        
        if missing_tables:
            logger.warning(f"Missing CIPP tables: {missing_tables}")
            logger.info("Please run the database initialization script to create missing tables")
            return False
        else:
            logger.info("✅ All CIPP tables exist")
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to check CIPP tables: {e}")
        return False

def main():
    """Main migration function."""
    try:
        logger.info("🛡️ Starting ONNYX CIPP Migration...")
        
        # Check if database is accessible
        try:
            db.query_one("SELECT COUNT(*) as count FROM identities")
        except Exception as e:
            logger.error(f"❌ Cannot access database: {e}")
            return False
        
        # Create missing tables if needed
        if not create_missing_tables():
            logger.error("❌ Missing required CIPP tables. Please run database initialization first.")
            return False
        
        # Verify biblical nations
        verify_biblical_nations()
        
        # Migrate existing identities
        migrate_existing_identities()
        
        logger.info("🎉 CIPP Migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ CIPP Migration failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
