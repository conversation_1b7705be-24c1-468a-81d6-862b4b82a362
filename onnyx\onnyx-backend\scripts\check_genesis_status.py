#!/usr/bin/env python3
"""
Quick Genesis Status Check
"""

import sys
import os
import json
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def check_genesis_status():
    """Check the current genesis block status."""
    print("🔍 ONNYX GENESIS STATUS CHECK")
    print("=" * 40)
    
    try:
        from shared.db.db import db
        print(f"✅ Database connected: {db.db_path}")
        
        # Check blocks table
        try:
            blocks = db.query("SELECT COUNT(*) as count FROM blocks")
            block_count = blocks[0]['count'] if blocks else 0
            print(f"📦 Total blocks: {block_count}")
            
            if block_count > 0:
                # Check genesis block
                genesis = db.query_one("SELECT * FROM blocks WHERE block_height = 0")
                if genesis:
                    print("✅ GENESIS BLOCK EXISTS:")
                    print(f"   Hash: {genesis['block_hash'][:16]}...")
                    print(f"   Miner: {genesis['miner']}")
                    print(f"   Time: {datetime.fromtimestamp(genesis['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # Check genesis transaction
                    tx = db.query_one("SELECT * FROM transactions WHERE block_height = 0")
                    if tx and tx['op'] == 'OP_COVENANT_FOUNDING':
                        print("✅ COVENANT FOUNDING CONFIRMED")
                        
                        # Check nations
                        nations = db.query("SELECT COUNT(*) as count FROM biblical_nations")
                        nation_count = nations[0]['count'] if nations else 0
                        print(f"🌍 Nations registered: {nation_count}")
                        
                        if nation_count == 47:
                            print("🎉 GENESIS COMPLETE - READY FOR STEP 8!")
                            return True
                        else:
                            print(f"⚠️ Expected 47 nations, found {nation_count}")
                    else:
                        print("❌ No covenant founding transaction")
                else:
                    print("❌ No genesis block found")
            else:
                print("❌ No blocks in database")
                
        except Exception as e:
            print(f"❌ Database query error: {e}")
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        
    return False

if __name__ == '__main__':
    success = check_genesis_status()
    if success:
        print("\n🚀 READY TO PROCEED TO STEP 8: TRIBAL ELDER ONBOARDING")
    else:
        print("\n⚠️ Genesis setup incomplete - manual intervention needed")
    sys.exit(0 if success else 1)
