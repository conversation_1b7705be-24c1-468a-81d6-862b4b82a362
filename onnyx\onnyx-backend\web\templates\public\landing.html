<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join the ONNYX Covenant Community</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .landing-hero {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(138, 43, 226, 0.1));
            padding: 80px 24px;
            text-align: center;
            border-radius: 16px;
            margin: 24px 0;
            backdrop-filter: blur(24px);
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #00d4ff, #8a2be2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 24px;
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 32px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin: 48px 0;
        }
        
        .feature-card {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(16px);
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 16px;
        }
        
        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 12px;
        }
        
        .feature-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }
        
        .cta-section {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 16px;
            padding: 48px 24px;
            text-align: center;
            margin: 48px 0;
            backdrop-filter: blur(16px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #8a2be2);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 8px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }
        
        .btn-secondary {
            background: transparent;
            color: #00d4ff;
            border: 2px solid #00d4ff;
            padding: 14px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 8px;
        }
        
        .btn-secondary:hover {
            background: rgba(0, 212, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin: 48px 0;
        }
        
        .stat-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            backdrop-filter: blur(8px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}" alt="ONNYX" class="nav-logo">
                <span class="nav-title">ONNYX COVENANT</span>
            </div>
            <div class="nav-links">
                <a href="/genesis/" class="nav-link">Genesis Status</a>
                <a href="/public/register" class="nav-link">Join Community</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="landing-hero">
            <h1 class="hero-title">Join the ONNYX Covenant Community</h1>
            <p class="hero-subtitle">
                A biblical blockchain community built on divine economic principles. 
                Experience true covenant governance, anti-usury economics, and spiritual community.
            </p>
            <a href="/public/register" class="btn-primary">Join the Covenant</a>
            <a href="/genesis/" class="btn-secondary">View Genesis Status</a>
        </div>

        <div id="stats-section" class="stats-grid">
            <div class="stat-card">
                <div id="total-members" class="stat-number">-</div>
                <div class="stat-label">Community Members</div>
            </div>
            <div class="stat-card">
                <div id="tribal-elders" class="stat-number">12</div>
                <div class="stat-label">Tribal Elders</div>
            </div>
            <div class="stat-card">
                <div id="covenant-acceptance" class="stat-number">-</div>
                <div class="stat-label">Covenant Acceptance Rate</div>
            </div>
            <div class="stat-card">
                <div id="active-scrolls" class="stat-number">3</div>
                <div class="stat-label">Active Voice Scrolls</div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📜</div>
                <h3 class="feature-title">Biblical Governance</h3>
                <p class="feature-description">
                    Participate in covenant governance through the Council of Twelve Tribes. 
                    Vote on Voice Scrolls and help shape the community according to biblical principles.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3 class="feature-title">Anti-Usury Economics</h3>
                <p class="feature-description">
                    Experience biblical economics with no interest between covenant members, 
                    automatic gleaning pools for community support, and Yovel wealth redistribution.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🛡️</div>
                <h3 class="feature-title">Identity Protection</h3>
                <p class="feature-description">
                    Advanced 4-tier Covenant Identity Protection Protocol (CIPP) with 
                    vault freeze capabilities and exile mode for ultimate security.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🏛️</div>
                <h3 class="feature-title">Tribal Community</h3>
                <p class="feature-description">
                    Join one of the 12 tribes of Israel and participate in a community 
                    built on biblical values, mutual support, and covenant relationships.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚖️</div>
                <h3 class="feature-title">Divine Justice</h3>
                <p class="feature-description">
                    Benefit from biblical justice systems with automatic debt forgiveness, 
                    Sabbath enforcement, and protection for widows, orphans, and strangers.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌱</div>
                <h3 class="feature-title">Spiritual Growth</h3>
                <p class="feature-description">
                    Advance through covenant tiers by demonstrating biblical understanding, 
                    community contribution, and spiritual maturity.
                </p>
            </div>
        </div>

        <div class="cta-section">
            <h2>Ready to Join the Covenant?</h2>
            <p>Take the first step toward biblical community and covenant governance.</p>
            <a href="/public/register" class="btn-primary">Start Registration</a>
            <a href="/public/covenant-test" class="btn-secondary">Take Covenant Test</a>
        </div>
    </div>

    <script>
        async function loadStats() {
            try {
                const response = await fetch('/public/api/stats');
                const stats = await response.json();
                
                document.getElementById('total-members').textContent = stats.total_members || 0;
                document.getElementById('covenant-acceptance').textContent = (stats.covenant_acceptance_rate || 0) + '%';
                
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        // Load stats on page load
        loadStats();
        
        // Refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>
</body>
</html>
