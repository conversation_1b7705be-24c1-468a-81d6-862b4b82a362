"""
ONNYX Community Light Node
Enables Tier 1+ community members to participate in the covenant network
"""

import asyncio
import json
import time
import logging
import uuid
from typing import Dict, List, Optional, Set
from dataclasses import dataclass

from network.p2p.peer_manager import Peer<PERSON>anager, NodeType
from network.discovery.bootstrap import Discovery<PERSON>lient
from network.sync.blockchain_sync import BlockchainSync

logger = logging.getLogger(__name__)

@dataclass
class CommunityNodeConfig:
    identity_id: str
    name: str
    tribal_code: str
    covenant_tier: int
    port: int
    bootstrap_nodes: List[tuple]
    etzem_score: int = 100

class CommunityLightNode:
    """
    Community Light Node for ONNYX covenant blockchain
    Enables Tier 1+ community members to participate in network
    """
    
    def __init__(self, config: CommunityNodeConfig):
        self.config = config
        self.node_id = f"community_{config.tribal_code.lower()}_{config.identity_id[:8]}"
        self.running = False
        
        # Initialize P2P components
        self.peer_manager = PeerManager(
            node_id=self.node_id,
            node_type=NodeType.COMMUNITY_LIGHT,
            port=config.port
        )
        
        # Initialize blockchain sync (light mode)
        self.blockchain_sync = BlockchainSync(self.peer_manager)
        
        # Initialize discovery client
        self.discovery_client = DiscoveryClient(config.bootstrap_nodes)
        
        # Community member attributes
        self.identity_id = config.identity_id
        self.name = config.name
        self.tribal_code = config.tribal_code
        self.covenant_tier = config.covenant_tier
        self.etzem_score = config.etzem_score
        
        # Light node capabilities
        self.can_participate_governance = config.covenant_tier >= 1
        self.can_access_gleaning_pools = config.covenant_tier >= 1
        self.can_use_anti_usury_lending = config.covenant_tier >= 1
        
        # Participation tracking
        self.gleaning_pool_contributions: List[dict] = []
        self.anti_usury_transactions: List[dict] = []
        self.governance_observations: List[dict] = []
        
        # Biblical compliance tracking
        self.covenant_compliance_score = 1.0
        self.last_sabbath_observance = 0
        
        logger.info(f"Initialized Community Light Node: {self.name} ({self.tribal_code}, Tier {self.covenant_tier})")
    
    async def start(self) -> bool:
        """Start the community light node"""
        try:
            logger.info(f"Starting Community Light Node: {self.name}")
            
            # Start P2P server
            await self.peer_manager.start_server()
            
            # Register with bootstrap nodes
            peer_info = {
                "peer_id": self.node_id,
                "address": "127.0.0.1",  # In production, would be actual IP
                "port": self.config.port,
                "node_type": "community_light",
                "tribal_code": self.tribal_code,
                "voting_weight": 0,  # Community members don't vote directly
                "covenant_tier": self.covenant_tier,
                "reputation": self.etzem_score
            }
            
            registration_success = await self.discovery_client.register_with_bootstrap(peer_info)
            if not registration_success:
                logger.warning("Failed to register with bootstrap nodes")
            
            # Discover and connect to peers (prioritize tribal elders)
            await self._discover_and_connect_peers()
            
            # Start light blockchain synchronization
            await self._start_light_sync()
            
            # Start community participation monitoring
            asyncio.create_task(self._community_participation_loop())
            
            # Start biblical compliance monitoring
            asyncio.create_task(self._biblical_compliance_loop())
            
            # Start gleaning pool monitoring
            if self.can_access_gleaning_pools:
                asyncio.create_task(self._gleaning_pool_loop())
            
            self.running = True
            logger.info(f"Community Light Node {self.name} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start community light node: {e}")
            return False
    
    async def stop(self):
        """Stop the community light node"""
        try:
            self.running = False
            await self.peer_manager.stop_server()
            logger.info(f"Community Light Node {self.name} stopped")
        except Exception as e:
            logger.error(f"Error stopping community light node: {e}")
    
    async def _discover_and_connect_peers(self):
        """Discover and connect to network peers"""
        try:
            # Discover peers, prioritizing tribal elders
            peers = await self.discovery_client.discover_peers(
                requester_id=self.node_id,
                node_type="community_light",
                max_peers=10
            )
            
            # Connect to discovered peers
            connection_count = 0
            elder_connections = 0
            
            for peer in peers:
                if peer['peer_id'] != self.node_id:
                    success = await self.peer_manager.connect_to_peer(
                        peer['address'], peer['port']
                    )
                    if success:
                        connection_count += 1
                        if peer.get('node_type') == 'tribal_elder':
                            elder_connections += 1
            
            logger.info(f"Connected to {connection_count} peers ({elder_connections} tribal elders)")
            
        except Exception as e:
            logger.error(f"Peer discovery error: {e}")
    
    async def _start_light_sync(self):
        """Start light blockchain synchronization"""
        try:
            # Light nodes only sync block headers and relevant transactions
            # For demo purposes, we'll do a simplified sync
            sync_success = await self.blockchain_sync.start_sync()
            
            if sync_success:
                logger.info("Light blockchain synchronization completed")
            else:
                logger.warning("Light blockchain synchronization failed")
            
        except Exception as e:
            logger.error(f"Light sync error: {e}")
    
    async def participate_in_gleaning_pool(self, contribution_amount: float, 
                                         contribution_type: str = "harvest") -> bool:
        """Participate in gleaning pool system"""
        try:
            if not self.can_access_gleaning_pools:
                logger.warning("Insufficient covenant tier for gleaning pool participation")
                return False
            
            # Create gleaning pool contribution
            contribution = {
                "contributor_id": self.identity_id,
                "tribal_code": self.tribal_code,
                "amount": contribution_amount,
                "type": contribution_type,
                "timestamp": int(time.time()),
                "covenant_tier": self.covenant_tier
            }
            
            # Record contribution
            self.gleaning_pool_contributions.append(contribution)
            
            # Broadcast contribution to network (would create transaction)
            await self._broadcast_gleaning_contribution(contribution)
            
            logger.info(f"Community member {self.name} contributed {contribution_amount} to gleaning pool")
            return True
            
        except Exception as e:
            logger.error(f"Gleaning pool participation error: {e}")
            return False
    
    async def request_anti_usury_loan(self, amount: float, purpose: str, 
                                     repayment_period_days: int) -> Optional[str]:
        """Request an anti-usury loan from the covenant community"""
        try:
            if not self.can_use_anti_usury_lending:
                logger.warning("Insufficient covenant tier for anti-usury lending")
                return None
            
            # Create loan request
            loan_request = {
                "borrower_id": self.identity_id,
                "borrower_name": self.name,
                "tribal_code": self.tribal_code,
                "amount": amount,
                "purpose": purpose,
                "repayment_period_days": repayment_period_days,
                "interest_rate": 0.0,  # No interest in biblical system
                "covenant_tier": self.covenant_tier,
                "etzem_score": self.etzem_score,
                "timestamp": int(time.time())
            }
            
            # Generate loan request ID
            loan_id = f"loan_{self.identity_id[:8]}_{int(time.time())}"
            loan_request["loan_id"] = loan_id
            
            # Broadcast loan request to network
            await self._broadcast_loan_request(loan_request)
            
            logger.info(f"Community member {self.name} requested anti-usury loan: {amount} for {purpose}")
            return loan_id
            
        except Exception as e:
            logger.error(f"Anti-usury loan request error: {e}")
            return None
    
    async def observe_governance_activity(self) -> List[dict]:
        """Observe governance activity (voice scrolls, elder votes)"""
        try:
            if not self.can_participate_governance:
                logger.warning("Insufficient covenant tier for governance observation")
                return []
            
            # Get current governance activity from connected tribal elders
            governance_activity = []
            
            tribal_elders = self.peer_manager.get_tribal_elders()
            for elder in tribal_elders:
                # Request governance status from elder
                activity = await self._request_governance_status(elder.peer_id)
                if activity:
                    governance_activity.extend(activity)
            
            # Record observations
            observation = {
                "observer_id": self.identity_id,
                "timestamp": int(time.time()),
                "activity_count": len(governance_activity),
                "tribal_elders_active": len(tribal_elders)
            }
            
            self.governance_observations.append(observation)
            
            logger.debug(f"Community member {self.name} observed {len(governance_activity)} governance activities")
            return governance_activity
            
        except Exception as e:
            logger.error(f"Governance observation error: {e}")
            return []
    
    async def verify_covenant_compliance(self, transaction: dict) -> bool:
        """Verify a transaction for covenant compliance"""
        try:
            # Check for usury violations
            if transaction.get('op') == 'OP_LEND':
                interest_rate = transaction.get('data', {}).get('interest_rate', 0)
                if interest_rate > 0:
                    logger.warning(f"Usury violation detected: {interest_rate}% interest")
                    return False
            
            # Check for Sabbath violations
            if self._is_sabbath_violation(transaction):
                logger.warning("Sabbath violation detected")
                return False
            
            # Check for gleaning pool compliance
            if transaction.get('op') == 'OP_HARVEST':
                gleaning_reserved = transaction.get('data', {}).get('gleaning_reserved', False)
                if not gleaning_reserved:
                    logger.warning("Gleaning pool violation detected")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Covenant compliance verification error: {e}")
            return False
    
    def _is_sabbath_violation(self, transaction: dict) -> bool:
        """Check if transaction violates Sabbath principles"""
        tx_time = transaction.get('timestamp', 0)
        day_of_week = time.gmtime(tx_time).tm_wday
        
        # Saturday (5) is Sabbath - restrict certain operations
        if day_of_week == 5:
            forbidden_ops = ['OP_MINE', 'OP_TRADE', 'OP_BUSINESS', 'OP_WORK']
            if transaction.get('op') in forbidden_ops:
                return True
        
        return False
    
    async def _broadcast_gleaning_contribution(self, contribution: dict):
        """Broadcast gleaning pool contribution to network"""
        try:
            message = self.peer_manager.NetworkMessage(
                message_type="gleaning_contribution",
                sender_id=self.node_id,
                recipient_id=None,
                data=contribution,
                timestamp=int(time.time())
            )
            
            await self.peer_manager.broadcast_message(message)
            
        except Exception as e:
            logger.error(f"Gleaning contribution broadcast error: {e}")
    
    async def _broadcast_loan_request(self, loan_request: dict):
        """Broadcast anti-usury loan request to network"""
        try:
            message = self.peer_manager.NetworkMessage(
                message_type="loan_request",
                sender_id=self.node_id,
                recipient_id=None,
                data=loan_request,
                timestamp=int(time.time())
            )
            
            await self.peer_manager.broadcast_message(message)
            
        except Exception as e:
            logger.error(f"Loan request broadcast error: {e}")
    
    async def _request_governance_status(self, elder_peer_id: str) -> Optional[List[dict]]:
        """Request governance status from a tribal elder"""
        try:
            message = self.peer_manager.NetworkMessage(
                message_type="governance_status_request",
                sender_id=self.node_id,
                recipient_id=elder_peer_id,
                data={"requester_tier": self.covenant_tier},
                timestamp=int(time.time())
            )
            
            await self.peer_manager.send_to_peer(elder_peer_id, message)
            
            # Wait for response (simplified)
            await asyncio.sleep(1)
            
            # In a real implementation, this would wait for and parse the response
            return []
            
        except Exception as e:
            logger.error(f"Governance status request error: {e}")
            return None
    
    # Background Tasks
    async def _community_participation_loop(self):
        """Monitor and participate in community activities"""
        while self.running:
            try:
                # Check for gleaning pool opportunities
                if self.can_access_gleaning_pools:
                    await self._check_gleaning_opportunities()
                
                # Check for loan opportunities
                if self.can_use_anti_usury_lending:
                    await self._check_lending_opportunities()
                
                # Observe governance activity
                if self.can_participate_governance:
                    await self.observe_governance_activity()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Community participation loop error: {e}")
                await asyncio.sleep(300)
    
    async def _biblical_compliance_loop(self):
        """Monitor biblical compliance and update scores"""
        while self.running:
            try:
                # Update covenant compliance score
                await self._update_covenant_compliance_score()
                
                # Check Sabbath observance
                await self._check_sabbath_observance()
                
                # Update etzem score based on participation
                await self._update_etzem_score()
                
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Biblical compliance loop error: {e}")
                await asyncio.sleep(3600)
    
    async def _gleaning_pool_loop(self):
        """Monitor gleaning pool activities"""
        while self.running:
            try:
                # Check for gleaning pool distributions
                await self._check_gleaning_distributions()
                
                # Contribute to gleaning pool if able
                await self._auto_contribute_gleaning()
                
                await asyncio.sleep(1800)  # Check every 30 minutes
                
            except Exception as e:
                logger.error(f"Gleaning pool loop error: {e}")
                await asyncio.sleep(1800)
    
    async def _check_gleaning_opportunities(self):
        """Check for gleaning pool opportunities"""
        try:
            # This would check the network for gleaning opportunities
            logger.debug(f"Community member {self.name} checking gleaning opportunities")
            
        except Exception as e:
            logger.error(f"Gleaning opportunity check error: {e}")
    
    async def _check_lending_opportunities(self):
        """Check for anti-usury lending opportunities"""
        try:
            # This would check the network for lending opportunities
            logger.debug(f"Community member {self.name} checking lending opportunities")
            
        except Exception as e:
            logger.error(f"Lending opportunity check error: {e}")
    
    async def _update_covenant_compliance_score(self):
        """Update covenant compliance score based on recent activity"""
        try:
            # Calculate compliance based on recent transactions and behavior
            base_score = 1.0
            
            # Penalty for any compliance violations
            # Bonus for positive covenant activities
            recent_contributions = len([c for c in self.gleaning_pool_contributions 
                                      if int(time.time()) - c['timestamp'] < 86400])  # Last 24 hours
            
            if recent_contributions > 0:
                base_score += 0.1 * recent_contributions
            
            self.covenant_compliance_score = min(1.0, base_score)
            
        except Exception as e:
            logger.error(f"Covenant compliance score update error: {e}")
    
    async def _check_sabbath_observance(self):
        """Check and record Sabbath observance"""
        try:
            current_time = int(time.time())
            day_of_week = time.gmtime(current_time).tm_wday
            
            # If it's Sabbath (Saturday), record observance
            if day_of_week == 5:
                self.last_sabbath_observance = current_time
                logger.debug(f"Community member {self.name} observing Sabbath")
            
        except Exception as e:
            logger.error(f"Sabbath observance check error: {e}")
    
    async def _update_etzem_score(self):
        """Update etzem score based on community participation"""
        try:
            # Base etzem score
            base_score = 100
            
            # Bonus for gleaning pool participation
            gleaning_bonus = len(self.gleaning_pool_contributions) * 5
            
            # Bonus for governance observation
            governance_bonus = len(self.governance_observations) * 2
            
            # Bonus for covenant compliance
            compliance_bonus = int(self.covenant_compliance_score * 50)
            
            self.etzem_score = base_score + gleaning_bonus + governance_bonus + compliance_bonus
            
        except Exception as e:
            logger.error(f"Etzem score update error: {e}")
    
    async def _check_gleaning_distributions(self):
        """Check for gleaning pool distributions"""
        try:
            # This would check for available gleaning pool distributions
            logger.debug(f"Community member {self.name} checking gleaning distributions")
            
        except Exception as e:
            logger.error(f"Gleaning distribution check error: {e}")
    
    async def _auto_contribute_gleaning(self):
        """Automatically contribute to gleaning pool if conditions are met"""
        try:
            # Auto-contribute if etzem score is high and haven't contributed recently
            if self.etzem_score > 150:
                recent_contributions = [c for c in self.gleaning_pool_contributions 
                                      if int(time.time()) - c['timestamp'] < 86400]
                
                if len(recent_contributions) == 0:
                    # Make a small contribution
                    await self.participate_in_gleaning_pool(10.0, "auto_contribution")
            
        except Exception as e:
            logger.error(f"Auto gleaning contribution error: {e}")
    
    # Public API Methods
    def get_node_status(self) -> dict:
        """Get comprehensive node status"""
        return {
            "node_id": self.node_id,
            "identity_id": self.identity_id,
            "name": self.name,
            "tribal_code": self.tribal_code,
            "covenant_tier": self.covenant_tier,
            "etzem_score": self.etzem_score,
            "running": self.running,
            "connected_peers": len(self.peer_manager.get_connected_peers()),
            "tribal_elders_connected": len(self.peer_manager.get_tribal_elders()),
            "gleaning_contributions": len(self.gleaning_pool_contributions),
            "anti_usury_transactions": len(self.anti_usury_transactions),
            "governance_observations": len(self.governance_observations),
            "covenant_compliance_score": self.covenant_compliance_score,
            "sync_status": self.blockchain_sync.get_sync_status(),
            "network_stats": self.peer_manager.get_network_stats(),
            "capabilities": {
                "governance_participation": self.can_participate_governance,
                "gleaning_pool_access": self.can_access_gleaning_pools,
                "anti_usury_lending": self.can_use_anti_usury_lending
            }
        }
    
    def get_participation_history(self) -> dict:
        """Get community participation history"""
        return {
            "gleaning_contributions": self.gleaning_pool_contributions.copy(),
            "anti_usury_transactions": self.anti_usury_transactions.copy(),
            "governance_observations": self.governance_observations.copy(),
            "total_contributions": len(self.gleaning_pool_contributions),
            "last_activity": max([
                max([c['timestamp'] for c in self.gleaning_pool_contributions] or [0]),
                max([t['timestamp'] for t in self.anti_usury_transactions] or [0]),
                max([o['timestamp'] for o in self.governance_observations] or [0])
            ])
        }
    
    def get_covenant_status(self) -> dict:
        """Get covenant compliance status"""
        return {
            "covenant_tier": self.covenant_tier,
            "etzem_score": self.etzem_score,
            "compliance_score": self.covenant_compliance_score,
            "last_sabbath_observance": self.last_sabbath_observance,
            "capabilities": {
                "governance_participation": self.can_participate_governance,
                "gleaning_pool_access": self.can_access_gleaning_pools,
                "anti_usury_lending": self.can_use_anti_usury_lending
            }
        }
