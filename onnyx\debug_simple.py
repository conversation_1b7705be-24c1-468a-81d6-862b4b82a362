#!/usr/bin/env python3
"""
Debug Eden Mode database issues
"""
import sqlite3
import json
import os

def main():
    print("Eden Mode Database Debugging")
    print("=" * 50)
    
    # Database path
    db_path = r"onnyx-backend\shared\db\db\onnyx.db"
    
    print(f"Database path: {db_path}")
    print(f"Database exists: {os.path.exists(db_path)}")
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get table schema
    cursor.execute("PRAGMA table_info(identities);")
    columns_info = cursor.fetchall()
    columns = [col[1] for col in columns_info]
    
    print(f"\n=== IDENTITIES TABLE SCHEMA ===")
    print(f"Columns: {len(columns)}")
    for col in columns_info:
        print(f"  {col[1]} | {col[2]} | NOT NULL: {col[3]} | DEFAULT: {col[4]}")
    
    # Test data (what Eden Mode is trying to insert)
    test_data = {
        'identity_id': 'TEST_ID_123',
        'name': 'Test User',
        'public_key': 'test_public_key_123',
        'metadata': json.dumps({'test': True}),
        'created_at': 1749159999,
        'updated_at': 1749159999,
        'role_class': 'israelite',
        'tribal_affiliation': 'judah',
        'verification_level': 1,
        'covenant_accepted': True,
        'vault_status': 'active',
        'etzem_score': 100,
        'status': 'active',
        'email': '<EMAIL>',
        'protection_tier': 'Basic',
        'zeman_count': 0,
        'cipp_tier': 0,
        'nation_code': 'USA',
        'nation_name': 'United States',
        'nation_id': 'USA',
        'full_name': 'Test User',
        'tribe_name': 'Judah',
        'labor_category': 'priest',
        'eden_mode_completed': True,
        'sabbath_observer': True,
        'deeds_score': 50.0,
        'timezone': 'UTC'
    }
    
    print(f"\n=== TESTING DATA INSERTION ===")
    
    # Filter data to only include columns that exist in the table
    valid_columns = [col for col in test_data.keys() if col in columns]
    invalid_columns = [col for col in test_data.keys() if col not in columns]
    
    if invalid_columns:
        print(f"⚠️  Columns in data but not in table: {invalid_columns}")
    
    filtered_data = {k: v for k, v in test_data.items() if k in valid_columns}
    
    print(f"Valid columns: {len(valid_columns)}")
    print(f"Invalid columns: {len(invalid_columns)}")
    
    # Clean up any existing test data
    cursor.execute("DELETE FROM identities WHERE identity_id = ?", [test_data['identity_id']])
    
    # Test insert
    try:
        placeholders = ', '.join(['?' for _ in filtered_data])
        columns_str = ', '.join(filtered_data.keys())
        values = list(filtered_data.values())
        
        query = f"INSERT INTO identities ({columns_str}) VALUES ({placeholders})"
        print(f"Query: {query}")
        cursor.execute(query, values)
        conn.commit()
        print("✅ Identity insert successful")
        
        # Verify the insert
        cursor.execute("SELECT identity_id, name, email FROM identities WHERE identity_id = ?", 
                     [test_data['identity_id']])
        result = cursor.fetchone()
        if result:
            print(f"✅ Identity verified: {result[0]}, {result[1]}, {result[2]}")
        else:
            print("❌ Identity not found after insert")
            
    except Exception as e:
        print(f"❌ Identity insert failed: {e}")
        print("This is the cause of the 'Internal server error'")
        
        # Show the specific error details
        print(f"\nError type: {type(e).__name__}")
        print(f"Error message: {str(e)}")
    
    # Clean up
    cursor.execute("DELETE FROM identities WHERE identity_id = ?", [test_data['identity_id']])
    conn.commit()
    conn.close()
    
    print("\n=== DEBUGGING COMPLETE ===")

if __name__ == "__main__":
    main()
