#!/usr/bin/env python3
"""
Biblical Tokenomics Demo Script

This script demonstrates all 9 features of the biblical tokenomics system
implemented in the Onnyx blockchain platform.
"""

import os
import sys
import time
import json
import requests
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def print_header(title):
    """Print a formatted header."""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_feature(number, name, status="✅"):
    """Print a feature with status."""
    print(f"{status} Feature {number}: {name}")

def demo_api_call(method, endpoint, data=None, description=""):
    """Demonstrate an API call."""
    base_url = "http://localhost:5000"
    url = f"{base_url}{endpoint}"
    
    print(f"\n🔗 {description}")
    print(f"   {method} {endpoint}")
    
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success: {json.dumps(result, indent=2)[:200]}...")
        else:
            print(f"   ❌ Error {response.status_code}: {response.text[:100]}...")
    
    except requests.exceptions.ConnectionError:
        print(f"   ⚠️  Server not running - this is a demo of the API structure")
    except Exception as e:
        print(f"   ❌ Error: {e}")

def main():
    """Run the biblical tokenomics demonstration."""
    
    print_header("BIBLICAL TOKENOMICS DEMONSTRATION")
    print("Onnyx Blockchain Platform - Complete Implementation")
    print(f"Demo Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print_header("IMPLEMENTED FEATURES")
    
    # List all 9 features
    features = [
        "Jubilee-Based Circulatory Reset System",
        "Tiered Mining Rewards Based on Community Activity", 
        "Gleaning Pool for Community Support",
        "Anti-Usury Lending System",
        "Firstfruits Offering Mechanism",
        "Anti-Concentration Protocol",
        "Biblical Token Classification System",
        "Minimum/Maximum Wage Logic",
        "Sabbath Enforcement and Righteous Boost"
    ]
    
    for i, feature in enumerate(features, 1):
        print_feature(i, feature)
    
    print_header("API DEMONSTRATIONS")
    
    # Demo 1: Sabbath Status
    demo_api_call(
        "GET", 
        "/api/tokenomics/sabbath/status",
        description="Check if it's currently Sabbath period"
    )
    
    # Demo 2: Gleaning Pool Status
    demo_api_call(
        "GET",
        "/api/tokenomics/gleaning-pool", 
        description="Check gleaning pool balance and activity"
    )
    
    # Demo 3: Create a Loan
    demo_api_call(
        "POST",
        "/api/tokenomics/loans",
        data={
            "borrower_id": "demo_borrower_123",
            "amount": 100,
            "token_id": "ONX",
            "grace_blocks": 14400
        },
        description="Create an interest-free loan"
    )
    
    # Demo 4: Make Firstfruits Offering
    demo_api_call(
        "POST",
        "/api/tokenomics/firstfruits",
        data={
            "amount": 25,
            "token_id": "ONX"
        },
        description="Make a firstfruits offering (awards Etzem tokens)"
    )
    
    # Demo 5: Classify a Token
    demo_api_call(
        "POST",
        "/api/tokenomics/tokens/DEMO_TOKEN/classify",
        data={
            "purpose": "labor"
        },
        description="Classify token as 'Avodah' (labor) class"
    )
    
    # Demo 6: Check Concentration
    demo_api_call(
        "GET",
        "/api/tokenomics/concentration/demo_identity_123",
        description="Check wealth concentration status"
    )
    
    # Demo 7: Get Deed History
    demo_api_call(
        "GET",
        "/api/tokenomics/deeds/demo_identity_123",
        description="Get righteous deed history and score"
    )
    
    print_header("TECHNICAL IMPLEMENTATION")
    
    print("📁 Files Created/Modified:")
    files = [
        "shared/models/tokenomics.py - Core biblical tokenomics logic",
        "shared/schemas/production_schema.sql - Database schema updates", 
        "shared/config/chain_parameters.py - Tokenomics configuration",
        "blockchain/vm/opcodes.py - New biblical opcodes",
        "blockchain/consensus/miner.py - Enhanced mining rewards",
        "web/routes/api.py - API endpoints",
        "scripts/migrate_tokenomics.py - Database migration",
        "tests/test_tokenomics.py - Comprehensive test suite",
        "docs/BIBLICAL_TOKENOMICS.md - Complete documentation"
    ]
    
    for file in files:
        print(f"   ✅ {file}")
    
    print("\n🗄️ Database Tables Added:")
    tables = [
        "jubilee_pools - Wealth redistribution pools",
        "dormant_accounts - Inactive account tracking",
        "deeds_ledger - Righteous activity records", 
        "loans - Anti-usury lending system",
        "token_classes - Biblical token classification",
        "sabbath_periods - Sabbath enforcement tracking"
    ]
    
    for table in tables:
        print(f"   ✅ {table}")
    
    print("\n⚙️ New Opcodes:")
    opcodes = [
        "OP_JUBILEE - Jubilee reset operations",
        "OP_LEND - Create interest-free loans",
        "OP_REPAY - Process loan repayments",
        "OP_FORGIVE - Forgive outstanding debts",
        "OP_FIRSTFRUITS - Community offerings",
        "OP_GLEANING_CLAIM - Claim from gleaning pool"
    ]
    
    for opcode in opcodes:
        print(f"   ✅ {opcode}")
    
    print_header("BIBLICAL PRINCIPLES IMPLEMENTED")
    
    principles = [
        "Jubilee (Leviticus 25) - Periodic wealth redistribution",
        "Gleaning (Leviticus 19:9-10) - Provision for the poor",
        "Anti-Usury (Exodus 22:25) - Interest-free lending",
        "Firstfruits (Deuteronomy 26:2) - Community offerings",
        "Sabbath (Exodus 20:8-11) - Rest and spiritual observance",
        "Justice (Deuteronomy 16:20) - Fair reward distribution",
        "Stewardship (1 Corinthians 4:2) - Anti-concentration measures",
        "Community Care (Acts 2:44-47) - Mutual aid and support"
    ]
    
    for principle in principles:
        print(f"   📖 {principle}")
    
    print_header("NEXT STEPS")
    
    steps = [
        "Run migration: python scripts/migrate_tokenomics.py",
        "Run tests: python tests/test_tokenomics.py", 
        "Start server and test API endpoints",
        "Monitor mining rewards with biblical bonuses",
        "Observe Sabbath enforcement in action",
        "Test loan creation and automatic forgiveness",
        "Make firstfruits offerings and receive Etzem tokens",
        "Monitor gleaning pool allocations and claims"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"   {i}. {step}")
    
    print_header("CONCLUSION")
    
    print("🎉 Biblical Tokenomics Implementation Complete!")
    print("\nAll 9 core features have been successfully implemented:")
    print("✅ Jubilee Reset System")
    print("✅ Tiered Mining Rewards") 
    print("✅ Gleaning Pool")
    print("✅ Anti-Usury Lending")
    print("✅ Firstfruits Offerings")
    print("✅ Anti-Concentration Protocol")
    print("✅ Token Classification")
    print("✅ Min/Max Wage Logic")
    print("✅ Sabbath Enforcement")
    
    print("\n🏗️ The system is fully integrated with the existing Onnyx blockchain")
    print("   infrastructure and maintains backward compatibility.")
    
    print("\n📚 For detailed documentation, see: docs/BIBLICAL_TOKENOMICS.md")
    print("🧪 For testing, run: python tests/test_tokenomics.py")
    print("🔧 For migration, run: python scripts/migrate_tokenomics.py")
    
    print("\n" + "="*60)
    print("  Biblical wisdom meets blockchain technology! 🙏")
    print("="*60)

if __name__ == "__main__":
    main()
