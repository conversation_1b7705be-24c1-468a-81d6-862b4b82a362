#!/usr/bin/env python3
"""
Reset Genesis Admin Password
"""

import hashlib
import time
from shared.db.db import db

def reset_genesis_password():
    """Reset the genesis admin password to a known value"""
    
    # New password
    new_password = "Genesis2024!"
    
    print("🔐 Resetting Genesis Admin Password...")
    print(f"Email: <EMAIL>")
    print(f"New Password: {new_password}")
    
    try:
        # Check if account exists
        identity = db.query_one("SELECT * FROM identities WHERE email = ?", ('<EMAIL>',))
        if not identity:
            print("❌ Genesis admin account not found")
            return False
        
        print(f"✅ Found account: {identity['name']} ({identity['identity_id']})")
        
        # Hash the new password
        password_hash = hashlib.sha256(new_password.encode()).hexdigest()
        timestamp = int(time.time())
        
        # Update password in identity_passwords table
        try:
            # First try to update existing password
            result = db.execute("""
                UPDATE identity_passwords 
                SET password_hash = ?, created_at = ?
                WHERE identity_id = ?
            """, (password_hash, timestamp, identity['identity_id']))
            
            if result == 0:
                # No existing password, insert new one
                db.execute("""
                    INSERT INTO identity_passwords (identity_id, password_hash, created_at)
                    VALUES (?, ?, ?)
                """, (identity['identity_id'], password_hash, timestamp))
                print("✅ Password created")
            else:
                print("✅ Password updated")
                
        except Exception as e:
            print(f"❌ Password update failed: {e}")
            return False
        
        # Log the password reset
        try:
            db.execute("""
                INSERT INTO security_audit_log (identity_id, event_type, description, metadata, timestamp)
                VALUES (?, 'PASSWORD_RESET', 'Genesis admin password reset', '{"method": "manual_reset"}', ?)
            """, (identity['identity_id'], timestamp))
            print("✅ Security audit log updated")
        except Exception as e:
            print(f"⚠️ Audit log failed: {e}")
        
        print("\n🎉 Password Reset Successful!")
        print("\n📋 Login Credentials:")
        print(f"   Email: <EMAIL>")
        print(f"   Password: {new_password}")
        print("\n🌐 Login URL:")
        print("   http://127.0.0.1:5000/auth/login")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting password: {e}")
        return False

def verify_password():
    """Verify the password was set correctly"""
    print("\n🔍 Verifying Password...")
    
    try:
        password_record = db.query_one("""
            SELECT * FROM identity_passwords 
            WHERE identity_id = 'ONX4d0fb6bb0dda45a7'
        """)
        
        if password_record:
            print("✅ Password hash exists in database")
            print(f"   Created at: {password_record['created_at']}")
            
            # Verify the hash matches our expected password
            expected_hash = hashlib.sha256("Genesis2024!".encode()).hexdigest()
            if password_record['password_hash'] == expected_hash:
                print("✅ Password hash matches expected value")
                return True
            else:
                print("❌ Password hash does not match expected value")
                return False
        else:
            print("❌ No password record found")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ONNYX Genesis Password Reset")
    print("=" * 50)
    
    success = reset_genesis_password()
    if success:
        verify_success = verify_password()
        if verify_success:
            print("\n✅ Password reset and verification complete!")
            print("\nYou can now login with:")
            print("Email: <EMAIL>")
            print("Password: Genesis2024!")
        else:
            print("\n⚠️ Password reset but verification failed")
    else:
        print("\n❌ Password reset failed")
