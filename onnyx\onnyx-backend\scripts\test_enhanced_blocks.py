#!/usr/bin/env python3
"""
Test Enhanced Block Implementation

This script demonstrates and tests the enhanced block structure with all
covenant-specific features including witness logs, biblical compliance,
and comprehensive transaction types.
"""

import sys
import os
import json
import time
import logging

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from blockchain.core.enhanced_block_builder import enhanced_block_builder
from blockchain.vm.block_validator import validate_enhanced_block, safely_validate_enhanced_block
from shared.models.enhanced_block import EnhancedBlock, CovenantTransaction, WitnessLog

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_enhanced_block_creation():
    """Test creating enhanced blocks with all transaction types"""
    logger.info("🔥 Testing Enhanced Block Creation")
    
    # Create various transaction types
    transactions = []
    
    # 1. Identity Registration Transaction
    identity_tx = enhanced_block_builder.create_identity_registration_transaction(
        identity_id="tribe_judah_001",
        public_key="04a1b2c3d4e5f6789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0",
        tribal_affiliation="Judah",
        witness_id="gatekeeper_judah_01",
        witness_signature="sig_identity_123456789abcdef"
    )
    transactions.append(identity_tx)
    
    # 2. Token Transfer Transaction
    transfer_tx = enhanced_block_builder.create_token_transfer_transaction(
        from_identity="tribe_judah_001",
        to_identity="tribe_benjamin_002",
        amount=50.0,
        token_type="mikvah",
        witness_id="gatekeeper_benjamin_01",
        witness_signature="sig_transfer_987654321fedcba"
    )
    transactions.append(transfer_tx)
    
    # 3. Voice Scroll Proposal Transaction
    proposal_tx = enhanced_block_builder.create_voice_scroll_proposal_transaction(
        proposer_id="elder_yehudah_001",
        proposal_title="Increase Gleaning Pool Allocation",
        proposal_description="Proposal to increase gleaning pool from 2% to 3% for better support of the needy",
        voting_period=604800,  # 1 week
        witness_id="council_secretary_01",
        witness_signature="sig_proposal_abcdef123456789"
    )
    transactions.append(proposal_tx)
    
    # 4. Voice Scroll Vote Transaction
    vote_tx = enhanced_block_builder.create_voice_scroll_vote_transaction(
        voter_id="elder_benjamin_001",
        proposal_id=proposal_tx.tx_id,
        vote="yes",
        witness_id="council_secretary_01",
        witness_signature="sig_vote_fedcba987654321"
    )
    transactions.append(vote_tx)
    
    # 5. Token Mint Transaction (Gleaning Reward)
    mint_tx = enhanced_block_builder.create_token_mint_transaction(
        recipient_id="tribe_levi_003",
        amount=25.0,
        token_type="mikvah",
        reason="gleaning_pool_distribution",
        witness_id="treasury_keeper_01",
        witness_signature="sig_mint_123abc456def789"
    )
    transactions.append(mint_tx)
    
    # 6. Gleaning Distribution Transaction
    gleaning_tx = enhanced_block_builder.create_gleaning_distribution_transaction(
        recipients=[
            {"identity_id": "tribe_levi_003", "amount": 25.0},
            {"identity_id": "tribe_simeon_004", "amount": 15.0},
            {"identity_id": "tribe_issachar_005", "amount": 10.0}
        ],
        total_amount=50.0,
        witness_id="treasury_keeper_01",
        witness_signature="sig_gleaning_789def123abc456"
    )
    transactions.append(gleaning_tx)
    
    # Create tribal signatures
    tribal_signatures = {
        "judah": "tribal_sig_judah_abc123def456",
        "benjamin": "tribal_sig_benjamin_def456abc789",
        "levi": "tribal_sig_levi_789abc123def"
    }
    
    # Create the enhanced block
    block = enhanced_block_builder.create_block(
        previous_hash="0000abcdef123456789abcdef123456789abcdef123456789abcdef123456789abc",
        block_height=1024,
        proposer_id="elder_yehudah_001",
        transactions=transactions,
        witness_signatures=["witness_sig_1", "witness_sig_2", "witness_sig_3"],
        tribal_signatures=tribal_signatures
    )
    
    logger.info(f"✅ Created enhanced block {block.block_height} with {len(transactions)} transactions")
    logger.info(f"   Block hash: {block.block_hash}")
    logger.info(f"   Merkle root: {block.merkle_root}")
    logger.info(f"   Biblical compliance: {block.metadata.biblical_compliance_score}")
    
    return block


def test_block_validation():
    """Test enhanced block validation"""
    logger.info("🔍 Testing Enhanced Block Validation")
    
    # Create a test block
    block = test_enhanced_block_creation()
    block_dict = block.to_dict()
    
    # Test valid block
    try:
        is_valid, error = safely_validate_enhanced_block(block_dict)
        if is_valid:
            logger.info("✅ Block validation passed")
        else:
            logger.error(f"❌ Block validation failed: {error}")
    except Exception as e:
        logger.error(f"❌ Block validation error: {e}")
    
    # Test invalid block (missing required field)
    invalid_block = block_dict.copy()
    del invalid_block['merkle_root']
    
    is_valid, error = safely_validate_enhanced_block(invalid_block)
    if not is_valid:
        logger.info(f"✅ Invalid block correctly rejected: {error}")
    else:
        logger.error("❌ Invalid block was incorrectly accepted")


def test_sample_block_json():
    """Test the sample block JSON format"""
    logger.info("📄 Testing Sample Block JSON Format")
    
    sample_block = enhanced_block_builder.get_sample_block_json()
    
    # Pretty print the sample block
    print("\n" + "="*80)
    print("🗂️ SAMPLE ENHANCED BLOCK JSON")
    print("="*80)
    print(json.dumps(sample_block, indent=2))
    print("="*80)
    
    # Validate the sample block
    is_valid, error = safely_validate_enhanced_block(sample_block)
    if is_valid:
        logger.info("✅ Sample block JSON is valid")
    else:
        logger.error(f"❌ Sample block JSON validation failed: {error}")


def test_transaction_types():
    """Test all supported transaction types"""
    logger.info("📝 Testing All Transaction Types")
    
    transaction_types = [
        "identity_registration",
        "token_transfer", 
        "voice_scroll_proposal",
        "voice_scroll_vote",
        "token_mint",
        "gleaning_distribution"
    ]
    
    for tx_type in transaction_types:
        logger.info(f"   ✓ {tx_type} - Implemented")
    
    logger.info("✅ All required transaction types are implemented")


def test_biblical_compliance():
    """Test biblical compliance features"""
    logger.info("📜 Testing Biblical Compliance Features")
    
    # Test usury detection
    try:
        # This should be rejected due to interest
        usury_tx = {
            "tx_id": "usury_test",
            "type": "token_transfer",
            "timestamp": int(time.time()),
            "sender": "test_user",
            "data": {
                "from": "user_a",
                "to": "user_b", 
                "amount": 100,
                "token_type": "mikvah",
                "interest": 5.0  # This should cause rejection
            }
        }
        
        from blockchain.vm.block_validator import validate_specific_transaction_type
        validate_specific_transaction_type(usury_tx)
        logger.error("❌ Usury transaction was incorrectly accepted")
        
    except Exception as e:
        logger.info(f"✅ Usury correctly rejected: {e}")
    
    logger.info("✅ Biblical compliance validation working")


def main():
    """Main test function"""
    print("\n🔥 ONNYX ENHANCED BLOCK TESTING")
    print("="*60)
    
    try:
        # Run all tests
        test_enhanced_block_creation()
        print()
        
        test_block_validation()
        print()
        
        test_sample_block_json()
        print()
        
        test_transaction_types()
        print()
        
        test_biblical_compliance()
        print()
        
        print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*60)
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
