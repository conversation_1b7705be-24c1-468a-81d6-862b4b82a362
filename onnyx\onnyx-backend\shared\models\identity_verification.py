"""
Identity Verification Model for Gate Keeper Voting System

This module handles the IDENTITY_VERIFICATION proposal type for the Voice Scrolls system.
Gate Keepers vote on Israelite identity registrations through this system.
"""

import json
import time
import uuid
import logging
from typing import Dict, Any, List, Optional
from shared.db.db import db
from shared.models.voice_scroll import VoiceScroll

logger = logging.getLogger(__name__)

class IdentityVerificationProposal:
    """
    Identity Verification Proposal for Gate Keeper voting on Israelite registrations.
    """
    
    def __init__(self, proposal_id: str, identity_id: str, tribe_code: str, 
                 applicant_data: Dict[str, Any], created_at: int = None):
        self.proposal_id = proposal_id
        self.identity_id = identity_id
        self.tribe_code = tribe_code
        self.applicant_data = applicant_data
        self.created_at = created_at or int(time.time())
        self.votes = {}
        self.status = "PENDING"  # PENDING, APPROVED, REJECTED, EXPIRED
        
    @classmethod
    def create_verification_proposal(cls, identity_id: str, tribe_code: str, 
                                   applicant_data: Dict[str, Any]) -> "IdentityVerificationProposal":
        """
        Create a new identity verification proposal for Gate Keeper voting.
        
        Args:
            identity_id: The identity ID requesting verification
            tribe_code: The tribe code they're claiming (e.g., 'JU' for Judah)
            applicant_data: Data submitted by the applicant (proofs, documents, etc.)
            
        Returns:
            IdentityVerificationProposal instance
        """
        proposal_id = f"IDENTITY_VERIFICATION_{tribe_code}_{int(time.time())}_{str(uuid.uuid4())[:8]}"
        
        # Create the proposal
        proposal = cls(
            proposal_id=proposal_id,
            identity_id=identity_id,
            tribe_code=tribe_code,
            applicant_data=applicant_data
        )
        
        # Create corresponding Voice Scroll
        voice_scroll = VoiceScroll.create(
            scroll_id=proposal_id,
            creator_id="SYSTEM_GATE_KEEPER",
            title=f"Identity Verification: {applicant_data.get('name', 'Unknown')} - Tribe of {tribe_code}",
            description=f"Gate Keeper verification required for Israelite identity registration. "
                       f"Applicant claims lineage to tribe {tribe_code}. "
                       f"Review submitted proofs and vote to approve or reject.",
            category="IDENTITY_VERIFICATION",
            expiry_days=30,  # 30 days for Gate Keepers to vote
            effect={
                "type": "IDENTITY_VERIFICATION",
                "identity_id": identity_id,
                "tribe_code": tribe_code,
                "requires_gate_keeper_quorum": True,
                "minimum_votes": 7,  # 7 out of 12 Gate Keepers
                "eligible_voters": cls._get_gate_keeper_identities()
            },
            metadata={
                "applicant_data": applicant_data,
                "verification_type": "israelite_lineage",
                "tribal_verification": True
            }
        )
        
        # Save the proposal to database
        proposal._save_to_db()
        
        logger.info(f"Created identity verification proposal {proposal_id} for {identity_id}")
        return proposal
    
    @classmethod
    def _get_gate_keeper_identities(cls) -> List[str]:
        """Get the list of Gate Keeper identity IDs."""
        try:
            gate_keepers = db.query("""
                SELECT identity_id FROM identities 
                WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
                AND JSON_EXTRACT(metadata, '$.council_member') = 1
            """)
            return [gk['identity_id'] for gk in gate_keepers]
        except Exception as e:
            logger.error(f"Error getting Gate Keeper identities: {e}")
            return []
    
    def add_gate_keeper_vote(self, gate_keeper_id: str, vote: str, 
                           justification: str = None) -> bool:
        """
        Add a Gate Keeper vote to this verification proposal.
        
        Args:
            gate_keeper_id: Identity ID of the Gate Keeper
            vote: "APPROVE" or "REJECT"
            justification: Optional justification for the vote
            
        Returns:
            True if vote was recorded successfully
        """
        try:
            # Verify this is a valid Gate Keeper
            gate_keeper = db.query_one("""
                SELECT identity_id, metadata FROM identities 
                WHERE identity_id = ?
                AND JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
            """, (gate_keeper_id,))
            
            if not gate_keeper:
                logger.warning(f"Invalid Gate Keeper ID: {gate_keeper_id}")
                return False
            
            # Record the vote
            vote_data = {
                "vote": vote,
                "timestamp": int(time.time()),
                "justification": justification
            }
            
            self.votes[gate_keeper_id] = vote_data
            
            # Also add vote to the Voice Scroll
            voice_scroll = VoiceScroll.find_by_id(self.proposal_id)
            if voice_scroll:
                voice_scroll.add_vote(gate_keeper_id, vote.lower())
            
            # Update database
            self._save_to_db()
            
            # Check if we have reached quorum
            self._check_quorum()
            
            logger.info(f"Gate Keeper {gate_keeper_id} voted {vote} on proposal {self.proposal_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording Gate Keeper vote: {e}")
            return False
    
    def _check_quorum(self):
        """Check if quorum has been reached and finalize the proposal."""
        try:
            approve_votes = sum(1 for vote_data in self.votes.values() 
                              if vote_data['vote'] == 'APPROVE')
            reject_votes = sum(1 for vote_data in self.votes.values() 
                             if vote_data['vote'] == 'REJECT')
            total_votes = len(self.votes)
            
            # Quorum: 7 out of 12 Gate Keepers must vote
            quorum_threshold = 7
            
            if total_votes >= quorum_threshold:
                if approve_votes >= quorum_threshold:
                    self._approve_identity()
                elif reject_votes >= (12 - quorum_threshold + 1):  # 6 or more rejections
                    self._reject_identity()
                    
        except Exception as e:
            logger.error(f"Error checking quorum: {e}")
    
    def _approve_identity(self):
        """Approve the identity verification and finalize registration."""
        try:
            self.status = "APPROVED"
            
            # Update the identity with verified status
            db.execute("""
                UPDATE identities 
                SET verification_level = 'Tier_1_Verified',
                    metadata = JSON_SET(
                        metadata,
                        '$.gate_keeper_verified', 1,
                        '$.verification_date', ?,
                        '$.tribal_verification', 1,
                        '$.mikvah_token_eligible', 1
                    )
                WHERE identity_id = ?
            """, (int(time.time()), self.identity_id))
            
            # Create COVENANT_IDENTITY_CREATE transaction
            self._create_covenant_transaction()
            
            # Update Voice Scroll status
            voice_scroll = VoiceScroll.find_by_id(self.proposal_id)
            if voice_scroll:
                voice_scroll.status = "approved"
                voice_scroll.outcome = "IDENTITY_APPROVED"
                voice_scroll.resolved_at = int(time.time())
                voice_scroll.save()
            
            self._save_to_db()
            
            logger.info(f"Identity {self.identity_id} approved by Gate Keepers")
            
        except Exception as e:
            logger.error(f"Error approving identity: {e}")
    
    def _reject_identity(self):
        """Reject the identity verification."""
        try:
            self.status = "REJECTED"
            
            # Update Voice Scroll status
            voice_scroll = VoiceScroll.find_by_id(self.proposal_id)
            if voice_scroll:
                voice_scroll.status = "rejected"
                voice_scroll.outcome = "IDENTITY_REJECTED"
                voice_scroll.resolved_at = int(time.time())
                voice_scroll.save()
            
            self._save_to_db()
            
            logger.info(f"Identity {self.identity_id} rejected by Gate Keepers")
            
        except Exception as e:
            logger.error(f"Error rejecting identity: {e}")
    
    def _create_covenant_transaction(self):
        """Create the COVENANT_IDENTITY_CREATE transaction."""
        try:
            # This would integrate with the blockchain transaction system
            # For now, we'll create a record in the transactions table
            tx_id = f"COVENANT_IDENTITY_{self.identity_id}_{int(time.time())}"
            
            db.execute("""
                INSERT INTO transactions (
                    tx_id, op, identity_id, status, metadata, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                tx_id,
                "COVENANT_IDENTITY_CREATE",
                self.identity_id,
                "confirmed",
                json.dumps({
                    "tribe_code": self.tribe_code,
                    "gate_keeper_verified": True,
                    "verification_proposal_id": self.proposal_id,
                    "vote_count": len(self.votes)
                }),
                int(time.time())
            ))
            
            logger.info(f"Created COVENANT_IDENTITY_CREATE transaction {tx_id}")
            
        except Exception as e:
            logger.error(f"Error creating covenant transaction: {e}")
    
    def _save_to_db(self):
        """Save the proposal to the database."""
        try:
            db.execute("""
                INSERT OR REPLACE INTO identity_verification_proposals (
                    proposal_id, identity_id, tribe_code, applicant_data,
                    votes, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                self.proposal_id,
                self.identity_id,
                self.tribe_code,
                json.dumps(self.applicant_data),
                json.dumps(self.votes),
                self.status,
                self.created_at
            ))
        except Exception as e:
            logger.error(f"Error saving verification proposal: {e}")
    
    @classmethod
    def get_pending_proposals(cls) -> List["IdentityVerificationProposal"]:
        """Get all pending identity verification proposals."""
        try:
            proposals = db.query("""
                SELECT proposal_id, identity_id, tribe_code, applicant_data,
                       votes, status, created_at
                FROM identity_verification_proposals
                WHERE status = 'PENDING'
                ORDER BY created_at DESC
            """)
            
            result = []
            for p in proposals:
                proposal = cls(
                    proposal_id=p['proposal_id'],
                    identity_id=p['identity_id'],
                    tribe_code=p['tribe_code'],
                    applicant_data=json.loads(p['applicant_data']),
                    created_at=p['created_at']
                )
                proposal.votes = json.loads(p['votes'])
                proposal.status = p['status']
                result.append(proposal)
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting pending proposals: {e}")
            return []
