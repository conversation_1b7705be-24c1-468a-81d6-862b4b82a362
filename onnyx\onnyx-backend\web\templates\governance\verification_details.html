{% extends "base.html" %}

{% block title %}Identity Verification Details - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Hero Section -->
    <div class="py-20 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h1 class="text-4xl md:text-6xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Identity Verification Details
                    </span>
                </h1>
                {% if verification %}
                <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                    Gate Keeper verification for {{ verification.applicant_data.name }} 
                    claiming lineage to the Tribe of {{ verification.tribe_code }}
                </p>
                {% endif %}
            </div>
        </div>
    </div>

    {% if verification %}
    <!-- Verification Status -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="glass-card-premium p-8 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl mb-2">
                            {% if verification.status == 'PENDING' %}🔍
                            {% elif verification.status == 'APPROVED' %}✅
                            {% elif verification.status == 'REJECTED' %}❌
                            {% else %}⏳{% endif %}
                        </div>
                        <h3 class="font-orbitron font-bold text-cyber-cyan mb-2">Status</h3>
                        <p class="text-text-secondary">{{ verification.status|title }}</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl mb-2">⚖️</div>
                        <h3 class="font-orbitron font-bold text-cyber-purple mb-2">Votes Cast</h3>
                        <p class="text-text-secondary">{{ verification.votes|length }}/12</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl mb-2">🏛️</div>
                        <h3 class="font-orbitron font-bold text-cyber-blue mb-2">Tribe</h3>
                        <p class="text-text-secondary">{{ verification.tribe_code }}</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl mb-2">📅</div>
                        <h3 class="font-orbitron font-bold text-cyber-green mb-2">Submitted</h3>
                        <p class="text-text-secondary">{{ verification.created_at|format_timestamp }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applicant Information -->
    <div class="py-16 relative bg-gradient-to-r from-cyber-cyan/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-4">
                    Applicant Information
                </h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="glass-card p-8">
                    <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Name:</span>
                            <span class="text-white">{{ verification.applicant_data.name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Email:</span>
                            <span class="text-white">{{ verification.applicant_data.email }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Claimed Tribe:</span>
                            <span class="text-white">{{ verification.applicant_data.tribe_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Registration Date:</span>
                            <span class="text-white">{{ verification.applicant_data.registration_timestamp|format_timestamp }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="glass-card p-8">
                    <h3 class="text-xl font-orbitron font-bold text-cyber-blue mb-4">Evidence Summary</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Lineage Evidence:</span>
                            <span class="text-cyber-green">
                                {% if verification.applicant_data.lineage_evidence %}✓ Provided{% else %}❌ Missing{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Spiritual Testimony:</span>
                            <span class="text-cyber-green">
                                {% if verification.applicant_data.spiritual_testimony %}✓ Provided{% else %}❌ Missing{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Cultural Connections:</span>
                            <span class="text-cyber-green">
                                {% if verification.applicant_data.cultural_connections %}✓ Provided{% else %}❌ Missing{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Community Testimony:</span>
                            <span class="text-cyber-green">
                                {% if verification.applicant_data.community_testimony %}✓ Provided{% else %}❌ Missing{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gate Keeper Votes -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-purple mb-4">
                    Gate Keeper Votes
                </h2>
                <p class="text-lg text-text-secondary">
                    Requires 7 out of 12 Gate Keeper approvals for verification
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for gk in gate_keepers %}
                <div class="glass-card p-6">
                    <div class="text-center mb-4">
                        <div class="text-3xl mb-2">
                            {% if verification.votes.get(gk.identity_id) %}
                                {% if verification.votes[gk.identity_id].vote == 'APPROVE' %}✅
                                {% elif verification.votes[gk.identity_id].vote == 'REJECT' %}❌
                                {% else %}❓{% endif %}
                            {% else %}⏳{% endif %}
                        </div>
                        <h3 class="font-orbitron font-bold text-cyber-cyan mb-2">{{ gk.name }}</h3>
                        <p class="text-sm text-text-secondary">Gate Keeper</p>
                        {% if gk.metadata and gk.metadata.tribe_name %}
                        <p class="text-xs text-cyber-purple">Tribe of {{ gk.metadata.tribe_name }}</p>
                        {% endif %}
                    </div>
                    
                    {% if verification.votes.get(gk.identity_id) %}
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Vote:</span>
                            <span class="{% if verification.votes[gk.identity_id].vote == 'APPROVE' %}text-cyber-green{% else %}text-red-400{% endif %}">
                                {{ verification.votes[gk.identity_id].vote }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Date:</span>
                            <span class="text-white">{{ verification.votes[gk.identity_id].timestamp|format_timestamp }}</span>
                        </div>
                        {% if verification.votes[gk.identity_id].justification %}
                        <div class="mt-3 p-3 bg-gradient-to-r from-cyber-blue/10 to-transparent rounded">
                            <p class="text-xs text-text-tertiary italic">
                                "{{ verification.votes[gk.identity_id].justification }}"
                            </p>
                        </div>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="text-center text-text-tertiary">
                        <p class="text-sm">Vote pending</p>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Voice Scroll Information -->
    {% if voice_scroll %}
    <div class="py-16 relative bg-gradient-to-r from-cyber-blue/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-blue mb-4">
                    Voice Scroll Details
                </h2>
            </div>
            
            <div class="glass-card p-8">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">{{ voice_scroll.title }}</h3>
                <p class="text-text-secondary mb-6">{{ voice_scroll.description }}</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="font-semibold text-cyber-purple mb-2">Category</h4>
                        <p class="text-text-secondary">{{ voice_scroll.category }}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold text-cyber-purple mb-2">Status</h4>
                        <p class="text-text-secondary">{{ voice_scroll.status|title }}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold text-cyber-purple mb-2">Expires</h4>
                        <p class="text-text-secondary">{{ voice_scroll.expires_at|format_timestamp }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- No Verification Found -->
    <div class="py-20 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-card-premium p-12">
                <div class="text-6xl mb-6">❓</div>
                <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Verification Not Found
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    The requested identity verification proposal could not be found or may have been removed.
                </p>
                <a href="{{ url_for('governance.public_governance') }}"
                   class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                    ← Back to Governance
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
