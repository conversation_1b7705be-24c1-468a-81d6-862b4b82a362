{% extends "base.html" %}

{% block title %}Page Not Found - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative flex items-center justify-center py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
        <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyber-cyan rounded-full animate-ping"></div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <!-- ONNYX Logo -->
        <div class="mb-8 flex justify-center">
            <div class="flex items-center justify-center group">
                <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                     alt="ONNYX Logo"
                     class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <!-- Fallback symbol only if image fails -->
                <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
            </div>
        </div>

        <!-- Error Display -->
        <div class="glass-card-premium p-12 mb-12 rounded-3xl">
            <div class="w-32 h-32 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl shadow-cyber-cyan/30 animate-pulse">
                <svg class="w-16 h-16 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>

            <h1 class="text-8xl md:text-9xl font-orbitron font-bold mb-6">
                <span class="hologram-text bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">404</span>
            </h1>
            <h2 class="text-3xl md:text-4xl font-orbitron font-bold text-cyber-cyan mb-6">Page Not Found</h2>
            <p class="text-xl text-text-secondary mb-8 max-w-2xl mx-auto leading-relaxed">
                The page you're looking for doesn't exist on the ONNYX platform.
                It might have been moved, deleted, or you entered the wrong URL.
            </p>

            <!-- Primary Action -->
            <div class="mb-8">
                <a href="{{ url_for('index') }}"
                   class="glass-button-primary px-12 py-4 rounded-2xl text-lg font-orbitron font-bold transition-all duration-300 hover:scale-105 shadow-lg shadow-cyber-cyan/20 hover:shadow-xl hover:shadow-cyber-cyan/30 min-h-[56px] flex items-center justify-center mx-auto">
                    <span class="mr-3 text-2xl">🏠</span>
                    <span>Return Home</span>
                </a>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="glass-card-enhanced p-8 rounded-2xl">
            <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6 text-center">Quick Navigation</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <a href="{{ url_for('sela.directory') }}"
                   class="glass-button-secondary px-6 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 text-center block min-h-[60px] flex items-center justify-center">
                    <span class="mr-3 text-xl">🏢</span>
                    <span>Validator Directory</span>
                </a>
                <a href="{{ url_for('explorer.index') }}"
                   class="glass-button-secondary px-6 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 text-center block min-h-[60px] flex items-center justify-center">
                    <span class="mr-3 text-xl">🔍</span>
                    <span>Blockchain Explorer</span>
                </a>
                <a href="{{ url_for('register_choice') }}"
                   class="glass-button-secondary px-6 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 text-center block min-h-[60px] flex items-center justify-center">
                    <span class="mr-3 text-xl">🔐</span>
                    <span>Identity Portal</span>
                </a>
            </div>
        </div>

        <!-- Network Status -->
        <div class="mt-12 glass-card p-4 rounded-xl">
            <div class="flex items-center justify-center space-x-8 text-sm">
                <div class="flex items-center space-x-3">
                    <div class="status-dot online"></div>
                    <span class="text-text-secondary font-medium">Network Online</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="status-dot confirmed"></div>
                    <span class="text-text-secondary font-medium">Blockchain Active</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="status-dot online"></div>
                    <span class="text-text-secondary font-medium">ONNYX Platform</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
