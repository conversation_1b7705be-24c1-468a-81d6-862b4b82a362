#!/usr/bin/env python3
"""
Fix Remaining Column Issues
Adds missing columns that are causing errors in the explorer
"""

import sys
import os
import sqlite3

def get_db_connection():
    """Get database connection."""
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'db', 'db', 'onnyx.db')
    return sqlite3.connect(db_path)

def fix_selas_table():
    """Add missing status column to selas table."""
    print("🔧 Fixing selas table...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Check if status column exists
    cursor.execute("PRAGMA table_info(selas)")
    columns = [col[1] for col in cursor.fetchall()]
    
    if 'status' not in columns:
        try:
            cursor.execute("ALTER TABLE selas ADD COLUMN status TEXT DEFAULT 'active'")
            print("✅ Added status column to selas table")
        except Exception as e:
            print(f"❌ Failed to add status column: {e}")
    else:
        print("ℹ️ Status column already exists in selas table")
    
    conn.commit()
    conn.close()

def fix_identities_table():
    """Add missing cipp_tier column to identities table."""
    print("🔧 Fixing identities table...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Check if cipp_tier column exists
    cursor.execute("PRAGMA table_info(identities)")
    columns = [col[1] for col in cursor.fetchall()]
    
    if 'cipp_tier' not in columns:
        try:
            cursor.execute("ALTER TABLE identities ADD COLUMN cipp_tier INTEGER DEFAULT 0")
            print("✅ Added cipp_tier column to identities table")
        except Exception as e:
            print(f"❌ Failed to add cipp_tier column: {e}")
    else:
        print("ℹ️ cipp_tier column already exists in identities table")
    
    # Update existing tribal elders to have proper cipp_tier values
    try:
        cursor.execute("""
            UPDATE identities 
            SET cipp_tier = verification_level 
            WHERE role_class = 'Tribal_Elder' AND cipp_tier != verification_level
        """)
        updated = cursor.rowcount
        if updated > 0:
            print(f"✅ Updated {updated} tribal elders with proper cipp_tier values")
    except Exception as e:
        print(f"❌ Failed to update cipp_tier values: {e}")
    
    conn.commit()
    conn.close()

def verify_fixes():
    """Verify that the fixes worked."""
    print("\n🔍 Verifying fixes...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Test selas query
    try:
        cursor.execute("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")
        selas_count = cursor.fetchone()[0]
        print(f"✅ Selas query works: {selas_count} active selas")
    except Exception as e:
        print(f"❌ Selas query still failing: {e}")
    
    # Test identities query
    try:
        cursor.execute("""
            SELECT identity_id, name, tribal_affiliation, cipp_tier
            FROM identities
            WHERE tribal_affiliation IS NOT NULL
        """)
        tribal_members = cursor.fetchall()
        print(f"✅ Identities query works: {len(tribal_members)} tribal members")
        
        # Show tribal elders
        for member in tribal_members:
            print(f"   - {member[1]} ({member[2]}) - CIPP Tier {member[3]}")
            
    except Exception as e:
        print(f"❌ Identities query still failing: {e}")
    
    conn.close()

def main():
    """Main function to fix remaining column issues."""
    print("🌟 FIXING REMAINING COLUMN ISSUES")
    print("=" * 50)
    
    try:
        fix_selas_table()
        fix_identities_table()
        verify_fixes()
        
        print(f"\n🎉 COLUMN FIXES COMPLETED!")
        print("✅ selas table: status column added")
        print("✅ identities table: cipp_tier column added")
        print("✅ Explorer should now work without column errors")
        
    except Exception as e:
        print(f"❌ Error fixing columns: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
