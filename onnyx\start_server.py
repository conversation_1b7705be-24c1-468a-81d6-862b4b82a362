#!/usr/bin/env python3
"""
Test server for Eden Mode debugging
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'onnyx-backend'))

from web.app import create_app

if __name__ == "__main__":
    app = create_app()
    print("Starting test server on http://127.0.0.1:5000")
    print("Navigate to http://127.0.0.1:5000/auth/eden-mode/step1 to test")
    app.run(debug=True, host='127.0.0.1', port=5000)
