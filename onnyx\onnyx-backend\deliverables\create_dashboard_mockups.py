#!/usr/bin/env python3
"""
Create ONNYX Dashboard Mockups using HTML/CSS/JS
"""

import os
from datetime import datetime

def create_dashboard_mockups():
    """Create comprehensive dashboard mockups for ONNYX platform"""

    print("🎨 Creating ONNYX Dashboard Mockups")
    print("=" * 40)

    # Ensure mockups directory exists
    os.makedirs('deliverables/dashboard_mockups', exist_ok=True)

    # Create main dashboard HTML
    create_main_dashboard()

    # Create sabbath dashboard
    create_sabbath_dashboard()

    # Create lending dashboard
    create_lending_dashboard()

    # Create tribal governance dashboard
    create_tribal_dashboard()

    # Create shared CSS
    create_shared_css()

    # Create shared JavaScript
    create_shared_js()

    print("✅ All dashboard mockups created successfully")

def create_main_dashboard():
    """Create the main dashboard mockup"""

    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONNYX Platform - Main Dashboard</title>
    <link rel="stylesheet" href="shared_styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body class="dashboard-body">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="spinning-n">N</div>
                <h1>ONNYX PLATFORM</h1>
                <p>Biblical Economic Covenant Community</p>
            </div>
            <nav class="main-nav">
                <a href="#dashboard" class="nav-link active">Dashboard</a>
                <a href="#sabbath" class="nav-link">Sabbath</a>
                <a href="#lending" class="nav-link">Lending</a>
                <a href="#tribal" class="nav-link">Tribal</a>
                <a href="#explorer" class="nav-link">Explorer</a>
            </nav>
            <div class="user-section">
                <div class="user-info">
                    <span class="user-name">Jedidiah Israel</span>
                    <span class="user-tribe">Tribe of Benjamin</span>
                </div>
                <div class="user-avatar">JI</div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Status Cards -->
        <section class="status-cards">
            <div class="status-card sabbath-status">
                <div class="card-icon">🕯️</div>
                <div class="card-content">
                    <h3>Sabbath Status</h3>
                    <p class="status-text">Not Currently Sabbath</p>
                    <p class="next-sabbath">Next: Friday 6:00 PM</p>
                    <div class="compliance-score">
                        <span>Compliance: 98%</span>
                        <div class="score-bar">
                            <div class="score-fill" style="width: 98%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="status-card balance-status">
                <div class="card-icon">💎</div>
                <div class="card-content">
                    <h3>ONX Balance</h3>
                    <p class="balance-amount">1,247.89 ONX</p>
                    <p class="balance-usd">≈ $3,743.67 USD</p>
                    <div class="balance-change positive">
                        <span>+5.2% (24h)</span>
                    </div>
                </div>
            </div>

            <div class="status-card lending-status">
                <div class="card-icon">🤝</div>
                <div class="card-content">
                    <h3>Active Loans</h3>
                    <p class="loan-count">3 Active Loans</p>
                    <p class="loan-total">Total: 500 ONX</p>
                    <div class="loan-breakdown">
                        <span>2 Interest-Free</span>
                        <span>1 Standard</span>
                    </div>
                </div>
            </div>

            <div class="status-card tribal-status">
                <div class="card-icon">👑</div>
                <div class="card-content">
                    <h3>Tribal Standing</h3>
                    <p class="tribal-role">Gate Keeper</p>
                    <p class="tribal-rank">Rank: Elder</p>
                    <div class="tribal-score">
                        <span>Etzem: 850</span>
                        <div class="etzem-bar">
                            <div class="etzem-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <h2>Quick Actions</h2>
            <div class="action-grid">
                <button class="action-btn primary">
                    <span class="btn-icon">💰</span>
                    <span>Send ONX</span>
                </button>
                <button class="action-btn secondary">
                    <span class="btn-icon">📥</span>
                    <span>Receive ONX</span>
                </button>
                <button class="action-btn tertiary">
                    <span class="btn-icon">🤝</span>
                    <span>Create Loan</span>
                </button>
                <button class="action-btn quaternary">
                    <span class="btn-icon">⚖️</span>
                    <span>Tribal Vote</span>
                </button>
            </div>
        </section>

        <!-- Recent Activity -->
        <section class="recent-activity">
            <h2>Recent Activity</h2>
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon">💰</div>
                    <div class="activity-details">
                        <p class="activity-title">Sent 50 ONX to Ephraim Trader</p>
                        <p class="activity-time">2 hours ago</p>
                    </div>
                    <div class="activity-amount">-50 ONX</div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">🤝</div>
                    <div class="activity-details">
                        <p class="activity-title">Interest-free loan repayment received</p>
                        <p class="activity-time">5 hours ago</p>
                    </div>
                    <div class="activity-amount">+25 ONX</div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">⚖️</div>
                    <div class="activity-details">
                        <p class="activity-title">Voted on Gate Keeper proposal</p>
                        <p class="activity-time">1 day ago</p>
                    </div>
                    <div class="activity-status">Approved</div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">🕯️</div>
                    <div class="activity-details">
                        <p class="activity-title">Sabbath observance completed</p>
                        <p class="activity-time">2 days ago</p>
                    </div>
                    <div class="activity-bonus">+10 Etzem</div>
                </div>
            </div>
        </section>

        <!-- Charts Section -->
        <section class="charts-section">
            <div class="chart-container">
                <h3>ONX Price Chart (7 Days)</h3>
                <div class="chart-placeholder">
                    <canvas id="priceChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="chart-container">
                <h3>Sabbath Compliance Trend</h3>
                <div class="chart-placeholder">
                    <canvas id="complianceChart" width="400" height="200"></canvas>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="dashboard-footer">
        <div class="footer-content">
            <p>&copy; 2025 ONNYX Platform | Biblical Economic Covenant Community</p>
            <div class="footer-links">
                <a href="#terms">Terms</a>
                <a href="#privacy">Privacy</a>
                <a href="#support">Support</a>
            </div>
        </div>
    </footer>

    <script src="shared_scripts.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            updateRealTimeData();

            // Update data every 30 seconds
            setInterval(updateRealTimeData, 30000);
        });
    </script>
</body>
</html>'''

    with open('deliverables/dashboard_mockups/main_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("✅ Created main dashboard mockup")

def create_sabbath_dashboard():
    """Create the sabbath dashboard mockup"""

    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONNYX Platform - Sabbath Dashboard</title>
    <link rel="stylesheet" href="shared_styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body class="dashboard-body sabbath-theme">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="spinning-n">N</div>
                <h1>SABBATH ENFORCEMENT</h1>
                <p>Biblical Rest & Observance</p>
            </div>
            <nav class="main-nav">
                <a href="#dashboard" class="nav-link">Dashboard</a>
                <a href="#sabbath" class="nav-link active">Sabbath</a>
                <a href="#lending" class="nav-link">Lending</a>
                <a href="#tribal" class="nav-link">Tribal</a>
                <a href="#explorer" class="nav-link">Explorer</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Current Sabbath Status -->
        <section class="sabbath-status-section">
            <div class="sabbath-clock">
                <div class="clock-face">
                    <div class="clock-hands">
                        <div class="hour-hand"></div>
                        <div class="minute-hand"></div>
                    </div>
                    <div class="clock-center"></div>
                </div>
                <div class="sabbath-info">
                    <h2>Current Status</h2>
                    <p class="status-text not-sabbath">Not Currently Sabbath</p>
                    <p class="next-sabbath">Next Sabbath begins in:</p>
                    <div class="countdown">
                        <div class="countdown-item">
                            <span class="countdown-number">2</span>
                            <span class="countdown-label">Days</span>
                        </div>
                        <div class="countdown-item">
                            <span class="countdown-number">14</span>
                            <span class="countdown-label">Hours</span>
                        </div>
                        <div class="countdown-item">
                            <span class="countdown-number">32</span>
                            <span class="countdown-label">Minutes</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Sabbath Types -->
        <section class="sabbath-types">
            <h2>Sabbath Calendar</h2>
            <div class="sabbath-grid">
                <div class="sabbath-card weekly">
                    <div class="sabbath-icon">🕯️</div>
                    <h3>Weekly Sabbath</h3>
                    <p>Every Friday evening to Saturday evening</p>
                    <div class="sabbath-reference">Exodus 20:8-11</div>
                    <div class="next-occurrence">Next: Friday 6:00 PM</div>
                </div>

                <div class="sabbath-card new-moon">
                    <div class="sabbath-icon">🌙</div>
                    <h3>New Moon Sabbath</h3>
                    <p>Monthly lunar calendar observance</p>
                    <div class="sabbath-reference">Numbers 28:11</div>
                    <div class="next-occurrence">Next: Adar 1st (Feb 17)</div>
                </div>

                <div class="sabbath-card feast-day">
                    <div class="sabbath-icon">🎭</div>
                    <h3>Biblical Feast Days</h3>
                    <p>Annual holy day observances</p>
                    <div class="sabbath-reference">Leviticus 23</div>
                    <div class="next-occurrence">Next: Passover (Apr 13)</div>
                </div>

                <div class="sabbath-card high-sabbath">
                    <div class="sabbath-icon">👑</div>
                    <h3>High Sabbaths</h3>
                    <p>Special covenant observances</p>
                    <div class="sabbath-reference">Various</div>
                    <div class="next-occurrence">Next: Day of Atonement</div>
                </div>
            </div>
        </section>

        <!-- Compliance Tracking -->
        <section class="compliance-section">
            <h2>Sabbath Compliance</h2>
            <div class="compliance-dashboard">
                <div class="compliance-score-card">
                    <div class="score-circle">
                        <svg class="score-svg" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="45" fill="none" stroke="#333" stroke-width="8"/>
                            <circle cx="50" cy="50" r="45" fill="none" stroke="#00fff7" stroke-width="8"
                                    stroke-dasharray="283" stroke-dashoffset="28.3" transform="rotate(-90 50 50)"/>
                        </svg>
                        <div class="score-text">
                            <span class="score-number">98%</span>
                            <span class="score-label">Compliance</span>
                        </div>
                    </div>
                    <div class="score-details">
                        <p>Excellent sabbath observance!</p>
                        <p>52 of 53 sabbaths observed</p>
                        <p>Last violation: 3 months ago</p>
                    </div>
                </div>

                <div class="violation-history">
                    <h3>Recent Activity</h3>
                    <div class="violation-list">
                        <div class="violation-item success">
                            <div class="violation-icon">✅</div>
                            <div class="violation-details">
                                <p>Weekly Sabbath Observed</p>
                                <p class="violation-time">Last Saturday</p>
                            </div>
                            <div class="violation-bonus">+5 Etzem</div>
                        </div>

                        <div class="violation-item success">
                            <div class="violation-icon">✅</div>
                            <div class="violation-details">
                                <p>New Moon Sabbath Observed</p>
                                <p class="violation-time">2 weeks ago</p>
                            </div>
                            <div class="violation-bonus">+10 Etzem</div>
                        </div>

                        <div class="violation-item warning">
                            <div class="violation-icon">⚠️</div>
                            <div class="violation-details">
                                <p>Late sabbath entry (5 min grace)</p>
                                <p class="violation-time">3 weeks ago</p>
                            </div>
                            <div class="violation-penalty">Warning</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upcoming Sabbaths -->
        <section class="upcoming-sabbaths">
            <h2>Upcoming Sabbaths</h2>
            <div class="sabbath-calendar">
                <div class="calendar-item">
                    <div class="calendar-date">
                        <span class="date-day">17</span>
                        <span class="date-month">Feb</span>
                    </div>
                    <div class="calendar-details">
                        <h4>New Moon of Adar</h4>
                        <p>Monthly sabbath observance</p>
                        <p class="calendar-time">Sunset to Sunset</p>
                    </div>
                    <div class="calendar-type new-moon">New Moon</div>
                </div>

                <div class="calendar-item">
                    <div class="calendar-date">
                        <span class="date-day">21</span>
                        <span class="date-month">Feb</span>
                    </div>
                    <div class="calendar-details">
                        <h4>Weekly Sabbath</h4>
                        <p>Regular weekly observance</p>
                        <p class="calendar-time">Friday 6:00 PM - Saturday 7:00 PM</p>
                    </div>
                    <div class="calendar-type weekly">Weekly</div>
                </div>

                <div class="calendar-item">
                    <div class="calendar-date">
                        <span class="date-day">13</span>
                        <span class="date-month">Apr</span>
                    </div>
                    <div class="calendar-details">
                        <h4>Passover (First Day)</h4>
                        <p>Annual feast day celebration</p>
                        <p class="calendar-time">Evening to Evening</p>
                    </div>
                    <div class="calendar-type feast">Feast Day</div>
                </div>
            </div>
        </section>
    </main>

    <script src="shared_scripts.js"></script>
    <script>
        // Initialize sabbath dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeSabbathDashboard();
            updateSabbathCountdown();

            // Update countdown every minute
            setInterval(updateSabbathCountdown, 60000);
        });
    </script>
</body>
</html>'''

    with open('deliverables/dashboard_mockups/sabbath_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("✅ Created sabbath dashboard mockup")

def create_lending_dashboard():
    """Create the lending dashboard mockup"""

    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONNYX Platform - Biblical Lending</title>
    <link rel="stylesheet" href="shared_styles.css">
</head>
<body class="dashboard-body lending-theme">
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="spinning-n">N</div>
                <h1>BIBLICAL LENDING</h1>
                <p>Interest-Free Covenant Economics</p>
            </div>
        </div>
    </header>

    <main class="dashboard-main">
        <section class="lending-overview">
            <h2>Lending Overview</h2>
            <div class="lending-stats">
                <div class="stat-card">
                    <h3>Active Loans</h3>
                    <p class="stat-number">3</p>
                    <p class="stat-detail">Total: 500 ONX</p>
                </div>
                <div class="stat-card">
                    <h3>Interest-Free Loans</h3>
                    <p class="stat-number">2</p>
                    <p class="stat-detail">Israelite to Israelite</p>
                </div>
                <div class="stat-card">
                    <h3>Loans Given</h3>
                    <p class="stat-number">7</p>
                    <p class="stat-detail">Total: 1,200 ONX</p>
                </div>
            </div>
        </section>

        <section class="biblical-rules">
            <h2>Biblical Lending Rules</h2>
            <div class="rules-grid">
                <div class="rule-card israelite-to-israelite">
                    <h3>Israelite → Israelite</h3>
                    <p class="interest-rate">0% Interest</p>
                    <p class="biblical-ref">Exodus 22:25</p>
                    <p>"Thou shalt not be to him as an usurer"</p>
                </div>
                <div class="rule-card israelite-to-witness">
                    <h3>Israelite → Witness Nation</h3>
                    <p class="interest-rate">Interest Allowed</p>
                    <p class="biblical-ref">Deuteronomy 23:20</p>
                    <p>"Unto a stranger thou mayest lend upon usury"</p>
                </div>
            </div>
        </section>

        <section class="loan-management">
            <h2>Your Loans</h2>
            <div class="loan-tabs">
                <button class="tab-btn active" data-tab="borrowed">Borrowed</button>
                <button class="tab-btn" data-tab="lent">Lent</button>
            </div>

            <div class="loan-list" id="borrowed-loans">
                <div class="loan-item">
                    <div class="loan-header">
                        <h4>Loan from Judah Leader</h4>
                        <span class="loan-status active">Active</span>
                    </div>
                    <div class="loan-details">
                        <p>Amount: 200 ONX | Interest: 0% | Due: March 15, 2025</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                        <p>Repaid: 120 ONX (60%) | Remaining: 80 ONX</p>
                    </div>
                    <div class="loan-actions">
                        <button class="btn-primary">Make Payment</button>
                        <button class="btn-secondary">View Details</button>
                    </div>
                </div>
            </div>
        </section>
    </main>
</body>
</html>'''

    with open('deliverables/dashboard_mockups/lending_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("✅ Created lending dashboard mockup")

def create_tribal_dashboard():
    """Create the tribal governance dashboard mockup"""

    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONNYX Platform - Tribal Governance</title>
    <link rel="stylesheet" href="shared_styles.css">
</head>
<body class="dashboard-body tribal-theme">
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="spinning-n">N</div>
                <h1>TRIBAL GOVERNANCE</h1>
                <p>Covenant Community Leadership</p>
            </div>
        </div>
    </header>

    <main class="dashboard-main">
        <section class="tribal-overview">
            <h2>Tribe of Benjamin</h2>
            <div class="tribal-stats">
                <div class="tribal-symbol">🏹</div>
                <div class="tribal-info">
                    <h3>Your Role: Gate Keeper</h3>
                    <p>Rank: Elder | Etzem Score: 850</p>
                    <p>Tribal Specialty: Technology & Innovation</p>
                </div>
            </div>
        </section>

        <section class="twelve-tribes">
            <h2>The Twelve Tribes</h2>
            <div class="tribes-grid">
                <div class="tribe-card judah">
                    <div class="tribe-symbol">🦁</div>
                    <h3>Judah</h3>
                    <p>Leadership & Governance</p>
                    <span class="member-count">247 members</span>
                </div>
                <div class="tribe-card levi">
                    <div class="tribe-symbol">⚖️</div>
                    <h3>Levi</h3>
                    <p>Spiritual Oversight</p>
                    <span class="member-count">89 members</span>
                </div>
                <div class="tribe-card benjamin active">
                    <div class="tribe-symbol">🏹</div>
                    <h3>Benjamin</h3>
                    <p>Technology & Innovation</p>
                    <span class="member-count">156 members</span>
                </div>
                <!-- More tribes... -->
            </div>
        </section>

        <section class="governance-proposals">
            <h2>Active Proposals</h2>
            <div class="proposal-list">
                <div class="proposal-item">
                    <h4>Gate Keeper Verification: Sarah Ephraim</h4>
                    <p>Proposal to verify new Gate Keeper from Tribe of Ephraim</p>
                    <div class="voting-status">
                        <span>Votes: 12 For, 2 Against</span>
                        <div class="vote-buttons">
                            <button class="vote-for">Vote For</button>
                            <button class="vote-against">Vote Against</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</body>
</html>'''

    with open('deliverables/dashboard_mockups/tribal_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("✅ Created tribal governance dashboard mockup")

def create_shared_css():
    """Create shared CSS for all dashboard mockups"""

    css_content = '''/* ONNYX Platform Dashboard Styles */
/* Cyber-Tribal Aesthetic with Biblical Elements */

:root {
    /* ONNYX Color Palette */
    --cyber-cyan: #00fff7;
    --cyber-purple: #9a00ff;
    --onyx-black: #0a0a0a;
    --cyber-gold: #ffd700;
    --tribal-silver: #c0c0c0;
    --deep-space: #1a1a2e;
    --electric-blue: #16213e;
    --neon-green: #39ff14;

    /* Typography */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Arial', sans-serif;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* Borders & Shadows */
    --border-radius: 8px;
    --glow-cyan: 0 0 20px var(--cyber-cyan);
    --glow-purple: 0 0 20px var(--cyber-purple);
    --glow-gold: 0 0 20px var(--cyber-gold);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--onyx-black) 0%, var(--deep-space) 100%);
    color: var(--tribal-silver);
    min-height: 100vh;
    overflow-x: hidden;
}

.dashboard-body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles */
.dashboard-header {
    background: linear-gradient(90deg, var(--onyx-black) 0%, var(--electric-blue) 100%);
    border-bottom: 2px solid var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.spinning-n {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 900;
    color: var(--onyx-black);
    animation: spin 10s linear infinite;
    box-shadow: var(--glow-cyan);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.logo-section h1 {
    font-size: 28px;
    color: var(--cyber-cyan);
    text-shadow: var(--glow-cyan);
    margin: 0;
}

.logo-section p {
    font-size: 14px;
    color: var(--cyber-purple);
    margin: 0;
}

.main-nav {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--tribal-silver);
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.nav-link:hover {
    color: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
}

.nav-link.active {
    color: var(--cyber-gold);
    border-color: var(--cyber-gold);
    box-shadow: var(--glow-gold);
}

.user-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-info {
    text-align: right;
}

.user-name {
    display: block;
    font-weight: bold;
    color: var(--cyber-cyan);
}

.user-tribe {
    display: block;
    font-size: 12px;
    color: var(--cyber-purple);
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--onyx-black);
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Status Cards */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.status-card {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 1px solid var(--cyber-cyan);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--glow-cyan);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 247, 0.3);
}

.card-icon {
    font-size: 48px;
    opacity: 0.8;
}

.card-content h3 {
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-sm);
    font-size: 18px;
}

.status-text {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
}

.balance-amount {
    font-size: 24px;
    color: var(--cyber-gold);
    font-weight: bold;
}

.compliance-score, .tribal-score {
    margin-top: var(--spacing-sm);
}

.score-bar, .etzem-bar {
    width: 100%;
    height: 8px;
    background: var(--onyx-black);
    border-radius: 4px;
    overflow: hidden;
    margin-top: var(--spacing-xs);
}

.score-fill, .etzem-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    transition: width 0.3s ease;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-xxl);
}

.quick-actions h2 {
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-lg);
    font-size: 24px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.action-btn {
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-cyan));
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    color: var(--onyx-black);
    font-family: var(--font-primary);
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(154, 0, 255, 0.4);
}

.btn-icon {
    font-size: 20px;
}

/* Activity List */
.recent-activity {
    margin-bottom: var(--spacing-xxl);
}

.recent-activity h2 {
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-lg);
    font-size: 24px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 1px solid var(--cyber-purple);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.3s ease;
}

.activity-item:hover {
    border-color: var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
}

.activity-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.activity-details {
    flex: 1;
}

.activity-title {
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: 12px;
    color: var(--tribal-silver);
    opacity: 0.7;
}

.activity-amount {
    font-weight: bold;
    color: var(--cyber-gold);
}

/* Footer */
.dashboard-footer {
    background: var(--onyx-black);
    border-top: 1px solid var(--cyber-cyan);
    padding: var(--spacing-lg);
    text-align: center;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--tribal-silver);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--cyber-cyan);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .main-nav {
        order: 3;
    }

    .status-cards {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* Theme Variations */
.sabbath-theme {
    --primary-color: var(--cyber-gold);
    --secondary-color: var(--cyber-purple);
}

.lending-theme {
    --primary-color: var(--neon-green);
    --secondary-color: var(--cyber-cyan);
}

.tribal-theme {
    --primary-color: var(--cyber-purple);
    --secondary-color: var(--cyber-gold);
}'''

    with open('deliverables/dashboard_mockups/shared_styles.css', 'w', encoding='utf-8') as f:
        f.write(css_content)

    print("✅ Created shared CSS styles")

def create_shared_js():
    """Create shared JavaScript for dashboard functionality"""

    js_content = '''// ONNYX Platform Dashboard JavaScript
// Handles interactive elements and real-time updates

// Initialize dashboard functionality
function initializeDashboard() {
    console.log('🚀 ONNYX Dashboard Initialized');

    // Add hover effects to cards
    addCardHoverEffects();

    // Initialize navigation
    initializeNavigation();

    // Add click handlers
    addClickHandlers();
}

// Add hover effects to status cards
function addCardHoverEffects() {
    const cards = document.querySelectorAll('.status-card, .activity-item, .action-btn');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Initialize navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Navigate to appropriate dashboard
            const target = this.getAttribute('href').substring(1);
            navigateToDashboard(target);
        });
    });
}

// Navigate to different dashboard sections
function navigateToDashboard(section) {
    console.log(`Navigating to ${section} dashboard`);

    // In a real application, this would handle routing
    switch(section) {
        case 'dashboard':
            window.location.href = 'main_dashboard.html';
            break;
        case 'sabbath':
            window.location.href = 'sabbath_dashboard.html';
            break;
        case 'lending':
            window.location.href = 'lending_dashboard.html';
            break;
        case 'tribal':
            window.location.href = 'tribal_dashboard.html';
            break;
        default:
            console.log('Unknown section:', section);
    }
}

// Add click handlers for interactive elements
function addClickHandlers() {
    // Action buttons
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            handleActionClick(action);
        });
    });

    // Tab buttons
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.getAttribute('data-tab');
            switchTab(tab);
        });
    });
}

// Handle action button clicks
function handleActionClick(action) {
    console.log(`Action clicked: ${action}`);

    // Show modal or navigate based on action
    switch(action) {
        case 'Send ONX':
            showSendModal();
            break;
        case 'Receive ONX':
            showReceiveModal();
            break;
        case 'Create Loan':
            showLoanModal();
            break;
        case 'Tribal Vote':
            showVoteModal();
            break;
        default:
            console.log('Unknown action:', action);
    }
}

// Switch between tabs
function switchTab(tabName) {
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to clicked tab
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Show/hide content based on tab
    console.log(`Switched to ${tabName} tab`);
}

// Update real-time data
function updateRealTimeData() {
    console.log('📊 Updating real-time data');

    // Update balance
    updateBalance();

    // Update sabbath countdown
    updateSabbathCountdown();

    // Update compliance score
    updateComplianceScore();
}

// Update ONX balance
function updateBalance() {
    const balanceElement = document.querySelector('.balance-amount');
    if (balanceElement) {
        // Simulate real-time balance updates
        const currentBalance = parseFloat(balanceElement.textContent.replace(/[^0-9.]/g, ''));
        const fluctuation = (Math.random() - 0.5) * 10; // ±5 ONX fluctuation
        const newBalance = Math.max(0, currentBalance + fluctuation);

        balanceElement.textContent = `${newBalance.toFixed(2)} ONX`;
    }
}

// Update sabbath countdown
function updateSabbathCountdown() {
    const countdownElements = document.querySelectorAll('.countdown-number');

    if (countdownElements.length > 0) {
        // Calculate time until next Friday 6 PM
        const now = new Date();
        const nextFriday = new Date();

        // Find next Friday
        const daysUntilFriday = (5 - now.getDay() + 7) % 7;
        if (daysUntilFriday === 0 && now.getHours() >= 18) {
            nextFriday.setDate(now.getDate() + 7);
        } else {
            nextFriday.setDate(now.getDate() + daysUntilFriday);
        }

        nextFriday.setHours(18, 0, 0, 0); // 6 PM

        const timeDiff = nextFriday - now;
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

        if (countdownElements[0]) countdownElements[0].textContent = days;
        if (countdownElements[1]) countdownElements[1].textContent = hours;
        if (countdownElements[2]) countdownElements[2].textContent = minutes;
    }
}

// Update compliance score
function updateComplianceScore() {
    const scoreElements = document.querySelectorAll('.score-fill');

    scoreElements.forEach(element => {
        // Simulate slight compliance score changes
        const currentWidth = parseFloat(element.style.width || '98%');
        const change = (Math.random() - 0.5) * 0.5; // ±0.25% change
        const newWidth = Math.max(0, Math.min(100, currentWidth + change));

        element.style.width = `${newWidth}%`;
    });
}

// Initialize sabbath-specific dashboard
function initializeSabbathDashboard() {
    console.log('🕯️ Sabbath Dashboard Initialized');

    // Initialize clock animation
    initializeSabbathClock();

    // Update sabbath status
    updateSabbathStatus();
}

// Initialize sabbath clock
function initializeSabbathClock() {
    const hourHand = document.querySelector('.hour-hand');
    const minuteHand = document.querySelector('.minute-hand');

    if (hourHand && minuteHand) {
        function updateClock() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();

            const hourAngle = (hours * 30) + (minutes * 0.5);
            const minuteAngle = minutes * 6;

            hourHand.style.transform = `rotate(${hourAngle}deg)`;
            minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
        }

        updateClock();
        setInterval(updateClock, 60000); // Update every minute
    }
}

// Update sabbath status
function updateSabbathStatus() {
    const statusElement = document.querySelector('.status-text');

    if (statusElement) {
        const now = new Date();
        const dayOfWeek = now.getDay();
        const hour = now.getHours();

        // Check if it's sabbath (Friday 6 PM to Saturday 7 PM)
        const isSabbath = (dayOfWeek === 5 && hour >= 18) ||
                         (dayOfWeek === 6 && hour < 19);

        if (isSabbath) {
            statusElement.textContent = 'Currently Sabbath';
            statusElement.className = 'status-text in-sabbath';
        } else {
            statusElement.textContent = 'Not Currently Sabbath';
            statusElement.className = 'status-text not-sabbath';
        }
    }
}

// Modal functions (placeholders for full implementation)
function showSendModal() {
    alert('Send ONX Modal - Would open transaction form');
}

function showReceiveModal() {
    alert('Receive ONX Modal - Would show QR code and address');
}

function showLoanModal() {
    alert('Create Loan Modal - Would open biblical lending form');
}

function showVoteModal() {
    alert('Tribal Vote Modal - Would show governance proposals');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check which dashboard we're on and initialize accordingly
    if (document.body.classList.contains('sabbath-theme')) {
        initializeSabbathDashboard();
    } else {
        initializeDashboard();
    }

    // Start real-time updates
    updateRealTimeData();
    setInterval(updateRealTimeData, 30000); // Update every 30 seconds
});'''

    with open('deliverables/dashboard_mockups/shared_scripts.js', 'w', encoding='utf-8') as f:
        f.write(js_content)

    print("✅ Created shared JavaScript functionality")

if __name__ == "__main__":
    create_dashboard_mockups()