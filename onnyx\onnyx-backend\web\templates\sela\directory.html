{% extends "base.html" %}

{% block title %}Validator Network Directory - ONNYX Platform{% endblock %}

{% block content %}
<div class="directory-content hero-gradient cyber-grid relative py-8">
    <!-- Floating particles with precise positioning -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="container-xl px-4 relative z-10">
        <!-- Precision Header Section -->
        <div class="text-center mb-12">
            <!-- ONNYX Logo with precise measurements -->
            <div class="mb-8 flex justify-center">
                <div class="flex items-center justify-center group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <!-- Fallback symbol only if image fails -->
                    <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Validator Network</span>
            </h1>
            <p class="text-xl md:text-2xl text-secondary container-lg mx-auto leading-relaxed mb-8">
                Discover verified business validators securing the ONNYX blockchain network
            </p>

            <!-- Precision Network Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-8">
                <div class="glass-card text-center p-4">
                    <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">{{ network_stats.active_validators or selas|length }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Active Validators</div>
                </div>
                <div class="glass-card text-center p-4">
                    <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">{{ network_stats.total_blocks or total_blocks or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Blocks Validated</div>
                </div>
                <div class="glass-card text-center p-4">
                    <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2">{{ network_stats.total_transactions or total_transactions or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Transactions</div>
                </div>
                <div class="glass-card text-center p-4">
                    <div class="text-3xl font-orbitron font-bold text-cyber-green mb-2">{{ "%.1f"|format(network_stats.network_uptime or 99.9) }}%</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Network Uptime</div>
                </div>
            </div>

            <!-- Additional Mining Network Stats with Precision -->
            {% if network_stats %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-8">
                <div class="glass-card text-center p-4">
                    <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ "%.1f"|format(network_stats.total_mining_power or 0) }}x</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Total Mining Power</div>
                </div>
                <div class="glass-card text-center p-4">
                    <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2">{{ "%.1f"|format(network_stats.total_rewards or 0) }} ONX</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Rewards Distributed</div>
                </div>
                <div class="glass-card text-center p-4">
                    <div class="text-2xl font-orbitron font-bold text-cyber-blue mb-2">{{ network_stats.mempool_count or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Pending Transactions</div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Precision Search and Filters -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h3 class="card-title">Search & Filter Validators</h3>
                <p class="card-subtitle">Find validators by name, category, or status</p>
            </div>
            <div class="card-body">
                <div class="flex flex-col lg:flex-row gap-4 items-end">
                    <div class="flex-1">
                        <div class="mb-4">
                            <label for="validator-search" class="block text-sm font-medium text-secondary mb-2">Search Validators</label>
                            <div class="relative">
                                <input type="text"
                                       id="validator-search"
                                       placeholder="Search by name, category, or ID..."
                                       class="glass-button w-full pl-12 pr-4 py-3 rounded-lg border border-glass-border bg-glass-bg backdrop-blur-md text-primary placeholder-muted focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-normal">
                                <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <div class="mb-4">
                            <label for="category-filter" class="block text-sm font-medium text-secondary mb-2">Category</label>
                            <select class="glass-button px-4 py-3 rounded-lg border border-glass-border bg-glass-bg backdrop-blur-md text-primary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-normal" id="category-filter">
                                <option value="">All Categories</option>
                                <option value="technology">Technology</option>
                                <option value="finance">Finance</option>
                                <option value="retail">Retail</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="services">Services</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="status-filter" class="block text-sm font-medium text-secondary mb-2">Status</label>
                            <select class="glass-button px-4 py-3 rounded-lg border border-glass-border bg-glass-bg backdrop-blur-md text-primary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-normal" id="status-filter">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Precision Validators Grid -->
        <div id="validators-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            {% if selas %}
                {% for sela in selas %}
                <div class="glass-card group validator-card h-auto"
                     data-category="{{ sela.category|lower }}"
                     data-status="{{ sela.status|lower }}"
                     data-name="{{ sela.name|lower }}">

                    <!-- Precision Validator Header -->
                    <div class="card-header">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <h3 class="card-title group-hover:text-cyber-cyan transition-normal">
                                    <a href="{{ url_for('sela.profile', sela_id=sela.sela_id) }}">
                                        {{ sela.name }}
                                    </a>
                                </h3>
                                <p class="card-subtitle uppercase tracking-wider">{{ sela.category }}</p>
                                {% if sela.mining_tier %}
                                <div class="flex items-center gap-2 mt-2">
                                    <span class="px-2 py-1 bg-cyber-blue/20 text-cyber-blue text-xs rounded-md font-medium">{{ sela.mining_tier_display }}</span>
                                    <span class="text-xs text-cyber-cyan font-orbitron font-bold">{{ sela.mining_power_display }}</span>
                                </div>
                                {% endif %}
                            </div>
                            <div class="ml-4 flex flex-col items-end gap-2">
                                {% if sela.status == 'active' %}
                                <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md font-medium">ACTIVE</span>
                                {% else %}
                                <span class="px-2 py-1 bg-cyber-red/20 text-cyber-red text-xs rounded-md font-medium">INACTIVE</span>
                                {% endif %}
                                {% if sela.mining_power %}
                                <span class="px-2 py-1 bg-cyber-blue/20 text-cyber-blue text-xs rounded-md font-medium">MINING</span>
                                {% endif %}
                            </div>
                        </div>
                        <p class="text-secondary text-sm">{{ sela.description or "Verified blockchain validator" }}</p>
                    </div>

                    <!-- Precision Validator Metrics -->
                    <div class="card-body">
                        <div class="space-y-3">
                            <!-- ONX Balance -->
                            {% if sela.onx_balance is defined %}
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-muted">ONX Balance</span>
                                <span class="text-sm font-orbitron font-bold text-cyber-cyan">{{ "%.1f"|format(sela.onx_balance or 0) }} ONX</span>
                            </div>
                            {% endif %}

                            <!-- Mining Power -->
                            {% if sela.mining_power %}
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-muted">Mining Power</span>
                                <span class="text-sm font-orbitron font-bold text-cyber-purple">{{ sela.mining_power_display }}</span>
                            </div>
                            {% endif %}

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-muted">Owner ID</span>
                                <span class="text-sm text-secondary font-mono">{{ sela.identity_id[:8] }}...{{ sela.identity_id[-4:] }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-muted">Trust Score</span>
                                <span class="text-sm font-orbitron font-bold text-cyber-purple">{{ sela.trust_score or 'N/A' }}</span>
                            </div>

                            <!-- Mining Stats Grid with Precision -->
                            {% if sela.blocks_mined is defined or sela.total_rewards is defined %}
                            <div class="grid grid-cols-2 gap-3 pt-3 border-t border-glass-border">
                                <div class="text-center">
                                    <div class="text-lg font-orbitron font-bold text-cyber-cyan">{{ sela.blocks_mined or 0 }}</div>
                                    <div class="text-xs text-tertiary uppercase tracking-wider">Blocks Mined</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-orbitron font-bold text-cyber-purple">{{ "%.1f"|format(sela.total_rewards or 0) }}</div>
                                    <div class="text-xs text-tertiary uppercase tracking-wider">ONX Earned</div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Precision Action Buttons -->
                    <div class="card-footer flex gap-3">
                        <a href="{{ url_for('sela.profile', sela_id=sela.sela_id) }}" class="glass-button-primary flex-1 text-center py-2 px-4 rounded-lg font-medium transition-normal text-sm">
                            View Profile
                        </a>
                        <button class="glass-button p-2 rounded-lg transition-normal hover:bg-glass-hover flex-shrink-0" onclick="copyValidatorId('{{ sela.sela_id }}')">
                            📋
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- Precision Empty State -->
                <div class="col-span-full text-center py-12">
                    <div class="glass-card max-w-md mx-auto">
                        <div class="card-header text-center">
                            <div class="w-24 h-24 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl flex items-center justify-center mx-auto mb-6">
                                <svg class="w-12 h-12 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                </svg>
                            </div>
                            <h3 class="card-title">No Validators Found</h3>
                            <p class="card-subtitle">Be the first to register your business as a verified validator</p>
                        </div>
                        <div class="card-body text-center">
                            <p class="text-muted mb-6">
                                Join the ONNYX network and help secure the future of trustworthy commerce.
                            </p>
                        </div>
                        <div class="card-footer justify-center">
                            <a href="{{ url_for('auth.register_sela') }}" class="glass-button-primary px-6 py-3 rounded-lg font-medium transition-normal">
                                <span class="text-xl mr-2">🏢</span>
                                <span>Register Validator</span>
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Precision Load More Button -->
        {% if selas and selas|length >= 12 %}
        <div class="text-center mt-8">
            <button id="load-more" class="glass-button px-8 py-4 rounded-lg font-medium transition-normal hover:bg-glass-hover">
                <span class="mr-2">Load More Validators</span>
                <svg class="w-5 h-5 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
        {% endif %}

        <!-- Validator Spotlight Section -->
        <div class="mt-16 mb-16">
            <h2 class="text-4xl font-orbitron font-bold text-center mb-12">
                <span class="hologram-text">Validator Spotlight</span>
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Featured Validator Story -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center gap-4 mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex items-center justify-center">
                            <span class="text-2xl">🏢</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">TechCorp Solutions</h3>
                            <p class="text-sm text-secondary">Technology Validator • Pro Tier</p>
                        </div>
                    </div>

                    <blockquote class="text-lg text-primary mb-6 italic border-l-4 border-cyber-cyan pl-6">
                        "Joining ONNYX as a validator transformed our business credibility. We've earned 2,847 ONX tokens
                        while building unshakeable trust with our clients through blockchain verification."
                    </blockquote>

                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan">2,847</div>
                            <div class="text-xs text-tertiary">ONX Earned</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple">156</div>
                            <div class="text-xs text-tertiary">Blocks Mined</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-orbitron font-bold text-cyber-blue">5x</div>
                            <div class="text-xs text-tertiary">Mining Power</div>
                        </div>
                    </div>

                    <div class="text-sm text-secondary">
                        <strong>Sarah Chen, CTO:</strong> "The transparency and trust we've built through ONNYX validation
                        has increased our client retention by 40% and opened doors to enterprise contracts."
                    </div>
                </div>

                <!-- ROI Calculator -->
                <div class="glass-card-enhanced p-8">
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">Validator ROI Calculator</h3>

                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary mb-2">Business Type</label>
                            <select id="business-type" class="glass-button w-full px-4 py-3 rounded-lg border border-glass-border bg-glass-bg">
                                <option value="small">Small Business (1-10 employees)</option>
                                <option value="medium">Medium Business (11-50 employees)</option>
                                <option value="large">Large Business (50+ employees)</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-secondary mb-2">Monthly Transactions</label>
                            <input type="range" id="transaction-volume" min="100" max="10000" value="1000"
                                   class="w-full h-2 bg-glass-bg rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-tertiary mt-1">
                                <span>100</span>
                                <span id="transaction-display">1,000</span>
                                <span>10,000</span>
                            </div>
                        </div>

                        <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                            <h4 class="font-orbitron font-bold text-cyber-cyan mb-4">Estimated Monthly Earnings</h4>

                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div class="text-center">
                                    <div class="text-3xl font-orbitron font-bold text-cyber-cyan" id="onx-earnings">247</div>
                                    <div class="text-sm text-tertiary">ONX Tokens</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-orbitron font-bold text-cyber-purple" id="usd-value">$1,235</div>
                                    <div class="text-sm text-tertiary">USD Value*</div>
                                </div>
                            </div>

                            <div class="space-y-2 text-sm text-secondary">
                                <div class="flex justify-between">
                                    <span>Base Mining Rewards:</span>
                                    <span id="base-rewards">147 ONX</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Transaction Fees:</span>
                                    <span id="tx-fees">75 ONX</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Righteous Deed Bonus:</span>
                                    <span id="deed-bonus">25 ONX</span>
                                </div>
                            </div>

                            <div class="text-xs text-tertiary mt-4">
                                *Based on current market conditions. Actual earnings may vary.
                            </div>
                        </div>

                        {% if not current_user %}
                        <a href="{{ url_for('register_choice') }}"
                           class="glass-button-primary w-full text-center py-4 rounded-lg font-orbitron font-bold">
                            🚀 Start Earning as a Validator
                        </a>
                        {% else %}
                        <a href="{{ url_for('auth.register_sela') }}"
                           class="glass-button-primary w-full text-center py-4 rounded-lg font-orbitron font-bold">
                            🏢 Register Your Business
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Why Become a Validator Section -->
        <div class="mt-16 mb-16">
            <div class="glass-card-enhanced p-12 text-center">
                <h2 class="text-4xl font-orbitron font-bold mb-8">
                    <span class="hologram-text">Why Become a Validator?</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <span class="text-3xl">💰</span>
                        </div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Earn Passive Income</h3>
                        <p class="text-secondary">Generate ONX tokens through mining rewards, transaction fees, and biblical tokenomics bonuses.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <span class="text-3xl">🛡️</span>
                        </div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Build Trust</h3>
                        <p class="text-secondary">Blockchain verification increases customer confidence and opens doors to enterprise opportunities.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-cyber-blue to-cyber-green rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <span class="text-3xl">🌐</span>
                        </div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-blue mb-4">Network Growth</h3>
                        <p class="text-secondary">Join a growing ecosystem of verified businesses and benefit from network effects.</p>
                    </div>
                </div>

                <div class="bg-glass-bg rounded-2xl p-8 border border-glass-border mb-8">
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">Validator Benefits</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                        <div class="flex items-start gap-3">
                            <div class="w-6 h-6 bg-cyber-cyan rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <svg class="w-4 h-4 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-primary mb-1">Mining Rewards</h4>
                                <p class="text-sm text-secondary">Earn ONX tokens for validating transactions and securing the network</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="w-6 h-6 bg-cyber-purple rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <svg class="w-4 h-4 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-primary mb-1">Trust Verification</h4>
                                <p class="text-sm text-secondary">Blockchain-verified business credentials and reputation scoring</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="w-6 h-6 bg-cyber-blue rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <svg class="w-4 h-4 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-primary mb-1">Biblical Economics</h4>
                                <p class="text-sm text-secondary">Bonus rewards for righteous business practices and community service</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="w-6 h-6 bg-cyber-green rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <svg class="w-4 h-4 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-primary mb-1">Network Access</h4>
                                <p class="text-sm text-secondary">Connect with other verified businesses and expand your network</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    {% if not current_user %}
                    <a href="{{ url_for('register_choice') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold">
                        ✨ Verify Your Identity First
                    </a>
                    {% else %}
                    <a href="{{ url_for('auth.register_sela') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold">
                        🏢 Register as Validator
                    </a>
                    {% endif %}
                    <a href="{{ url_for('tokenomics.overview') }}"
                       class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold">
                        📜 Learn Biblical Economics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('validator-search');
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const validatorCards = document.querySelectorAll('.validator-card');

    function filterValidators() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value.toLowerCase();
        const selectedStatus = statusFilter.value.toLowerCase();

        validatorCards.forEach(card => {
            const name = card.dataset.name;
            const category = card.dataset.category;
            const status = card.dataset.status;

            const matchesSearch = !searchTerm || name.includes(searchTerm);
            const matchesCategory = !selectedCategory || category === selectedCategory;
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesCategory && matchesStatus) {
                card.style.display = 'block';
                card.classList.add('fade-in');
            } else {
                card.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterValidators);
    categoryFilter.addEventListener('change', filterValidators);
    statusFilter.addEventListener('change', filterValidators);
});

// Copy validator ID function
function copyValidatorId(validatorId) {
    navigator.clipboard.writeText(validatorId).then(() => {
        Onnyx.utils.showNotification('Validator ID copied to clipboard!', 'success');
    }).catch(() => {
        Onnyx.utils.showNotification('Failed to copy validator ID', 'error');
    });
}

// ROI Calculator functionality
document.addEventListener('DOMContentLoaded', function() {
    const businessTypeSelect = document.getElementById('business-type');
    const transactionVolumeSlider = document.getElementById('transaction-volume');
    const transactionDisplay = document.getElementById('transaction-display');
    const onxEarnings = document.getElementById('onx-earnings');
    const usdValue = document.getElementById('usd-value');
    const baseRewards = document.getElementById('base-rewards');
    const txFees = document.getElementById('tx-fees');
    const deedBonus = document.getElementById('deed-bonus');

    function calculateROI() {
        const businessType = businessTypeSelect.value;
        const transactionVolume = parseInt(transactionVolumeSlider.value);

        // Update transaction display
        transactionDisplay.textContent = transactionVolume.toLocaleString();

        // Business type multipliers
        const multipliers = {
            small: { base: 0.8, tx: 0.005, deed: 0.15 },
            medium: { base: 1.2, tx: 0.008, deed: 0.25 },
            large: { base: 2.0, tx: 0.012, deed: 0.40 }
        };

        const mult = multipliers[businessType];

        // Calculate earnings components
        const baseRewardAmount = Math.floor(100 * mult.base + (transactionVolume * 0.02));
        const txFeeAmount = Math.floor(transactionVolume * mult.tx);
        const deedBonusAmount = Math.floor(baseRewardAmount * mult.deed);

        const totalONX = baseRewardAmount + txFeeAmount + deedBonusAmount;
        const usdAmount = Math.floor(totalONX * 5.2); // Estimated ONX to USD rate

        // Update display
        onxEarnings.textContent = totalONX.toLocaleString();
        usdValue.textContent = `$${usdAmount.toLocaleString()}`;
        baseRewards.textContent = `${baseRewardAmount} ONX`;
        txFees.textContent = `${txFeeAmount} ONX`;
        deedBonus.textContent = `${deedBonusAmount} ONX`;
    }

    // Event listeners
    businessTypeSelect.addEventListener('change', calculateROI);
    transactionVolumeSlider.addEventListener('input', calculateROI);

    // Initial calculation
    calculateROI();
});

// Precision grid initialization - no forced heights needed with CSS Grid
document.addEventListener('DOMContentLoaded', function() {
    // Grid is now handled by CSS Grid system - no JavaScript manipulation needed
    console.log('Precision validator grid initialized');
});
</script>
{% endblock %}
