#!/usr/bin/env python3
"""
Enhanced Mining Database Migration Script
Adds tables and indexes for 24/6 biblical mining system
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

logger = logging.getLogger(__name__)

def migrate_enhanced_mining():
    """Migrate database for enhanced biblical mining system."""

    migrations = {
        'enhanced_mining_rewards': """
            CREATE TABLE IF NOT EXISTS enhanced_mining_rewards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                reward_amount DECIMAL(18,8) NOT NULL,
                base_reward DECIMAL(18,8) NOT NULL,
                tier_multiplier DECIMAL(4,2) NOT NULL,
                righteous_bonus BOOLEAN DEFAULT FALSE,
                penalty_multiplier DECIMAL(4,2) DEFAULT 1.0,
                effective_multiplier DECIMAL(4,2) NOT NULL,
                gleaning_allocation DECIMAL(18,8) DEFAULT 0.0,
                block_height INTEGER,
                mining_period TEXT, -- 'WORK_DAY', 'SABBATH_VIOLATION', etc.
                timestamp INTEGER NOT NULL,
                created_at INTEGER NOT NULL
            )
        """,

        'annual_emission_tracking': """
            CREATE TABLE IF NOT EXISTS annual_emission_tracking (
                year INTEGER PRIMARY KEY,
                total_emitted DECIMAL(18,8) DEFAULT 0.0,
                dev_allocation_used DECIMAL(18,8) DEFAULT 0.0,
                cap_reached_date INTEGER,
                active_mining_days INTEGER DEFAULT 313,
                sabbath_violations INTEGER DEFAULT 0,
                feast_violations INTEGER DEFAULT 0,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL
            )
        """,

        'enhanced_tier_history': """
            CREATE TABLE IF NOT EXISTS enhanced_tier_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                tier_name TEXT NOT NULL,
                tier_multiplier DECIMAL(4,2) NOT NULL,
                sela_count INTEGER DEFAULT 0,
                pro_validator BOOLEAN DEFAULT FALSE,
                effective_date INTEGER NOT NULL,
                end_date INTEGER,
                created_at INTEGER NOT NULL
            )
        """,

        'holy_day_calendar': """
            CREATE TABLE IF NOT EXISTS holy_day_calendar (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                holy_day_type TEXT NOT NULL, -- 'SABBATH', 'NEW_MOON', 'FEAST'
                name TEXT NOT NULL,
                start_timestamp INTEGER NOT NULL,
                end_timestamp INTEGER NOT NULL,
                hebrew_date TEXT,
                mining_restricted BOOLEAN DEFAULT TRUE,
                observers_count INTEGER DEFAULT 0,
                violations_count INTEGER DEFAULT 0,
                created_at INTEGER NOT NULL
            )
        """,

        'righteous_bonus_tracking': """
            CREATE TABLE IF NOT EXISTS righteous_bonus_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                week_start INTEGER NOT NULL,
                week_end INTEGER NOT NULL,
                sabbath_compliance BOOLEAN DEFAULT FALSE,
                feast_compliance BOOLEAN DEFAULT FALSE,
                new_moon_compliance BOOLEAN DEFAULT FALSE,
                bonus_eligible BOOLEAN DEFAULT FALSE,
                bonus_applied_count INTEGER DEFAULT 0,
                total_bonus_amount DECIMAL(18,8) DEFAULT 0.0,
                created_at INTEGER NOT NULL
            )
        """,

        'dev_allocation_requests': """
            CREATE TABLE IF NOT EXISTS dev_allocation_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_id TEXT UNIQUE NOT NULL,
                purpose TEXT NOT NULL, -- 'CORE_DEV', 'AUDITS', 'INNOVATION', 'EMERGENCY'
                amount DECIMAL(18,8) NOT NULL,
                requested_by TEXT NOT NULL,
                council_approval BOOLEAN DEFAULT FALSE,
                scroll_reference TEXT,
                approved_at INTEGER,
                distributed_at INTEGER,
                status TEXT DEFAULT 'PENDING', -- 'PENDING', 'APPROVED', 'DISTRIBUTED', 'REJECTED'
                created_at INTEGER NOT NULL
            )
        """
    }

    # Create indexes for performance
    indexes = {
        'idx_enhanced_mining_rewards_identity': 'CREATE INDEX IF NOT EXISTS idx_enhanced_mining_rewards_identity ON enhanced_mining_rewards(identity_id)',
        'idx_enhanced_mining_rewards_timestamp': 'CREATE INDEX IF NOT EXISTS idx_enhanced_mining_rewards_timestamp ON enhanced_mining_rewards(timestamp)',
        'idx_enhanced_mining_rewards_period': 'CREATE INDEX IF NOT EXISTS idx_enhanced_mining_rewards_period ON enhanced_mining_rewards(mining_period)',
        'idx_enhanced_tier_history_identity': 'CREATE INDEX IF NOT EXISTS idx_enhanced_tier_history_identity ON enhanced_tier_history(identity_id)',
        'idx_enhanced_tier_history_effective': 'CREATE INDEX IF NOT EXISTS idx_enhanced_tier_history_effective ON enhanced_tier_history(effective_date)',
        'idx_holy_day_calendar_type': 'CREATE INDEX IF NOT EXISTS idx_holy_day_calendar_type ON holy_day_calendar(holy_day_type)',
        'idx_holy_day_calendar_timestamp': 'CREATE INDEX IF NOT EXISTS idx_holy_day_calendar_timestamp ON holy_day_calendar(start_timestamp, end_timestamp)',
        'idx_righteous_bonus_identity': 'CREATE INDEX IF NOT EXISTS idx_righteous_bonus_identity ON righteous_bonus_tracking(identity_id)',
        'idx_righteous_bonus_week': 'CREATE INDEX IF NOT EXISTS idx_righteous_bonus_week ON righteous_bonus_tracking(week_start, week_end)',
        'idx_dev_allocation_status': 'CREATE INDEX IF NOT EXISTS idx_dev_allocation_status ON dev_allocation_requests(status)',
        'idx_dev_allocation_purpose': 'CREATE INDEX IF NOT EXISTS idx_dev_allocation_purpose ON dev_allocation_requests(purpose)'
    }

    try:
        # Execute table migrations
        for table_name, sql in migrations.items():
            logger.info(f"Creating table: {table_name}")
            db.execute(sql)

        # Execute index migrations
        for index_name, sql in indexes.items():
            logger.info(f"Creating index: {index_name}")
            db.execute(sql)

        # Initialize current year tracking
        current_year = datetime.now().year
        existing_year = db.query_one("SELECT year FROM annual_emission_tracking WHERE year = ?", (current_year,))

        if not existing_year:
            db.execute("""
                INSERT INTO annual_emission_tracking (year, total_emitted, dev_allocation_used, active_mining_days, created_at, updated_at)
                VALUES (?, 0.0, 0.0, 313, ?, ?)
            """, (current_year, int(datetime.now().timestamp()), int(datetime.now().timestamp())))
            logger.info(f"Initialized emission tracking for year {current_year}")

        logger.info("Enhanced mining database migration completed successfully")
        return True

    except Exception as e:
        logger.error(f"Enhanced mining migration failed: {e}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    success = migrate_enhanced_mining()
    if success:
        print("✅ Enhanced mining migration completed successfully")
    else:
        print("❌ Enhanced mining migration failed")
        sys.exit(1)
