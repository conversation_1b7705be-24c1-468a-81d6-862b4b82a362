{% extends "base.html" %}

{% block title %}Genesis Identity Created - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Success Header -->
        <div class="text-center mb-12">
            <!-- ONNYX Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-20 h-20 md:w-24 md:h-24 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-16 h-16 md:w-20 md:h-20 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 glow-effect">
                <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-orbitron font-bold mb-4">
                <span class="hologram-text">🎉 Genesis Identity Created!</span>
            </h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
                Welcome to the ONNYX platform, <span class="text-cyber-cyan font-semibold">{{ identity.name }}</span>
            </p>
        </div>

        <!-- Identity Information -->
        <div class="glass-card p-8 neuro-card mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">🔐 Your Genesis Identity Details</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-orbitron font-semibold text-cyber-purple mb-3">Identity ID</label>
                    <div class="p-4 glass-card border border-cyber-cyan/30 rounded-xl font-mono text-sm break-all text-white">
                        {{ identity.identity_id }}
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-orbitron font-semibold text-cyber-purple mb-3">Name</label>
                    <div class="p-4 glass-card border border-cyber-cyan/30 rounded-xl text-white">
                        {{ identity.name }}
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-orbitron font-semibold text-cyber-purple mb-3">Email</label>
                    <div class="p-4 glass-card border border-cyber-cyan/30 rounded-xl text-white">
                        {{ identity.email }}
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-orbitron font-semibold text-cyber-purple mb-3">Status</label>
                    <div class="p-4 glass-card border border-cyber-cyan/30 rounded-xl">
                        <span class="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm font-semibold border border-green-500/30">
                            ✅ {{ identity.status|title }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical: Private Key Download -->
        <div class="glass-card p-8 neuro-card mb-8 border-2 border-red-500/50 bg-red-500/10">
            <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-xl font-orbitron font-bold text-red-300 mb-3">🔒 CRITICAL: Download Your Private Key</h3>
                    <p class="text-red-200 mb-6 leading-relaxed">
                        Your private key will only be shown once. Download it immediately and store it securely.
                        Without this key, you cannot access your identity or assets on the ONNYX network.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4">
                        <button onclick="downloadKeys()"
                                class="glass-button-primary bg-red-500/20 border-red-500/50 text-red-300 hover:bg-red-500/30 px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            📥 Download Key File
                        </button>

                        <button onclick="showQRCode()"
                                class="glass-button bg-cyber-blue/20 border-cyber-blue/50 text-cyber-blue hover:bg-cyber-blue/30 px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            📱 Show QR Code
                        </button>

                        <button onclick="copyToClipboard()"
                                class="glass-button bg-gray-500/20 border-gray-500/50 text-gray-300 hover:bg-gray-500/30 px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            📋 Copy to Clipboard
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- QR Code Modal -->
        <div id="qrModal" class="hidden fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="glass-card p-8 max-w-md mx-4 border border-cyber-cyan/30">
                <div class="text-center">
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">🔐 Private Key QR Code</h3>
                    <div id="qrcode" class="flex justify-center mb-4 p-4 bg-white rounded-xl"></div>
                    <p class="text-sm text-gray-300 mb-6">Scan with a secure device to save your private key</p>
                    <button onclick="hideQRCode()"
                            class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        Close
                    </button>
                </div>
            </div>
        </div>

        <!-- Security Checklist -->
        <div class="glass-card p-8 neuro-card mb-8 border border-yellow-500/30 bg-yellow-500/10">
            <h3 class="text-xl font-orbitron font-bold text-yellow-400 mb-6">🛡️ Security Protocol Checklist</h3>

            <div class="space-y-4">
                <label class="flex items-start space-x-4 cursor-pointer group">
                    <input type="checkbox" class="mt-1 h-5 w-5 text-green-500 focus:ring-green-500/50 border-gray-500 rounded bg-transparent">
                    <span class="text-sm text-gray-300 group-hover:text-white transition-colors">I have downloaded my private key file</span>
                </label>

                <label class="flex items-start space-x-4 cursor-pointer group">
                    <input type="checkbox" class="mt-1 h-5 w-5 text-green-500 focus:ring-green-500/50 border-gray-500 rounded bg-transparent">
                    <span class="text-sm text-gray-300 group-hover:text-white transition-colors">I have stored the key file in multiple secure locations</span>
                </label>

                <label class="flex items-start space-x-4 cursor-pointer group">
                    <input type="checkbox" class="mt-1 h-5 w-5 text-green-500 focus:ring-green-500/50 border-gray-500 rounded bg-transparent">
                    <span class="text-sm text-gray-300 group-hover:text-white transition-colors">I understand that losing this key means losing access to my identity</span>
                </label>

                <label class="flex items-start space-x-4 cursor-pointer group">
                    <input type="checkbox" class="mt-1 h-5 w-5 text-green-500 focus:ring-green-500/50 border-gray-500 rounded bg-transparent">
                    <span class="text-sm text-gray-300 group-hover:text-white transition-colors">I will never share my private key with anyone</span>
                </label>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="glass-card p-8 neuro-card">
            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">🚀 Your Genesis Journey Continues</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="glass-card p-6 border border-green-500/30 bg-green-500/10 hover:bg-green-500/20 transition-all duration-300 group">
                    <h4 class="font-orbitron font-semibold text-green-300 mb-3 group-hover:text-green-200">🏢 Register Your Business</h4>
                    <p class="text-gray-300 text-sm mb-6 leading-relaxed">
                        Add your business as a verified Sela validator to start mining blocks and securing the ONNYX network.
                    </p>
                    <a href="{{ url_for('auth.register_sela') }}"
                       class="glass-button-primary bg-green-500/20 border-green-500/50 text-green-300 hover:bg-green-500/30 px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 inline-block">
                        Register Business
                    </a>
                </div>

                <div class="glass-card p-6 border border-cyber-blue/30 bg-cyber-blue/10 hover:bg-cyber-blue/20 transition-all duration-300 group">
                    <h4 class="font-orbitron font-semibold text-cyber-blue mb-3 group-hover:text-blue-200">📊 Explore Your Dashboard</h4>
                    <p class="text-gray-300 text-sm mb-6 leading-relaxed">
                        View your identity details, manage settings, and track your activities on the ONNYX platform.
                    </p>
                    <a href="{{ url_for('dashboard.overview') }}"
                       class="glass-button-primary bg-cyber-blue/20 border-cyber-blue/50 text-cyber-blue hover:bg-cyber-blue/30 px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 inline-block">
                        Go to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Store the key data
const keyData = {{ key_data|safe }};

function downloadKeys() {
    const blob = new Blob([keyData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `onnyx-identity-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function showQRCode() {
    const modal = document.getElementById('qrModal');
    const qrContainer = document.getElementById('qrcode');

    // Clear previous QR code
    qrContainer.innerHTML = '';

    // Generate QR code
    QRCode.toCanvas(qrContainer, keyData, {
        width: 256,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    });

    modal.classList.remove('hidden');
}

function hideQRCode() {
    document.getElementById('qrModal').classList.add('hidden');
}

function copyToClipboard() {
    navigator.clipboard.writeText(keyData).then(() => {
        alert('Private key copied to clipboard! Paste it into a secure location immediately.');
    }).catch(() => {
        alert('Failed to copy to clipboard. Please download the key file instead.');
    });
}

// Close modal when clicking outside
document.getElementById('qrModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideQRCode();
    }
});
</script>
{% endblock %}
