{% extends "base.html" %}

{% block title %}Interest-Free Loans - Biblical Tokenomics{% endblock %}

{% block head %}
<style>
    .loan-form {
        background: linear-gradient(135deg, rgba(0, 102, 255, 0.1) 0%, rgba(0, 255, 247, 0.1) 100%);
        border: 1px solid rgba(0, 102, 255, 0.3);
    }
    
    .loan-card {
        transition: all 0.3s ease;
        border-left: 4px solid;
    }
    
    .loan-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 247, 0.15);
    }
    
    .loan-active { border-left-color: #0066ff; }
    .loan-repaid { border-left-color: #10b981; }
    .loan-forgiven { border-left-color: #fbbf24; }
    
    .progress-bar {
        background: linear-gradient(90deg, #2a2a2a 0%, #0066ff 100%);
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .progress-fill {
        background: linear-gradient(90deg, #0066ff 0%, #00fff7 100%);
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .borrower-selector {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .borrower-selector:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 255, 247, 0.2);
    }
    
    .borrower-selector.selected {
        border-color: #00fff7;
        background: rgba(0, 255, 247, 0.1);
    }
    
    .verse-highlight {
        background: linear-gradient(90deg, rgba(0, 102, 255, 0.1) 0%, transparent 100%);
        border-left: 3px solid #0066ff;
        padding: 1rem;
        font-style: italic;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-blue to-cyber-cyan bg-clip-text text-transparent">
                    Interest-Free Loans
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Biblical lending without usury - supporting community through mutual aid
            </p>
            
            <!-- Biblical Foundation -->
            <div class="verse-highlight max-w-4xl mx-auto mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div class="text-center">
                        <div class="text-cyber-cyan font-semibold mb-2">📜 Covenant Brothers</div>
                        <div class="italic">"If you lend money to one of my people among you who is needy, do not treat it like a business deal; charge no interest."</div>
                        <div class="font-bold mt-2">- Exodus 22:25</div>
                    </div>
                    <div class="text-center">
                        <div class="text-cyber-purple font-semibold mb-2">🌍 Witness Nations</div>
                        <div class="italic">"Unto a stranger thou mayest lend upon usury; but unto thy brother thou shalt not lend upon usury."</div>
                        <div class="font-bold mt-2">- Deuteronomy 23:20</div>
                    </div>
                </div>
            </div>

            <!-- Biblical Compliance Guide -->
            <div class="glass-card p-6 max-w-4xl mx-auto mb-8">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4 text-center">📖 Biblical Lending Rules</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div class="text-center p-4 bg-cyber-green/10 rounded-lg border border-cyber-green/30">
                        <div class="text-cyber-green font-semibold mb-2">🤝 Israelite → Israelite</div>
                        <div class="text-xs">0% Interest</div>
                        <div class="text-xs text-gray-400">Always interest-free</div>
                    </div>
                    <div class="text-center p-4 bg-cyber-blue/10 rounded-lg border border-cyber-blue/30">
                        <div class="text-cyber-blue font-semibold mb-2">🌟 Israelite → Witness</div>
                        <div class="text-xs">Interest Allowed</div>
                        <div class="text-xs text-gray-400">Mercy encouraged</div>
                    </div>
                    <div class="text-center p-4 bg-cyber-purple/10 rounded-lg border border-cyber-purple/30">
                        <div class="text-cyber-purple font-semibold mb-2">🌍 Witness → Israelite</div>
                        <div class="text-xs">Interest Allowed</div>
                        <div class="text-xs text-gray-400">Negotiate wisely</div>
                    </div>
                    <div class="text-center p-4 bg-gray-500/10 rounded-lg border border-gray-500/30">
                        <div class="text-gray-300 font-semibold mb-2">🏛️ Witness → Witness</div>
                        <div class="text-xs">Market Practice</div>
                        <div class="text-xs text-gray-400">Standard terms</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="glass-card p-6 text-center">
                <div class="text-3xl mb-2">🤝</div>
                <h3 class="text-lg font-semibold text-cyber-blue">Zero Interest</h3>
                <p class="text-sm text-gray-400">All loans are completely interest-free</p>
            </div>
            
            <div class="glass-card p-6 text-center">
                <div class="text-3xl mb-2">🕐</div>
                <h3 class="text-lg font-semibold text-cyber-cyan">Grace Period</h3>
                <p class="text-sm text-gray-400">{{ (grace_blocks / 1440)|round }} days for repayment</p>
            </div>
            
            <div class="glass-card p-6 text-center">
                <div class="text-3xl mb-2">🎁</div>
                <h3 class="text-lg font-semibold text-yellow-400">Auto Forgiveness</h3>
                <p class="text-sm text-gray-400">{{ (forgiveness_threshold * 100)|round }}% repayment triggers forgiveness</p>
            </div>
        </div>
        
        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Create New Loan -->
            <div class="loan-form glass-card p-6">
                <h2 class="text-2xl font-semibold mb-6">Create Interest-Free Loan</h2>
                
                <form id="loanForm" action="/api/tokenomics/loans" method="POST">
                    <!-- Loan Type Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-4">Loan Type</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="flex items-center space-x-3 glass-card p-4 cursor-pointer hover:bg-white/5">
                                <input type="radio" name="loan_type" value="lend" class="form-radio text-cyber-blue" checked>
                                <div>
                                    <span class="font-semibold">Lend Money</span>
                                    <p class="text-sm text-gray-400">Offer a loan to someone in need</p>
                                </div>
                            </label>
                            <label class="flex items-center space-x-3 glass-card p-4 cursor-pointer hover:bg-white/5">
                                <input type="radio" name="loan_type" value="request" class="form-radio text-cyber-blue">
                                <div>
                                    <span class="font-semibold">Request Loan</span>
                                    <p class="text-sm text-gray-400">Ask for financial assistance</p>
                                </div>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Borrower Selection (for lending) -->
                    <div id="borrowerSelection" class="mb-6">
                        <label class="block text-sm font-medium mb-4">Select Borrower</label>
                        <div class="grid grid-cols-1 gap-3 max-h-48 overflow-y-auto">
                            {% for borrower in available_borrowers %}
                            <div class="borrower-selector glass-card p-3" data-borrower-id="{{ borrower.identity_id }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-cyber-blue/20 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold">{{ borrower.name[0].upper() }}</span>
                                    </div>
                                    <div>
                                        <p class="font-semibold">{{ borrower.name }}</p>
                                        <p class="text-xs text-gray-400">{{ borrower.identity_id[:16] }}...</p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <input type="hidden" id="selectedBorrower" name="borrower_id">
                    </div>
                    
                    <!-- Token Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-4">Token & Amount</label>
                        <div class="grid grid-cols-2 gap-4">
                            <select id="tokenSelect" name="token_id" class="px-4 py-3 bg-onyx-gray border border-gray-600 rounded-lg focus:border-cyber-blue focus:ring-1 focus:ring-cyber-blue text-white" required>
                                <option value="">Select Token</option>
                                {% for token in token_balances %}
                                <option value="{{ token.token_id }}" data-balance="{{ token.balance }}">
                                    {{ token.symbol }} ({{ "%.2f"|format(token.balance) }} available)
                                </option>
                                {% endfor %}
                            </select>
                            
                            <input type="number" 
                                   id="loanAmount" 
                                   name="amount" 
                                   step="0.01" 
                                   min="0.01" 
                                   class="px-4 py-3 bg-onyx-gray border border-gray-600 rounded-lg focus:border-cyber-blue focus:ring-1 focus:ring-cyber-blue text-white"
                                   placeholder="Loan amount"
                                   required>
                        </div>
                        <div class="mt-2 text-sm text-gray-400">
                            Available: <span id="availableAmount">Select token first</span>
                        </div>
                    </div>
                    
                    <!-- Loan Purpose -->
                    <div class="mb-6">
                        <label for="purpose" class="block text-sm font-medium mb-2">Purpose (Optional)</label>
                        <textarea id="purpose"
                                  name="purpose"
                                  rows="3"
                                  class="w-full px-4 py-3 bg-onyx-gray border border-gray-600 rounded-lg focus:border-cyber-blue focus:ring-1 focus:ring-cyber-blue text-white"
                                  placeholder="Describe the purpose of this loan..."></textarea>
                    </div>

                    <!-- Grace Period -->
                    <div class="mb-6">
                        <label for="gracePeriod" class="block text-sm font-medium text-gray-300 mb-2">
                            Grace Period (Days)
                        </label>
                        <input type="number"
                               id="gracePeriod"
                               name="grace_period"
                               min="1"
                               max="365"
                               value="10"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-cyber-blue focus:border-transparent text-white">
                        <p class="text-xs text-gray-400 mt-1">Default: 10 days (biblical sabbatical pause)</p>
                    </div>

                    <!-- Interest Rate (for non-Israelite loans) -->
                    <div id="interestSection" class="mb-6" style="display: none;">
                        <label for="interestRate" class="block text-sm font-medium text-gray-300 mb-2">
                            Interest Rate (% per year)
                        </label>
                        <input type="number"
                               id="interestRate"
                               name="interest_rate"
                               min="0"
                               max="50"
                               step="0.1"
                               value="0"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-cyber-blue focus:border-transparent text-white">
                        <div class="flex items-center mt-2">
                            <input type="checkbox"
                                   id="allowInterest"
                                   name="allow_interest"
                                   class="mr-2 rounded">
                            <label for="allowInterest" class="text-sm text-gray-300">
                                Allow interest on this loan
                            </label>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">Interest only applies to non-covenant loans (Deuteronomy 23:20)</p>
                    </div>

                    <!-- Biblical Compliance Preview -->
                    <div id="compliancePreview" class="mb-6 p-4 bg-gray-800 rounded-lg border border-gray-600">
                        <div class="text-sm font-medium text-gray-300 mb-2">📖 Biblical Compliance</div>
                        <div id="complianceText" class="text-sm text-gray-400">
                            Enter borrower details to see biblical lending rules
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            id="submitLoan"
                            class="w-full py-4 bg-gradient-to-r from-cyber-blue to-cyber-cyan text-white font-semibold rounded-lg hover:from-cyber-blue/90 hover:to-cyber-cyan/90 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                        <span class="mr-2">🤝</span><span id="submitText">Create Biblical Loan</span>
                    </button>
                </form>
            </div>
            
            <!-- Loan Management -->
            <div class="space-y-6">
                <!-- Active Loans as Borrower -->
                {% if loans_as_borrower %}
                <div class="glass-card p-6">
                    <h3 class="text-xl font-semibold mb-4 flex items-center">
                        <span class="mr-3">💰</span>My Loans (As Borrower)
                    </h3>
                    
                    <div class="space-y-4">
                        {% for loan in loans_as_borrower %}
                        <div class="loan-card loan-{{ loan.status.lower() }} glass-card p-4">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h4 class="font-semibold">{{ "%.2f"|format(loan.amount) }} {{ loan.token_id }}</h4>
                                    <p class="text-sm text-gray-400">From: {{ loan.lender_name }}</p>
                                    <p class="text-xs text-gray-500">{{ loan.created_at|timestamp_to_date if loan.created_at else 'Unknown date' }}</p>
                                </div>
                                <span class="px-3 py-1 bg-{{ 'blue' if loan.status == 'ACTIVE' else 'green' if loan.status == 'REPAID' else 'yellow' }}-500/20 text-{{ 'blue' if loan.status == 'ACTIVE' else 'green' if loan.status == 'REPAID' else 'yellow' }}-400 rounded text-sm">
                                    {{ loan.status }}
                                </span>
                            </div>
                            
                            {% if loan.status == 'ACTIVE' %}
                            <!-- Repayment Progress -->
                            <div class="mb-3">
                                <div class="flex justify-between text-sm mb-1">
                                    <span>Repaid: {{ "%.2f"|format(loan.amount_paid) }} / {{ "%.2f"|format(loan.amount) }}</span>
                                    <span>{{ "%.0f"|format((loan.amount_paid / loan.amount * 100) if loan.amount > 0 else 0) }}%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ (loan.amount_paid / loan.amount * 100) if loan.amount > 0 else 0 }}%"></div>
                                </div>
                            </div>
                            
                            <!-- Repayment Form -->
                            <form class="repayment-form flex gap-2" data-loan-id="{{ loan.loan_id }}">
                                <input type="number" 
                                       step="0.01" 
                                       min="0.01" 
                                       max="{{ loan.amount - loan.amount_paid }}"
                                       placeholder="Repayment amount"
                                       class="flex-1 px-3 py-2 bg-onyx-gray border border-gray-600 rounded focus:border-cyber-blue text-white text-sm">
                                <button type="submit" 
                                        class="px-4 py-2 bg-cyber-blue text-white rounded hover:bg-cyber-blue/90 text-sm">
                                    Repay
                                </button>
                            </form>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Active Loans as Lender -->
                {% if loans_as_lender %}
                <div class="glass-card p-6">
                    <h3 class="text-xl font-semibold mb-4 flex items-center">
                        <span class="mr-3">🏦</span>My Loans (As Lender)
                    </h3>
                    
                    <div class="space-y-4">
                        {% for loan in loans_as_lender %}
                        <div class="loan-card loan-{{ loan.status.lower() }} glass-card p-4">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h4 class="font-semibold">{{ "%.2f"|format(loan.amount) }} {{ loan.token_id }}</h4>
                                    <p class="text-sm text-gray-400">To: {{ loan.borrower_name }}</p>
                                    <p class="text-xs text-gray-500">{{ loan.created_at|timestamp_to_date if loan.created_at else 'Unknown date' }}</p>
                                </div>
                                <span class="px-3 py-1 bg-{{ 'blue' if loan.status == 'ACTIVE' else 'green' if loan.status == 'REPAID' else 'yellow' }}-500/20 text-{{ 'blue' if loan.status == 'ACTIVE' else 'green' if loan.status == 'REPAID' else 'yellow' }}-400 rounded text-sm">
                                    {{ loan.status }}
                                </span>
                            </div>
                            
                            {% if loan.status == 'ACTIVE' %}
                            <!-- Repayment Progress -->
                            <div class="mb-3">
                                <div class="flex justify-between text-sm mb-1">
                                    <span>Repaid: {{ "%.2f"|format(loan.amount_paid) }} / {{ "%.2f"|format(loan.amount) }}</span>
                                    <span>{{ "%.0f"|format((loan.amount_paid / loan.amount * 100) if loan.amount > 0 else 0) }}%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ (loan.amount_paid / loan.amount * 100) if loan.amount > 0 else 0 }}%"></div>
                                </div>
                            </div>
                            
                            <!-- Forgiveness Option -->
                            {% if (loan.amount_paid / loan.amount) >= forgiveness_threshold %}
                            <button class="forgive-loan-btn w-full py-2 bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 rounded hover:bg-yellow-500/30 text-sm"
                                    data-loan-id="{{ loan.loan_id }}">
                                <span class="mr-2">🎁</span>Forgive Remaining Debt
                            </button>
                            {% endif %}
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- No Loans Message -->
                {% if not loans_as_borrower and not loans_as_lender %}
                <div class="glass-card p-8 text-center">
                    <div class="text-4xl mb-4">🤝</div>
                    <h3 class="text-xl font-semibold mb-2">No Active Loans</h3>
                    <p class="text-gray-400 mb-4">Start by creating an interest-free loan to help the community.</p>
                    <p class="text-sm text-gray-500">Biblical lending builds trust and supports those in need.</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Educational Information -->
        <div class="glass-card p-6 mt-8">
            <h3 class="text-xl font-semibold mb-4">About Biblical Lending</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-cyber-blue mb-2">No Interest Charged</h4>
                    <p class="text-sm text-gray-300 mb-4">
                        Following biblical principles, all loans are completely interest-free. This prevents 
                        exploitation and encourages genuine mutual aid within the community.
                    </p>
                    
                    <h4 class="font-semibold text-cyber-cyan mb-2">Automatic Forgiveness</h4>
                    <p class="text-sm text-gray-300">
                        When {{ (forgiveness_threshold * 100)|round }}% of a loan is repaid, the remaining debt can be forgiven, 
                        demonstrating mercy and preventing perpetual debt cycles.
                    </p>
                </div>
                
                <div>
                    <h4 class="font-semibold text-yellow-400 mb-2">Grace Periods</h4>
                    <p class="text-sm text-gray-300 mb-4">
                        Borrowers have {{ (grace_blocks / 1440)|round }} days to repay loans without penalty, 
                        allowing time for financial recovery and preventing rushed decisions.
                    </p>
                    
                    <h4 class="font-semibold text-cyber-purple mb-2">Community Building</h4>
                    <p class="text-sm text-gray-300">
                        Interest-free lending builds trust, strengthens relationships, and creates a 
                        supportive economic environment based on biblical values.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loanTypeRadios = document.querySelectorAll('input[name="loan_type"]');
    const borrowerSelection = document.getElementById('borrowerSelection');
    const borrowerSelectors = document.querySelectorAll('.borrower-selector');
    const selectedBorrowerInput = document.getElementById('selectedBorrower');
    const tokenSelect = document.getElementById('tokenSelect');
    const loanAmount = document.getElementById('loanAmount');
    const availableAmount = document.getElementById('availableAmount');
    const submitLoan = document.getElementById('submitLoan');
    
    // Loan type change
    loanTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'lend') {
                borrowerSelection.style.display = 'block';
            } else {
                borrowerSelection.style.display = 'none';
                selectedBorrowerInput.value = '';
            }
            validateForm();
        });
    });
    
    // Borrower selection
    borrowerSelectors.forEach(selector => {
        selector.addEventListener('click', function() {
            borrowerSelectors.forEach(s => s.classList.remove('selected'));
            this.classList.add('selected');
            selectedBorrowerInput.value = this.dataset.borrowerId;
            validateForm();
        });
    });
    
    // Token selection
    tokenSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const balance = selectedOption.dataset.balance;
            availableAmount.textContent = parseFloat(balance).toFixed(2);
            loanAmount.max = balance;
        } else {
            availableAmount.textContent = 'Select token first';
            loanAmount.max = '';
        }
        validateForm();
    });
    
    // Amount input
    loanAmount.addEventListener('input', validateForm);
    
    function validateForm() {
        const loanType = document.querySelector('input[name="loan_type"]:checked').value;
        const hasToken = tokenSelect.value !== '';
        const hasAmount = loanAmount.value && parseFloat(loanAmount.value) > 0;
        const hasBorrower = loanType === 'request' || selectedBorrowerInput.value !== '';
        
        submitLoan.disabled = !(hasToken && hasAmount && hasBorrower);
    }
    
    // Form submission
    document.getElementById('loanForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            loan_type: formData.get('loan_type'),
            borrower_id: formData.get('borrower_id'),
            token_id: formData.get('token_id'),
            amount: parseFloat(formData.get('amount')),
            purpose: formData.get('purpose') || ''
        };
        
        try {
            submitLoan.disabled = true;
            submitLoan.innerHTML = '<span class="mr-2">⏳</span>Creating Loan...';
            
            const response = await fetch('/api/tokenomics/loans', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                alert('Loan created successfully!');
                window.location.reload();
            } else {
                alert(`Error: ${result.error || 'Failed to create loan'}`);
            }
        } catch (error) {
            alert(`Error: ${error.message}`);
        } finally {
            submitLoan.disabled = false;
            submitLoan.innerHTML = '<span class="mr-2">🤝</span>Create Interest-Free Loan';
        }
    });
    
    // Repayment forms
    document.querySelectorAll('.repayment-form').forEach(form => {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loanId = this.dataset.loanId;
            const amount = parseFloat(this.querySelector('input').value);
            
            if (!amount || amount <= 0) {
                alert('Please enter a valid repayment amount');
                return;
            }
            
            try {
                const response = await fetch(`/api/tokenomics/loans/${loanId}/repay`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ amount })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert('Repayment successful!');
                    window.location.reload();
                } else {
                    alert(`Error: ${result.error || 'Failed to process repayment'}`);
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        });
    });
    
    // Forgiveness buttons
    document.querySelectorAll('.forgive-loan-btn').forEach(button => {
        button.addEventListener('click', async function() {
            const loanId = this.dataset.loanId;
            
            if (!confirm('Are you sure you want to forgive the remaining debt? This action cannot be undone.')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/tokenomics/loans/${loanId}/forgive`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason: 'MANUAL_FORGIVENESS' })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert('Loan forgiven successfully!');
                    window.location.reload();
                } else {
                    alert(`Error: ${result.error || 'Failed to forgive loan'}`);
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        });
    });
});
</script>
{% endblock %}
