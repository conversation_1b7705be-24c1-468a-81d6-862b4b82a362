# Biblical Tokenomics Implementation

This document describes the implementation of biblical economic principles in the Onnyx blockchain platform.

## Overview

The biblical tokenomics system implements nine core features based on biblical economic principles:

1. **Jubilee-Based Circulatory Reset System**
2. **Tiered Mining Rewards Based on Community Activity**
3. **Gleaning Pool for Community Support**
4. **Anti-Usury Lending System**
5. **Firstfruits Offering Mechanism**
6. **Anti-Concentration Protocol**
7. **Biblical Token Classification System**
8. **Minimum/Maximum Wage Logic**
9. **Sabbath Enforcement and Righteous Boost**

## Features Implemented

### 1. Jubilee-Based Circulatory Reset System ✅

**Purpose**: Periodic wealth redistribution to prevent extreme inequality.

**Implementation**:
- Tracks wallet activity via `last_active_timestamp` and `last_transaction_height` fields
- Identifies dormant accounts after configurable inactivity periods
- Reclaims percentage of inactive balances into community gleaning pool
- Provides debt forgiveness mechanisms

**Database Tables**:
- `dormant_accounts` - Tracks inactive accounts
- `jubilee_pools` - Manages redistribution pools

**Opcodes**: `OP_JUBILEE`

### 2. Tiered Mining Rewards Based on Community Activity ✅

**Purpose**: Reward righteous behavior with increased mining rewards.

**Implementation**:
- Tracks "deeds score" for each identity based on community activities
- Calculates effective reward: `base_reward * (1 + deeds_score_bonus)`
- Activities tracked: mutual aid, donations, firstfruits participation, sabbath observance
- Maximum 10% bonus from deed score

**Database Tables**:
- `deeds_ledger` - Records righteous activities
- `identities.deeds_score` - Current deed score

**Configuration**:
- `deed_score_multiplier`: 0.1 (10% max bonus)

### 3. Gleaning Pool for Community Support ✅

**Purpose**: Provide safety net for community members in need.

**Implementation**:
- Automatically allocates 2% of mining rewards to gleaning pool
- Special `GLEANS_POOL` identity address
- Need-based claiming with justification required
- API endpoints for pool management

**Database Tables**:
- `jubilee_pools` (pool_type='GLEANING')

**API Endpoints**:
- `GET /api/tokenomics/gleaning-pool` - Pool status
- `POST /api/tokenomics/gleaning-pool/claim` - Claim from pool

### 4. Anti-Usury Lending System ✅

**Purpose**: Enable interest-free lending with automatic forgiveness.

**Implementation**:
- Tracks loans with grace periods and forgiveness thresholds
- Auto-forgiveness when borrower has paid 80% and grace period expired
- No interest charges
- Automatic loan forgiveness checking during mining

**Database Tables**:
- `loans` - Loan tracking and metadata

**Opcodes**: `OP_LEND`, `OP_REPAY`, `OP_FORGIVE`

**API Endpoints**:
- `POST /api/tokenomics/loans` - Create loan
- `POST /api/tokenomics/loans/<loan_id>/repay` - Repay loan
- `POST /api/tokenomics/loans/<loan_id>/forgive` - Forgive loan
- `GET /api/tokenomics/loans/<identity_id>` - Get loans

### 5. Firstfruits Offering Mechanism ✅

**Purpose**: Voluntary community contributions with rewards.

**Implementation**:
- Voluntary offerings to community pool
- Awards +2 Etzem tokens per offering
- Tracks contributions for community recognition
- Automatic token classification as "Etzem" class

**Database Tables**:
- `jubilee_pools` (pool_type='FIRSTFRUITS')
- `deeds_ledger` (deed_type='FIRSTFRUITS')

**Opcodes**: `OP_FIRSTFRUITS`

**API Endpoints**:
- `POST /api/tokenomics/firstfruits` - Make offering

### 6. Anti-Concentration Protocol ✅

**Purpose**: Prevent excessive wealth concentration.

**Implementation**:
- Monitors wallet balances against concentration thresholds
- Reduces mining rewards to 10% when threshold exceeded
- Encourages redistribution to new validators or gleaning pool
- Automatic enforcement during mining process

**Configuration**:
- `concentration_threshold`: 1,000,000 ONX
- `concentration_penalty_rate`: 0.1 (10% of normal rewards)

**API Endpoints**:
- `GET /api/tokenomics/concentration/<identity_id>` - Check concentration status

### 7. Biblical Token Classification System ✅

**Purpose**: Categorize tokens by biblical principles.

**Implementation**:
- Token classes: 'Avodah' (labor), 'Zedek' (righteousness), 'Yovel' (jubilee), 'Etzem' (essence)
- Assigned during minting, rewards, and jubilee operations
- Automatic classification by purpose
- Manual classification via API

**Database Tables**:
- `token_classes` - Token classification metadata

**API Endpoints**:
- `POST /api/tokenomics/tokens/<token_id>/classify` - Classify token
- `GET /api/tokenomics/tokens/<token_id>/class` - Get token class

### 8. Minimum/Maximum Wage Logic ✅

**Purpose**: Ensure fair reward distribution.

**Implementation**:
- `MIN_BLOCK_REWARD`: 2 ONX
- `MAX_BLOCK_REWARD`: 200 ONX
- All calculated rewards capped within these bounds
- Integrated into mining reward calculation

**Configuration**:
- `min_block_reward`: 2
- `max_block_reward`: 200

### 9. Sabbath Enforcement and Righteous Boost ✅

**Purpose**: Encourage rest and spiritual observance.

**Implementation**:
- Detects Sabbath periods (Friday sunset to Saturday sunset)
- Blocks mining operations during Sabbath
- Awards 0.2 deed score bonus for Sabbath observance
- Tracks sabbath observers
- Automatic Sabbath period tracking

**Database Tables**:
- `sabbath_periods` - Sabbath period tracking
- `identities.sabbath_observer` - Observer status

**Configuration**:
- `sabbath_start_day`: 5 (Friday)
- `sabbath_start_hour`: 18 (6 PM)
- `sabbath_duration_hours`: 25
- `sabbath_deed_bonus`: 0.2

**API Endpoints**:
- `GET /api/tokenomics/sabbath/status` - Get Sabbath status

## API Endpoints

### Gleaning Pool
- `GET /api/tokenomics/gleaning-pool` - Get pool status and recent activity
- `POST /api/tokenomics/gleaning-pool/claim` - Claim tokens from pool (requires justification)

### Anti-Usury Lending
- `POST /api/tokenomics/loans` - Create new interest-free loan
- `POST /api/tokenomics/loans/<loan_id>/repay` - Make loan repayment
- `POST /api/tokenomics/loans/<loan_id>/forgive` - Forgive loan (lender only)
- `GET /api/tokenomics/loans/<identity_id>` - Get loans for identity

### Deeds and Scoring
- `GET /api/tokenomics/deeds/<identity_id>` - Get deed history and current score

### Firstfruits Offerings
- `POST /api/tokenomics/firstfruits` - Make firstfruits offering (awards Etzem tokens)

### Token Classification
- `POST /api/tokenomics/tokens/<token_id>/classify` - Classify token with biblical class
- `GET /api/tokenomics/tokens/<token_id>/class` - Get token's biblical class

### Anti-Concentration
- `GET /api/tokenomics/concentration/<identity_id>` - Check concentration status and penalties

### Sabbath Enforcement
- `GET /api/tokenomics/sabbath/status` - Get current Sabbath status and configuration

## Database Schema

### New Tables Added

```sql
-- Jubilee pools for wealth redistribution
CREATE TABLE jubilee_pools (
    pool_id TEXT PRIMARY KEY,
    pool_type TEXT NOT NULL,
    total_amount REAL NOT NULL DEFAULT 0.0,
    token_id TEXT NOT NULL DEFAULT 'ONX',
    created_at INTEGER NOT NULL,
    last_distribution INTEGER DEFAULT 0,
    metadata TEXT NOT NULL DEFAULT '{}'
);

-- Deed tracking for righteous activities
CREATE TABLE deeds_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    deed_type TEXT NOT NULL,
    deed_value REAL NOT NULL,
    description TEXT,
    timestamp INTEGER NOT NULL,
    block_height INTEGER,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Anti-usury lending system
CREATE TABLE loans (
    loan_id TEXT PRIMARY KEY,
    lender_id TEXT NOT NULL,
    borrower_id TEXT NOT NULL,
    amount REAL NOT NULL,
    token_id TEXT NOT NULL DEFAULT 'ONX',
    grace_blocks INTEGER NOT NULL,
    forgiveness_threshold REAL DEFAULT 0.8,
    amount_paid REAL DEFAULT 0.0,
    status TEXT DEFAULT 'ACTIVE',
    created_at INTEGER NOT NULL,
    created_block INTEGER NOT NULL,
    FOREIGN KEY (lender_id) REFERENCES identities(identity_id),
    FOREIGN KEY (borrower_id) REFERENCES identities(identity_id)
);
```

### Modified Tables

```sql
-- Added tokenomics fields to identities
ALTER TABLE identities ADD COLUMN last_active_timestamp INTEGER DEFAULT 0;
ALTER TABLE identities ADD COLUMN last_transaction_height INTEGER DEFAULT 0;
ALTER TABLE identities ADD COLUMN deeds_score REAL DEFAULT 0.0;
ALTER TABLE identities ADD COLUMN sabbath_observer BOOLEAN DEFAULT 0;
```

## Configuration Parameters

All tokenomics parameters are configurable via `shared/config/chain_parameters.py`:

```python
# Biblical Tokenomics Parameters
"min_block_reward": 2,
"max_block_reward": 200,
"gleaning_pool_percentage": 0.02,  # 2% of mining rewards
"jubilee_interval_blocks": 50400,  # ~35 days at 60s blocks
"dormancy_threshold_blocks": 7200,  # ~5 days of inactivity
"deed_score_multiplier": 0.1,  # Max 10% bonus from deeds
"firstfruits_etzem_reward": 2,  # Etzem tokens per offering
"sabbath_deed_bonus": 0.2,  # Deed score bonus for sabbath observance
"concentration_threshold": 1000000,  # Max balance before penalties
"sabbath_start_day": 5,  # Friday
"sabbath_start_hour": 18,  # 6 PM
"sabbath_duration_hours": 25  # Friday 6 PM to Saturday 7 PM
```

## Installation and Migration

1. **Run Migration Script**:
   ```bash
   python scripts/migrate_tokenomics.py
   ```

2. **Run Tests**:
   ```bash
   python tests/test_tokenomics.py
   ```

3. **Verify Installation**:
   - Check that new tables exist in database
   - Verify chain parameters are updated
   - Test API endpoints

## Usage Examples

### Creating an Interest-Free Loan
```bash
curl -X POST http://localhost:5000/api/tokenomics/loans \
  -H "Content-Type: application/json" \
  -d '{
    "borrower_id": "borrower_identity_123",
    "amount": 100,
    "token_id": "ONX",
    "grace_blocks": 14400
  }'
```

### Repaying a Loan
```bash
curl -X POST http://localhost:5000/api/tokenomics/loans/loan_123/repay \
  -H "Content-Type: application/json" \
  -d '{"amount": 50}'
```

### Making a Firstfruits Offering
```bash
curl -X POST http://localhost:5000/api/tokenomics/firstfruits \
  -H "Content-Type: application/json" \
  -d '{"amount": 10, "token_id": "ONX"}'
```

### Claiming from Gleaning Pool
```bash
curl -X POST http://localhost:5000/api/tokenomics/gleaning-pool/claim \
  -H "Content-Type: application/json" \
  -d '{"amount": 5, "justification": "Need assistance for family"}'
```

### Classifying a Token
```bash
curl -X POST http://localhost:5000/api/tokenomics/tokens/WORK_TOKEN/classify \
  -H "Content-Type: application/json" \
  -d '{"purpose": "labor"}'
```

### Checking Concentration Status
```bash
curl http://localhost:5000/api/tokenomics/concentration/identity_123
```

### Checking Sabbath Status
```bash
curl http://localhost:5000/api/tokenomics/sabbath/status
```

### Getting Deed History
```bash
curl http://localhost:5000/api/tokenomics/deeds/identity_123
```

## Testing

The implementation includes comprehensive tests in `tests/test_tokenomics.py`:

- Tiered mining reward calculations
- Deed recording and scoring
- Sabbath period detection
- Opcode validation
- Chain parameter integration

Run tests with:
```bash
python tests/test_tokenomics.py
```

## Future Enhancements

1. **Jubilee Automation**: Automatic triggering of jubilee resets
2. **Advanced Deed Scoring**: More sophisticated deed value calculations
3. **Community Governance**: Voting on gleaning pool distributions
4. **Token Class Automation**: Automatic token classification
5. **Enhanced Sabbath Detection**: Timezone-aware Sabbath calculation
