# ONNYX Design System & Component Library

## Overview
The ONNYX design system provides a comprehensive set of modular components, utilities, and design tokens that maintain consistency across the platform while embodying the tribal/cyberpunk aesthetic.

## Design Tokens

### Color System
```css
/* Primary Colors */
--onyx-black: #0a0a0a;
--onyx-dark: #1a1a1a;
--cyber-cyan: #00fff7;
--cyber-purple: #9a00ff;
--cyber-blue: #0080ff;
--cyber-green: #00ff80;

/* Text Colors */
--text-primary: #ffffff;
--text-secondary: #b3b3b3;
--text-tertiary: #808080;
--text-muted: #666666;
```

### Spacing System (8px Grid)
```css
--space-1: 8px;    /* 8px */
--space-2: 16px;   /* 16px */
--space-3: 24px;   /* 24px */
--space-4: 32px;   /* 32px */
--space-6: 48px;   /* 48px */
--space-8: 64px;   /* 64px */
--space-12: 96px;  /* 96px */
--space-16: 128px; /* 128px */
--space-20: 160px; /* 160px */
```

### Typography Scale
```css
--font-size-xs: 0.75rem;   /* 12px */
--font-size-sm: 0.875rem;  /* 14px */
--font-size-base: 1rem;    /* 16px */
--font-size-lg: 1.125rem;  /* 18px */
--font-size-xl: 1.25rem;   /* 20px */
--font-size-2xl: 1.5rem;   /* 24px */
--font-size-3xl: 1.875rem; /* 30px */
--font-size-4xl: 2.25rem;  /* 36px */
--font-size-5xl: 3rem;     /* 48px */
--font-size-6xl: 3.75rem;  /* 60px */
```

## Core Components

### Glass Morphism Cards
```html
<!-- Basic glass card -->
<div class="glass-card">Content</div>

<!-- Light variant -->
<div class="glass-card-light">Content</div>

<!-- Heavy variant -->
<div class="glass-card-heavy">Content</div>

<!-- Premium variant -->
<div class="glass-card-premium">Content</div>
```

### Button System
```html
<!-- Primary button -->
<button class="btn btn-primary">Primary Action</button>

<!-- Secondary button -->
<button class="btn btn-secondary">Secondary Action</button>

<!-- Outline button -->
<button class="btn btn-outline">Outline Action</button>

<!-- Disabled state -->
<button class="btn btn-primary" disabled>Disabled</button>
```

### Grid System
```html
<!-- 2-column grid -->
<div class="grid grid-2">
  <div>Item 1</div>
  <div>Item 2</div>
</div>

<!-- 3-column grid -->
<div class="grid grid-3">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>

<!-- 4-column grid -->
<div class="grid grid-4">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
  <div>Item 4</div>
</div>

<!-- Equal height grid -->
<div class="grid grid-3 grid-equal-height">
  <div>Different content lengths</div>
  <div>Will all be same height</div>
  <div>Automatically</div>
</div>
```

### Statistics Cards
```html
<div class="stat-card">
  <div class="stat-value text-cyber-cyan">1,234</div>
  <div class="stat-label">Active Validators</div>
</div>

<!-- With icon -->
<div class="stat-card">
  <div class="stat-icon bg-gradient-to-br from-cyber-cyan to-cyber-blue">
    <svg>...</svg>
  </div>
  <div class="stat-value text-cyber-cyan">1,234</div>
  <div class="stat-label">Active Validators</div>
</div>
```

### Form Components
```html
<!-- Form group -->
<div class="form-group">
  <label class="form-label">Label Text</label>
  <input type="text" class="form-input" placeholder="Placeholder">
  <p class="form-help">Helper text</p>
</div>

<!-- Form select -->
<div class="form-group">
  <label class="form-label">Select Option</label>
  <select class="form-select">
    <option>Option 1</option>
    <option>Option 2</option>
  </select>
</div>
```

### Layout Containers
```html
<!-- Standard container -->
<div class="container">Content</div>

<!-- Large container -->
<div class="container container-lg">Content</div>

<!-- Extra large container -->
<div class="container container-xl">Content</div>

<!-- Section with spacing -->
<section class="section-spacing">
  <div class="container">Content</div>
</section>

<!-- Hero section -->
<section class="hero-section">
  <div class="container">
    <div class="hero-content">Hero content</div>
  </div>
</section>
```

## Utility Classes

### Flexbox Utilities
```css
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-start { display: flex; align-items: center; justify-content: flex-start; }
.flex-end { display: flex; align-items: center; justify-content: flex-end; }
```

### Animation Utilities
```css
.fade-in { animation: fadeIn 0.8s ease-out; }
.slide-in { animation: slideIn 0.5s ease-out; }
.slide-up { animation: slideUp 0.6s ease-out; }
.glow-cyan { animation: cyberGlow 2s ease-in-out infinite; }
.glow-purple { animation: purpleGlow 2s ease-in-out infinite; }
.glow-blue { animation: blueGlow 2s ease-in-out infinite; }
```

### Special Effects
```css
.hologram-text { /* Animated gradient text */ }
.spinning-n { /* Rotating tribal N's */ }
.cyber-grid { /* Background grid pattern */ }
.hero-gradient { /* Hero section gradient */ }
```

## Responsive Design

### Breakpoints
- **Mobile**: 0-640px
- **Tablet**: 641-768px
- **Desktop**: 769-1024px
- **Large Desktop**: 1025px+

### Mobile-First Approach
All components are designed mobile-first with progressive enhancement for larger screens.

## Accessibility Features

### Focus States
All interactive elements have visible focus indicators for keyboard navigation.

### Touch Targets
Minimum 44px touch targets for mobile accessibility.

### Reduced Motion
Respects `prefers-reduced-motion` user preference.

### High Contrast
Supports `prefers-contrast: high` for better visibility.

## Performance Optimizations

### Hardware Acceleration
Critical animations use `transform` and `opacity` for GPU acceleration.

### Efficient Selectors
CSS uses efficient selectors and avoids deep nesting.

### Minimal Reflows
Layout changes use `transform` instead of position properties.

## Usage Guidelines

### Component Composition
Components can be combined for complex layouts:

```html
<section class="section-spacing">
  <div class="container container-lg">
    <div class="grid grid-3 grid-equal-height slide-up">
      <div class="glass-card">
        <div class="stat-card">
          <div class="stat-value text-cyber-cyan">1,234</div>
          <div class="stat-label">Validators</div>
        </div>
      </div>
      <!-- More cards... -->
    </div>
  </div>
</section>
```

### Consistent Spacing
Always use the 8px grid system for consistent spacing.

### Color Usage
Use semantic color classes for consistent theming.

### Animation Guidelines
Use entrance animations sparingly and respect user preferences.

## Maintenance

### Adding New Components
1. Follow the modular structure
2. Use design tokens
3. Ensure mobile responsiveness
4. Add accessibility features
5. Document usage examples

### Removing Dead Styles
Regular audits should remove unused CSS to maintain performance.

This design system ensures consistency, maintainability, and performance across the ONNYX platform.
