{% extends "base.html" %}

{% block title %}Genesis Identity Creation - ONNYX Platform Founder{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative py-20">
    <!-- Enhanced floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-3 h-3 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-2 h-2 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-2.5 h-2.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
        <div class="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse opacity-40"></div>
    </div>

    <div class="container-md relative z-10">
        <div class="card">
            <!-- Enhanced Header -->
            <div class="card-header text-center">
                <!-- ONNYX Logo -->
                <div class="mb-8 flex justify-center">
                    <div class="flex items-center justify-center group">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <!-- Fallback symbol only if image fails -->
                        <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
                    </div>
                </div>

                <!-- Genesis Icon -->
                <div class="w-24 h-24 bg-gradient-to-br from-cyber-cyan via-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-6 glow-effect shadow-cyber">
                    <svg class="w-12 h-12 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>

                <h1 class="card-title text-4xl md:text-5xl mb-4">
                    <span class="hologram-text">Genesis Identity Creation</span>
                </h1>
                <p class="card-subtitle text-xl max-w-3xl mx-auto">
                    Initialize the foundational Platform Founder identity for the ONNYX blockchain network
                </p>
            </div>

            <!-- Enhanced Form -->
            <div class="card-body">
                <form method="POST" x-data="genesisForm()" @submit="handleSubmit" class="space-y-8">
                    <!-- Platform Founder Notice -->
                    <div class="card border border-cyber-cyan/40 bg-cyber-cyan/10">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">🌟 Platform Founder Status</h3>
                                <p class="text-cyber-cyan/90 leading-relaxed">
                                    You are creating the Genesis Identity - the foundational identity that will anchor the entire ONNYX trusted business network. This identity will have special privileges and responsibilities as the Platform Founder.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Name Field -->
                    <div class="form-group">
                        <label for="name" class="form-label required">
                            🔐 Full Legal Name
                        </label>
                        <input type="text"
                               id="name"
                               name="name"
                               required
                               x-model="form.name"
                               class="form-control"
                               placeholder="Enter your full legal name as Platform Founder">
                        <div class="form-help">This will be your public identity as the ONNYX Platform Founder</div>
                    </div>

                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="email" class="form-label required">
                            📧 Founder Email Address
                        </label>
                        <input type="email"
                               id="email"
                               name="email"
                               required
                               x-model="form.email"
                               @blur="validateEmail"
                               class="form-control"
                               placeholder="Enter your primary business email">
                        <div x-show="emailValidation.checking" class="form-help flex items-center gap-2">
                            <div class="spinner inline-block w-4 h-4"></div>
                            <span>Checking availability...</span>
                        </div>
                        <div x-show="emailValidation.message"
                             :class="emailValidation.valid ? 'form-success' : 'form-error'"
                             x-text="emailValidation.message">
                        </div>
                    </div>

                    <!-- Organization Field -->
                    <div class="form-group">
                        <label for="organization" class="form-label">
                            🏢 Organization/Company
                        </label>
                        <input type="text"
                               id="organization"
                               name="organization"
                               x-model="form.organization"
                               class="form-control"
                               placeholder="Your company or organization name (optional)">
                        <div class="form-help">Optional: The organization you represent as Platform Founder</div>
                    </div>

                    <!-- Purpose Field -->
                    <div class="form-group">
                        <label for="purpose" class="form-label required">
                            🎯 Platform Purpose Statement
                        </label>
                        <textarea id="purpose"
                                  name="purpose"
                                  required
                                  x-model="form.purpose"
                                  rows="4"
                                  class="form-control"
                                  placeholder="Describe the purpose and vision for the ONNYX platform...">Establishing a trusted business network for secure commerce and decentralized validation through quantum-resistant cryptographic identities and blockchain technology.</textarea>
                        <div class="form-help">This statement will be recorded in the Genesis block</div>
                    </div>

                    <!-- Hidden Role Field -->
                    <input type="hidden" name="role" value="Platform Founder">

                    <!-- Genesis Block Information -->
                    <div class="card border border-cyber-purple/40 bg-cyber-purple/10">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">⛓️ Genesis Block Creation</h3>
                                <p class="text-cyber-purple/90 leading-relaxed">
                                    Your Genesis Identity will be recorded in Block #0 of the ONNYX blockchain, establishing the foundation for all future transactions and validations. This is a historic moment in the platform's development.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Cryptographic Key Generation -->
                    <div class="card border border-cyber-blue/40 bg-cyber-blue/10">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-cyber-blue to-green-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">🔑 Quantum-Resistant Cryptography</h3>
                                <p class="text-cyber-blue/90 leading-relaxed">
                                    Advanced ECDSA cryptographic key pairs will be generated for your Genesis Identity. Your private key will be provided after creation - store it securely as the master key for the platform.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox"
                                   id="acceptTerms"
                                   required
                                   x-model="form.acceptTerms"
                                   class="form-check-input">
                            <label for="acceptTerms" class="form-check-label">
                                I understand my responsibilities as Platform Founder, acknowledge that I will receive cryptographic keys that I must keep secure, and agree to establish and maintain the ONNYX trusted business network with integrity and transparency.
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-group">
                        <button type="submit"
                                :disabled="!canSubmit"
                                :class="canSubmit ? 'btn btn-primary btn-lg w-full' : 'btn btn-primary btn-lg w-full opacity-50 cursor-not-allowed'"
                                class="transition-all duration-300">
                            <span x-show="!submitting" class="flex items-center justify-center gap-3">
                                <span class="text-xl">🌟</span>
                                <span>Create Genesis Identity</span>
                            </span>
                            <span x-show="submitting" class="flex items-center justify-center gap-3">
                                <div class="spinner inline-block w-5 h-5"></div>
                                <span>Initializing Genesis Block...</span>
                            </span>
                        </button>
                    </div>

                    <!-- Critical Security Warning -->
                    <div class="card border border-yellow-500/40 bg-yellow-500/10">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-orbitron font-bold text-yellow-400 mb-2">⚠️ Genesis Identity Security Protocol</h3>
                                <p class="text-yellow-200 leading-relaxed">
                                    As Platform Founder, your private key is the master key for the entire ONNYX network. Store it in multiple secure locations immediately after generation. This key cannot be recovered if lost and controls the foundational authority of the platform.
                                </p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Enhanced Footer -->
            <div class="card-footer justify-center">
                <a href="{{ url_for('register_choice') }}" class="btn btn-ghost">
                    ← Return to Verification Portal
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function genesisForm() {
    return {
        form: {
            name: '',
            email: '',
            organization: '',
            purpose: 'Establishing a trusted business network for secure commerce and decentralized validation through quantum-resistant cryptographic identities and blockchain technology.',
            acceptTerms: false
        },
        emailValidation: {
            valid: null,
            message: '',
            checking: false
        },
        submitting: false,

        get canSubmit() {
            return this.form.name &&
                   this.form.email &&
                   this.form.purpose &&
                   this.form.acceptTerms &&
                   this.emailValidation.valid !== false &&
                   !this.submitting;
        },

        async validateEmail() {
            if (!this.form.email) return;

            this.emailValidation.checking = true;
            this.emailValidation.message = '';

            try {
                const response = await fetch('/auth/api/validate/email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: this.form.email })
                });

                const result = await response.json();
                this.emailValidation.valid = result.valid;
                this.emailValidation.message = result.message;
            } catch (error) {
                this.emailValidation.valid = false;
                this.emailValidation.message = 'Validation failed';
            } finally {
                this.emailValidation.checking = false;
            }
        },

        handleSubmit(event) {
            if (!this.canSubmit) {
                event.preventDefault();
                return;
            }
            this.submitting = true;
        }
    }
}
</script>
{% endblock %}
