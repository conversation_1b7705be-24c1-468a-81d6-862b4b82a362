{% extends "base.html" %}

{% block title %}Tribal Heritage Selection - Israelite Path{% endblock %}

{% block description %}Choose your tribal lineage from the 12 Tribes of Israel. Your heritage will be verified by the Gate Keeper Council.{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 opacity-20">
        <div class="floating-particles"></div>
        <div class="data-streams"></div>
    </div>

    <!-- Progress Indicator -->
    <div class="flex justify-center mb-12 pt-8">
        <div class="glass-card-enhanced px-8 py-6 rounded-3xl shadow-lg shadow-cyber-cyan/10">            <div class="flex items-center space-x-4">
                <div class="w-10 h-10 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold text-lg">✓</div>
                <div class="w-8 h-1 bg-gradient-to-r from-cyber-green to-cyber-cyan rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-cyber-cyan to-cyber-blue flex items-center justify-center text-onyx-black font-bold text-lg shadow-lg shadow-cyber-cyan/30 animate-pulse">2</div>
                <div class="w-8 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg">3</div>
                <div class="w-8 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-purple-600 to-violet-600 flex items-center justify-center text-white font-bold text-lg shadow-lg shadow-purple-500/30">🗿</div>
                <div class="w-8 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg">5</div>
            </div>
            <div class="flex items-center justify-center mt-4">
                <span class="text-sm font-orbitron text-cyber-cyan">Choose Your Tribal Heritage</span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-xl mx-auto px-6 pt-16 pb-16">
        <div class="max-w-6xl mx-auto">

            <!-- Header -->
            <div class="text-center mb-16 fade-in-sequence" data-delay="0">
                <div class="mb-8">
                    <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                        <div class="text-6xl">🗿</div>
                    </div>
                    <h1 class="text-5xl md:text-6xl font-orbitron font-bold text-cyber-cyan mb-6 glow-text">
                        The Twelve Tribes
                    </h1>
                    <p class="text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                        "And these are the names of the children of Israel, which came into Egypt, Jacob and his sons"
                    </p>
                    <p class="text-lg text-cyber-purple font-orbitron mt-4">
                        — Genesis 46:8
                    </p>
                </div>
            </div>

            <!-- Tribal Selection -->
            <div class="space-y-12">
                <!-- Introduction -->
                <div class="glass-card-premium p-8 rounded-3xl fade-in-sequence" data-delay="500">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                            Reclaim Your Tribal Identity
                        </h2>
                        <p class="text-lg text-text-secondary max-w-4xl mx-auto leading-relaxed">
                            Each tribe carries a unique calling, blessing, and responsibility within the covenant community. 
                            Select your ancestral tribe to begin the Gate Keeper verification process.
                        </p>                        <div class="bg-cyber-cyan/10 border border-cyber-cyan/20 rounded-lg p-4 mt-6 max-w-3xl mx-auto">
                            <p class="text-sm text-cyber-cyan font-semibold">
                                ⚠️ Your tribal selection will be reviewed by the Gate Keeper Council for verification before covenant inscription
                            </p>
                        </div>                        <!-- Developer Bypass Section (Hidden by default) -->
                        <div class="mt-8">
                            <button onclick="toggleDeveloperBypass()" 
                                    class="text-sm text-gray-400 hover:text-cyber-cyan transition-colors duration-300 px-4 py-2 rounded border border-gray-600 hover:border-cyber-cyan"
                                    style="opacity: 0.7;">
                                🔑 Developer Access
                            </button>
                            <div id="developerBypass" class="hidden mt-4 max-w-md mx-auto">
                                <div class="bg-cyber-purple/10 border border-cyber-purple/20 rounded-lg p-6">
                                    <h4 class="text-lg font-orbitron font-semibold text-cyber-purple mb-4">
                                        🔑 Developer Bypass
                                    </h4>
                                    <p class="text-sm text-text-secondary mb-4">
                                        Enter the covenant phrase to bypass Gate Keeper verification
                                    </p>
                                    <input type="password" 
                                           id="bypassPassword" 
                                           placeholder="Enter covenant phrase..."
                                           class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-lg text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-cyber-purple focus:border-cyber-purple transition-all duration-300 mb-4">
                                    <button onclick="validateBypass()" 
                                            class="w-full px-6 py-3 bg-gradient-to-r from-cyber-purple to-cyber-blue text-white rounded-lg font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                        🚀 Bypass Gate Keepers
                                    </button>
                                    <p class="text-xs text-text-muted mt-2 text-center">
                                        This will mark your identity as developer-verified
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tribal Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in-sequence" data-delay="1000">
                    {% for tribe in israelite_tribes %}
                    <div class="glass-card-enhanced p-6 rounded-2xl hover:scale-105 transition-all duration-300 group cursor-pointer tribal-card border-2 border-transparent hover:border-cyber-cyan/50"
                         onclick="selectTribe('{{ tribe.tribe_name }}', '{{ tribe.description }}', '{{ tribe.symbol }}', '{{ tribe.stone }}')">
                        <div class="text-center">
                            <div class="text-5xl mb-4">{{ tribe.symbol }}</div>
                            <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-3">
                                {{ tribe.tribe_name }}
                            </h3>
                            <p class="text-text-secondary mb-4 leading-relaxed">
                                {{ tribe.description }}
                            </p>
                            <div class="bg-cyber-cyan/5 border border-cyber-cyan/20 rounded-lg p-3">
                                <p class="text-sm font-orbitron font-semibold text-cyber-cyan">
                                    Breastplate Stone: {{ tribe.stone }}
                                </p>
                            </div>
                        </div>
                        <!-- Selection indicator -->
                        <div class="absolute inset-0 bg-gradient-to-r from-cyber-cyan/10 to-cyber-blue/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Sacred Context -->
                <div class="glass-card-premium p-8 rounded-3xl fade-in-sequence" data-delay="1500">
                    <div class="text-center">
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">
                            The Sacred Order
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                            <div>
                                <h4 class="text-lg font-orbitron font-semibold text-cyber-cyan mb-3">First Onyx Stone</h4>
                                <div class="space-y-2 text-text-secondary">
                                    <p>Reuben • Simeon • Levi</p>
                                    <p>Judah • Dan • Naphtali</p>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-orbitron font-semibold text-cyber-cyan mb-3">Second Onyx Stone</h4>
                                <div class="space-y-2 text-text-secondary">
                                    <p>Gad • Asher • Issachar</p>
                                    <p>Zebulun • Joseph • Benjamin</p>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-cyber-purple mt-6 font-orbitron">
                            "According to their birth" - Exodus 28:10
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tribal Selection Modal -->
<div id="tribalModal" class="modal-overlay">
    <div class="modal-content">
        <div class="text-center">
            <div id="modalSymbol" class="text-6xl mb-4"></div>
            <h3 id="modalTribe" class="text-3xl font-orbitron font-bold text-cyber-cyan mb-4"></h3>
            <p id="modalDescription" class="text-text-secondary mb-6 leading-relaxed"></p>
            <div class="bg-cyber-cyan/10 border border-cyber-cyan/20 rounded-lg p-4 mb-6">
                <p class="text-sm font-orbitron font-semibold text-cyber-cyan">
                    Breastplate Stone: <span id="modalStone"></span>
                </p>
            </div>
            <p class="text-sm text-text-secondary mb-8">
                By selecting this tribe, you commit to Gate Keeper verification of your lineage claim.
            </p>
            <div class="flex gap-4 justify-center">
                <button onclick="closeModal()" 
                        class="px-6 py-3 bg-glass-border text-text-secondary rounded-xl font-orbitron font-semibold transition-all duration-300 hover:bg-glass-hover">
                    Choose Different Tribe
                </button>
                <button onclick="confirmTribe()" 
                        class="px-8 py-3 bg-gradient-to-r from-cyber-cyan to-cyber-blue text-white rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    Confirm Selection
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedTribalData = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeSequentialAnimations();
    
    // Close modal when clicking on overlay
    document.getElementById('tribalModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('tribalModal');
            if (modal.classList.contains('show')) {
                closeModal();
            }
        }
    });
});

function initializeSequentialAnimations() {
    const elements = document.querySelectorAll('.fade-in-sequence');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = entry.target.dataset.delay || 0;
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, delay);
            }
        });
    }, { threshold: 0.1 });

    elements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(40px)';
        element.style.transition = 'opacity 1s ease, transform 1s ease';
        observer.observe(element);
    });
}

function selectTribe(tribe, description, symbol, stone) {
    selectedTribalData = { tribe, description, symbol, stone };
    
    document.getElementById('modalSymbol').textContent = symbol;
    document.getElementById('modalTribe').textContent = tribe;
    document.getElementById('modalDescription').textContent = description;
    document.getElementById('modalStone').textContent = stone;
    
    const modal = document.getElementById('tribalModal');
    modal.classList.remove('hidden');
    modal.classList.add('show');
    
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    const modal = document.getElementById('tribalModal');
    modal.classList.remove('show');
    modal.classList.add('hidden');
    selectedTribalData = null;
    
    // Restore body scroll
    document.body.style.overflow = 'auto';
}

function confirmTribe() {
    if (!selectedTribalData) return;
    
    // Store selection
    sessionStorage.setItem('selectedTribe', selectedTribalData.tribe);
    sessionStorage.setItem('tribalData', JSON.stringify(selectedTribalData));
    
    // Show loading state
    const confirmButton = document.querySelector('button[onclick="confirmTribe()"]');
    confirmButton.innerHTML = '🌟 Preparing for Verification...';
    confirmButton.disabled = true;
    
    // Redirect to next step
    setTimeout(() => {
        window.location.href = `/auth/eden-mode/israelite-step3?tribe=${encodeURIComponent(selectedTribalData.tribe)}`;
    }, 1500);
}

function toggleDeveloperBypass() {
    const bypassSection = document.getElementById('developerBypass');
    bypassSection.classList.toggle('hidden');
    
    // Focus on password input if showing
    if (!bypassSection.classList.contains('hidden')) {
        setTimeout(() => {
            document.getElementById('bypassPassword').focus();
        }, 100);
    }
}

function validateBypass() {
    const password = document.getElementById('bypassPassword').value.trim();
    const correctPassword = "Israel United In Christ";
    
    if (password === correctPassword) {
        // Store bypass flag
        sessionStorage.setItem('developerBypass', 'true');
        sessionStorage.setItem('bypassType', 'developer');
        sessionStorage.setItem('bypassTimestamp', Date.now().toString());
        
        // Show success message
        const button = document.querySelector('button[onclick="validateBypass()"]');
        button.innerHTML = '✅ Bypass Activated';
        button.disabled = true;
        button.classList.remove('bg-gradient-to-r', 'from-cyber-purple', 'to-cyber-blue');
        button.classList.add('bg-cyber-green');
        
        // Hide the regular tribal selection
        const tribalGrid = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (tribalGrid) {
            tribalGrid.style.opacity = '0.5';
            tribalGrid.style.pointerEvents = 'none';
        }
        
        // Show bypass success message
        setTimeout(() => {
            showBypassSuccess();
        }, 1000);
        
    } else {
        // Show error
        const input = document.getElementById('bypassPassword');
        input.style.borderColor = '#ff3366';
        input.style.boxShadow = '0 0 10px rgba(255, 51, 102, 0.3)';
        input.value = '';
        input.placeholder = 'Incorrect phrase - try again';
        
        setTimeout(() => {
            input.style.borderColor = '';
            input.style.boxShadow = '';
            input.placeholder = 'Enter covenant phrase...';
        }, 2000);
    }
}

function showBypassSuccess() {
    // Create success overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center';
    overlay.innerHTML = `
        <div class="glass-card-premium p-12 rounded-3xl max-w-lg mx-4 text-center">
            <div class="text-6xl mb-6">🚀</div>
            <h3 class="text-3xl font-orbitron font-bold text-cyber-green mb-4">
                Developer Bypass Activated
            </h3>
            <p class="text-text-secondary mb-6 leading-relaxed">
                Your identity has been marked as developer-verified. You will skip the Gate Keeper verification process.
            </p>
            <div class="bg-cyber-green/10 border border-cyber-green/20 rounded-lg p-4 mb-6">
                <p class="text-sm font-orbitron font-semibold text-cyber-green">
                    ✅ Proceeding directly to covenant inscription
                </p>
            </div>
            <button onclick="proceedWithBypass()" 
                    class="px-8 py-3 bg-gradient-to-r from-cyber-green to-cyber-cyan text-white rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                Continue to Inscription
            </button>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function proceedWithBypass() {
    // Store that we're bypassing and proceed to final step
    sessionStorage.setItem('skipGateKeeper', 'true');
    sessionStorage.setItem('verificationStatus', 'developer-bypassed');
    
    // Go directly to step 5 (covenant inscription) or create a direct inscription flow
    window.location.href = '/auth/eden-mode/israelite-step5?bypass=developer';
}
</script>

<style>
.glow-text {
    text-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
}

.tribal-card {
    position: relative;
    overflow: hidden;
}

.tribal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 247, 0.1), transparent);
    transition: left 0.5s;
}

.tribal-card:hover::before {
    left: 100%;
}

.floating-particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 247, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(154, 0, 255, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 255, 247, 0.2), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(-10px) translateX(-10px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    min-height: 100vh;
    box-sizing: border-box;
}

.modal-overlay.show {
    display: flex;
}

.modal-content {
    background: linear-gradient(135deg, rgba(20, 30, 40, 0.95), rgba(10, 20, 30, 0.95));
    border: 2px solid rgba(0, 255, 247, 0.3);
    border-radius: 24px;
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 25px 50px rgba(0, 255, 247, 0.15);
    animation: modalSlideIn 0.3s ease-out;
    margin: auto;
    transform: translateY(0);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Make sure modal content is above everything */
.modal-content * {
    position: relative;
    z-index: 10000;
}
</style>
{% endblock %}
