"""
Wisdom Routes - Solomonic Judgment System Interface
Handles wisdom scroll management, dispute resolution, and elder decision support.

Routes:
- /wisdom/ - Main wisdom dashboard
- /wisdom/scrolls - Browse wisdom scrolls
- /wisdom/search - Search wisdom and precedents
- /wisdom/disputes - Dispute resolution interface
- /wisdom/judgments - Judgment records
- /wisdom/create-scroll - Create new wisdom scroll
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from shared.db.db import db
from shared.models.wisdom_engine import WisdomEngine, WisdomType, WisdomCategory, DisputeType
import json
import logging
import time

logger = logging.getLogger(__name__)

wisdom_bp = Blueprint('wisdom', __name__, url_prefix='/wisdom')

@wisdom_bp.route('/')
def dashboard():
    """Main wisdom dashboard."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        identity_id = session['identity_id']
        
        # Get user info
        user = db.query_one("""
            SELECT name, verification_level, etzem_score, nation_name 
            FROM identities WHERE identity_id = ?
        """, (identity_id,))
        
        if not user:
            return redirect(url_for('auth.login'))
        
        # Check if user has elder privileges (Tier 2+ required for wisdom access)
        if user['verification_level'] < 2:
            return render_template('wisdom/access_denied.html', 
                                 required_tier=2, 
                                 current_tier=user['verification_level'])
        
        wisdom_engine = WisdomEngine()
        
        # Get recent wisdom scrolls
        recent_scrolls = db.query("""
            SELECT scroll_id, title, scroll_type, wisdom_category, 
                   precedent_weight, usage_count, created_at
            FROM wisdom_scrolls 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        # Get recent judgments
        recent_judgments = db.query("""
            SELECT judgment_id, case_title, dispute_type, 
                   presiding_elder_id, created_at
            FROM judgment_records 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        # Get pending disputes
        pending_disputes = db.query("""
            SELECT case_id, case_title, dispute_type, priority_level, submitted_at
            FROM dispute_cases 
            WHERE case_status IN ('SUBMITTED', 'UNDER_REVIEW')
            ORDER BY priority_level DESC, submitted_at ASC
            LIMIT 5
        """)
        
        # Get wisdom statistics
        stats = {
            'total_scrolls': db.query_one("SELECT COUNT(*) as count FROM wisdom_scrolls")['count'],
            'total_judgments': db.query_one("SELECT COUNT(*) as count FROM judgment_records")['count'],
            'pending_disputes': db.query_one("SELECT COUNT(*) as count FROM dispute_cases WHERE case_status IN ('SUBMITTED', 'UNDER_REVIEW')")['count'],
            'biblical_references': db.query_one("SELECT COUNT(*) as count FROM biblical_references")['count']
        }
        
        return render_template('wisdom/dashboard.html',
                             user=user,
                             recent_scrolls=recent_scrolls,
                             recent_judgments=recent_judgments,
                             pending_disputes=pending_disputes,
                             stats=stats)
        
    except Exception as e:
        logger.error(f"Error in wisdom dashboard: {e}")
        return render_template('error.html', error="Failed to load wisdom dashboard"), 500

@wisdom_bp.route('/scrolls')
def browse_scrolls():
    """Browse wisdom scrolls."""
    try:
        # Check authentication and permissions
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("SELECT verification_level FROM identities WHERE identity_id = ?", 
                           (session['identity_id'],))
        
        if not user or user['verification_level'] < 2:
            return render_template('wisdom/access_denied.html', required_tier=2)
        
        # Get filter parameters
        category = request.args.get('category', '')
        scroll_type = request.args.get('type', '')
        search_query = request.args.get('q', '')
        
        # Build query
        sql = "SELECT * FROM wisdom_scrolls WHERE 1=1"
        params = []
        
        if category:
            sql += " AND wisdom_category = ?"
            params.append(category)
        
        if scroll_type:
            sql += " AND scroll_type = ?"
            params.append(scroll_type)
        
        if search_query:
            sql += " AND (title LIKE ? OR content LIKE ?)"
            params.extend([f"%{search_query}%", f"%{search_query}%"])
        
        sql += " ORDER BY precedent_weight DESC, usage_count DESC"
        
        scrolls = db.query(sql, params)
        
        # Get categories and types for filters
        categories = [cat.value for cat in WisdomCategory]
        types = [typ.value for typ in WisdomType]
        
        return render_template('wisdom/scrolls.html',
                             scrolls=scrolls,
                             categories=categories,
                             types=types,
                             current_category=category,
                             current_type=scroll_type,
                             search_query=search_query)
        
    except Exception as e:
        logger.error(f"Error browsing scrolls: {e}")
        return render_template('error.html', error="Failed to load wisdom scrolls"), 500

@wisdom_bp.route('/search', methods=['GET', 'POST'])
def search_wisdom():
    """Search wisdom and precedents."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("SELECT verification_level FROM identities WHERE identity_id = ?", 
                           (session['identity_id'],))
        
        if not user or user['verification_level'] < 2:
            return render_template('wisdom/access_denied.html', required_tier=2)
        
        if request.method == 'POST':
            data = request.get_json()
            case_description = data.get('case_description', '')
            dispute_type_str = data.get('dispute_type', 'PROPERTY')
            
            try:
                dispute_type = DisputeType(dispute_type_str)
            except ValueError:
                dispute_type = DisputeType.PROPERTY
            
            wisdom_engine = WisdomEngine()
            suggestions = wisdom_engine.suggest_wisdom_for_case(case_description, dispute_type)
            
            return jsonify(suggestions)
        
        # GET request - show search form
        dispute_types = [dt.value for dt in DisputeType]
        
        return render_template('wisdom/search.html', dispute_types=dispute_types)
        
    except Exception as e:
        logger.error(f"Error in wisdom search: {e}")
        return jsonify({'error': str(e)}), 500

@wisdom_bp.route('/create-scroll', methods=['GET', 'POST'])
def create_scroll():
    """Create new wisdom scroll."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        identity_id = session['identity_id']
        user = db.query_one("SELECT verification_level, name FROM identities WHERE identity_id = ?", 
                           (identity_id,))
        
        if not user or user['verification_level'] < 3:  # Tier 3+ required to create wisdom
            return render_template('wisdom/access_denied.html', required_tier=3)
        
        if request.method == 'POST':
            data = request.get_json()
            
            title = data.get('title', '').strip()
            content = data.get('content', '').strip()
            scroll_type_str = data.get('scroll_type', 'TEACHING')
            category_str = data.get('category', 'JUSTICE')
            application_context = data.get('application_context', '').strip()
            biblical_references = data.get('biblical_references', [])
            precedent_weight = int(data.get('precedent_weight', 1))
            
            # Validation
            if not title or not content or not application_context:
                return jsonify({'error': 'Title, content, and application context are required'}), 400
            
            try:
                scroll_type = WisdomType(scroll_type_str)
                category = WisdomCategory(category_str)
            except ValueError:
                return jsonify({'error': 'Invalid scroll type or category'}), 400
            
            # Create the wisdom scroll
            wisdom_engine = WisdomEngine()
            scroll_id = wisdom_engine.create_wisdom_scroll(
                title=title,
                content=content,
                scroll_type=scroll_type,
                wisdom_category=category,
                application_context=application_context,
                author_identity_id=identity_id,
                biblical_references=biblical_references,
                precedent_weight=precedent_weight
            )
            
            if scroll_id:
                logger.info(f"Created wisdom scroll {scroll_id} by {user['name']}")
                return jsonify({'success': True, 'scroll_id': scroll_id})
            else:
                return jsonify({'error': 'Failed to create wisdom scroll'}), 500
        
        # GET request - show creation form
        categories = [cat.value for cat in WisdomCategory]
        types = [typ.value for typ in WisdomType]
        
        return render_template('wisdom/create_scroll.html',
                             categories=categories,
                             types=types,
                             user=user)
        
    except Exception as e:
        logger.error(f"Error creating wisdom scroll: {e}")
        return jsonify({'error': str(e)}), 500

@wisdom_bp.route('/disputes')
def view_disputes():
    """View dispute cases."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("SELECT verification_level FROM identities WHERE identity_id = ?", 
                           (session['identity_id'],))
        
        if not user or user['verification_level'] < 2:
            return render_template('wisdom/access_denied.html', required_tier=2)
        
        # Get dispute cases
        disputes = db.query("""
            SELECT dc.*, 
                   p.name as plaintiff_name,
                   d.name as defendant_name,
                   e.name as elder_name
            FROM dispute_cases dc
            LEFT JOIN identities p ON dc.plaintiff_id = p.identity_id
            LEFT JOIN identities d ON dc.defendant_id = d.identity_id
            LEFT JOIN identities e ON dc.assigned_elder_id = e.identity_id
            ORDER BY dc.priority_level DESC, dc.submitted_at ASC
        """)
        
        return render_template('wisdom/disputes.html', disputes=disputes)
        
    except Exception as e:
        logger.error(f"Error viewing disputes: {e}")
        return render_template('error.html', error="Failed to load disputes"), 500

@wisdom_bp.route('/judgments')
def view_judgments():
    """View judgment records."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("SELECT verification_level FROM identities WHERE identity_id = ?", 
                           (session['identity_id'],))
        
        if not user or user['verification_level'] < 2:
            return render_template('wisdom/access_denied.html', required_tier=2)
        
        # Get judgment records
        judgments = db.query("""
            SELECT jr.*, 
                   p.name as plaintiff_name,
                   d.name as defendant_name,
                   e.name as elder_name
            FROM judgment_records jr
            LEFT JOIN identities p ON jr.plaintiff_id = p.identity_id
            LEFT JOIN identities d ON jr.defendant_id = d.identity_id
            LEFT JOIN identities e ON jr.presiding_elder_id = e.identity_id
            ORDER BY jr.created_at DESC
        """)
        
        return render_template('wisdom/judgments.html', judgments=judgments)
        
    except Exception as e:
        logger.error(f"Error viewing judgments: {e}")
        return render_template('error.html', error="Failed to load judgments"), 500

@wisdom_bp.route('/api/scroll/<scroll_id>')
def get_scroll_details(scroll_id):
    """Get detailed information about a wisdom scroll."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        
        user = db.query_one("SELECT verification_level FROM identities WHERE identity_id = ?", 
                           (session['identity_id'],))
        
        if not user or user['verification_level'] < 2:
            return jsonify({'error': 'Insufficient privileges'}), 403
        
        scroll = db.query_one("""
            SELECT ws.*, i.name as author_name
            FROM wisdom_scrolls ws
            LEFT JOIN identities i ON ws.author_identity_id = i.identity_id
            WHERE ws.scroll_id = ?
        """, (scroll_id,))
        
        if not scroll:
            return jsonify({'error': 'Wisdom scroll not found'}), 404
        
        # Get applications of this wisdom
        applications = db.query("""
            SELECT wa.*, i.name as elder_name
            FROM wisdom_applications wa
            LEFT JOIN identities i ON wa.applied_by_elder_id = i.identity_id
            WHERE wa.wisdom_scroll_id = ?
            ORDER BY wa.applied_at DESC
        """, (scroll_id,))
        
        scroll_dict = dict(scroll)
        scroll_dict['biblical_references'] = json.loads(scroll.get('biblical_references', '[]'))
        scroll_dict['metadata'] = json.loads(scroll.get('metadata', '{}'))
        scroll_dict['applications'] = [dict(app) for app in applications]
        
        return jsonify(scroll_dict)
        
    except Exception as e:
        logger.error(f"Error getting scroll details: {e}")
        return jsonify({'error': str(e)}), 500
