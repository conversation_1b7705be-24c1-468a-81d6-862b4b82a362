"""
Mikvah Token Manager
Manages biblical tokenomics rewards, gleaning pools, and Yovel (jubilee) cycles
"""

import time
import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger("onnyx.tokenomics.mikvah")

class MikvahTokenManager:
    """
    Manages Mikvah tokens - the biblical tokenomics reward system.
    """
    
    def __init__(self, db_connection):
        """Initialize the Mikvah token manager."""
        self.db = db_connection
        
        # Token constants
        self.GLEANING_PERCENTAGE = 0.1  # 10% of all rewards go to gleaning pool
        self.YOVEL_CYCLE_YEARS = 7      # 7-year jubilee cycle
        self.MAX_TOKENS_PER_YOVEL = 10000.0  # Maximum tokens per cycle
        self.SEASON_DAYS = 90           # 90-day seasons
        
        logger.info("Mikvah Token Manager initialized")

    def get_token_balance(self, identity_id: str) -> Dict[str, Any]:
        """
        Get comprehensive token balance information for an identity.
        
        Args:
            identity_id: Identity to check
            
        Returns:
            Dictionary with balance details
        """
        try:
            # Current total balance
            total_balance = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as balance
                FROM mikvah_transactions 
                WHERE identity_id = ?
            """, (identity_id,))
            
            # Current Yovel cycle balance
            current_yovel = self._get_current_yovel_cycle()
            yovel_balance = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as balance
                FROM mikvah_transactions 
                WHERE identity_id = ? AND yovel_cycle = ? AND amount > 0
            """, (identity_id, current_yovel))
            
            # Current season earnings
            current_season = self._get_current_season()
            season_earnings = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as earnings
                FROM mikvah_transactions 
                WHERE identity_id = ? AND season_period = ? AND amount > 0
            """, (identity_id, current_season))
            
            # Recent transactions
            recent_transactions = self.db.query("""
                SELECT * FROM mikvah_transactions 
                WHERE identity_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 10
            """, (identity_id,))
            
            # Gleaning contributions
            gleaning_contributed = self.db.query_one("""
                SELECT COALESCE(SUM(amount * 0.1), 0) as contributed
                FROM mikvah_transactions 
                WHERE identity_id = ? AND transaction_type = 'labor_reward'
            """, (identity_id,))
            
            return {
                'total_balance': float(total_balance['balance']) if total_balance else 0.0,
                'yovel_balance': float(yovel_balance['balance']) if yovel_balance else 0.0,
                'yovel_limit': self.MAX_TOKENS_PER_YOVEL,
                'yovel_remaining': self.MAX_TOKENS_PER_YOVEL - (float(yovel_balance['balance']) if yovel_balance else 0.0),
                'season_earnings': float(season_earnings['earnings']) if season_earnings else 0.0,
                'gleaning_contributed': float(gleaning_contributed['contributed']) if gleaning_contributed else 0.0,
                'recent_transactions': recent_transactions,
                'current_yovel_cycle': current_yovel,
                'current_season': current_season
            }
            
        except Exception as e:
            logger.error(f"Error getting token balance: {e}")
            return {}

    def process_token_reward(self, identity_id: str, amount: float, transaction_type: str, 
                           labor_id: str = None, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a token reward transaction.
        
        Args:
            identity_id: Recipient identity
            amount: Token amount
            transaction_type: Type of transaction
            labor_id: Optional labor record ID
            metadata: Optional transaction metadata
            
        Returns:
            Transaction result dictionary
        """
        try:
            if amount <= 0:
                return {'success': False, 'error': 'Invalid amount'}
            
            # Check Yovel eligibility for positive amounts
            current_yovel = self._get_current_yovel_cycle()
            if amount > 0 and not self._check_yovel_eligibility(identity_id, amount):
                return {'success': False, 'error': 'Yovel limit exceeded'}
            
            # Get current balance
            current_balance = self._get_current_balance(identity_id)
            new_balance = current_balance + amount
            
            # Create transaction
            transaction_id = f"MIKVAH_{int(time.time())}_{identity_id[:8]}"
            current_season = self._get_current_season()
            
            self.db.execute("""
                INSERT INTO mikvah_transactions 
                (transaction_id, identity_id, labor_id, transaction_type, amount, 
                 balance_before, balance_after, season_period, yovel_cycle, 
                 metadata, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (transaction_id, identity_id, labor_id, transaction_type, amount,
                  current_balance, new_balance, current_season, current_yovel,
                  json.dumps(metadata or {}), int(time.time())))
            
            # Contribute to gleaning pool for labor rewards
            if transaction_type == 'labor_reward' and amount > 0:
                gleaning_amount = amount * self.GLEANING_PERCENTAGE
                self._contribute_to_gleaning_pool(gleaning_amount, current_season)
            
            logger.info(f"Token reward processed: {amount} tokens for {identity_id}")
            
            return {
                'success': True,
                'transaction_id': transaction_id,
                'amount': amount,
                'new_balance': new_balance,
                'gleaning_contribution': amount * self.GLEANING_PERCENTAGE if transaction_type == 'labor_reward' else 0
            }
            
        except Exception as e:
            logger.error(f"Error processing token reward: {e}")
            return {'success': False, 'error': str(e)}

    def get_gleaning_pool_status(self, season_period: int = None) -> Dict[str, Any]:
        """
        Get status of gleaning pool for a season.
        
        Args:
            season_period: Season to check (current if None)
            
        Returns:
            Gleaning pool status dictionary
        """
        try:
            if season_period is None:
                season_period = self._get_current_season()
            
            pool = self.db.query_one("""
                SELECT * FROM gleaning_pool WHERE season_period = ?
            """, (season_period,))
            
            if not pool:
                return {
                    'season_period': season_period,
                    'total_contributions': 0.0,
                    'total_distributions': 0.0,
                    'current_balance': 0.0,
                    'contributor_count': 0,
                    'recipient_count': 0
                }
            
            # Get recent distributions
            recent_distributions = self.db.query("""
                SELECT gd.*, i.name as recipient_name
                FROM gleaning_distributions gd
                JOIN identities i ON gd.identity_id = i.identity_id
                WHERE gd.pool_id = ?
                ORDER BY gd.timestamp DESC
                LIMIT 10
            """, (pool['pool_id'],))
            
            return {
                'season_period': season_period,
                'pool_id': pool['pool_id'],
                'total_contributions': pool['total_contributions'],
                'total_distributions': pool['total_distributions'],
                'current_balance': pool['current_balance'],
                'contributor_count': pool['contributor_count'],
                'recipient_count': pool['recipient_count'],
                'recent_distributions': recent_distributions,
                'last_updated': pool['last_updated']
            }
            
        except Exception as e:
            logger.error(f"Error getting gleaning pool status: {e}")
            return {}

    def request_gleaning_support(self, identity_id: str, amount: float, reason: str) -> Dict[str, Any]:
        """
        Request support from the gleaning pool.
        
        Args:
            identity_id: Identity requesting support
            amount: Amount requested
            reason: Reason for request
            
        Returns:
            Request result dictionary
        """
        try:
            current_season = self._get_current_season()
            pool_status = self.get_gleaning_pool_status(current_season)
            
            if amount <= 0:
                return {'success': False, 'error': 'Invalid amount'}
            
            if amount > pool_status.get('current_balance', 0):
                return {'success': False, 'error': 'Insufficient pool balance'}
            
            # Check if user has already received gleaning this season
            existing_distribution = self.db.query_one("""
                SELECT gd.* FROM gleaning_distributions gd
                JOIN gleaning_pool gp ON gd.pool_id = gp.pool_id
                WHERE gd.identity_id = ? AND gp.season_period = ?
            """, (identity_id, current_season))
            
            if existing_distribution:
                return {'success': False, 'error': 'Already received gleaning support this season'}
            
            # Auto-approve small amounts, require approval for larger amounts
            auto_approve_limit = 50.0
            approved = amount <= auto_approve_limit
            
            # Create distribution record
            distribution_id = f"GLEANING_{int(time.time())}_{identity_id[:8]}"
            pool_id = pool_status.get('pool_id')
            
            if not pool_id:
                return {'success': False, 'error': 'No active gleaning pool'}
            
            self.db.execute("""
                INSERT INTO gleaning_distributions 
                (distribution_id, identity_id, pool_id, amount, reason, 
                 approved_by, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (distribution_id, identity_id, pool_id, amount, reason,
                  'auto_approved' if approved else None, int(time.time())))
            
            if approved:
                # Process the distribution
                self._process_gleaning_distribution(distribution_id)
                
                return {
                    'success': True,
                    'distribution_id': distribution_id,
                    'amount': amount,
                    'status': 'approved_and_processed'
                }
            else:
                return {
                    'success': True,
                    'distribution_id': distribution_id,
                    'amount': amount,
                    'status': 'pending_approval'
                }
            
        except Exception as e:
            logger.error(f"Error requesting gleaning support: {e}")
            return {'success': False, 'error': str(e)}

    def _process_gleaning_distribution(self, distribution_id: str) -> bool:
        """Process an approved gleaning distribution."""
        try:
            # Get distribution details
            distribution = self.db.query_one("""
                SELECT * FROM gleaning_distributions WHERE distribution_id = ?
            """, (distribution_id,))
            
            if not distribution:
                return False
            
            # Create token transaction
            result = self.process_token_reward(
                identity_id=distribution['identity_id'],
                amount=distribution['amount'],
                transaction_type='gleaning_distribution',
                metadata={'distribution_id': distribution_id, 'reason': distribution['reason']}
            )
            
            if result['success']:
                # Update gleaning pool
                self.db.execute("""
                    UPDATE gleaning_pool SET 
                        total_distributions = total_distributions + ?,
                        current_balance = current_balance - ?,
                        recipient_count = recipient_count + 1,
                        last_updated = ?
                    WHERE pool_id = ?
                """, (distribution['amount'], distribution['amount'], 
                      int(time.time()), distribution['pool_id']))
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error processing gleaning distribution: {e}")
            return False

    def get_yovel_cycle_summary(self, yovel_cycle: int = None) -> Dict[str, Any]:
        """
        Get summary of a Yovel (jubilee) cycle.
        
        Args:
            yovel_cycle: Cycle to summarize (current if None)
            
        Returns:
            Yovel cycle summary dictionary
        """
        try:
            if yovel_cycle is None:
                yovel_cycle = self._get_current_yovel_cycle()
            
            # Total tokens distributed in cycle
            total_distributed = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM mikvah_transactions 
                WHERE yovel_cycle = ? AND amount > 0
            """, (yovel_cycle,))
            
            # Number of participants
            participants = self.db.query_one("""
                SELECT COUNT(DISTINCT identity_id) as count
                FROM mikvah_transactions 
                WHERE yovel_cycle = ? AND amount > 0
            """, (yovel_cycle,))
            
            # Top earners
            top_earners = self.db.query("""
                SELECT mt.identity_id, i.name, SUM(mt.amount) as total_earned
                FROM mikvah_transactions mt
                JOIN identities i ON mt.identity_id = i.identity_id
                WHERE mt.yovel_cycle = ? AND mt.amount > 0
                GROUP BY mt.identity_id, i.name
                ORDER BY total_earned DESC
                LIMIT 10
            """, (yovel_cycle,))
            
            # Transaction types breakdown
            transaction_types = self.db.query("""
                SELECT transaction_type, COUNT(*) as count, SUM(amount) as total
                FROM mikvah_transactions 
                WHERE yovel_cycle = ? AND amount > 0
                GROUP BY transaction_type
            """, (yovel_cycle,))
            
            return {
                'yovel_cycle': yovel_cycle,
                'total_distributed': float(total_distributed['total']) if total_distributed else 0.0,
                'participant_count': participants['count'] if participants else 0,
                'average_per_participant': (float(total_distributed['total']) / max(1, participants['count'])) if total_distributed and participants else 0.0,
                'top_earners': top_earners,
                'transaction_types': transaction_types,
                'cycle_start_year': 1970 + (yovel_cycle * self.YOVEL_CYCLE_YEARS),
                'cycle_end_year': 1970 + ((yovel_cycle + 1) * self.YOVEL_CYCLE_YEARS)
            }
            
        except Exception as e:
            logger.error(f"Error getting Yovel cycle summary: {e}")
            return {}

    def _get_current_balance(self, identity_id: str) -> float:
        """Get current token balance for an identity."""
        try:
            result = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as balance
                FROM mikvah_transactions 
                WHERE identity_id = ?
            """, (identity_id,))
            
            return float(result['balance']) if result else 0.0
            
        except Exception as e:
            logger.error(f"Error getting current balance: {e}")
            return 0.0

    def _check_yovel_eligibility(self, identity_id: str, additional_amount: float) -> bool:
        """Check if identity can receive additional tokens within Yovel limit."""
        try:
            current_yovel = self._get_current_yovel_cycle()
            
            current_yovel_tokens = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM mikvah_transactions 
                WHERE identity_id = ? AND yovel_cycle = ? AND amount > 0
            """, (identity_id, current_yovel))
            
            total = float(current_yovel_tokens['total']) if current_yovel_tokens else 0.0
            
            return (total + additional_amount) <= self.MAX_TOKENS_PER_YOVEL
            
        except Exception as e:
            logger.error(f"Error checking Yovel eligibility: {e}")
            return False

    def _contribute_to_gleaning_pool(self, amount: float, season_period: int) -> bool:
        """Contribute to the gleaning pool for a season."""
        try:
            # Get or create pool for season
            pool = self.db.query_one("""
                SELECT * FROM gleaning_pool WHERE season_period = ?
            """, (season_period,))
            
            if pool:
                # Update existing pool
                self.db.execute("""
                    UPDATE gleaning_pool SET 
                        total_contributions = total_contributions + ?,
                        current_balance = current_balance + ?,
                        contributor_count = contributor_count + 1,
                        last_updated = ?
                    WHERE pool_id = ?
                """, (amount, amount, int(time.time()), pool['pool_id']))
            else:
                # Create new pool
                pool_id = f"POOL_{season_period}_{int(time.time())}"
                self.db.execute("""
                    INSERT INTO gleaning_pool 
                    (pool_id, season_period, total_contributions, current_balance, 
                     contributor_count, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (pool_id, season_period, amount, amount, 1, int(time.time())))
            
            return True
            
        except Exception as e:
            logger.error(f"Error contributing to gleaning pool: {e}")
            return False

    def _get_current_yovel_cycle(self) -> int:
        """Calculate current Yovel cycle."""
        epoch_start = datetime(1970, 1, 1)
        now = datetime.now()
        years_since_epoch = (now - epoch_start).days / 365.25
        return int(years_since_epoch // self.YOVEL_CYCLE_YEARS)

    def _get_current_season(self) -> int:
        """Calculate current season period."""
        epoch_start = datetime(1970, 1, 1)
        now = datetime.now()
        days_since_epoch = (now - epoch_start).days
        return int(days_since_epoch // self.SEASON_DAYS)
