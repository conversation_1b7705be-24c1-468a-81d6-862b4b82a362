#!/usr/bin/env python3
"""
Simple Flask test to debug the Eden Mode error
"""
import sys
import os
import json
from flask import Flask, request, jsonify

# Add the backend directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'onnyx-backend'))

from shared.db.db import db
import hashlib
import secrets
import time

app = Flask(__name__)

@app.route('/test-create-identity', methods=['POST'])
def test_create_identity():
    """Test the create identity function"""
    try:
        # Sample data that Eden Mode would send
        data = {
            'fullName': 'Test User',
            'email': '<EMAIL>',
            'selectedNation': 'USA',
            'selectedNationName': 'United States',
            'selectedTribe': 'Judah',
            'laborCategory': 'priest',
            'selaChoice': 'community',
            'covenantPath': 'israelite',
            'developerBypass': False
        }
        
        print(f"Received data: {data}")
        
        # Generate cryptographic identity
        private_key = secrets.token_hex(32)
        public_key = hashlib.sha256(private_key.encode()).hexdigest()
        wallet_address = f"ONX{hashlib.sha256(public_key.encode()).hexdigest()[:34]}"
        
        # Generate unique identity ID
        identity_data = f"{data['fullName']}_{data['email']}_{int(time.time())}"
        identity_id = f"ONX{hashlib.sha256(identity_data.encode()).hexdigest()[:16]}"
        
        print(f"Generated identity_id: {identity_id}")
        print(f"Generated public_key: {public_key}")
        print(f"Generated wallet_address: {wallet_address}")
        
        # Create identity record
        identity_record = {
            'identity_id': identity_id,
            'name': data['fullName'],
            'email': data['email'],
            'public_key': public_key,
            'nation_id': data['selectedNation'],
            'nation_code': data['selectedNation'],
            'nation_name': data.get('selectedNationName', data.get('selectedNation', 'Unknown')),
            'metadata': json.dumps({
                'tribe_name': data.get('selectedTribe'),
                'labor_category': data['laborCategory'],
                'wallet_address': wallet_address,
                'eden_mode_completed': True,
                'registration_source': 'eden_mode',
                'covenant_path': data.get('covenantPath', 'witness')
            }),
            'status': 'active',
            'created_at': int(time.time()),
            'updated_at': int(time.time()),
            'covenant_accepted': True,
            'verification_level': 1,
            'role_class': 'israelite',
            'tribal_affiliation': data.get('selectedTribe', '').lower(),
            'vault_status': 'active',
            'etzem_score': 100,
            'protection_tier': 'Basic',
            'zeman_count': 0,
            'cipp_tier': 1,
            'full_name': data['fullName'],
            'tribe_name': data.get('selectedTribe'),
            'labor_category': data['laborCategory'],
            'eden_mode_completed': True,
            'sabbath_observer': True,
            'deeds_score': 50.0,
            'timezone': 'UTC'
        }
        
        print(f"About to insert record: {identity_record}")
        
        # Insert into database
        db.insert('identities', identity_record)
        
        print("✅ Identity created successfully")
        
        return jsonify({
            'success': True,
            'identity_id': identity_id,
            'wallet_address': wallet_address,
            'message': 'Identity created successfully'
        })
        
    except Exception as e:
        print(f"❌ Error creating identity: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/test', methods=['GET'])
def test():
    return jsonify({'message': 'Test endpoint working'})

if __name__ == '__main__':
    app.run(debug=True, port=5001)
