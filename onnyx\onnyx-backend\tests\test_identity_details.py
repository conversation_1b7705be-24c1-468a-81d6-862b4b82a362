#!/usr/bin/env python3
"""
Test Identity Details Loading
"""

import requests
import sys

def test_identity_details():
    """Test identity details loading"""
    print("Testing identity details loading...")
    
    # First, get a list of identities to test with
    try:
        import sys
        sys.path.append('.')
        from shared.db.db import db
        
        # Get an identity ID to test with
        identities = db.query("SELECT identity_id FROM identities LIMIT 1")
        if not identities:
            print("❌ No identities found in database")
            return False
            
        test_identity_id = identities[0]['identity_id']
        print(f"Testing with identity ID: {test_identity_id[:16]}...")
        
    except Exception as e:
        print(f"❌ Error getting test identity: {e}")
        return False
    
    # Test various identity detail endpoints
    endpoints_to_test = [
        f"/api/identity/{test_identity_id}",
        f"/api/identity/{test_identity_id}/details",
        f"/dashboard/profile",
        f"/identity/{test_identity_id}"
    ]
    
    success_count = 0
    
    for endpoint in endpoints_to_test:
        try:
            print(f"Testing {endpoint}...")
            response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {endpoint}: Success")
                success_count += 1
            elif response.status_code == 302:
                print(f"🔄 {endpoint}: Redirected (auth required)")
                success_count += 1
            elif response.status_code == 403:
                print(f"🔒 {endpoint}: Forbidden (auth required)")
                success_count += 1
            elif response.status_code == 404:
                print(f"❓ {endpoint}: Not found")
            elif response.status_code == 500:
                print(f"❌ {endpoint}: Server error")
                # Try to get error details
                content = response.text.lower()
                if 'no such table' in content:
                    print(f"   - Missing table error")
                elif 'no such column' in content:
                    print(f"   - Missing column error")
            else:
                print(f"❓ {endpoint}: Status {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint}: Connection error")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    return success_count > 0

def test_identity_queries():
    """Test database queries for identity details"""
    print("\nTesting identity detail database queries...")
    
    try:
        import sys
        sys.path.append('.')
        from shared.db.db import db
        
        # Get a test identity
        identities = db.query("SELECT identity_id FROM identities LIMIT 1")
        if not identities:
            print("❌ No identities found")
            return False
            
        test_id = identities[0]['identity_id']
        
        # Test queries commonly used for identity details
        queries_to_test = [
            ("Basic identity", "SELECT * FROM identities WHERE identity_id = ?", (test_id,)),
            ("Identity with role", "SELECT identity_id, name, email, role, role_class FROM identities WHERE identity_id = ?", (test_id,)),
            ("User selas", "SELECT * FROM selas WHERE identity_id = ?", (test_id,)),
            ("Token balances", "SELECT * FROM token_balances WHERE identity_id = ?", (test_id,)),
            ("Recent deeds", "SELECT * FROM deeds_ledger WHERE identity_id = ? ORDER BY timestamp DESC LIMIT 5", (test_id,)),
            ("Etzem score", "SELECT * FROM etzem_scores WHERE identity_id = ?", (test_id,))
        ]
        
        success_count = 0
        for name, query, params in queries_to_test:
            try:
                result = db.query(query, params)
                print(f"✅ {name}: Success ({len(result)} results)")
                success_count += 1
            except Exception as e:
                print(f"❌ {name}: {e}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Error testing queries: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Identity Details Functionality")
    print("=" * 50)
    
    db_success = test_identity_queries()
    endpoint_success = test_identity_details()
    
    print("\n📊 Results:")
    if db_success:
        print("✅ Database queries working")
    else:
        print("❌ Database queries have issues")
        
    if endpoint_success:
        print("✅ Identity endpoints accessible")
    else:
        print("❌ Identity endpoints have issues")
    
    overall_success = db_success and endpoint_success
    print(f"\n{'✅ SUCCESS' if overall_success else '❌ ISSUES FOUND'}: Identity details functionality")
    
    sys.exit(0 if overall_success else 1)
