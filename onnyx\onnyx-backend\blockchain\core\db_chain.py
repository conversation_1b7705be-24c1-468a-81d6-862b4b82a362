"""
Onnyx Database-Backed Blockchain Module

This module provides a SQLite-backed blockchain implementation.
"""

import os
import json
import time
import hashlib
import logging
from typing import Dict, Any, List, Optional

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.chain.db_chain")

class DBBlockchain:
    """
    DBBlockchain manages the blockchain data stored in a SQLite database.
    """
    
    def __init__(self):
        """Initialize the DBBlockchain."""
        # Check if the blocks table exists
        if not db.table_exists("blocks"):
            logger.warning("Blocks table does not exist. Creating genesis block.")
            self._create_genesis_block()
        elif self.get_chain_length() == 0:
            logger.warning("Blockchain is empty. Creating genesis block.")
            self._create_genesis_block()
    
    def _create_genesis_block(self) -> Dict[str, Any]:
        """
        Create the genesis block.
        
        Returns:
            The genesis block
        """
        # Create the genesis block
        genesis_block = {
            "index": 0,
            "timestamp": int(time.time()),
            "proposer": "genesis",
            "previous_hash": "0",
            "hash": "0",
            "nonce": 0,
            "signature": "",
            "signed_by": "genesis"
        }
        
        # Calculate the hash of the genesis block
        genesis_block["hash"] = self._calculate_hash(genesis_block)
        
        # Insert the genesis block into the database
        db.insert("blocks", genesis_block)
        
        logger.info("Created genesis block")
        
        return genesis_block
    
    def _calculate_hash(self, block: Dict[str, Any]) -> str:
        """
        Calculate the hash of a block.
        
        Args:
            block: The block to hash
        
        Returns:
            The hash of the block
        """
        # Create a copy of the block without the hash
        block_copy = block.copy()
        if "hash" in block_copy:
            del block_copy["hash"]
        
        # Convert the block to a string and hash it
        block_string = json.dumps(block_copy, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()
    
    def get_latest(self) -> Dict[str, Any]:
        """
        Get the latest block in the chain.
        
        Returns:
            The latest block
        """
        # Query the database for the latest block
        latest_block = db.query_one("SELECT * FROM blocks ORDER BY index DESC LIMIT 1")
        
        if not latest_block:
            # If there are no blocks, create the genesis block
            return self._create_genesis_block()
        
        return latest_block
    
    def get_block(self, index: int) -> Optional[Dict[str, Any]]:
        """
        Get a block by index.
        
        Args:
            index: The index of the block
        
        Returns:
            The block or None if not found
        """
        # Query the database for the block
        return db.query_one("SELECT * FROM blocks WHERE index = ?", (index,))
    
    def get_block_by_hash(self, hash: str) -> Optional[Dict[str, Any]]:
        """
        Get a block by hash.
        
        Args:
            hash: The hash of the block
        
        Returns:
            The block or None if not found
        """
        # Query the database for the block
        return db.query_one("SELECT * FROM blocks WHERE hash = ?", (hash,))
    
    def add_block(self, block: Dict[str, Any]) -> bool:
        """
        Add a block to the chain.
        
        Args:
            block: The block to add
        
        Returns:
            True if the block was added, False otherwise
        """
        # Verify that the block's previous_hash matches the latest block's hash
        latest_block = self.get_latest()
        if block["previous_hash"] != latest_block["hash"]:
            logger.warning(f"Block {block['index']} has invalid previous_hash")
            return False
        
        # Verify that the block's index is one more than the latest block's index
        if block["index"] != latest_block["index"] + 1:
            logger.warning(f"Block {block['index']} has invalid index")
            return False
        
        # Verify that the block's hash is valid
        if block["hash"] != self._calculate_hash(block):
            logger.warning(f"Block {block['index']} has invalid hash")
            return False
        
        # Insert the block into the database
        try:
            db.insert("blocks", block)
            logger.info(f"Added block {block['index']} to chain")
            return True
        except Exception as e:
            logger.error(f"Error adding block {block['index']} to chain: {str(e)}")
            return False
    
    def get_chain_length(self) -> int:
        """
        Get the length of the chain.
        
        Returns:
            The length of the chain
        """
        # Query the database for the number of blocks
        result = db.query_one("SELECT COUNT(*) as count FROM blocks")
        return result["count"] if result else 0
    
    def get_blocks(self, start: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get blocks from the chain.
        
        Args:
            start: The starting index
            limit: The maximum number of blocks to return
        
        Returns:
            A list of blocks
        """
        # Query the database for the blocks
        return db.query(
            "SELECT * FROM blocks WHERE index >= ? ORDER BY index ASC LIMIT ?",
            (start, limit)
        )
    
    def get_transactions(self, block_index: int) -> List[Dict[str, Any]]:
        """
        Get transactions for a block.
        
        Args:
            block_index: The index of the block
        
        Returns:
            A list of transactions
        """
        # Query the database for the transactions
        return db.query(
            "SELECT * FROM transactions WHERE block_index = ? ORDER BY txid ASC",
            (block_index,)
        )
    
    def get_transaction(self, txid: str) -> Optional[Dict[str, Any]]:
        """
        Get a transaction by ID.
        
        Args:
            txid: The transaction ID
        
        Returns:
            The transaction or None if not found
        """
        # Query the database for the transaction
        return db.query_one("SELECT * FROM transactions WHERE txid = ?", (txid,))
    
    def is_valid_chain(self) -> bool:
        """
        Validate the entire chain.
        
        Returns:
            True if the chain is valid, False otherwise
        """
        # Get all blocks
        blocks = self.get_blocks(0, self.get_chain_length())
        
        # Check each block
        for i in range(1, len(blocks)):
            current_block = blocks[i]
            previous_block = blocks[i - 1]
            
            # Check if the previous hash matches
            if current_block["previous_hash"] != previous_block["hash"]:
                logger.warning(f"Block {current_block['index']} has invalid previous_hash")
                return False
            
            # Check if the hash is correct
            if current_block["hash"] != self._calculate_hash(current_block):
                logger.warning(f"Block {current_block['index']} has invalid hash")
                return False
        
        return True
    
    def write_block_to_db(self, block: Dict[str, Any], transactions: List[Dict[str, Any]]) -> bool:
        """
        Write a block and its transactions to the database.
        
        Args:
            block: The block to write
            transactions: The transactions to write
        
        Returns:
            True if the block was written, False otherwise
        """
        try:
            # Start a transaction
            conn = db.get_connection()
            
            # Insert the block
            cursor = conn.execute(
                """
                INSERT INTO blocks (
                    index, timestamp, proposer, hash, previous_hash, nonce, signature, signed_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    block["index"],
                    block["timestamp"],
                    block.get("proposer", block.get("signed_by", "")),
                    block["hash"],
                    block["previous_hash"],
                    block.get("nonce", 0),
                    block.get("signature", ""),
                    block.get("signed_by", "")
                )
            )
            
            # Insert the transactions
            for tx in transactions:
                conn.execute(
                    """
                    INSERT INTO transactions (
                        txid, block_index, timestamp, type, op, sender, recipient, token_id, amount, data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        tx.get("txid", f"tx_{int(time.time())}_{block['index']}"),
                        block["index"],
                        tx.get("timestamp", block["timestamp"]),
                        tx.get("type", ""),
                        tx.get("op", ""),
                        tx.get("from", None),
                        tx.get("to", None),
                        tx.get("token_id", None),
                        tx.get("amount", None),
                        json.dumps(tx.get("data", {}))
                    )
                )
            
            # Commit the transaction
            conn.commit()
            
            # Close the connection
            conn.close()
            
            logger.info(f"Wrote block {block['index']} with {len(transactions)} transactions to database")
            return True
        except Exception as e:
            logger.error(f"Error writing block {block['index']} to database: {str(e)}")
            return False

# Create a global instance of the DBBlockchain
db_blockchain = DBBlockchain()
