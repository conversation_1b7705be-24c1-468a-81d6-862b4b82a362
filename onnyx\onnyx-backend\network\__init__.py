"""
ONNYX P2P Network Module
Multi-node blockchain network with biblical covenant governance
"""

__version__ = "1.0.0"
__author__ = "ONNYX Covenant Development Team"

from .p2p.peer_manager import PeerManager
from .consensus.proof_of_covenant import ProofOfCovenant
from .discovery.bootstrap import Bootstrap<PERSON><PERSON>
from .sync.blockchain_sync import BlockchainSync

__all__ = [
    'PeerManager',
    'ProofOfCovenant', 
    'BootstrapNode',
    'BlockchainSync'
]
