#!/usr/bin/env python3
"""
ONNYX Database User Creation Test
Creates test users directly through the database layer
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
import hashlib

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """Test database connectivity"""
    try:
        db_path = "shared/db/db/onnyx.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        print("✅ Database connection successful")
        print(f"📊 Found {len(tables)} tables: {', '.join(tables)}")
        
        # Check identities table structure
        if 'identities' in tables:
            cursor.execute("PRAGMA table_info(identities)")
            columns = [row[1] for row in cursor.fetchall()]
            print(f"👤 Identities table columns: {', '.join(columns)}")
        else:
            print("❌ Identities table not found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def create_test_israelite_user():
    """Create a test Israelite user directly in database"""
    try:
        db_path = "shared/db/db/onnyx.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
          # Test data for Israelite user
        user_data = {
            'name': 'Test Israelite User',
            'email': '<EMAIL>',
            'covenant_accepted': True,
            'tribal_affiliation': 'Judah',
            'verification_level': 'developer-bypassed',
            'created_at': datetime.now().isoformat(),
            'status': 'active',
            'eden_mode_completed': True,
            'tribe_name': 'Judah',
            'metadata': json.dumps({
                'covenant_path': 'israelite',
                'selected_tribe': 'Judah',
                'developer_bypass': True,
                'tribal_data': {
                    'tribe': 'Judah',
                    'symbol': '🦁',
                    'stone': 'Sardius',
                    'blessing': 'Leadership and kingship'
                }
            })
        }
        
        # Insert user into identities table
        cursor.execute("""
            INSERT INTO identities (
                name, email, covenant_accepted, tribal_affiliation, 
                verification_level, created_at, status, eden_mode_completed,
                tribe_name, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            user_data['name'],
            user_data['email'],
            user_data['covenant_accepted'],
            user_data['tribal_affiliation'],
            user_data['verification_level'],
            user_data['created_at'],
            user_data['status'],
            user_data['eden_mode_completed'],
            user_data['tribe_name'],
            user_data['metadata']
        ))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
          print("✅ Test Israelite user created successfully")
        print(f"   User ID: {user_id}")
        print(f"   Name: {user_data['name']}")
        print(f"   Email: {user_data['email']}")
        print(f"   Tribe: {user_data['tribal_affiliation']}")
        print(f"   Status: {user_data['verification_level']}")
        
        return user_id
        
    except Exception as e:
        print(f"❌ Failed to create Israelite user: {e}")
        return None

def create_test_witness_user():
    """Create a test Witness Nation user directly in database"""
    try:
        db_path = "shared/db/db/onnyx.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
          # Test data for Witness Nation user
        user_data = {
            'name': 'Test Witness User',
            'email': '<EMAIL>',
            'covenant_accepted': True,
            'nation_code': 'US',
            'nation_name': 'United States',
            'verification_level': 'verified',
            'created_at': datetime.now().isoformat(),
            'status': 'active',
            'eden_mode_completed': True,
            'metadata': json.dumps({
                'covenant_path': 'witness',
                'selected_nation': 'United States',
                'ancestral_group': 'Germanic Peoples',
                'developer_bypass': False,
                'nation_data': {
                    'nation': 'United States',
                    'group': 'Germanic Peoples',
                    'symbol': '🇺🇸',
                    'heritage': 'European settlers'
                }
            })
        }
        
        # Insert user into identities table
        cursor.execute("""
            INSERT INTO identities (
                name, email, covenant_accepted, nation_code, nation_name,
                verification_level, created_at, status, eden_mode_completed,
                metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            user_data['name'],
            user_data['email'],
            user_data['covenant_accepted'],
            user_data['nation_code'],
            user_data['nation_name'],
            user_data['verification_level'],
            user_data['created_at'],
            user_data['status'],
            user_data['eden_mode_completed'],
            user_data['metadata']
        ))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
          print("✅ Test Witness Nation user created successfully")
        print(f"   User ID: {user_id}")
        print(f"   Name: {user_data['name']}")
        print(f"   Email: {user_data['email']}")
        print(f"   Nation: {user_data['nation_name']}")
        print(f"   Status: {user_data['verification_level']}")
        
        return user_id
        
    except Exception as e:
        print(f"❌ Failed to create Witness Nation user: {e}")
        return None

def verify_users_created():
    """Verify that the test users were created successfully"""
    try:
        db_path = "shared/db/db/onnyx.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
          # Get all identities
        cursor.execute("SELECT identity_id, name, email, tribal_affiliation, nation_name, verification_level FROM identities ORDER BY created_at DESC LIMIT 10")
        users = cursor.fetchall()
        
        print(f"\n📊 Recent users in database ({len(users)} found):")
        for user in users:
            print(f"   - ID: {user[0]}, Name: {user[1]}, Email: {user[2]}, Tribe/Nation: {user[3] or user[4]}, Status: {user[5]}")
        
        # Check for our test users specifically
        cursor.execute("SELECT COUNT(*) FROM identities WHERE email LIKE '<EMAIL>'")
        test_count = cursor.fetchone()[0]
        
        print(f"\n✅ Found {test_count} test users")
        
        conn.close()
        return test_count > 0
        
    except Exception as e:
        print(f"❌ Failed to verify users: {e}")
        return False

def cleanup_test_users():
    """Clean up test users from database"""
    try:
        db_path = "shared/db/db/onnyx.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Delete test users
        cursor.execute("DELETE FROM identities WHERE email LIKE '<EMAIL>'")
        deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"🧹 Cleaned up {deleted_count} test users")
        return True
        
    except Exception as e:
        print(f"❌ Failed to cleanup test users: {e}")
        return False

def run_user_creation_tests():
    """Run all user creation tests"""
    print("🔍 ONNYX User Creation Tests")
    print("=" * 50)
    
    # Test database connection
    if not test_database_connection():
        print("❌ Cannot proceed without database connection")
        return False
    
    print("\n🔹 Creating Test Users")
    
    # Create Israelite user
    israelite_id = create_test_israelite_user()
    
    # Create Witness Nation user  
    witness_id = create_test_witness_user()
    
    # Verify users were created
    if israelite_id and witness_id:
        print("\n🔹 Verifying User Creation")
        verify_users_created()
        
        print("\n✅ Both test users created successfully!")
        print("   - Israelite user (with developer bypass)")
        print("   - Witness Nation user (standard verification)")
        
        # Ask if user wants to cleanup
        cleanup = input("\n🧹 Clean up test users? (y/n): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_users()
        else:
            print("⚠️ Test users left in database for manual inspection")
        
        return True
    else:
        print("\n❌ Failed to create one or both test users")
        return False

if __name__ == "__main__":
    print("ONNYX Database User Creation Test")
    print("This tests user creation directly through the database layer")
    print("=" * 70)
    
    success = run_user_creation_tests()
    
    if success:
        print("\n🎉 User creation testing completed successfully!")
        print("\n📋 What was tested:")
        print("   ✅ Database connectivity")
        print("   ✅ Israelite user creation (with bypass)")
        print("   ✅ Witness Nation user creation")
        print("   ✅ Data persistence verification")
        
        print("\n🔄 Manual Web Testing Recommended:")
        print("   1. Visit http://localhost:5000/auth/login")
        print("   2. Click 'Eden Mode - Reclaim Your Legacy'")
        print("   3. Test both Israelite and Witness flows")
        print("   4. Verify developer bypass works")
        print("   5. Complete registration forms")
    else:
        print("\n❌ User creation testing failed")
        print("Check database configuration and table structure")
    
    sys.exit(0 if success else 1)
