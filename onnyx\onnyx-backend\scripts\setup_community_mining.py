#!/usr/bin/env python3
"""
Setup Community Mining Tables

Creates the necessary database tables for community mining functionality.
"""

import os
import sys
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

logger = logging.getLogger("onnyx.setup.community_mining")

def create_community_mining_tables():
    """Create tables for community mining system."""
    try:
        print("🏗️ Creating community mining tables...")
        
        # Community Mining Sessions Table
        db.execute("""
            CREATE TABLE IF NOT EXISTS community_mining_sessions (
                session_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'active',
                attempts INTEGER DEFAULT 0,
                created_at INTEGER NOT NULL,
                last_activity INTEGER NOT NULL,
                ended_at INTEGER,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Community Mining Rewards Table
        db.execute("""
            CREATE TABLE IF NOT EXISTS community_mining_rewards (
                reward_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                session_id TEXT NOT NULL,
                reward_amount REAL NOT NULL,
                created_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (session_id) REFERENCES community_mining_sessions(session_id)
            )
        """)
        
        # Create indexes for better performance
        db.execute("""
            CREATE INDEX IF NOT EXISTS idx_community_mining_sessions_identity 
            ON community_mining_sessions(identity_id)
        """)
        
        db.execute("""
            CREATE INDEX IF NOT EXISTS idx_community_mining_sessions_status 
            ON community_mining_sessions(status)
        """)
        
        db.execute("""
            CREATE INDEX IF NOT EXISTS idx_community_mining_rewards_identity 
            ON community_mining_rewards(identity_id)
        """)
        
        db.execute("""
            CREATE INDEX IF NOT EXISTS idx_community_mining_rewards_session 
            ON community_mining_rewards(session_id)
        """)
        
        print("✅ Community mining tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating community mining tables: {e}")
        return False

def verify_tables():
    """Verify that all tables were created correctly."""
    try:
        print("🔍 Verifying community mining tables...")
        
        # Check if tables exist
        tables = [
            'community_mining_sessions',
            'community_mining_rewards'
        ]
        
        for table in tables:
            result = db.query_one("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table,))
            
            if result:
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                return False
        
        # Check table schemas
        print("\n📋 Table schemas:")
        
        for table in tables:
            schema = db.query(f"PRAGMA table_info({table})")
            print(f"\n{table}:")
            for column in schema:
                print(f"  - {column['name']}: {column['type']}")
        
        print("\n✅ All community mining tables verified!")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying tables: {e}")
        return False

def add_sample_data():
    """Add sample community mining data for testing."""
    try:
        print("📊 Adding sample community mining data...")
        
        # Get a sample citizen identity
        citizen = db.query_one("""
            SELECT identity_id FROM identities 
            WHERE role = 'citizen' 
            LIMIT 1
        """)
        
        if not citizen:
            print("⚠️ No citizen identities found for sample data")
            return True
        
        identity_id = citizen['identity_id']
        current_time = int(time.time())
        
        # Create a sample mining session
        session_id = f"cms_sample_{current_time}"
        db.execute("""
            INSERT INTO community_mining_sessions 
            (session_id, identity_id, status, attempts, created_at, last_activity)
            VALUES (?, ?, 'completed', 5, ?, ?)
        """, (session_id, identity_id, current_time - 3600, current_time - 1800))
        
        # Add sample rewards
        reward_id = f"cmr_sample_{current_time}"
        db.execute("""
            INSERT INTO community_mining_rewards
            (reward_id, identity_id, session_id, reward_amount, created_at)
            VALUES (?, ?, ?, 0.5, ?)
        """, (reward_id, identity_id, session_id, current_time - 1800))
        
        print(f"✅ Sample data added for identity: {identity_id}")
        return True
        
    except Exception as e:
        print(f"❌ Error adding sample data: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Community Mining System")
    print("=" * 50)
    
    try:
        # Create tables
        if not create_community_mining_tables():
            return False
        
        # Verify tables
        if not verify_tables():
            return False
        
        # Add sample data
        import time
        if not add_sample_data():
            print("⚠️ Failed to add sample data, but tables are ready")
        
        print("\n🎉 Community Mining setup completed successfully!")
        print("\nNext steps:")
        print("1. Register the community_mining blueprint in app.py")
        print("2. Create the community mining dashboard template")
        print("3. Test the mining functionality")
        
        return True
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
