{% extends "base.html" %}

{% block title %}Manual Onboarding Dashboard - ONNYX{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
    <!-- Header -->
    <section class="pt-24 pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-6xl font-orbitron font-bold text-white mb-6">
                    🛡️ Manual Onboarding
                </h1>
                <p class="text-xl text-text-secondary max-w-3xl mx-auto">
                    Streamlined process for adding citizens, validators, and tribal elders to the ONNYX covenant blockchain
                </p>
            </div>
        </div>
    </section>

    <!-- Platform Statistics -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="glass-card-premium text-center p-8 cursor-pointer hover:scale-105 transition-all duration-300" onclick="showIdentitiesList()">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyan-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">👤</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">{{ stats.identities }}</div>
                    <div class="text-text-secondary">Total Identities</div>
                    <div class="text-xs text-cyber-cyan mt-2">Click to view list</div>
                </div>
                <div class="glass-card-premium text-center p-8 cursor-pointer hover:scale-105 transition-all duration-300" onclick="showValidatorsList()">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-purple-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🏢</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-purple mb-2">{{ stats.selas }}</div>
                    <div class="text-text-secondary">Active Validators</div>
                    <div class="text-xs text-cyber-purple mt-2">Click to view list</div>
                </div>
                <div class="glass-card-premium text-center p-8 cursor-pointer hover:scale-105 transition-all duration-300" onclick="showEldersList()">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">👑</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-green mb-2">{{ stats.tribal_elders }}</div>
                    <div class="text-text-secondary">Tribal Elders</div>
                    <div class="text-xs text-cyber-green mt-2">Click to view list</div>
                </div>
                <div class="glass-card-premium text-center p-8 cursor-pointer hover:scale-105 transition-all duration-300" onclick="showCitizensList()">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-blue to-blue-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🌟</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-blue mb-2">{{ stats.citizens }}</div>
                    <div class="text-text-secondary">Citizens</div>
                    <div class="text-xs text-cyber-blue mt-2">Click to view list</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Onboarding Actions -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <h2 class="text-3xl font-orbitron font-bold text-white mb-8 text-center">Add New Participants</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-16">
                <a href="{{ url_for('onboarding.add_citizen') }}" class="glass-card-premium p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-14 h-14 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyan-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-2xl">👤</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Add Citizen</h3>
                    <p class="text-text-secondary text-sm">Register a new citizen with covenant identity</p>
                </a>

                <a href="{{ url_for('onboarding.add_validator') }}" class="glass-card-premium p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-14 h-14 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-purple-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-2xl">🏢</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Add Validator</h3>
                    <p class="text-text-secondary text-sm">Register a business validator (Sela) with mining tier</p>
                </a>

                <a href="{{ url_for('admin.tribal_elders') }}" class="glass-card-premium p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-14 h-14 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-2xl">👑</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-green mb-2">Tribal Elders</h3>
                    <p class="text-text-secondary text-sm">Comprehensive tribal elder management</p>
                </a>

                <a href="{{ url_for('admin.sela_management') }}" class="glass-card-premium p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-14 h-14 mx-auto mb-4 bg-gradient-to-br from-yellow-500 to-orange-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-2xl">🏪</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-yellow-400 mb-2">Sela Management</h3>
                    <p class="text-text-secondary text-sm">Manage all business validators</p>
                </a>

                <a href="{{ url_for('admin.bulk_import') }}" class="glass-card-premium p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-14 h-14 mx-auto mb-4 bg-gradient-to-br from-cyber-blue to-blue-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-2xl">📊</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">Bulk Import</h3>
                    <p class="text-text-secondary text-sm">Advanced CSV/JSON import system</p>
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Recent Identities -->
                <div class="glass-card-premium p-8">
                    <h3 class="text-2xl font-orbitron font-bold text-white mb-6">Recent Identities</h3>
                    <div class="space-y-4">
                        {% for identity in recent_identities %}
                        <div class="flex items-center justify-between p-4 bg-dark-800/50 rounded-lg">
                            <div>
                                <div class="font-semibold text-white">{{ identity.name }}</div>
                                <div class="text-sm text-text-secondary">{{ identity.identity_id }}</div>
                                <div class="text-xs text-cyber-cyan">{{ identity.role.title() }}{% if identity.covenant_tribe %} - {{ identity.covenant_tribe }}{% endif %}</div>
                            </div>
                            <div class="text-xs text-text-secondary">
                                {{ moment(identity.created_at).fromNow() }}
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-text-secondary py-8">
                            No identities registered yet
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Recent Validators -->
                <div class="glass-card-premium p-8">
                    <h3 class="text-2xl font-orbitron font-bold text-white mb-6">Recent Validators</h3>
                    <div class="space-y-4">
                        {% for sela in recent_selas %}
                        <div class="flex items-center justify-between p-4 bg-dark-800/50 rounded-lg">
                            <div>
                                <div class="font-semibold text-white">{{ sela.business_name }}</div>
                                <div class="text-sm text-text-secondary">{{ sela.sela_id }}</div>
                                <div class="text-xs text-cyber-purple">{{ sela.mining_tier.title() }} Tier</div>
                            </div>
                            <div class="text-xs text-text-secondary">
                                {{ moment(sela.created_at).fromNow() }}
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-text-secondary py-8">
                            No validators registered yet
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal for displaying lists -->
<div id="listModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-dark-800 rounded-xl p-8 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="modalTitle" class="text-2xl font-orbitron font-bold text-white"></h2>
            <button onclick="closeModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
        </div>
        <div id="modalContent" class="text-white">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<script>
// Add moment.js for time formatting if not already included
if (typeof moment === 'undefined') {
    window.moment = function(timestamp) {
        const date = new Date(timestamp * 1000);
        return {
            fromNow: function() {
                const now = new Date();
                const diff = now - date;
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(diff / 3600000);
                const days = Math.floor(diff / 86400000);
                
                if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
                if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
                if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
                return 'Just now';
            }
        };
    };
}

// Modal functions
function closeModal() {
    document.getElementById('listModal').classList.add('hidden');
}

function showModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalContent').innerHTML = content;
    document.getElementById('listModal').classList.remove('hidden');
}

function formatDate(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// API functions
async function showIdentitiesList() {
    try {
        const response = await fetch('/onboarding/api/identities');
        const data = await response.json();

        if (data.success) {
            let content = `
                <div class="flex justify-between items-center mb-4">
                    <strong>Total: ${data.count} identities</strong>
                    <button onclick="showAddCitizenForm()" class="px-4 py-2 bg-cyber-cyan text-onyx-black rounded-lg hover:bg-cyan-400 transition-colors">
                        + Add Citizen
                    </button>
                </div>`;
            content += '<div class="overflow-x-auto"><table class="w-full text-sm">';
            content += '<thead><tr class="border-b border-gray-600"><th class="text-left p-2">Name</th><th class="text-left p-2">Email</th><th class="text-left p-2">Role</th><th class="text-left p-2">Tribe</th><th class="text-left p-2">Created</th><th class="text-left p-2">Actions</th></tr></thead><tbody>';

            data.identities.forEach(identity => {
                const canDelete = identity.role !== 'system_admin' && identity.role_class !== 'system_admin';
                const deleteButton = canDelete ?
                    `<button onclick="confirmDeleteUser('${identity.identity_id}', '${identity.name}')" class="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">Remove</button>` :
                    '<span class="text-xs text-gray-500">Protected</span>';

                content += `<tr class="border-b border-gray-700 hover:bg-gray-800">
                    <td class="p-2">${identity.name}</td>
                    <td class="p-2">${identity.email || 'N/A'}</td>
                    <td class="p-2">${identity.role || identity.role_class}</td>
                    <td class="p-2">${identity.tribe_name || 'N/A'}</td>
                    <td class="p-2">${formatDate(identity.created_at)}</td>
                    <td class="p-2">${deleteButton}</td>
                </tr>`;
            });

            content += '</tbody></table></div>';
            showModal('All Identities', content);
        } else {
            showModal('Error', 'Failed to load identities: ' + data.error);
        }
    } catch (error) {
        showModal('Error', 'Failed to load identities: ' + error.message);
    }
}

async function showValidatorsList() {
    try {
        const response = await fetch('/onboarding/api/validators');
        const data = await response.json();

        if (data.success) {
            let content = `
                <div class="flex justify-between items-center mb-4">
                    <strong>Total: ${data.count} validators</strong>
                    <button onclick="showAddValidatorForm()" class="px-4 py-2 bg-cyber-purple text-white rounded-lg hover:bg-purple-600 transition-colors">
                        + Add Validator
                    </button>
                </div>`;
            content += '<div class="overflow-x-auto"><table class="w-full text-sm">';
            content += '<thead><tr class="border-b border-gray-600"><th class="text-left p-2">Sela Name</th><th class="text-left p-2">Owner</th><th class="text-left p-2">Mining Tier</th><th class="text-left p-2">Category</th><th class="text-left p-2">Created</th><th class="text-left p-2">Actions</th></tr></thead><tbody>';

            data.validators.forEach(validator => {
                content += `<tr class="border-b border-gray-700 hover:bg-gray-800">
                    <td class="p-2">${validator.name}</td>
                    <td class="p-2">${validator.owner_name}</td>
                    <td class="p-2">${validator.mining_tier || 'Basic'}</td>
                    <td class="p-2">${validator.category || 'N/A'}</td>
                    <td class="p-2">${formatDate(validator.created_at)}</td>
                    <td class="p-2">
                        <button onclick="confirmDeleteValidator('${validator.sela_id}', '${validator.name}')" class="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">Remove</button>
                    </td>
                </tr>`;
            });

            content += '</tbody></table></div>';
            showModal('Active Validators', content);
        } else {
            showModal('Error', 'Failed to load validators: ' + data.error);
        }
    } catch (error) {
        showModal('Error', 'Failed to load validators: ' + error.message);
    }
}

async function showEldersList() {
    try {
        const response = await fetch('/onboarding/api/tribal-elders');
        const data = await response.json();

        if (data.success) {
            let content = `
                <div class="flex justify-between items-center mb-4">
                    <strong>Total: ${data.count} tribal elders</strong>
                    <button onclick="showAddElderForm()" class="px-4 py-2 bg-cyber-green text-onyx-black rounded-lg hover:bg-green-500 transition-colors">
                        + Add Tribal Elder
                    </button>
                </div>`;
            content += '<div class="overflow-x-auto"><table class="w-full text-sm">';
            content += '<thead><tr class="border-b border-gray-600"><th class="text-left p-2">Name</th><th class="text-left p-2">Email</th><th class="text-left p-2">Role</th><th class="text-left p-2">Tribe</th><th class="text-left p-2">Created</th><th class="text-left p-2">Actions</th></tr></thead><tbody>';

            data.tribal_elders.forEach(elder => {
                const canDelete = elder.role !== 'system_admin' && elder.role_class !== 'system_admin';
                const deleteButton = canDelete ?
                    `<button onclick="confirmDeleteUser('${elder.identity_id}', '${elder.name}')" class="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">Remove</button>` :
                    '<span class="text-xs text-gray-500">Protected</span>';

                content += `<tr class="border-b border-gray-700 hover:bg-gray-800">
                    <td class="p-2">${elder.name}</td>
                    <td class="p-2">${elder.email || 'N/A'}</td>
                    <td class="p-2">${elder.role || elder.role_class}</td>
                    <td class="p-2">${elder.tribe_name || 'N/A'}</td>
                    <td class="p-2">${formatDate(elder.created_at)}</td>
                    <td class="p-2">${deleteButton}</td>
                </tr>`;
            });

            content += '</tbody></table></div>';
            showModal('Tribal Elders', content);
        } else {
            showModal('Error', 'Failed to load tribal elders: ' + data.error);
        }
    } catch (error) {
        showModal('Error', 'Failed to load tribal elders: ' + error.message);
    }
}

async function showCitizensList() {
    try {
        const response = await fetch('/onboarding/api/citizens');
        const data = await response.json();

        if (data.success) {
            let content = `
                <div class="flex justify-between items-center mb-4">
                    <strong>Total: ${data.count} citizens</strong>
                    <button onclick="showAddCitizenForm()" class="px-4 py-2 bg-cyber-blue text-white rounded-lg hover:bg-blue-600 transition-colors">
                        + Add Citizen
                    </button>
                </div>`;
            content += '<div class="overflow-x-auto"><table class="w-full text-sm">';
            content += '<thead><tr class="border-b border-gray-600"><th class="text-left p-2">Name</th><th class="text-left p-2">Email</th><th class="text-left p-2">Role</th><th class="text-left p-2">Tribe</th><th class="text-left p-2">Created</th><th class="text-left p-2">Actions</th></tr></thead><tbody>';

            data.citizens.forEach(citizen => {
                const canDelete = citizen.role !== 'system_admin' && citizen.role_class !== 'system_admin';
                const deleteButton = canDelete ?
                    `<button onclick="confirmDeleteUser('${citizen.identity_id}', '${citizen.name}')" class="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">Remove</button>` :
                    '<span class="text-xs text-gray-500">Protected</span>';

                content += `<tr class="border-b border-gray-700 hover:bg-gray-800">
                    <td class="p-2">${citizen.name}</td>
                    <td class="p-2">${citizen.email || 'N/A'}</td>
                    <td class="p-2">${citizen.role || citizen.role_class}</td>
                    <td class="p-2">${citizen.tribe_name || 'N/A'}</td>
                    <td class="p-2">${formatDate(citizen.created_at)}</td>
                    <td class="p-2">${deleteButton}</td>
                </tr>`;
            });

            content += '</tbody></table></div>';
            showModal('Citizens', content);
        } else {
            showModal('Error', 'Failed to load citizens: ' + data.error);
        }
    } catch (error) {
        showModal('Error', 'Failed to load citizens: ' + error.message);
    }
}

// User deletion functions
async function confirmDeleteUser(identityId, userName) {
    if (confirm(`Are you sure you want to permanently delete user "${userName}"?\n\nThis action cannot be undone and will remove:\n- User identity and credentials\n- All token balances\n- All Selas owned by this user\n- All associated data\n\nType "DELETE" to confirm:`)) {
        const confirmation = prompt(`To confirm deletion of "${userName}", type DELETE:`);
        if (confirmation === 'DELETE') {
            await deleteUser(identityId, userName);
        }
    }
}

async function deleteUser(identityId, userName) {
    try {
        const response = await fetch(`/admin/api/users/${identityId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(`User "${userName}" has been successfully deleted.`);
            // Refresh the current modal
            refreshCurrentModal();
        } else {
            alert(`Failed to delete user: ${result.error}`);
        }
    } catch (error) {
        alert(`Error deleting user: ${error.message}`);
    }
}

async function confirmDeleteValidator(selaId, selaName) {
    if (confirm(`Are you sure you want to permanently delete validator "${selaName}"?\n\nThis action cannot be undone and will remove:\n- Validator (Sela) registration\n- All mining history\n- All associated data\n\nType "DELETE" to confirm:`)) {
        const confirmation = prompt(`To confirm deletion of validator "${selaName}", type DELETE:`);
        if (confirmation === 'DELETE') {
            await deleteValidator(selaId, selaName);
        }
    }
}

async function deleteValidator(selaId, selaName) {
    try {
        const response = await fetch(`/admin/api/validators/${selaId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(`Validator "${selaName}" has been successfully deleted.`);
            // Refresh the current modal
            refreshCurrentModal();
        } else {
            alert(`Failed to delete validator: ${result.error}`);
        }
    } catch (error) {
        alert(`Error deleting validator: ${error.message}`);
    }
}

// Form navigation functions
function showAddCitizenForm() {
    window.location.href = '/onboarding/citizen';
}

function showAddValidatorForm() {
    window.location.href = '/onboarding/validator';
}

function showAddElderForm() {
    window.location.href = '/onboarding/tribal-elder';
}

function showBulkImportForm() {
    window.location.href = '/onboarding/bulk-import';
}

// Refresh current modal
function refreshCurrentModal() {
    const modalTitle = document.getElementById('modalTitle').textContent;

    if (modalTitle.includes('Identities')) {
        showIdentitiesList();
    } else if (modalTitle.includes('Validators')) {
        showValidatorsList();
    } else if (modalTitle.includes('Elders')) {
        showEldersList();
    } else if (modalTitle.includes('Citizens')) {
        showCitizensList();
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('listModal');
    if (event.target === modal) {
        closeModal();
    }
});
</script>
{% endblock %}
