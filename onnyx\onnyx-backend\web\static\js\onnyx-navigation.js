/**
 * ONNYX Platform - Desktop Navigation System
 * Implements active page detection and user dropdown functionality
 */

class ONNYXNavigation {
    constructor() {
        this.userDropdownOpen = false;
        this.activeRoute = 'index';
        
        this.init();
    }

    init() {
        console.log('🚀 ONNYX Navigation System Initializing...');
        
        // Initialize navigation components
        this.initializeActivePageDetection();
        this.initializeUserDropdown();
        this.initializeKeyboardNavigation();
        
        console.log('✅ ONNYX Navigation System Ready');
    }

    /**
     * Active Page Detection & Highlighting
     */
    initializeActivePageDetection() {
        const currentPath = window.location.pathname;
        
        // Route detection logic
        if (currentPath === '/' || currentPath === '/index') {
            this.activeRoute = 'index';
        } else if (currentPath.startsWith('/sela')) {
            this.activeRoute = 'sela';
        } else if (currentPath.startsWith('/explorer')) {
            this.activeRoute = 'explorer';
        } else if (currentPath.startsWith('/tokenomics')) {
            this.activeRoute = 'tokenomics';
        } else if (currentPath.startsWith('/dashboard')) {
            this.activeRoute = 'dashboard';
        } else if (currentPath.startsWith('/auto_mining')) {
            this.activeRoute = 'auto_mining';
        }
        
        console.log(`🎯 Active route detected: ${this.activeRoute} (path: ${currentPath})`);
        
        this.applyActiveStates();
    }

    applyActiveStates() {
        // Apply active state to desktop navigation
        const desktopNavItems = document.querySelectorAll('.navbar-nav .nav-item[data-route]');
        desktopNavItems.forEach(item => {
            const itemRoute = item.getAttribute('data-route');
            if (itemRoute === this.activeRoute) {
                item.classList.add('nav-item-active');
                console.log(`✅ Desktop nav item activated: ${itemRoute}`);
            } else {
                item.classList.remove('nav-item-active');
            }
        });
    }

    /**
     * User Dropdown System
     */
    initializeUserDropdown() {
        const userButton = document.querySelector('.user-button');
        const userMenu = document.querySelector('.user-menu');
        const chevron = document.querySelector('.dropdown-chevron');

        if (!userButton || !userMenu) {
            console.log('ℹ️ User dropdown not present (guest user)');
            return;
        }

        userButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleUserDropdown();
        });

        // Close when clicking outside
        document.addEventListener('click', (e) => {
            if (!userButton.contains(e.target) && !userMenu.contains(e.target) && this.userDropdownOpen) {
                this.closeUserDropdown();
            }
        });

        console.log('✅ User dropdown initialized');
    }

    toggleUserDropdown() {
        if (this.userDropdownOpen) {
            this.closeUserDropdown();
        } else {
            this.openUserDropdown();
        }
    }

    openUserDropdown() {
        const userMenu = document.querySelector('.user-menu');
        const chevron = document.querySelector('.dropdown-chevron');

        this.userDropdownOpen = true;

        userMenu.style.display = 'block';
        userMenu.style.opacity = '0';
        userMenu.style.transform = 'scale(0.95)';

        // Force reflow
        userMenu.offsetHeight;

        userMenu.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        userMenu.style.opacity = '1';
        userMenu.style.transform = 'scale(1)';

        if (chevron) {
            chevron.style.transform = 'rotate(180deg)';
        }

        console.log('👤 User dropdown opened');
    }

    closeUserDropdown() {
        const userMenu = document.querySelector('.user-menu');
        const chevron = document.querySelector('.dropdown-chevron');

        this.userDropdownOpen = false;

        userMenu.style.transition = 'opacity 0.15s ease, transform 0.15s ease';
        userMenu.style.opacity = '0';
        userMenu.style.transform = 'scale(0.95)';

        setTimeout(() => {
            userMenu.style.display = 'none';
        }, 150);

        if (chevron) {
            chevron.style.transform = 'rotate(0deg)';
        }

        console.log('👤 User dropdown closed');
    }

    /**
     * Keyboard Navigation Support
     */
    initializeKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Escape key closes user dropdown
            if (e.key === 'Escape') {
                if (this.userDropdownOpen) {
                    this.closeUserDropdown();
                }
            }
        });

        console.log('✅ Keyboard navigation initialized');
    }
}

// Initialize navigation system when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.onnyxNavigation = new ONNYXNavigation();
    });
} else {
    window.onnyxNavigation = new ONNYXNavigation();
}
