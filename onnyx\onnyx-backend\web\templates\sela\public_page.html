{% extends "base.html" %}

{% block title %}{{ sela.name }} - ONNYX Business Directory{% endblock %}

{% block description %}{{ sela.description or (sela.name + " - Professional " + (sela.category or "business") + " services on the ONNYX covenant blockchain platform.") }}{% endblock %}

{% block head %}
<style>
.business-hero {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.1), rgba(154, 0, 255, 0.1));
    border: 1px solid rgba(0, 255, 247, 0.2);
}

.service-card {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    border: 1px solid rgba(0, 255, 247, 0.2);
    transition: all 0.3s ease;
}

.service-card:hover {
    border-color: rgba(0, 255, 247, 0.4);
    transform: translateY(-2px);
}

.contact-card {
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.1), rgba(0, 255, 247, 0.1));
    border: 1px solid rgba(154, 0, 255, 0.2);
}

.hours-card {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(0, 255, 247, 0.1));
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.stats-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.875rem;
}

.verification-badge {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.category-badge {
    background: rgba(154, 0, 255, 0.2);
    color: var(--cyber-purple);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.price-tag {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: bold;
    font-size: 0.875rem;
}

.contact-button {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 1rem 2rem;
    border-radius: 1rem;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.contact-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 247, 0.3);
}

.business-logo {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hours-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.hours-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(0, 255, 247, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--cyber-cyan);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(0, 255, 247, 0.3);
    transform: scale(1.1);
}

.breadcrumb {
    background: rgba(0, 255, 247, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.breadcrumb a {
    color: var(--cyber-cyan);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <nav class="text-sm">
                <a href="{{ url_for('home.index') }}">🏠 Home</a>
                <span class="mx-2 text-secondary">›</span>
                <a href="{{ url_for('home.index') }}#businesses">🏢 Businesses</a>
                <span class="mx-2 text-secondary">›</span>
                <span class="text-white">{{ sela.name }}</span>
            </nav>
        </div>

        <!-- Business Hero Section -->
        <div class="business-hero p-8 rounded-2xl mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center gap-8">
                <!-- Business Logo & Info -->
                <div class="text-center lg:text-left">
                    <div class="business-logo mx-auto lg:mx-0">
                        <span>🏢</span>
                    </div>
                    <h1 class="text-4xl font-orbitron font-bold text-white mb-2">{{ sela.name }}</h1>
                    <p class="text-xl text-secondary mb-4">{{ sela.description or "Professional " + (sela.category or "business") + " services" }}</p>
                    
                    <div class="flex flex-wrap gap-3 justify-center lg:justify-start mb-6">
                        <div class="category-badge">{{ sela.category.title() if sela.category else 'General Business' }}</div>
                        <div class="verification-badge">{{ business_stats.status }}</div>
                        <div class="stats-badge">{{ business_stats.nation }}</div>
                    </div>
                    
                    <div class="flex flex-wrap gap-4 justify-center lg:justify-start">
                        <a href="mailto:{{ contact_info.email }}" class="contact-button">
                            📧 Contact Us
                        </a>
                        {% if contact_info.phone != 'Contact via email' %}
                        <a href="tel:{{ contact_info.phone }}" class="contact-button">
                            📞 Call Now
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Business Stats -->
                <div class="flex-1 lg:max-w-md">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyber-cyan">{{ business_stats.verification_level }}</div>
                            <div class="text-sm text-secondary">Verification Level</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyber-purple">{{ moment(business_stats.established).format('YYYY') }}</div>
                            <div class="text-sm text-secondary">Established</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyber-green">{{ services|length }}</div>
                            <div class="text-sm text-secondary">Services</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-400">⭐</div>
                            <div class="text-sm text-secondary">ONNYX Verified</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">Our Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for service in services %}
                <div class="service-card p-6 rounded-xl">
                    <h3 class="text-lg font-orbitron font-bold text-white mb-3">{{ service.name }}</h3>
                    <p class="text-secondary mb-4">{{ service.description }}</p>
                    <div class="flex justify-between items-center">
                        <div class="price-tag">{{ service.price }}</div>
                        <button onclick="contactForService('{{ service.name }}')" class="text-cyber-cyan hover:text-cyan-400 transition-colors">
                            Learn More →
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Contact & Hours Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Contact Information -->
            <div class="contact-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Contact Information</h3>
                <div class="space-y-3">
                    <div class="flex items-center gap-3">
                        <span class="text-cyber-cyan">📧</span>
                        <div>
                            <div class="text-sm text-secondary">Email</div>
                            <a href="mailto:{{ contact_info.email }}" class="text-white hover:text-cyber-cyan transition-colors">
                                {{ contact_info.email }}
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-3">
                        <span class="text-cyber-cyan">📞</span>
                        <div>
                            <div class="text-sm text-secondary">Phone</div>
                            <div class="text-white">{{ contact_info.phone }}</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-3">
                        <span class="text-cyber-cyan">📍</span>
                        <div>
                            <div class="text-sm text-secondary">Address</div>
                            <div class="text-white">{{ contact_info.address }}</div>
                        </div>
                    </div>
                    
                    {% if contact_info.website %}
                    <div class="flex items-center gap-3">
                        <span class="text-cyber-cyan">🌐</span>
                        <div>
                            <div class="text-sm text-secondary">Website</div>
                            <a href="{{ contact_info.website }}" target="_blank" class="text-white hover:text-cyber-cyan transition-colors">
                                {{ contact_info.website }}
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Social Media Links -->
                {% if contact_info.social_media %}
                <div class="social-links">
                    {% for platform, url in contact_info.social_media.items() %}
                    <a href="{{ url }}" target="_blank" class="social-link" title="{{ platform.title() }}">
                        {% if platform == 'twitter' %}🐦
                        {% elif platform == 'facebook' %}📘
                        {% elif platform == 'linkedin' %}💼
                        {% elif platform == 'instagram' %}📷
                        {% else %}🔗{% endif %}
                    </a>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <!-- Business Hours -->
            <div class="hours-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-yellow-400 mb-4">Business Hours</h3>
                <div class="hours-grid">
                    {% for day, hours in business_hours.items() %}
                    <div class="hours-row">
                        <span class="text-secondary capitalize">{{ day }}</span>
                        <span class="text-white">{{ hours }}</span>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-6 p-4 bg-cyber-cyan bg-opacity-10 rounded-lg">
                    <div class="flex items-center gap-2 text-cyber-cyan">
                        <span>⏰</span>
                        <span class="font-semibold">Current Status:</span>
                    </div>
                    <div class="text-white mt-1" id="businessStatus">
                        Checking hours...
                    </div>
                </div>
            </div>
        </div>

        <!-- About Section -->
        <div class="glass-card p-6 rounded-xl mb-8">
            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">About {{ sela.name }}</h3>
            <div class="text-secondary leading-relaxed">
                <p class="mb-4">
                    {{ sela.name }} is a verified business on the ONNYX covenant blockchain platform, 
                    specializing in {{ sela.category or "professional services" }}. 
                    {% if business_stats.verification_level >= 2 %}
                    We are a fully verified and trusted member of the ONNYX business community.
                    {% else %}
                    We are an active member of the ONNYX business community.
                    {% endif %}
                </p>
                
                <p class="mb-4">
                    Established in {{ moment(business_stats.established).format('YYYY') }}, 
                    we are committed to providing exceptional service and building lasting relationships 
                    with our clients through the principles of covenant commerce.
                </p>
                
                <p>
                    As part of the ONNYX ecosystem, we operate with transparency, integrity, 
                    and a commitment to biblical business principles. Contact us today to learn 
                    how we can serve your needs.
                </p>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="business-hero p-8 rounded-2xl">
                <h3 class="text-2xl font-orbitron font-bold text-white mb-4">Ready to Get Started?</h3>
                <p class="text-secondary mb-6">Contact {{ sela.name }} today to discuss your needs and discover how we can help.</p>
                <div class="flex flex-wrap gap-4 justify-center">
                    <a href="mailto:{{ contact_info.email }}?subject=Inquiry about {{ sela.name }} services" class="contact-button">
                        📧 Send Email
                    </a>
                    <a href="{{ url_for('home.index') }}" class="glass-button-secondary px-6 py-3 rounded-lg">
                        🏠 Back to ONNYX
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function contactForService(serviceName) {
    const email = '{{ contact_info.email }}';
    const subject = `Inquiry about ${serviceName}`;
    const body = `Hello,\n\nI'm interested in learning more about your ${serviceName} service. Please provide more details.\n\nThank you!`;
    
    window.location.href = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
}

// Check business status
function updateBusinessStatus() {
    const now = new Date();
    const currentDay = now.toLocaleLowerCase().substring(0, 3);
    const currentTime = now.getHours() * 100 + now.getMinutes();
    
    // This is a simplified check - in a real app you'd parse the actual hours
    const businessHours = {{ business_hours | tojson }};
    const todayHours = businessHours[Object.keys(businessHours).find(day => day.startsWith(currentDay.substring(0, 3)))];
    
    const statusElement = document.getElementById('businessStatus');
    
    if (todayHours && todayHours.toLowerCase().includes('closed')) {
        statusElement.innerHTML = '<span class="text-red-400">🔴 Closed Today</span>';
    } else if (currentTime >= 900 && currentTime <= 1700) { // 9 AM to 5 PM
        statusElement.innerHTML = '<span class="text-green-400">🟢 Open Now</span>';
    } else {
        statusElement.innerHTML = '<span class="text-orange-400">🟡 Closed Now</span>';
    }
}

// Update status on page load
document.addEventListener('DOMContentLoaded', updateBusinessStatus);
</script>
{% endblock %}
