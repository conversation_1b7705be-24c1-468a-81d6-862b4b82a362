"""
Onnyx Validator Rotation Registry Module

This module provides the RotationRegistry class for managing validator rotation.
"""

import os
import json
import time
import random
import logging
from typing import Dict, Any, List, Optional, Set

from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry
from identity.trust.etzem_engine import EtzemEngine

# Set up logging
logger = logging.getLogger("onnyx.node.rotation_registry")

class RotationRegistry:
    """
    RotationRegistry manages the rotation of active validators.
    """

    def __init__(self, registry_path: str = "data/rotation_registry.json",
                 identity_registry_path: str = "data/identities.json",
                 sela_registry_path: str = "data/selas.json"):
        """
        Initialize the RotationRegistry.

        Args:
            registry_path: Path to the rotation registry JSON file
            identity_registry_path: Path to the identity registry JSON file
            sela_registry_path: Path to the Sela registry JSON file
        """
        self.registry_path = registry_path
        self.identity_registry = IdentityRegistry(identity_registry_path)
        self.sela_registry = SelaRegistry(sela_registry_path)
        self.etzem_engine = EtzemEngine()
        
        # Initialize registry
        self.registry = {
            "active_validators": [],
            "eligible_validators": [],
            "current_validator": None,
            "rotation_timestamp": 0,
            "rotation_interval": 3600,  # 1 hour in seconds
            "min_etzem_score": 30,
            "required_badges": ["SELA_FOUNDER", "VALIDATOR_ELIGIBLE_BADGE"]
        }
        
        # Load registry
        self._load_registry()
    
    def _load_registry(self):
        """Load the registry from the file."""
        try:
            if os.path.exists(self.registry_path) and os.path.getsize(self.registry_path) > 0:
                with open(self.registry_path, "r") as f:
                    loaded_registry = json.load(f)
                    # Update the registry with the loaded values
                    self.registry.update(loaded_registry)
                    logger.info(f"Loaded rotation registry from {self.registry_path}")
            else:
                # Save the default registry
                self._save_registry()
                logger.info(f"Created default rotation registry at {self.registry_path}")
        except Exception as e:
            logger.error(f"Error loading rotation registry: {str(e)}")
    
    def _save_registry(self):
        """Save the registry to the file."""
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.registry_path), exist_ok=True)
            
            with open(self.registry_path, "w") as f:
                json.dump(self.registry, f, indent=2)
                logger.info(f"Saved rotation registry to {self.registry_path}")
        except Exception as e:
            logger.error(f"Error saving rotation registry: {str(e)}")
    
    def update_eligible_validators(self):
        """
        Update the list of eligible validators based on Etzem scores and badges.
        """
        eligible_validators = []
        
        # Get all Selas
        selas = self.sela_registry.get_all_selas()
        
        for sela_id, sela in selas.items():
            # Get the founder's identity
            founder_id = sela.get("founder")
            if not founder_id:
                continue
            
            identity = self.identity_registry.get_identity(founder_id)
            if not identity:
                continue
            
            # Check Etzem score
            etzem_data = self.etzem_engine.compute_etzem(founder_id)
            etzem_score = etzem_data.get("etzem", 0)
            
            if etzem_score < self.registry["min_etzem_score"]:
                continue
            
            # Check required badges
            badges = identity.get("badges", [])
            has_required_badges = all(badge in badges for badge in self.registry["required_badges"])
            
            if not has_required_badges:
                continue
            
            # Add to eligible validators
            eligible_validators.append(sela_id)
        
        self.registry["eligible_validators"] = eligible_validators
        self._save_registry()
        
        logger.info(f"Updated eligible validators: {len(eligible_validators)} Selas are eligible")
        return eligible_validators
    
    def rotate_validators(self):
        """
        Rotate the active validators.
        
        Returns:
            The new current validator
        """
        # Check if it's time to rotate
        current_time = int(time.time())
        if current_time - self.registry["rotation_timestamp"] < self.registry["rotation_interval"]:
            return self.registry["current_validator"]
        
        # Update eligible validators
        self.update_eligible_validators()
        
        # Check if there are eligible validators
        if not self.registry["eligible_validators"]:
            logger.warning("No eligible validators found")
            return None
        
        # Select a new current validator
        if self.registry["eligible_validators"]:
            self.registry["current_validator"] = random.choice(self.registry["eligible_validators"])
        
        # Update active validators (keep a history of recent validators)
        if self.registry["current_validator"] not in self.registry["active_validators"]:
            self.registry["active_validators"].append(self.registry["current_validator"])
        
        # Keep only the last 10 active validators
        if len(self.registry["active_validators"]) > 10:
            self.registry["active_validators"] = self.registry["active_validators"][-10:]
        
        # Update rotation timestamp
        self.registry["rotation_timestamp"] = current_time
        
        self._save_registry()
        
        logger.info(f"Rotated validators: current validator is {self.registry['current_validator']}")
        return self.registry["current_validator"]
    
    def is_current_validator(self, sela_id: str) -> bool:
        """
        Check if a Sela is the current validator.
        
        Args:
            sela_id: The Sela ID
        
        Returns:
            True if the Sela is the current validator, False otherwise
        """
        return sela_id == self.registry["current_validator"]
    
    def get_current_validator(self) -> Optional[str]:
        """
        Get the current validator.
        
        Returns:
            The current validator Sela ID or None if there is no current validator
        """
        # Check if it's time to rotate
        current_time = int(time.time())
        if current_time - self.registry["rotation_timestamp"] >= self.registry["rotation_interval"]:
            return self.rotate_validators()
        
        return self.registry["current_validator"]
    
    def get_validator_status(self, sela_id: str) -> Dict[str, Any]:
        """
        Get the validator status for a Sela.
        
        Args:
            sela_id: The Sela ID
        
        Returns:
            The validator status
        """
        current_validator = self.get_current_validator()
        is_current = sela_id == current_validator
        is_eligible = sela_id in self.registry["eligible_validators"]
        is_active = sela_id in self.registry["active_validators"]
        
        next_rotation = self.registry["rotation_timestamp"] + self.registry["rotation_interval"]
        time_until_rotation = max(0, next_rotation - int(time.time()))
        
        return {
            "sela_id": sela_id,
            "is_current_validator": is_current,
            "is_eligible": is_eligible,
            "is_active": is_active,
            "current_validator": current_validator,
            "next_rotation": next_rotation,
            "time_until_rotation": time_until_rotation
        }
    
    def set_rotation_interval(self, interval: int):
        """
        Set the rotation interval.
        
        Args:
            interval: The rotation interval in seconds
        """
        self.registry["rotation_interval"] = interval
        self._save_registry()
        logger.info(f"Set rotation interval to {interval} seconds")
    
    def set_min_etzem_score(self, score: int):
        """
        Set the minimum Etzem score required for eligibility.
        
        Args:
            score: The minimum Etzem score
        """
        self.registry["min_etzem_score"] = score
        self._save_registry()
        logger.info(f"Set minimum Etzem score to {score}")
    
    def set_required_badges(self, badges: List[str]):
        """
        Set the required badges for eligibility.
        
        Args:
            badges: The required badges
        """
        self.registry["required_badges"] = badges
        self._save_registry()
        logger.info(f"Set required badges to {badges}")

# Create a global instance of the RotationRegistry
rotation_registry = RotationRegistry()
