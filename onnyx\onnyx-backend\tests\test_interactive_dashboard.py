#!/usr/bin/env python3
"""
Test Interactive Dashboard Features
"""

import requests
import sys

def test_onboarding_dashboard():
    """Test the onboarding dashboard interactive features"""
    print("Testing onboarding dashboard...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/onboarding/", timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text.lower()
            
            # Check for interactive elements
            checks = [
                ("clickable cards", "cursor-pointer" in content),
                ("hover effects", "hover:scale-105" in content),
                ("click handlers", "onclick=" in content),
                ("modal system", "listmodal" in content),
                ("javascript functions", "showidentitieslist" in content),
                ("api endpoints", "fetch(" in content)
            ]
            
            found_count = 0
            for check_name, check_result in checks:
                if check_result:
                    print(f"SUCCESS: {check_name} found")
                    found_count += 1
                else:
                    print(f"MISSING: {check_name}")
            
            print(f"Interactive features: {found_count}/{len(checks)} found")
            return found_count >= 4
            
        elif response.status_code in [302, 403]:
            print("PROTECTED: Dashboard requires authentication (expected)")
            return True
            
        else:
            print(f"ERROR: Unexpected status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def test_api_endpoints():
    """Test the API endpoints for interactive features"""
    print("\nTesting API endpoints...")
    
    endpoints = [
        "/onboarding/api/identities",
        "/onboarding/api/tribal-elders",
        "/onboarding/api/validators",
        "/onboarding/api/citizens"
    ]
    
    success_count = 0
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=5)
            
            if response.status_code in [200, 302, 403]:
                print(f"SUCCESS: {endpoint} accessible")
                success_count += 1
            elif response.status_code == 404:
                print(f"ERROR: {endpoint} not found")
            else:
                print(f"WARNING: {endpoint} status {response.status_code}")
                
        except Exception as e:
            print(f"ERROR: {endpoint} - {e}")
    
    return success_count == len(endpoints)

if __name__ == "__main__":
    print("Testing Interactive Dashboard Features")
    print("=" * 50)
    
    dashboard_success = test_onboarding_dashboard()
    api_success = test_api_endpoints()
    
    print("\nResults:")
    if dashboard_success:
        print("SUCCESS: Dashboard interactive features working")
    else:
        print("FAILURE: Dashboard interactive features need work")
        
    if api_success:
        print("SUCCESS: API endpoints accessible")
    else:
        print("FAILURE: API endpoints have issues")
    
    overall_success = dashboard_success and api_success
    print(f"\nOverall: {'SUCCESS' if overall_success else 'FAILURE'}")
    
    sys.exit(0 if overall_success else 1)
