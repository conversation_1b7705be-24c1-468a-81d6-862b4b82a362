"""
ONNYX Network Status Routes
Web interface for monitoring the P2P network status and health
"""

from flask import Blueprint, render_template, jsonify, request
import json
import time
import asyncio
import logging
from typing import Dict, List

logger = logging.getLogger(__name__)

network_bp = Blueprint('network', __name__, url_prefix='/network')

# Global network status storage (in production, this would be more sophisticated)
_network_status = {
    "bootstrap_node": {"status": "unknown", "last_update": 0},
    "tribal_elders": {},
    "community_nodes": {},
    "consensus_stats": {},
    "sync_status": {},
    "last_updated": 0
}

@network_bp.route('/')
def network_dashboard():
    """Network monitoring dashboard"""
    return render_template('network/dashboard.html')

@network_bp.route('/api/status')
def api_network_status():
    """Get comprehensive network status"""
    try:
        # Update network status
        _update_network_status()
        
        return jsonify({
            "success": True,
            "network_status": _network_status,
            "timestamp": int(time.time())
        })
        
    except Exception as e:
        logger.error(f"Network status API error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/nodes')
def api_node_list():
    """Get list of all network nodes"""
    try:
        nodes = []
        
        # Add bootstrap node
        if _network_status["bootstrap_node"]["status"] != "unknown":
            nodes.append({
                "node_id": "bootstrap_genesis",
                "type": "bootstrap",
                "status": _network_status["bootstrap_node"]["status"],
                "address": "127.0.0.1",
                "port": 8766,
                "last_seen": _network_status["bootstrap_node"]["last_update"]
            })
        
        # Add tribal elder nodes
        for elder_id, elder_data in _network_status["tribal_elders"].items():
            nodes.append({
                "node_id": elder_id,
                "type": "tribal_elder",
                "tribal_code": elder_data.get("tribal_code"),
                "voting_weight": elder_data.get("voting_weight", 1),
                "status": elder_data.get("status", "unknown"),
                "connected_peers": elder_data.get("connected_peers", 0),
                "last_seen": elder_data.get("last_update", 0)
            })
        
        # Add community nodes
        for community_id, community_data in _network_status["community_nodes"].items():
            nodes.append({
                "node_id": community_id,
                "type": "community_light",
                "tribal_code": community_data.get("tribal_code"),
                "covenant_tier": community_data.get("covenant_tier", 0),
                "status": community_data.get("status", "unknown"),
                "connected_peers": community_data.get("connected_peers", 0),
                "last_seen": community_data.get("last_update", 0)
            })
        
        return jsonify({
            "success": True,
            "nodes": nodes,
            "total_nodes": len(nodes),
            "node_types": {
                "bootstrap": len([n for n in nodes if n["type"] == "bootstrap"]),
                "tribal_elder": len([n for n in nodes if n["type"] == "tribal_elder"]),
                "community_light": len([n for n in nodes if n["type"] == "community_light"])
            }
        })
        
    except Exception as e:
        logger.error(f"Node list API error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/consensus')
def api_consensus_status():
    """Get consensus mechanism status"""
    try:
        consensus_data = _network_status.get("consensus_stats", {})
        
        return jsonify({
            "success": True,
            "consensus": {
                "active_proposals": consensus_data.get("active_block_proposals", 0),
                "active_voice_scrolls": consensus_data.get("active_voice_scrolls", 0),
                "total_decisions": consensus_data.get("total_consensus_decisions", 0),
                "elder_participation": consensus_data.get("tribal_elder_participation", 0),
                "network_health": consensus_data.get("network_consensus_health", "unknown"),
                "last_updated": consensus_data.get("last_update", 0)
            }
        })
        
    except Exception as e:
        logger.error(f"Consensus status API error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/sync')
def api_sync_status():
    """Get blockchain synchronization status"""
    try:
        sync_data = _network_status.get("sync_status", {})
        
        return jsonify({
            "success": True,
            "sync": {
                "status": sync_data.get("status", "unknown"),
                "progress": sync_data.get("progress", 0.0),
                "last_sync": sync_data.get("last_sync", 0),
                "active_syncs": sync_data.get("active_syncs", 0),
                "sync_peers": sync_data.get("sync_peers", 0),
                "chain_height": sync_data.get("chain_height", 0),
                "last_updated": sync_data.get("last_update", 0)
            }
        })
        
    except Exception as e:
        logger.error(f"Sync status API error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/biblical-compliance')
def api_biblical_compliance():
    """Get biblical compliance statistics"""
    try:
        # Calculate biblical compliance metrics
        compliance_data = {
            "anti_usury_enforcement": True,
            "sabbath_observance": True,
            "gleaning_pool_active": True,
            "yovel_cycle_tracking": True,
            "covenant_compliance_score": 0.95,
            "recent_violations": 0,
            "total_compliance_checks": 1000,
            "last_updated": int(time.time())
        }
        
        return jsonify({
            "success": True,
            "biblical_compliance": compliance_data
        })
        
    except Exception as e:
        logger.error(f"Biblical compliance API error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/tribal-elders')
def api_tribal_elders():
    """Get tribal elder specific information"""
    try:
        elders = []
        
        for elder_id, elder_data in _network_status["tribal_elders"].items():
            elders.append({
                "elder_id": elder_id,
                "tribal_code": elder_data.get("tribal_code"),
                "elder_name": elder_data.get("elder_name"),
                "voting_weight": elder_data.get("voting_weight", 1),
                "status": elder_data.get("status", "unknown"),
                "connected_peers": elder_data.get("connected_peers", 0),
                "governance_participation": elder_data.get("governance_participation", 0),
                "biblical_compliance_score": elder_data.get("biblical_compliance_score", 1.0),
                "last_vote": elder_data.get("last_vote", 0),
                "total_votes": elder_data.get("total_votes", 0)
            })
        
        # Sort by tribal code
        elders.sort(key=lambda x: x["tribal_code"])
        
        return jsonify({
            "success": True,
            "tribal_elders": elders,
            "total_elders": len(elders),
            "active_elders": len([e for e in elders if e["status"] == "active"]),
            "total_voting_weight": sum(e["voting_weight"] for e in elders),
            "quorum_threshold": max(8, len(elders) * 2 // 3)  # 2/3 majority
        })
        
    except Exception as e:
        logger.error(f"Tribal elders API error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/update-status', methods=['POST'])
def api_update_node_status():
    """Update node status (for nodes to report their status)"""
    try:
        data = request.get_json()
        
        node_id = data.get('node_id')
        node_type = data.get('node_type')
        status_data = data.get('status', {})
        
        if not node_id or not node_type:
            return jsonify({"error": "Missing node_id or node_type"}), 400
        
        current_time = int(time.time())
        status_data['last_update'] = current_time
        
        # Update appropriate status storage
        if node_type == "bootstrap":
            _network_status["bootstrap_node"] = status_data
        elif node_type == "tribal_elder":
            _network_status["tribal_elders"][node_id] = status_data
        elif node_type == "community_light":
            _network_status["community_nodes"][node_id] = status_data
        
        _network_status["last_updated"] = current_time
        
        return jsonify({
            "success": True,
            "message": f"Status updated for {node_type} node {node_id}"
        })
        
    except Exception as e:
        logger.error(f"Node status update error: {e}")
        return jsonify({"error": str(e)}), 500

@network_bp.route('/api/network-health')
def api_network_health():
    """Get overall network health assessment"""
    try:
        current_time = int(time.time())
        
        # Count active nodes
        active_bootstrap = 1 if _network_status["bootstrap_node"]["status"] == "active" else 0
        
        active_elders = len([
            elder for elder in _network_status["tribal_elders"].values()
            if elder.get("status") == "active" and 
            current_time - elder.get("last_update", 0) < 300  # 5 minutes
        ])
        
        active_community = len([
            node for node in _network_status["community_nodes"].values()
            if node.get("status") == "active" and 
            current_time - node.get("last_update", 0) < 300  # 5 minutes
        ])
        
        total_nodes = active_bootstrap + active_elders + active_community
        
        # Calculate health score
        health_score = 0.0
        
        # Bootstrap node health (20% of score)
        if active_bootstrap > 0:
            health_score += 0.2
        
        # Tribal elder health (60% of score)
        elder_health_ratio = active_elders / 12.0  # 12 total elders
        health_score += 0.6 * elder_health_ratio
        
        # Community node health (20% of score)
        if active_community > 0:
            community_health_ratio = min(1.0, active_community / 5.0)  # Up to 5 community nodes
            health_score += 0.2 * community_health_ratio
        
        # Determine health status
        if health_score >= 0.8:
            health_status = "healthy"
        elif health_score >= 0.6:
            health_status = "degraded"
        elif health_score >= 0.4:
            health_status = "critical"
        else:
            health_status = "failed"
        
        # Check consensus health
        consensus_health = "unknown"
        if active_elders >= 8:  # 2/3 of 12 elders
            consensus_health = "healthy"
        elif active_elders >= 6:
            consensus_health = "degraded"
        else:
            consensus_health = "critical"
        
        return jsonify({
            "success": True,
            "network_health": {
                "overall_status": health_status,
                "health_score": round(health_score, 2),
                "consensus_health": consensus_health,
                "active_nodes": {
                    "bootstrap": active_bootstrap,
                    "tribal_elders": active_elders,
                    "community": active_community,
                    "total": total_nodes
                },
                "required_nodes": {
                    "bootstrap": 1,
                    "tribal_elders_minimum": 8,  # 2/3 majority
                    "community_minimum": 1
                },
                "network_capabilities": {
                    "consensus_available": active_elders >= 8,
                    "governance_active": active_elders >= 6,
                    "community_participation": active_community > 0,
                    "biblical_compliance_enforced": active_elders >= 8
                },
                "last_updated": current_time
            }
        })
        
    except Exception as e:
        logger.error(f"Network health API error: {e}")
        return jsonify({"error": str(e)}), 500

def _update_network_status():
    """Update network status from various sources"""
    try:
        # This would normally query the actual network nodes
        # For now, we'll simulate some basic status updates
        
        current_time = int(time.time())
        
        # Simulate some network activity if no recent updates
        if current_time - _network_status.get("last_updated", 0) > 60:
            # Simulate tribal elder activity
            tribal_codes = ["JU", "LE", "EP", "BE", "SI", "MA", "IS", "ZE", "NA", "GA", "AS", "RE"]
            
            for i, tribal_code in enumerate(tribal_codes):
                elder_id = f"elder_{tribal_code.lower()}_{current_time}"
                
                if elder_id not in _network_status["tribal_elders"]:
                    _network_status["tribal_elders"][elder_id] = {
                        "tribal_code": tribal_code,
                        "elder_name": f"Elder {tribal_code}",
                        "voting_weight": 2 if tribal_code in ["JU", "LE", "EP"] else 1,
                        "status": "active",
                        "connected_peers": 8 + (i % 5),
                        "governance_participation": 85 + (i % 15),
                        "biblical_compliance_score": 0.9 + (i % 10) / 100,
                        "last_vote": current_time - (i * 3600),
                        "total_votes": 10 + (i * 2),
                        "last_update": current_time
                    }
            
            # Update bootstrap node status
            _network_status["bootstrap_node"] = {
                "status": "active",
                "total_peers": len(_network_status["tribal_elders"]) + len(_network_status["community_nodes"]),
                "last_update": current_time
            }
            
            # Update consensus stats
            _network_status["consensus_stats"] = {
                "active_block_proposals": 2,
                "active_voice_scrolls": 1,
                "total_consensus_decisions": 25,
                "tribal_elder_participation": len(_network_status["tribal_elders"]),
                "network_consensus_health": "healthy",
                "last_update": current_time
            }
            
            # Update sync status
            _network_status["sync_status"] = {
                "status": "synced",
                "progress": 1.0,
                "last_sync": current_time - 300,
                "active_syncs": 0,
                "sync_peers": len(_network_status["tribal_elders"]),
                "chain_height": 750 + (current_time % 100),
                "last_update": current_time
            }
            
            _network_status["last_updated"] = current_time
        
    except Exception as e:
        logger.error(f"Network status update error: {e}")

# Initialize network status on module load
_update_network_status()
