{% extends "base.html" %}

{% block title %}About ONNYX | Biblical Covenant Blockchain{% endblock %}

{% block description %}Learn about the ONNYX platform, its biblical foundations, and mission to serve the covenant community.{% endblock %}

{% block head %}
<style>
    .about-hero {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .feature-card {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.05) 0%, rgba(154, 0, 255, 0.05) 100%);
        border: 1px solid rgba(0, 255, 247, 0.1);
        backdrop-filter: blur(10px);
        transition: transform 0.3s ease, border-color 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        border-color: rgba(0, 255, 247, 0.3);
    }
    
    .biblical-principle {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
        border: 1px solid rgba(255, 215, 0, 0.2);
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="about-hero p-8 rounded-2xl mb-12 text-center">
            <h1 class="text-5xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                    ONNYX
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">{{ platform_info.description }}</p>
            <div class="flex flex-wrap justify-center gap-4 text-sm">
                <span class="px-4 py-2 bg-cyber-cyan bg-opacity-20 rounded-full text-cyber-cyan">
                    Version {{ platform_info.version }}
                </span>
                <span class="px-4 py-2 bg-cyber-purple bg-opacity-20 rounded-full text-cyber-purple">
                    Founded {{ platform_info.founded }}
                </span>
                <span class="px-4 py-2 bg-cyber-green bg-opacity-20 rounded-full text-cyber-green">
                    Creator: {{ platform_info.creator }}
                </span>
            </div>
        </div>

        <!-- Mission Statement -->
        <div class="glass-card p-8 rounded-xl mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6 text-center">Our Mission</h2>
            <p class="text-lg text-gray-300 text-center leading-relaxed">
                {{ platform_info.mission }}
            </p>
        </div>

        <!-- Biblical Foundations -->
        <div class="mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-8 text-center">Biblical Foundations</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for principle in biblical_foundations %}
                <div class="biblical-principle p-6 rounded-xl">
                    <h3 class="text-xl font-orbitron font-bold text-yellow-400 mb-3">{{ principle.principle }}</h3>
                    <p class="text-gray-300 mb-4">{{ principle.description }}</p>
                    <blockquote class="text-sm text-yellow-300 italic border-l-2 border-yellow-400 pl-3">
                        "{{ principle.scripture }}"
                    </blockquote>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Technical Features -->
        <div class="mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-green mb-8 text-center">Technical Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% for feature in technical_features %}
                <div class="feature-card p-6 rounded-xl">
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">{{ feature.feature }}</h3>
                    <p class="text-gray-300">{{ feature.description }}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <a href="{{ url_for('about.mission') }}" class="glass-button-primary p-6 rounded-xl text-center block">
                <div class="text-2xl mb-2">🎯</div>
                <div class="font-orbitron font-bold">Mission</div>
                <div class="text-sm text-gray-400 mt-1">Detailed mission statement</div>
            </a>
            
            <a href="{{ url_for('about.biblical_foundation') }}" class="glass-button-secondary p-6 rounded-xl text-center block">
                <div class="text-2xl mb-2">📖</div>
                <div class="font-orbitron font-bold">Biblical Foundation</div>
                <div class="text-sm text-gray-400 mt-1">Scriptural basis</div>
            </a>
            
            <a href="{{ url_for('about.technology') }}" class="glass-button-enhanced p-6 rounded-xl text-center block">
                <div class="text-2xl mb-2">⚙️</div>
                <div class="font-orbitron font-bold">Technology</div>
                <div class="text-sm text-gray-400 mt-1">Technical architecture</div>
            </a>
            
            <a href="{{ url_for('about.team') }}" class="glass-button-premium p-6 rounded-xl text-center block">
                <div class="text-2xl mb-2">👥</div>
                <div class="font-orbitron font-bold">Team</div>
                <div class="text-sm text-gray-400 mt-1">Leadership & vision</div>
            </a>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-12">
            <div class="glass-card p-8 rounded-xl">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">Join the Covenant Community</h3>
                <p class="text-gray-300 mb-6">
                    Experience blockchain technology built on biblical principles. 
                    Join the tribes of Israel and witness nations in this covenant platform.
                </p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="{{ url_for('eden_mode.index') }}" class="glass-button-primary px-8 py-3 rounded-xl font-orbitron">
                        🌱 Start Eden Mode
                    </a>
                    <a href="{{ url_for('auth.login') }}" class="glass-button-secondary px-8 py-3 rounded-xl font-orbitron">
                        🔑 Login
                    </a>
                    <a href="{{ url_for('explorer.index') }}" class="glass-button-enhanced px-8 py-3 rounded-xl font-orbitron">
                        🔍 Explore Blockchain
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
