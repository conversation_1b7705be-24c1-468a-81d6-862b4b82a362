"""
Labor Reward Calculator
Specialized calculations for different types of labor and community contributions
"""

import time
import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

logger = logging.getLogger("onnyx.tokenomics.labor_rewards")

class LaborRewardCalculator:
    """
    Advanced labor reward calculation system with biblical economic principles.
    """
    
    def __init__(self, db_connection):
        """Initialize the labor reward calculator."""
        self.db = db_connection
        
        # Labor type definitions with base multipliers
        self.LABOR_TYPES = {
            'service': {
                'multiplier': 1.0,
                'description': 'General service provision',
                'min_value': 1.0,
                'max_value': 1000.0,
                'verification_required': True
            },
            'product': {
                'multiplier': 0.9,
                'description': 'Physical or digital product creation',
                'min_value': 5.0,
                'max_value': 2000.0,
                'verification_required': True
            },
            'teaching': {
                'multiplier': 1.5,
                'description': 'Knowledge sharing and education',
                'min_value': 10.0,
                'max_value': 500.0,
                'verification_required': True
            },
            'healing': {
                'multiplier': 1.4,
                'description': 'Medical, therapeutic, or wellness services',
                'min_value': 20.0,
                'max_value': 800.0,
                'verification_required': True
            },
            'farming': {
                'multiplier': 1.1,
                'description': 'Agricultural and food production',
                'min_value': 5.0,
                'max_value': 1500.0,
                'verification_required': False  # Self-verifiable
            },
            'governance': {
                'multiplier': 1.2,
                'description': 'Community governance and administration',
                'min_value': 10.0,
                'max_value': 300.0,
                'verification_required': True
            },
            'community': {
                'multiplier': 1.3,
                'description': 'Community service and mutual aid',
                'min_value': 1.0,
                'max_value': 200.0,
                'verification_required': False  # Community self-validates
            }
        }
        
        # Quality multipliers based on verification consensus
        self.QUALITY_MULTIPLIERS = {
            'excellent': 1.3,    # 90%+ verifier approval
            'good': 1.1,         # 75-89% verifier approval
            'satisfactory': 1.0, # 60-74% verifier approval
            'poor': 0.7,         # 40-59% verifier approval
            'rejected': 0.0      # <40% verifier approval
        }
        
        # Experience multipliers based on verified labor history
        self.EXPERIENCE_MULTIPLIERS = {
            'master': 1.4,       # 100+ verified labors
            'expert': 1.2,       # 50-99 verified labors
            'skilled': 1.1,      # 20-49 verified labors
            'apprentice': 1.0,   # 5-19 verified labors
            'novice': 0.9        # 0-4 verified labors
        }

    def calculate_base_reward(self, labor_record: Dict[str, Any]) -> Tuple[float, int]:
        """
        Calculate base Mikvah token and Etzem point rewards.
        
        Args:
            labor_record: Labor record dictionary
            
        Returns:
            Tuple of (mikvah_tokens, etzem_points)
        """
        try:
            labor_type = labor_record.get('labor_type', 'service')
            value_estimate = float(labor_record.get('value_estimate', 0.0))
            identity_id = labor_record.get('identity_id')
            
            # Validate labor type
            if labor_type not in self.LABOR_TYPES:
                logger.warning(f"Unknown labor type: {labor_type}")
                labor_type = 'service'
            
            type_config = self.LABOR_TYPES[labor_type]
            
            # Validate value estimate
            if value_estimate < type_config['min_value']:
                value_estimate = type_config['min_value']
            elif value_estimate > type_config['max_value']:
                value_estimate = type_config['max_value']
            
            # Base calculation
            base_multiplier = type_config['multiplier']
            mikvah_tokens = value_estimate * 0.1 * base_multiplier  # 10% of value as tokens
            etzem_points = max(1, int(value_estimate * 0.05 * base_multiplier))  # 5% as points
            
            # Apply experience multiplier
            experience_level = self._get_experience_level(identity_id)
            experience_multiplier = self.EXPERIENCE_MULTIPLIERS.get(experience_level, 1.0)
            
            mikvah_tokens *= experience_multiplier
            etzem_points = int(etzem_points * experience_multiplier)
            
            return round(mikvah_tokens, 2), etzem_points
            
        except Exception as e:
            logger.error(f"Error calculating base reward: {e}")
            return 0.0, 0

    def calculate_quality_adjustment(self, labor_id: str) -> float:
        """
        Calculate quality adjustment based on community verification.
        
        Args:
            labor_id: ID of the labor record
            
        Returns:
            Quality multiplier (0.0 to 1.3)
        """
        try:
            # Get verification records
            verifications = self.db.query("""
                SELECT verification_type FROM labor_verifications 
                WHERE labor_id = ?
            """, (labor_id,))
            
            if not verifications:
                return 1.0  # No verifications yet, use base
            
            # Calculate approval rate
            total_verifications = len(verifications)
            approvals = sum(1 for v in verifications if v['verification_type'] == 'approve')
            approval_rate = (approvals / total_verifications) * 100
            
            # Determine quality level
            if approval_rate >= 90:
                quality_level = 'excellent'
            elif approval_rate >= 75:
                quality_level = 'good'
            elif approval_rate >= 60:
                quality_level = 'satisfactory'
            elif approval_rate >= 40:
                quality_level = 'poor'
            else:
                quality_level = 'rejected'
            
            return self.QUALITY_MULTIPLIERS[quality_level]
            
        except Exception as e:
            logger.error(f"Error calculating quality adjustment: {e}")
            return 1.0

    def calculate_seasonal_bonus(self, identity_id: str, season_period: int) -> float:
        """
        Calculate seasonal bonus based on consistent participation.
        
        Args:
            identity_id: Identity to check
            season_period: Current season period
            
        Returns:
            Seasonal bonus multiplier (1.0 to 1.5)
        """
        try:
            # Check labor count in current season
            season_labor = self.db.query_one("""
                SELECT COUNT(*) as count FROM labor_records 
                WHERE identity_id = ? AND season_period = ? AND verification_status = 'verified'
            """, (identity_id, season_period))
            
            labor_count = season_labor['count'] if season_labor else 0
            
            # Bonus tiers based on seasonal activity
            if labor_count >= 20:
                return 1.5  # Very active - 50% bonus
            elif labor_count >= 10:
                return 1.3  # Active - 30% bonus
            elif labor_count >= 5:
                return 1.1  # Moderate - 10% bonus
            else:
                return 1.0  # Base - no bonus
                
        except Exception as e:
            logger.error(f"Error calculating seasonal bonus: {e}")
            return 1.0

    def calculate_community_impact_bonus(self, labor_record: Dict[str, Any]) -> float:
        """
        Calculate bonus based on community impact and reach.
        
        Args:
            labor_record: Labor record dictionary
            
        Returns:
            Community impact multiplier (1.0 to 2.0)
        """
        try:
            labor_type = labor_record.get('labor_type', 'service')
            metadata = json.loads(labor_record.get('metadata', '{}'))
            
            # Community impact factors
            impact_multiplier = 1.0
            
            # Teaching and healing have higher community impact
            if labor_type in ['teaching', 'healing', 'community']:
                impact_multiplier += 0.2
            
            # Check for community reach indicators in metadata
            beneficiaries = metadata.get('beneficiaries', 1)
            if beneficiaries > 10:
                impact_multiplier += 0.3
            elif beneficiaries > 5:
                impact_multiplier += 0.2
            elif beneficiaries > 1:
                impact_multiplier += 0.1
            
            # Check for innovation or teaching components
            if metadata.get('innovative', False):
                impact_multiplier += 0.2
            
            if metadata.get('educational_value', False):
                impact_multiplier += 0.1
            
            # Cap at 2.0x multiplier
            return min(2.0, impact_multiplier)
            
        except Exception as e:
            logger.error(f"Error calculating community impact bonus: {e}")
            return 1.0

    def _get_experience_level(self, identity_id: str) -> str:
        """Get experience level based on verified labor history."""
        try:
            verified_count = self.db.query_one("""
                SELECT COUNT(*) as count FROM labor_records 
                WHERE identity_id = ? AND verification_status = 'verified'
            """, (identity_id,))
            
            count = verified_count['count'] if verified_count else 0
            
            if count >= 100:
                return 'master'
            elif count >= 50:
                return 'expert'
            elif count >= 20:
                return 'skilled'
            elif count >= 5:
                return 'apprentice'
            else:
                return 'novice'
                
        except Exception as e:
            logger.error(f"Error getting experience level: {e}")
            return 'novice'

    def validate_labor_submission(self, labor_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate labor submission data.
        
        Args:
            labor_data: Labor submission data
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Required fields
            required_fields = ['identity_id', 'labor_type', 'description', 'value_estimate']
            for field in required_fields:
                if field not in labor_data or not labor_data[field]:
                    return False, f"Missing required field: {field}"
            
            # Validate labor type
            labor_type = labor_data['labor_type']
            if labor_type not in self.LABOR_TYPES:
                return False, f"Invalid labor type: {labor_type}"
            
            # Validate value estimate
            value_estimate = float(labor_data['value_estimate'])
            type_config = self.LABOR_TYPES[labor_type]
            
            if value_estimate < type_config['min_value']:
                return False, f"Value estimate too low. Minimum: {type_config['min_value']}"
            
            if value_estimate > type_config['max_value']:
                return False, f"Value estimate too high. Maximum: {type_config['max_value']}"
            
            # Validate description length
            description = labor_data['description']
            if len(description) < 10:
                return False, "Description must be at least 10 characters"
            
            if len(description) > 1000:
                return False, "Description must be less than 1000 characters"
            
            # Check daily submission limit
            identity_id = labor_data['identity_id']
            today_start = int(time.time()) - (int(time.time()) % 86400)  # Start of today
            
            today_submissions = self.db.query_one("""
                SELECT COUNT(*) as count FROM labor_records 
                WHERE identity_id = ? AND timestamp >= ?
            """, (identity_id, today_start))
            
            if today_submissions and today_submissions['count'] >= 10:
                return False, "Daily submission limit reached (10 per day)"
            
            return True, "Valid"
            
        except Exception as e:
            logger.error(f"Error validating labor submission: {e}")
            return False, f"Validation error: {str(e)}"

    def get_labor_type_info(self) -> Dict[str, Any]:
        """Get information about all available labor types."""
        return {
            'labor_types': self.LABOR_TYPES,
            'quality_levels': {
                'excellent': {'multiplier': 1.3, 'description': '90%+ community approval'},
                'good': {'multiplier': 1.1, 'description': '75-89% community approval'},
                'satisfactory': {'multiplier': 1.0, 'description': '60-74% community approval'},
                'poor': {'multiplier': 0.7, 'description': '40-59% community approval'},
                'rejected': {'multiplier': 0.0, 'description': 'Less than 40% approval'}
            },
            'experience_levels': {
                'master': {'multiplier': 1.4, 'requirement': '100+ verified labors'},
                'expert': {'multiplier': 1.2, 'requirement': '50-99 verified labors'},
                'skilled': {'multiplier': 1.1, 'requirement': '20-49 verified labors'},
                'apprentice': {'multiplier': 1.0, 'requirement': '5-19 verified labors'},
                'novice': {'multiplier': 0.9, 'requirement': '0-4 verified labors'}
            }
        }

    def calculate_comprehensive_reward(self, labor_record: Dict[str, Any], labor_id: str = None) -> Dict[str, Any]:
        """
        Calculate comprehensive reward with all bonuses and adjustments.
        
        Args:
            labor_record: Labor record dictionary
            labor_id: Optional labor ID for quality adjustment
            
        Returns:
            Dictionary with detailed reward breakdown
        """
        try:
            # Base reward
            base_tokens, base_etzem = self.calculate_base_reward(labor_record)
            
            # Quality adjustment (if labor_id provided)
            quality_multiplier = 1.0
            if labor_id:
                quality_multiplier = self.calculate_quality_adjustment(labor_id)
            
            # Seasonal bonus
            seasonal_multiplier = self.calculate_seasonal_bonus(
                labor_record['identity_id'], 
                labor_record.get('season_period', 0)
            )
            
            # Community impact bonus
            impact_multiplier = self.calculate_community_impact_bonus(labor_record)
            
            # Calculate final rewards
            total_multiplier = quality_multiplier * seasonal_multiplier * impact_multiplier
            final_tokens = round(base_tokens * total_multiplier, 2)
            final_etzem = int(base_etzem * total_multiplier)
            
            return {
                'base_tokens': base_tokens,
                'base_etzem': base_etzem,
                'quality_multiplier': quality_multiplier,
                'seasonal_multiplier': seasonal_multiplier,
                'impact_multiplier': impact_multiplier,
                'total_multiplier': total_multiplier,
                'final_tokens': final_tokens,
                'final_etzem': final_etzem,
                'labor_type': labor_record.get('labor_type'),
                'experience_level': self._get_experience_level(labor_record['identity_id'])
            }
            
        except Exception as e:
            logger.error(f"Error calculating comprehensive reward: {e}")
            return {
                'base_tokens': 0.0,
                'base_etzem': 0,
                'final_tokens': 0.0,
                'final_etzem': 0,
                'error': str(e)
            }
