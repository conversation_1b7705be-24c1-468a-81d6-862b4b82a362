#!/usr/bin/env python3
"""
Fix Selas Table Schema

This script adds missing columns to the selas table to fix dashboard errors.
"""

import os
import sys
import time
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table"""
    try:
        result = db.query(f"PRAGMA table_info({table_name})")
        columns = [row['name'] if isinstance(row, dict) else row[1] for row in result]
        return column_name in columns
    except Exception as e:
        logger.error(f"Error checking column existence: {e}")
        return False

def add_missing_selas_columns():
    """Add missing columns to the selas table"""
    try:
        logger.info("Adding missing columns to selas table...")
        
        # Columns to add with their definitions
        columns_to_add = [
            ('status', 'TEXT DEFAULT "active"'),
            ('mining_tier', 'TEXT DEFAULT "basic"'),
            ('mining_power', 'INTEGER DEFAULT 1'),
            ('mining_rewards_earned', 'REAL DEFAULT 0'),
            ('blocks_mined', 'INTEGER DEFAULT 0'),
            ('updated_at', 'INTEGER DEFAULT 0'),
            ('description', 'TEXT DEFAULT ""'),
            ('address', 'TEXT DEFAULT ""'),  # Add missing address column
            ('location', 'TEXT DEFAULT ""'),
            ('website', 'TEXT DEFAULT ""'),
            ('contact_info', 'TEXT DEFAULT ""'),
            ('verification_status', 'TEXT DEFAULT "pending"'),
            ('reputation_score', 'REAL DEFAULT 50.0'),
            ('total_earnings', 'REAL DEFAULT 0'),
            ('active_contracts', 'INTEGER DEFAULT 0')
        ]
        
        added_count = 0
        for column_name, column_def in columns_to_add:
            if not check_column_exists('selas', column_name):
                logger.info(f"Adding {column_name} column...")
                db.execute(f"ALTER TABLE selas ADD COLUMN {column_name} {column_def}")
                logger.info(f"✅ Added {column_name} column")
                added_count += 1
            else:
                logger.info(f"✅ {column_name} column already exists")
        
        logger.info(f"✅ Added {added_count} new columns to selas table")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error adding selas columns: {e}")
        return False

def verify_selas_table():
    """Verify the selas table structure"""
    try:
        logger.info("Verifying selas table structure...")
        
        # Get table info
        result = db.query("PRAGMA table_info(selas)")
        
        logger.info("Current selas table columns:")
        for row in result:
            if isinstance(row, dict):
                logger.info(f"  - {row['name']} ({row['type']})")
            else:
                logger.info(f"  - {row[1]} ({row[2]})")
        
        # Check for required columns
        required_columns = [
            'sela_id', 'identity_id', 'name', 'category', 'stake_amount',
            'status', 'mining_tier', 'mining_power', 'mining_rewards_earned',
            'blocks_mined', 'created_at', 'updated_at', 'metadata'
        ]
        
        existing_columns = [row['name'] if isinstance(row, dict) else row[1] for row in result]
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        
        if missing_columns:
            logger.warning(f"⚠️ Missing columns: {missing_columns}")
            return False
        else:
            logger.info("✅ All required columns present")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error verifying table structure: {e}")
        return False

def test_selas_query():
    """Test the selas query that was failing"""
    try:
        logger.info("Testing selas query...")
        
        # Test the query that was failing in the dashboard
        test_query = """
            SELECT *,
                   COALESCE(mining_tier, 'basic') as mining_tier,
                   COALESCE(mining_power, 1) as mining_power,
                   COALESCE(mining_rewards_earned, 0) as mining_rewards_earned,
                   COALESCE(blocks_mined, 0) as blocks_mined
            FROM selas
            WHERE identity_id = ?
            ORDER BY created_at DESC
        """
        
        # Use a test identity_id
        result = db.query(test_query, ('test_identity',))
        logger.info(f"✅ Selas query test successful - returned {len(result)} results")
        
        # Test the second query that was failing
        test_query2 = """
            SELECT s.*, i.name as owner_name
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.status = 'active'
            ORDER BY s.created_at DESC
            LIMIT 6
        """
        
        result2 = db.query(test_query2)
        logger.info(f"✅ Recent selas query test successful - returned {len(result2)} results")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing selas query: {e}")
        return False

def update_existing_selas():
    """Update existing selas records with default values"""
    try:
        logger.info("Updating existing selas records...")
        
        # Update records that have NULL values for new columns
        current_time = int(time.time())
        
        update_queries = [
            "UPDATE selas SET updated_at = ? WHERE updated_at IS NULL OR updated_at = 0",
            "UPDATE selas SET status = 'active' WHERE status IS NULL OR status = ''",
            "UPDATE selas SET mining_tier = 'basic' WHERE mining_tier IS NULL OR mining_tier = ''",
            "UPDATE selas SET mining_power = 1 WHERE mining_power IS NULL OR mining_power = 0",
            "UPDATE selas SET mining_rewards_earned = 0 WHERE mining_rewards_earned IS NULL",
            "UPDATE selas SET blocks_mined = 0 WHERE blocks_mined IS NULL",
            "UPDATE selas SET reputation_score = 50.0 WHERE reputation_score IS NULL",
            "UPDATE selas SET verification_status = 'pending' WHERE verification_status IS NULL OR verification_status = ''"
        ]
        
        for query in update_queries:
            if "updated_at" in query:
                db.execute(query, (current_time,))
            else:
                db.execute(query)
        
        logger.info("✅ Updated existing selas records")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating existing selas: {e}")
        return False

def main():
    """Main function to fix selas table"""
    logger.info("🔧 Starting Selas Table Fix...")
    logger.info("=" * 60)
    
    try:
        # Step 1: Add missing columns
        if not add_missing_selas_columns():
            logger.error("❌ Failed to add missing selas columns")
            return 1
        
        # Step 2: Update existing records
        if not update_existing_selas():
            logger.error("❌ Failed to update existing selas records")
            return 1
        
        # Step 3: Verify table structure
        if not verify_selas_table():
            logger.error("❌ Selas table verification failed")
            return 1
        
        # Step 4: Test queries
        if not test_selas_query():
            logger.error("❌ Selas query test failed")
            return 1
        
        logger.info("=" * 60)
        logger.info("🎉 Selas Table Fix Complete!")
        logger.info("✅ Dashboard should now load without selas table errors")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Fix failed: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
