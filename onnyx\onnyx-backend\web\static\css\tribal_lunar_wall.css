/* Tribal Lunar Wall Styles */
/* Biblical Calendar with Cyber-Tribal Aesthetic */

:root {
    /* ONNYX Color Palette */
    --cyber-cyan: #00fff7;
    --cyber-purple: #9a00ff;
    --onyx-black: #0a0a0a;
    --cyber-gold: #ffd700;
    --tribal-silver: #c0c0c0;
    --deep-space: #1a1a2e;
    --electric-blue: #16213e;
    --neon-green: #39ff14;
    --sabbath-gold: #ffd700;
    --feast-purple: #9a00ff;
    --new-moon-cyan: #00fff7;
    
    /* Typography */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Arial', sans-serif;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* Animations */
    --glow-cyan: 0 0 20px var(--cyber-cyan);
    --glow-purple: 0 0 20px var(--cyber-purple);
    --glow-gold: 0 0 20px var(--cyber-gold);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.lunar-wall-body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--onyx-black) 0%, var(--deep-space) 100%);
    color: var(--tribal-silver);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Night Mode Styles */
.lunar-wall-body.night-mode {
    background: linear-gradient(135deg, #000011 0%, #001122 100%);
}

/* Starfield Background */
.starfield {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.starfield.hidden {
    display: none;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: white;
    border-radius: 50%;
    animation: twinkle 3s infinite;
}

.star:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.star:nth-child(2) { top: 30%; left: 80%; animation-delay: 0.5s; }
.star:nth-child(3) { top: 60%; left: 20%; animation-delay: 1s; }
.star:nth-child(4) { top: 80%; left: 70%; animation-delay: 1.5s; }
.star:nth-child(5) { top: 10%; left: 50%; animation-delay: 2s; }
.star:nth-child(6) { top: 40%; left: 90%; animation-delay: 2.5s; }
.star:nth-child(7) { top: 70%; left: 30%; animation-delay: 3s; }
.star:nth-child(8) { top: 90%; left: 60%; animation-delay: 3.5s; }
.star:nth-child(9) { top: 50%; left: 5%; animation-delay: 4s; }
.star:nth-child(10) { top: 25%; left: 95%; animation-delay: 4.5s; }

@keyframes twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* Moving Moon */
.moving-moon {
    position: fixed;
    top: 10%;
    left: -50px;
    font-size: 40px;
    z-index: 2;
    animation: moonMove 60s linear infinite;
    pointer-events: none;
}

.moving-moon.hidden {
    display: none;
}

@keyframes moonMove {
    0% { left: -50px; }
    100% { left: calc(100% + 50px); }
}

/* Header Styles */
.lunar-header {
    background: linear-gradient(90deg, var(--onyx-black) 0%, var(--electric-blue) 100%);
    border-bottom: 2px solid var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.spinning-n {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 900;
    color: var(--onyx-black);
    animation: spin 10s linear infinite;
    box-shadow: var(--glow-cyan);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.logo-section h1 {
    font-size: 28px;
    color: var(--cyber-cyan);
    text-shadow: var(--glow-cyan);
    margin: 0;
}

.logo-section p {
    font-size: 14px;
    color: var(--cyber-purple);
    margin: 0;
}

.controls-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.control-btn {
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-cyan));
    border: none;
    border-radius: 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--onyx-black);
    font-family: var(--font-primary);
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glow-purple);
}

.tribal-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.tribal-selector label {
    color: var(--cyber-cyan);
    font-weight: bold;
    font-family: 'Orbitron', monospace;
}

.tribe-select {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--cyber-cyan);
    border-radius: 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.tribe-select:hover {
    border-color: var(--cyber-purple);
    box-shadow: 0 0 10px var(--cyber-purple);
    transform: translateY(-2px);
}

.tribe-select:focus {
    outline: none;
    border-color: var(--cyber-gold);
    box-shadow: 0 0 15px var(--cyber-gold);
}

.tribal-info-btn {
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-cyan));
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tribal-info-btn:hover {
    transform: scale(1.1) rotate(15deg);
    box-shadow: 0 0 15px var(--cyber-cyan);
}

.tribal-blessing-display {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(0, 255, 247, 0.1);
    border-radius: 10px;
    border-left: 4px solid var(--cyber-cyan);
    min-height: 60px;
    display: flex;
    align-items: center;
}

.tribal-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    width: 100%;
}

.tribal-symbol {
    font-size: 1.5rem;
    text-align: center;
}

.tribal-blessing-text {
    color: var(--cyber-cyan);
    font-style: italic;
    text-align: center;
    font-size: 0.9rem;
}

.tribal-reference {
    color: var(--cyber-purple);
    font-family: 'Orbitron', monospace;
    font-size: 0.8rem;
    text-align: center;
    opacity: 0.8;
}

/* Main Content */
.lunar-main {
    padding: var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
}

/* Current Month Section */
.current-month-section {
    margin-bottom: var(--spacing-xxl);
}

.month-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.nav-btn {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: var(--onyx-black);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--glow-cyan);
}

.month-info {
    text-align: center;
}

.month-info h2 {
    font-size: 48px;
    color: var(--cyber-gold);
    text-shadow: var(--glow-gold);
    margin-bottom: var(--spacing-sm);
}

.month-info p {
    font-size: 18px;
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-sm);
}

/* Enhanced month details styling */
.month-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: center;
}

.biblical-name {
    font-size: 1.2rem;
    color: var(--cyber-gold);
    font-weight: bold;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 10px var(--cyber-gold);
    margin-bottom: var(--spacing-xs);
}

.description {
    font-size: 1rem;
    color: var(--cyber-cyan);
    font-style: italic;
    text-align: center;
    line-height: 1.4;
}

.month-position {
    font-size: 0.9rem;
    color: var(--cyber-purple);
    font-family: 'Orbitron', monospace;
    opacity: 0.8;
}

.cycle-text {
    font-size: 1rem;
    color: var(--cyber-cyan);
    font-weight: bold;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.biblical-context {
    font-size: 0.8rem;
    color: var(--cyber-purple);
    font-style: italic;
    opacity: 0.9;
    display: block;
}

.meaning {
    font-size: 1rem;
    color: var(--cyber-cyan);
    font-style: italic;
    margin-bottom: var(--spacing-xs);
    text-align: center;
}

.references {
    font-size: 0.8rem;
    color: var(--cyber-purple);
    font-family: 'Orbitron', monospace;
    margin-bottom: var(--spacing-xs);
    text-align: center;
    opacity: 0.9;
}

.significance {
    font-size: 0.9rem;
    color: var(--cyber-gold);
    font-style: italic;
    text-align: center;
    margin-top: var(--spacing-xs);
    padding: var(--spacing-xs);
    background: rgba(255, 215, 0, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--cyber-gold);
}

.accuracy-note {
    font-size: 0.7rem;
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    display: block;
    margin-top: var(--spacing-xs);
    text-align: center;
    opacity: 0.8;
    background: rgba(0, 255, 247, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
}

.lunar-cycle-indicator {
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-cyan));
    border-radius: 20px;
    padding: var(--spacing-sm) var(--spacing-md);
    display: inline-block;
}

.lunar-cycle-indicator span {
    color: var(--onyx-black);
    font-weight: bold;
}

/* New Moon Display */
.new-moon-display {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 2px solid var(--new-moon-cyan);
    border-radius: 16px;
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    box-shadow: var(--glow-cyan);
    margin-bottom: var(--spacing-xl);
}

.moon-phase {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.moon-icon {
    font-size: 64px;
    animation: moonGlow 3s ease-in-out infinite;
}

@keyframes moonGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.5) drop-shadow(0 0 20px #00fff7); }
}

.shofar-icon {
    font-size: 32px;
    animation: shofarPulse 2s ease-in-out infinite;
}

@keyframes shofarPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.moon-info h3 {
    color: var(--cyber-gold);
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
}

.moon-info p {
    margin-bottom: var(--spacing-sm);
}

.biblical-ref {
    font-style: italic;
    color: var(--cyber-purple);
    font-size: 14px;
}

/* Calendar Section */
.calendar-section {
    margin-bottom: var(--spacing-xxl);
}

.calendar-container {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 2px solid var(--cyber-cyan);
    border-radius: 16px;
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

/* Tribal Watermark */
.tribal-watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 200px;
    opacity: 0.1;
    z-index: 1;
    pointer-events: none;
}

.benjamin-watermark { color: var(--cyber-cyan); }
.judah-watermark { color: var(--cyber-gold); }
.levi-watermark { color: var(--cyber-purple); }

/* Calendar Header */
.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 2;
}

.day-header {
    text-align: center;
    font-weight: bold;
    color: var(--cyber-cyan);
    padding: var(--spacing-sm);
    background: var(--onyx-black);
    border-radius: 8px;
}

/* Calendar Grid */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: var(--spacing-sm);
    position: relative;
    z-index: 2;
}

.calendar-day {
    aspect-ratio: 1;
    background: var(--onyx-black);
    border: 1px solid var(--tribal-silver);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.calendar-day:hover {
    border-color: var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
}

.calendar-day.today {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    font-weight: bold;
}

.calendar-day.sabbath {
    background: linear-gradient(45deg, var(--sabbath-gold), #ffed4e);
    color: var(--onyx-black);
    font-weight: bold;
    box-shadow: var(--glow-gold);
}

.calendar-day.feast-day {
    background: linear-gradient(45deg, var(--feast-purple), #b347ff);
    color: white;
    font-weight: bold;
}

.calendar-day.new-moon {
    background: linear-gradient(45deg, var(--new-moon-cyan), #47ffff);
    color: var(--onyx-black);
    font-weight: bold;
}

.day-number {
    font-size: 18px;
    font-weight: bold;
}

.day-indicator {
    font-size: 12px;
    margin-top: var(--spacing-xs);
}

/* Upcoming Events */
.upcoming-events {
    margin-bottom: var(--spacing-xxl);
}

.upcoming-events h2 {
    color: var(--cyber-cyan);
    font-size: 28px;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.event-item {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 1px solid var(--cyber-purple);
    border-radius: 12px;
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.3s ease;
    position: relative;
}

.event-item:hover {
    border-color: var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
    transform: translateY(-2px);
}

.event-icon {
    font-size: 32px;
    width: 60px;
    text-align: center;
}

.event-details {
    flex: 1;
}

.event-details h3 {
    color: var(--cyber-gold);
    margin-bottom: var(--spacing-xs);
}

.event-details p {
    margin-bottom: var(--spacing-xs);
}

.event-reference {
    font-style: italic;
    color: var(--cyber-purple);
    font-size: 12px;
}

.event-countdown {
    background: var(--cyber-cyan);
    color: var(--onyx-black);
    padding: var(--spacing-sm);
    border-radius: 8px;
    font-weight: bold;
}

.tribal-banner {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 24px;
    background: var(--cyber-gold);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.benjamin-banner {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
}

/* Enhanced Torah Scroll Popup */
.torah-scroll-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(8px);
    animation: fadeIn 0.3s ease-out;
}

.torah-scroll-popup.hidden {
    display: none;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scrollUnfurl {
    from {
        transform: scale(0.9) rotateY(-10deg);
        opacity: 0;
    }
    to {
        transform: scale(1) rotateY(0deg);
        opacity: 1;
    }
}

.scroll-container {
    background: linear-gradient(135deg, var(--onyx-black) 0%, #1a1a2e 50%, #0f0f23 100%);
    border: 3px solid var(--cyber-cyan);
    border-radius: 20px;
    max-width: 700px;
    width: 95%;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    color: #e8e8e8;
    box-shadow:
        0 0 40px var(--cyber-cyan),
        0 0 80px rgba(0, 255, 247, 0.3),
        inset 0 0 30px rgba(0, 255, 247, 0.1);
    transform: scale(0.9);
    animation: scrollUnfurl 0.5s ease-out forwards;
}

.scroll-header {
    background: linear-gradient(135deg, var(--onyx-black), #1a1a2e);
    color: var(--cyber-cyan);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--cyber-purple);
    border-radius: 20px 20px 0 0;
}

.scroll-ornament {
    font-size: 2rem;
    margin-right: 1rem;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 5px var(--cyber-cyan); }
    to { text-shadow: 0 0 20px var(--cyber-cyan), 0 0 30px var(--cyber-cyan); }
}

.scroll-header h3 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 0 15px var(--cyber-cyan);
    flex-grow: 1;
}

.close-btn {
    background: linear-gradient(135deg, transparent, rgba(154, 0, 255, 0.2));
    border: 2px solid var(--cyber-purple);
    color: var(--cyber-purple);
    font-size: 1.4rem;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--cyber-purple);
    color: var(--onyx-black);
    box-shadow: 0 0 20px var(--cyber-purple);
    transform: rotate(90deg);
}

.scroll-content {
    padding: var(--spacing-lg);
    line-height: 1.7;
}

.verse-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(0, 255, 247, 0.05);
    border-radius: 10px;
    border-left: 4px solid var(--cyber-cyan);
}

.scroll-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-style: italic;
    color: #f5f5f5;
    text-align: justify;
}

.scroll-reference {
    font-weight: bold;
    font-size: 0.9rem;
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    text-align: right;
    margin-top: 1rem;
}

/* Tribal Significance Section */
.tribal-significance-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.1), rgba(0, 255, 247, 0.05));
    border-radius: 12px;
    border: 1px solid var(--cyber-purple);
}

.tribal-significance h4 {
    color: var(--cyber-purple);
    font-family: 'Orbitron', monospace;
    margin-bottom: 1rem;
    text-shadow: 0 0 10px var(--cyber-purple);
}

.tribal-significance p {
    color: #e0e0e0;
    font-style: italic;
    margin-bottom: 1rem;
}

.current-tribe-blessing {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border-left: 3px solid var(--cyber-cyan);
}

.current-tribe-blessing h4 {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.current-tribe-blessing p {
    color: #f0f0f0;
    font-style: italic;
    margin-bottom: 0.5rem;
}

.current-tribe-blessing small {
    color: var(--cyber-purple);
    font-size: 0.8rem;
}

.scroll-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(154, 0, 255, 0.3);
}

.covenant-reminder {
    text-align: center;
    color: var(--cyber-cyan);
    font-style: italic;
    opacity: 0.8;
}

/* Tribal Banner Mini */
.tribal-banner-mini {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.8rem;
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-cyan));
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.1); opacity: 1; }
}

/* Lunar Accuracy Information Display */
.lunar-accuracy-info {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.1), rgba(154, 0, 255, 0.05));
    border-radius: 8px;
    border: 1px solid var(--cyber-cyan);
}

.accuracy-display {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: center;
}

.next-new-moon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.next-new-moon .label {
    font-size: 0.8rem;
    color: var(--cyber-purple);
    font-family: 'Orbitron', monospace;
    opacity: 0.9;
}

.next-new-moon .date {
    font-size: 0.9rem;
    color: var(--cyber-cyan);
    font-weight: bold;
    text-shadow: 0 0 5px var(--cyber-cyan);
}

.verification-badge {
    margin-top: var(--spacing-xs);
}

.verification-badge .badge {
    font-size: 0.7rem;
    color: var(--cyber-gold);
    font-family: 'Orbitron', monospace;
    background: rgba(255, 215, 0, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    border: 1px solid var(--cyber-gold);
    display: inline-block;
}

/* Real-Time Clock and Date Display */
.current-time-display {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.1), rgba(154, 0, 255, 0.05));
    border-radius: 12px;
    border: 1px solid var(--cyber-cyan);
}

.live-clock {
    text-align: center;
}

.current-time {
    font-size: 2rem;
    font-family: 'Orbitron', monospace;
    color: var(--cyber-cyan);
    font-weight: bold;
    text-shadow: 0 0 15px var(--cyber-cyan);
    margin-bottom: var(--spacing-xs);
}

.current-date {
    font-size: 1rem;
    color: var(--cyber-purple);
    font-weight: 500;
}

.hebrew-date-display {
    text-align: center;
}

.hebrew-date {
    font-size: 1.2rem;
    color: var(--cyber-gold);
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 0 10px var(--cyber-gold);
}

.hebrew-day {
    font-size: 1.4rem;
    margin-right: var(--spacing-xs);
}

.hebrew-month {
    margin-right: var(--spacing-xs);
}

.hebrew-year {
    font-size: 0.9rem;
    opacity: 0.8;
}

.current-observance {
    font-size: 0.9rem;
    padding: var(--spacing-xs);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.current-observance.active-observance {
    background: rgba(255, 215, 0, 0.2);
    color: var(--cyber-gold);
    border: 1px solid var(--cyber-gold);
    animation: glow-pulse 2s infinite;
}

.current-observance.no-observance {
    background: rgba(154, 0, 255, 0.1);
    color: var(--cyber-purple);
    border: 1px solid var(--cyber-purple);
}

@keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 5px var(--cyber-gold); }
    50% { box-shadow: 0 0 20px var(--cyber-gold), 0 0 30px var(--cyber-gold); }
}

/* Current Observance Status */
.observance-status {
    margin-bottom: var(--spacing-md);
}

.current-status {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(0, 255, 247, 0.1));
    border: 2px solid var(--cyber-gold);
    animation: active-observance 3s infinite;
}

.status-indicator.inactive {
    background: rgba(154, 0, 255, 0.1);
    border: 1px solid var(--cyber-purple);
}

@keyframes active-observance {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.status-icon {
    font-size: 1.5rem;
}

.status-text {
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    color: var(--cyber-cyan);
}

/* Enhanced Event Items */
.event-item {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.event-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 247, 0.3);
}

.event-countdown {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.countdown-text {
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    color: var(--cyber-cyan);
    font-size: 0.9rem;
    text-align: center;
    text-shadow: 0 0 5px var(--cyber-cyan);
}

.loading-events {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    color: var(--cyber-purple);
}

.loading-spinner {
    font-size: 2rem;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .current-time-display {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .current-time {
        font-size: 1.5rem;
    }

    .hebrew-date {
        font-size: 1rem;
    }

    .status-indicator {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
    }

    .lunar-accuracy-info {
        margin-top: var(--spacing-sm);
        padding: var(--spacing-xs);
    }

    .next-new-moon .date {
        font-size: 0.8rem;
    }

    .verification-badge .badge {
        font-size: 0.6rem;
        padding: 1px 6px;
    }
}

/* Footer */
.lunar-footer {
    background: var(--onyx-black);
    border-top: 1px solid var(--cyber-cyan);
    padding: var(--spacing-lg);
    text-align: center;
    position: relative;
    z-index: 10;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--tribal-silver);
    text-decoration: none;
    transition: color 0.3s ease;
    cursor: pointer;
}

.footer-links a:hover {
    color: var(--cyber-cyan);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .month-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .new-moon-display {
        flex-direction: column;
        text-align: center;
    }
    
    .calendar-grid {
        gap: var(--spacing-xs);
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .tribal-watermark {
        font-size: 100px;
    }
}
