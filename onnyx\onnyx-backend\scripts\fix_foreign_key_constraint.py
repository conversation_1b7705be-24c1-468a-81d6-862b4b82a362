#!/usr/bin/env python3
"""
Fix Foreign Key Constraint Issue

This script fixes the foreign key constraint issue in the identities table
by populating the nations table with the 12 tribes of Israel and witness nations.
"""

import os
import sys
import time
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def populate_nations_table():
    """Populate the nations table with the 12 tribes of Israel"""
    try:
        logger.info("Populating nations table with 12 tribes of Israel...")

        # First, disable foreign key constraints temporarily
        db.execute("PRAGMA foreign_keys = OFF")

        # 12 Tribes of Israel
        tribes = [
            ("JU", "Judah", "The tribe of Judah, from which kings come"),
            ("<PERSON><PERSON>", "<PERSON>", "The tribe of <PERSON>, the beloved of the Lord"),
            ("<PERSON><PERSON>", "<PERSON>", "The tribe of <PERSON>, the priestly tribe"),
            ("<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "The tribe of Issachar, strong donkey"),
            ("<PERSON><PERSON>", "Zebulun", "The tribe of Zebulun, dwelling at the seashore"),
            ("RE", "<PERSON>", "The tribe of <PERSON>, the firstborn"),
            ("SI", "Simeon", "The tribe of Simeon, hearing"),
            ("GA", "Gad", "The tribe of Gad, a troop shall overcome"),
            ("AS", "Asher", "The tribe of <PERSON>, happy"),
            ("NA", "Naphtali", "The tribe of Naphtali, wrestling"),
            ("DA", "Dan", "The tribe of Dan, judging"),
            ("EP", "Ephraim", "The tribe of Ephraim, fruitful"),
            ("MA", "Manasseh", "The tribe of Manasseh, causing to forget")
        ]

        current_time = int(time.time())

        for nation_id, name, description in tribes:
            # Check if nation already exists
            existing = db.query_one("SELECT nation_id FROM nations WHERE nation_id = ?", (nation_id,))

            if not existing:
                # Use a placeholder founder_id that we'll fix later
                founder_id = "system"
                
                # Insert the nation
                db.execute("""
                    INSERT INTO nations (nation_id, name, description, founder_id, metadata, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    nation_id,
                    name,
                    description,
                    founder_id,
                    f'{{"tribe": "{name}", "type": "israelite", "biblical": true}}',
                    "active",
                    current_time,
                    current_time
                ))
                
                logger.info(f"✅ Added nation: {nation_id} - {name}")
            else:
                logger.info(f"✅ Nation already exists: {nation_id} - {name}")
        
        # Add a default witness nation
        witness_nations = [
            ("WN", "Witness Nations", "Nations that witness to the covenant"),
            ("SY", "System", "System-generated nation for administrative purposes")
        ]
        
        for nation_id, name, description in witness_nations:
            existing = db.query_one("SELECT nation_id FROM nations WHERE nation_id = ?", (nation_id,))

            if not existing:
                founder_id = "system"
                
                db.execute("""
                    INSERT INTO nations (nation_id, name, description, founder_id, metadata, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    nation_id,
                    name,
                    description,
                    founder_id,
                    f'{{"type": "witness", "biblical": false}}',
                    "active",
                    current_time,
                    current_time
                ))
                
                logger.info(f"✅ Added witness nation: {nation_id} - {name}")

        # Re-enable foreign key constraints
        db.execute("PRAGMA foreign_keys = ON")

        return True
        
    except Exception as e:
        logger.error(f"❌ Error populating nations table: {e}")
        return False

def get_nation_id_for_tribe(tribe_name):
    """Get the nation_id for a given tribe name"""
    tribe_mapping = {
        "judah": "JU",
        "benjamin": "BE", 
        "levi": "LE",
        "issachar": "IS",
        "zebulun": "ZE",
        "reuben": "RE",
        "simeon": "SI",
        "gad": "GA",
        "asher": "AS",
        "naphtali": "NA",
        "dan": "DA",
        "ephraim": "EP",
        "manasseh": "MA"
    }
    
    return tribe_mapping.get(tribe_name.lower(), "SY")  # Default to System

def test_identity_creation_with_nation():
    """Test identity creation with proper nation_id"""
    try:
        logger.info("Testing identity creation with nation_id...")
        
        # Test data
        test_identity = {
            'identity_id': 'TEST_NATION_FIX_123',
            'name': 'Test Nation User',
            'email': '<EMAIL>',
            'public_key': 'test_public_key_nation_123',
            'nation_id': 'JU',  # Judah
            'tribal_affiliation': 'judah',
            'status': 'active',
            'role_class': 'citizen',
            'verification_level': 1,
            'covenant_accepted': True,
            'eden_mode_completed': True,
            'metadata': '{"test": true, "nation_fix": true}',
            'created_at': int(time.time()),
            'updated_at': int(time.time())
        }
        
        # Insert test identity
        columns = ', '.join(test_identity.keys())
        placeholders = ', '.join(['?' for _ in test_identity.keys()])
        values = list(test_identity.values())
        
        db.execute(f"INSERT INTO identities ({columns}) VALUES ({placeholders})", values)
        
        # Verify insertion
        result = db.query_one("SELECT identity_id, name, nation_id, tribal_affiliation FROM identities WHERE identity_id = ?", 
                             (test_identity['identity_id'],))
        
        if result:
            logger.info("✅ Test identity creation with nation_id successful")
            logger.info(f"   Identity: {result[0] if isinstance(result, tuple) else result['identity_id']}")
            logger.info(f"   Name: {result[1] if isinstance(result, tuple) else result['name']}")
            logger.info(f"   Nation ID: {result[2] if isinstance(result, tuple) else result['nation_id']}")
            logger.info(f"   Tribal Affiliation: {result[3] if isinstance(result, tuple) else result['tribal_affiliation']}")
            
            # Clean up test data
            db.execute("DELETE FROM identities WHERE identity_id = ?", (test_identity['identity_id'],))
            logger.info("✅ Test data cleaned up")
            return True
        else:
            logger.error("❌ Test identity creation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing identity creation: {e}")
        return False

def update_eden_mode_to_use_nation_id():
    """Update existing identities to have proper nation_id based on tribal_affiliation"""
    try:
        logger.info("Updating existing identities with nation_id...")
        
        # Get identities without nation_id
        identities = db.query("SELECT identity_id, tribal_affiliation FROM identities WHERE nation_id IS NULL OR nation_id = ''")
        
        updated_count = 0
        for identity in identities:
            if isinstance(identity, dict):
                identity_id = identity['identity_id']
                tribal_affiliation = identity.get('tribal_affiliation', '')
            else:
                identity_id = identity[0]
                tribal_affiliation = identity[1] if len(identity) > 1 else ''
            
            if tribal_affiliation:
                nation_id = get_nation_id_for_tribe(tribal_affiliation)
                db.execute("UPDATE identities SET nation_id = ? WHERE identity_id = ?", (nation_id, identity_id))
                updated_count += 1
                logger.info(f"✅ Updated {identity_id}: {tribal_affiliation} -> {nation_id}")
        
        logger.info(f"✅ Updated {updated_count} identities with nation_id")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating identities: {e}")
        return False

def main():
    """Main function to fix foreign key constraint"""
    logger.info("🔧 Starting Foreign Key Constraint Fix...")
    logger.info("=" * 60)
    
    try:
        # Step 1: Populate nations table
        if not populate_nations_table():
            logger.error("❌ Failed to populate nations table")
            return 1
        
        # Step 2: Update existing identities
        if not update_eden_mode_to_use_nation_id():
            logger.error("❌ Failed to update existing identities")
            return 1
        
        # Step 3: Test identity creation
        if not test_identity_creation_with_nation():
            logger.error("❌ Identity creation test failed")
            return 1
        
        # Step 4: Verify nations table
        nations = db.query("SELECT nation_id, name FROM nations ORDER BY nation_id")
        logger.info(f"✅ Nations table now has {len(nations)} entries:")
        for nation in nations:
            if isinstance(nation, dict):
                logger.info(f"   - {nation['nation_id']}: {nation['name']}")
            else:
                logger.info(f"   - {nation[0]}: {nation[1]}")
        
        logger.info("=" * 60)
        logger.info("🎉 Foreign Key Constraint Fix Complete!")
        logger.info("✅ Eden Mode identity creation should now work without foreign key errors")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Fix failed: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
