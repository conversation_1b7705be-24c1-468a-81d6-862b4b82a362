#!/usr/bin/env python3
"""
ONNYX Platform User Experience Test
Tests the complete user journey for both Israelite and Witness Nation registration flows.
"""

import time
import sys
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from webdriver_manager.chrome import ChromeDriverManager
import json

class ONNYXUserExperienceTest:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.base_url = "http://localhost:5000"
        self.test_results = []
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 20)
            print("✅ Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize WebDriver: {e}")
            return False
    
    def log_test_result(self, step, success, message, error=None):
        """Log test results for reporting"""
        result = {
            "step": step,
            "success": success,
            "message": message,
            "error": str(error) if error else None,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {step}: {message}")
        if error:
            print(f"   Error: {error}")
    
    def safe_click(self, element, description="element"):
        """Safely click an element with error handling"""
        try:
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            element.click()
            return True
        except ElementClickInterceptedException:
            try:
                self.driver.execute_script("arguments[0].click();", element)
                return True
            except Exception as e:
                print(f"❌ Failed to click {description}: {e}")
                return False
        except Exception as e:
            print(f"❌ Failed to click {description}: {e}")
            return False
    
    def wait_and_click(self, by, value, description, timeout=20):
        """Wait for element and click it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return self.safe_click(element, description)
        except TimeoutException:
            print(f"❌ Timeout waiting for {description}: {by}={value}")
            return False
    
    def test_navigation_to_eden_mode(self):
        """Test navigation to Eden Mode"""
        try:
            self.driver.get(self.base_url)
            time.sleep(2)
            
            # Try to find Eden Mode link on homepage
            try:
                eden_link = self.driver.find_element(By.PARTIAL_LINK_TEXT, "Eden Mode")
                self.safe_click(eden_link, "Eden Mode link")
                time.sleep(2)
            except:
                # Direct navigation to Eden Mode
                self.driver.get(f"{self.base_url}/auth/eden-mode")
                time.sleep(2)
            
            # Verify we're on Eden Mode page
            if "eden-mode" in self.driver.current_url or "Choose Your Covenant Path" in self.driver.page_source:
                self.log_test_result("Navigation", True, "Successfully navigated to Eden Mode")
                return True
            else:
                self.log_test_result("Navigation", False, "Could not reach Eden Mode page")
                return False
                
        except Exception as e:
            self.log_test_result("Navigation", False, "Failed to navigate to Eden Mode", e)
            return False
    
    def test_israelite_flow(self):
        """Test complete Israelite registration flow"""
        print("\n🔹 Testing Israelite Registration Flow")
        
        try:
            # Step 1: Select Israelite path
            if not self.wait_and_click(By.XPATH, "//button[contains(text(), 'Begin Israelite Journey') or contains(@onclick, 'israelite')]", "Israelite Journey button"):
                self.log_test_result("Israelite Flow", False, "Could not find Israelite button")
                return False
            
            time.sleep(3)
            
            # Wait for tribal selection page
            try:
                WebDriverWait(self.driver, 10).until(
                    lambda driver: "israelite-step2" in driver.current_url or "tribal" in driver.page_source.lower()
                )
                self.log_test_result("Israelite Step 1", True, "Reached tribal selection page")
            except:
                self.log_test_result("Israelite Step 1", False, "Did not reach tribal selection page")
                return False
            
            # Step 2: Select a tribe
            tribe_buttons = self.driver.find_elements(By.XPATH, "//button[contains(@onclick, 'selectTribe') or contains(text(), 'Select')]")
            if tribe_buttons:
                self.safe_click(tribe_buttons[0], "First tribe")
                time.sleep(1)
                
                # Look for proceed button
                if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Proceed') or contains(text(), 'Continue')]", "Proceed button", 10):
                    self.log_test_result("Israelite Step 2", True, "Successfully selected tribe")
                    time.sleep(3)
                else:
                    self.log_test_result("Israelite Step 2", False, "Could not proceed after tribe selection")
                    return False
            else:
                self.log_test_result("Israelite Step 2", False, "No tribe selection buttons found")
                return False
            
            # Step 3: Heritage verification - use developer bypass
            # Look for developer bypass button
            if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Developer') and contains(text(), 'Bypass')]", "Developer Bypass button", 10):
                time.sleep(1)
                
                # Enter bypass password
                password_input = self.driver.find_element(By.ID, "bypassPassword")
                password_input.send_keys("Israel United In Christ")
                
                # Click validate button
                if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Bypass') or contains(text(), 'Validate')]", "Validate bypass button"):
                    time.sleep(3)
                    
                    # Look for success modal and proceed
                    if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Continue') or contains(text(), 'Proceed')]", "Continue button in modal", 10):
                        self.log_test_result("Israelite Step 3", True, "Successfully used developer bypass")
                        time.sleep(3)
                    else:
                        # Try to proceed anyway
                        self.log_test_result("Israelite Step 3", True, "Bypass activated, continuing")
                else:
                    self.log_test_result("Israelite Step 3", False, "Could not validate bypass")
                    return False
            else:
                self.log_test_result("Israelite Step 3", False, "Could not find developer bypass button")
                return False
            
            # Step 4: Complete inscription
            try:
                WebDriverWait(self.driver, 10).until(
                    lambda driver: "step5" in driver.current_url or "inscription" in driver.page_source.lower()
                )
                
                # Fill out inscription form
                try:
                    identity_input = self.driver.find_element(By.ID, "identityName")
                    identity_input.clear()
                    identity_input.send_keys("Test Israelite User")
                except:
                    pass
                
                try:
                    email_input = self.driver.find_element(By.ID, "email")
                    email_input.clear()
                    email_input.send_keys("<EMAIL>")
                except:
                    pass
                
                # Submit registration
                if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Complete') or contains(text(), 'Register') or contains(text(), 'Submit')]", "Complete Registration button"):
                    self.log_test_result("Israelite Step 4", True, "Successfully completed Israelite registration")
                    time.sleep(3)
                    return True
                else:
                    self.log_test_result("Israelite Step 4", False, "Could not complete registration")
                    return False
                    
            except:
                self.log_test_result("Israelite Step 4", False, "Could not reach inscription page")
                return False
                
        except Exception as e:
            self.log_test_result("Israelite Flow", False, "Unexpected error in Israelite flow", e)
            return False
    
    def test_witness_nation_flow(self):
        """Test complete Witness Nation registration flow"""
        print("\n🔹 Testing Witness Nation Registration Flow")
        
        try:
            # Navigate back to Eden Mode
            self.driver.get(f"{self.base_url}/auth/eden-mode")
            time.sleep(3)
            
            # Step 1: Select Witness Nation path
            if not self.wait_and_click(By.XPATH, "//button[contains(text(), 'Begin Witness Journey') or contains(@onclick, 'witness')]", "Witness Journey button"):
                self.log_test_result("Witness Flow", False, "Could not find Witness Nation button")
                return False
            
            time.sleep(3)
            
            # Wait for nation selection page
            try:
                WebDriverWait(self.driver, 10).until(
                    lambda driver: "witness-step2" in driver.current_url or "nation" in driver.page_source.lower()
                )
                self.log_test_result("Witness Step 1", True, "Reached nation selection page")
            except:
                self.log_test_result("Witness Step 1", False, "Did not reach nation selection page")
                return False
            
            # Step 2: Select a nation
            nation_buttons = self.driver.find_elements(By.XPATH, "//button[contains(@onclick, 'selectNation') or contains(text(), 'Select')]")
            if nation_buttons:
                self.safe_click(nation_buttons[0], "First nation")
                time.sleep(1)
                
                # Look for proceed button
                if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Proceed') or contains(text(), 'Continue')]", "Proceed button", 10):
                    self.log_test_result("Witness Step 2", True, "Successfully selected nation")
                    time.sleep(3)
                else:
                    self.log_test_result("Witness Step 2", False, "Could not proceed after nation selection")
                    return False
            else:
                self.log_test_result("Witness Step 2", False, "No nation selection buttons found")
                return False
            
            # Step 3: Complete registration (witnesses skip verification)
            try:
                # Fill out registration form
                try:
                    identity_input = self.driver.find_element(By.ID, "identityName")
                    identity_input.clear()
                    identity_input.send_keys("Test Witness User")
                except:
                    pass
                
                try:
                    email_input = self.driver.find_element(By.ID, "email")
                    email_input.clear()
                    email_input.send_keys("<EMAIL>")
                except:
                    pass
                
                # Submit registration
                if self.wait_and_click(By.XPATH, "//button[contains(text(), 'Complete') or contains(text(), 'Register') or contains(text(), 'Submit')]", "Complete Registration button"):
                    self.log_test_result("Witness Step 3", True, "Successfully completed Witness Nation registration")
                    time.sleep(3)
                    return True
                else:
                    self.log_test_result("Witness Step 3", False, "Could not complete registration")
                    return False
                    
            except Exception as e:
                self.log_test_result("Witness Step 3", False, "Error completing witness registration", e)
                return False
                
        except Exception as e:
            self.log_test_result("Witness Flow", False, "Unexpected error in Witness flow", e)
            return False
    
    def run_full_test(self):
        """Run complete user experience test"""
        print("🚀 Starting ONNYX User Experience Tests")
        print("=" * 60)
        
        if not self.setup_driver():
            return False
        
        try:
            # Test navigation
            if not self.test_navigation_to_eden_mode():
                return False
            
            # Test Israelite flow
            self.test_israelite_flow()
            
            # Test Witness Nation flow
            self.test_witness_nation_flow()
            
            # Generate report
            self.generate_report()
            
            return True
            
        except Exception as e:
            print(f"❌ Unexpected error during testing: {e}")
            return False
        finally:
            if self.driver:
                input("Press Enter to close browser...")  # Pause to review results
                self.driver.quit()
    
    def generate_report(self):
        """Generate test report"""
        print("\n📊 Test Results Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "No tests run")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['step']}: {result['message']}")
                    if result["error"]:
                        print(f"    Error: {result['error']}")
        
        # Save detailed report
        try:
            with open("test_report.json", "w") as f:
                json.dump(self.test_results, f, indent=2)
            print("\n📄 Detailed report saved to test_report.json")
        except:
            pass

def main():
    """Main function"""
    print("ONNYX Platform User Experience Test Suite")
    print("This will test both Israelite and Witness Nation registration flows")
    
    # Quick server check
    try:
        import requests
        response = requests.get("http://localhost:5000", timeout=5)
        print(f"✅ Server is running (Status: {response.status_code})")
    except:
        print("❌ Server check failed - please ensure the Flask app is running on localhost:5000")
        return False
    
    # Run tests
    tester = ONNYXUserExperienceTest()
    return tester.run_full_test()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
