"""
API Security Middleware for ONNYX Platform
Comprehensive security for all API endpoints
"""

import json
import time
import hashlib
import secrets
import re
from functools import wraps
from flask import request, jsonify, session, current_app
from web.auth_decorators import get_current_user, log_security_event
from shared.db.db import db

class APISecurityManager:
    """Centralized API security management"""
    
    # Rate limiting configuration
    RATE_LIMITS = {
        'default': {'requests': 100, 'window': 3600},  # 100 requests per hour
        'auth': {'requests': 10, 'window': 900},       # 10 requests per 15 minutes
        'registration': {'requests': 5, 'window': 3600}, # 5 registrations per hour
        'admin': {'requests': 1000, 'window': 3600}    # 1000 requests per hour for admins
    }
    
    @staticmethod
    def generate_csrf_token():
        """Generate CSRF token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_csrf_token(token):
        """Validate CSRF token"""
        session_token = session.get('csrf_token')
        if not session_token or not token:
            return False
        return secrets.compare_digest(session_token, token)
    
    @staticmethod
    def check_rate_limit(identifier, limit_type='default'):
        """Check if request is within rate limits"""
        try:
            limit_config = APISecurityManager.RATE_LIMITS.get(limit_type, APISecurityManager.RATE_LIMITS['default'])
            window_start = int(time.time()) - limit_config['window']
            
            # Count recent requests
            count = db.query_one("""
                SELECT COUNT(*) as count FROM api_rate_limits 
                WHERE identifier = ? AND timestamp > ?
            """, (identifier, window_start))
            
            current_count = count['count'] if count else 0
            
            # Record this request
            db.execute("""
                INSERT INTO api_rate_limits (identifier, timestamp, limit_type)
                VALUES (?, ?, ?)
            """, (identifier, int(time.time()), limit_type))
            
            # Clean old records
            db.execute("""
                DELETE FROM api_rate_limits WHERE timestamp < ?
            """, (window_start,))
            
            return current_count < limit_config['requests']
            
        except Exception as e:
            # If rate limiting fails, allow request but log error
            log_security_event('RATE_LIMIT_ERROR', None, {'error': str(e)})
            return True
    
    @staticmethod
    def sanitize_input(data):
        """Sanitize input data to prevent XSS and injection attacks"""
        if isinstance(data, str):
            # Remove potentially dangerous characters
            data = re.sub(r'[<>"\']', '', data)
            # Limit length
            data = data[:1000]
            return data.strip()
        elif isinstance(data, dict):
            return {k: APISecurityManager.sanitize_input(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [APISecurityManager.sanitize_input(item) for item in data]
        else:
            return data
    
    @staticmethod
    def validate_input_types(data, schema):
        """Validate input data against schema"""
        errors = []
        
        for field, field_config in schema.items():
            value = data.get(field)
            
            # Check required fields
            if field_config.get('required', False) and not value:
                errors.append(f"Field '{field}' is required")
                continue
            
            if value is not None:
                # Check type
                expected_type = field_config.get('type')
                if expected_type and not isinstance(value, expected_type):
                    errors.append(f"Field '{field}' must be of type {expected_type.__name__}")
                
                # Check length for strings
                if isinstance(value, str):
                    min_len = field_config.get('min_length', 0)
                    max_len = field_config.get('max_length', 1000)
                    if len(value) < min_len:
                        errors.append(f"Field '{field}' must be at least {min_len} characters")
                    if len(value) > max_len:
                        errors.append(f"Field '{field}' must be at most {max_len} characters")
                
                # Check pattern for strings
                pattern = field_config.get('pattern')
                if pattern and isinstance(value, str):
                    if not re.match(pattern, value):
                        errors.append(f"Field '{field}' format is invalid")
        
        return errors

def require_api_auth(f):
    """Decorator to require authentication for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check authentication
        user = get_current_user()
        if not user:
            log_security_event('API_AUTH_FAILED', None, {
                'endpoint': request.endpoint,
                'ip': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')[:100]
            })
            return jsonify({'error': 'Authentication required'}), 401
        
        # Check rate limiting
        rate_limit_type = 'admin' if user.get('is_admin') else 'default'
        if not APISecurityManager.check_rate_limit(user['identity_id'], rate_limit_type):
            log_security_event('API_RATE_LIMIT_EXCEEDED', user['identity_id'], {
                'endpoint': request.endpoint,
                'limit_type': rate_limit_type
            })
            return jsonify({'error': 'Rate limit exceeded'}), 429
        
        # Log API access
        log_security_event('API_ACCESS', user['identity_id'], {
            'endpoint': request.endpoint,
            'method': request.method,
            'user_role': user['role']
        })
        
        return f(*args, **kwargs)
    return decorated_function

def require_csrf_token(f):
    """Decorator to require CSRF token for state-changing operations"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
            csrf_token = request.headers.get('X-CSRF-Token') or request.form.get('csrf_token')
            
            if not APISecurityManager.validate_csrf_token(csrf_token):
                log_security_event('CSRF_TOKEN_INVALID', session.get('identity_id'), {
                    'endpoint': request.endpoint,
                    'ip': request.remote_addr
                })
                return jsonify({'error': 'Invalid CSRF token'}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def validate_api_input(schema):
    """Decorator to validate API input against schema"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get request data
                if request.is_json:
                    data = request.get_json() or {}
                else:
                    data = request.form.to_dict()
                
                # Sanitize input
                data = APISecurityManager.sanitize_input(data)
                
                # Validate against schema
                errors = APISecurityManager.validate_input_types(data, schema)
                if errors:
                    log_security_event('API_INPUT_VALIDATION_FAILED', session.get('identity_id'), {
                        'endpoint': request.endpoint,
                        'errors': errors
                    })
                    return jsonify({'error': 'Input validation failed', 'details': errors}), 400
                
                # Add sanitized data to request
                request.validated_data = data
                
                return f(*args, **kwargs)
                
            except Exception as e:
                log_security_event('API_INPUT_PROCESSING_ERROR', session.get('identity_id'), {
                    'endpoint': request.endpoint,
                    'error': str(e)
                })
                return jsonify({'error': 'Input processing failed'}), 400
        
        return decorated_function
    return decorator

def secure_api_response(data):
    """Secure API response by removing sensitive data"""
    if isinstance(data, dict):
        # Remove sensitive fields
        sensitive_fields = ['password', 'private_key', 'secret', 'token', 'hash']
        cleaned_data = {}
        
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_fields):
                cleaned_data[key] = '[REDACTED]'
            else:
                cleaned_data[key] = secure_api_response(value)
        
        return cleaned_data
    elif isinstance(data, list):
        return [secure_api_response(item) for item in data]
    else:
        return data

def api_error_handler(error_message, status_code=500):
    """Secure error handler that doesn't expose sensitive information"""
    # Log the actual error
    log_security_event('API_ERROR', session.get('identity_id'), {
        'error': error_message,
        'endpoint': request.endpoint,
        'status_code': status_code
    })
    
    # Return sanitized error message
    if status_code >= 500:
        # Don't expose internal errors
        return jsonify({'error': 'Internal server error'}), status_code
    else:
        # Client errors can be more specific
        return jsonify({'error': error_message}), status_code
