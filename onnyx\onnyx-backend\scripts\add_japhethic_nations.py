#!/usr/bin/env python3
"""
Add Japhethic Nations (Genesis 10:2-4)
Adds the 7 sons of Japheth and their branches as specified in Genesis 10:2-4
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
import time

def add_japhethic_nations():
    """Add Japhethic nations from Genesis 10:2-4."""
    print("🌍 ADDING JAPHETHIC NATIONS (Genesis 10:2-4)")
    print("=" * 50)
    
    try:
        # The 7 sons of Japheth and their branches
        japhethic_nations = [
            # The 7 sons of Japheth (Genesis 10:2)
            ('<PERSON>', 'Gomer', 'House of Gomer', '🏔️', 'Son of <PERSON><PERSON><PERSON>, father of northern peoples'),
            ('<PERSON><PERSON>', 'Magog', 'House of Magog', '🗻', 'Son of Japheth, the land of Gog'),
            ('MD', 'Madai', 'House of Madai', '🏺', 'Son of Japheth, the Medes'),
            ('J<PERSON>', '<PERSON><PERSON>', 'House of Javan', '🌊', 'Son of <PERSON><PERSON><PERSON>, father of the Greeks'),
            ('<PERSON><PERSON>', '<PERSON><PERSON>', 'House of Tubal', '⚒️', 'Son of <PERSON><PERSON><PERSON>, the metalworkers'),
            ('<PERSON>', 'Meshech', 'House of Meshech', '🏹', 'Son of Japheth, the archers'),
            ('TR', 'Tiras', 'House of Tiras', '⚓', 'Son of Japheth, the sea peoples'),
            
            # Sons of Gomer (Genesis 10:3)
            ('AS', 'Ashkenaz', 'House of Ashkenaz', '🔥', 'Son of Gomer, the fire spreaders'),
            ('RI', 'Riphath', 'House of Riphath', '🏔️', 'Son of Gomer, the mountain dwellers'),
            ('TO', 'Togarmah', 'House of Togarmah', '🐎', 'Son of Gomer, the horsemen'),
            
            # Sons of Javan (Genesis 10:4)
            ('ES', 'Elishah', 'House of Elishah', '🏝️', 'Son of Javan, the island peoples'),
            ('TA', 'Tarshish', 'House of Tarshish', '🚢', 'Son of Javan, the merchants of the sea'),
            ('KI', 'Kittim', 'House of Kittim', '🏛️', 'Son of Javan, the bruisers'),
            ('DO', 'Dodanim', 'House of Dodanim', '🌊', 'Son of Javan, the leaders')
        ]
        
        print(f"Adding {len(japhethic_nations)} Japhethic nations...")
        
        for nation_code, nation_name, tribe_name, flag_symbol, description in japhethic_nations:
            # Check if exists
            existing = db.query_one("SELECT nation_code FROM biblical_nations WHERE nation_code = ?", (nation_code,))
            if existing:
                # Update existing
                db.execute("""
                    UPDATE biblical_nations 
                    SET nation_name = ?, tribe_name = ?, flag_symbol = ?, description = ?,
                        ancestral_group = 'Japhethic Nations', 
                        ancestral_description = 'Sons of Japheth and their descendants (Genesis 10:2-4)',
                        historical_connection = 'Descendants of Japheth who populated Europe and parts of Asia'
                    WHERE nation_code = ?
                """, (nation_name, tribe_name, flag_symbol, description, nation_code))
                print(f"   ✅ Updated {nation_name}")
            else:
                # Insert new
                db.execute("""
                    INSERT INTO biblical_nations (
                        nation_code, nation_name, tribe_name, nation_type, flag_symbol, description,
                        ancestral_group, ancestral_description, historical_connection
                    ) VALUES (?, ?, ?, 'witness', ?, ?, 'Japhethic Nations', 
                             'Sons of Japheth and their descendants (Genesis 10:2-4)',
                             'Descendants of Japheth who populated Europe and parts of Asia')
                """, (nation_code, nation_name, tribe_name, flag_symbol, description))
                print(f"   ✅ Added {nation_name}")
        
        # Verify the results
        print("\n🎉 VERIFICATION")
        print("-" * 40)
        
        japhethic_count = db.query_one("SELECT COUNT(*) as count FROM biblical_nations WHERE ancestral_group = 'Japhethic Nations'")['count']
        print(f"   Japhethic Nations: {japhethic_count}/14 {'✅' if japhethic_count == 14 else '❌'}")
        
        # Show all ancestral groups
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as count
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)
        
        print("\n📋 All Biblical Ancestral Groups:")
        total_witness = 0
        for group in ancestral_groups:
            print(f"   {group['ancestral_group']:20s}: {group['count']} nations")
            total_witness += group['count']
        
        print(f"\n✅ Total witness nations: {total_witness}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_japhethic_nations()
    if success:
        print("\n✅ Japhethic nations added successfully!")
    else:
        print("\n❌ Japhethic nations addition failed!")
