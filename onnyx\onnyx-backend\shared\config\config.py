"""
Onnyx Configuration Module

This module provides a centralized configuration system for the Onnyx blockchain.
It supports default configurations in code and overrides in JSON/YAML files.
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, List, Union

# Set up logging
logger = logging.getLogger("onnyx.config")

class OnnyxConfig:
    """
    OnnyxConfig is a centralized configuration system for the Onnyx blockchain.
    It supports default configurations in code and overrides in JSON/YAML files.
    """
    
    # Default chain parameters
    DEFAULT_CHAIN_PARAMS = {
        "block_reward": 10,
        "reward_token": "ONX",
        "quorum_percent": 50,
        "vote_pass_ratio": 0.6,
        "mint_cap_factor": 1.0,
        "min_etzem_score": 30,
        "validator_badges": ["VALIDATOR_ELIGIBLE_BADGE"],
        "council_badges": ["COUNCIL_ELIGIBLE_BADGE"],
        "guardian_badges": ["GUARDIAN_ELIGIBLE_BADGE"],
        "proposal_badges": ["PROPOSAL_ELIGIBLE_BADGE"],
        "validator_rotation_interval": 3600,  # 1 hour in seconds
        "scroll_voting_period": 604800,  # 7 days in seconds
        "scroll_implementation_delay": 86400,  # 1 day in seconds
        "max_token_supply": 1000000000,
        "min_stake_amount": 100,
        "stake_lock_period": 2592000,  # 30 days in seconds
        "max_mempool_size": 1000,
        "max_block_size": 1000000,
        "target_block_time": 60,  # seconds
        "difficulty_adjustment_period": 100,  # blocks
        "max_transaction_size": 100000,
        "max_transactions_per_block": 1000
    }
    
    # Default network parameters
    DEFAULT_NETWORK_PARAMS = {
        "networks": {
            "mainnet": {
                "name": "Onnyx Mainnet",
                "chain_id": "onnyx-main-1",
                "p2p_port": 8333,
                "rpc_port": 8332,
                "seed_nodes": [
                    "seed1.onnyx.chain:8333",
                    "seed2.onnyx.chain:8333",
                    "seed3.onnyx.chain:8333"
                ],
                "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"
            },
            "testnet": {
                "name": "Onnyx Testnet",
                "chain_id": "onnyx-test-1",
                "p2p_port": 18333,
                "rpc_port": 18332,
                "seed_nodes": [
                    "test-seed1.onnyx.chain:18333",
                    "test-seed2.onnyx.chain:18333"
                ],
                "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"
            },
            "devnet": {
                "name": "Onnyx Development Network",
                "chain_id": "onnyx-dev-1",
                "p2p_port": 28333,
                "rpc_port": 28332,
                "seed_nodes": [
                    "127.0.0.1:28333"
                ],
                "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"
            }
        },
        "protocol_version": "0.1.0",
        "min_peer_version": "0.1.0",
        "max_connections": 125,
        "connection_timeout_seconds": 30,
        "handshake_timeout_seconds": 5
    }
    
    # Default node parameters
    DEFAULT_NODE_PARAMS = {
        "node_id": "onnyx_node",
        "node_port": 8080,
        "node_host": "localhost",
        "node_peers": [],
        "node_public_key": "",
        "node_private_key": "",
        "data_dir": "data",
        "max_peers": 10,
        "sync_interval": 60,
        "broadcast_interval": 30,
        "mempool_sync_interval": 15,
        "block_sync_interval": 60,
        "peer_discovery_interval": 300,
        "heartbeat_interval": 30,
        "connection_timeout": 10,
        "max_mempool_size": 1000,
        "max_block_size": 1000,
        "max_message_size": 1048576,
        "log_level": "INFO"
    }
    
    # Default Sela parameters
    DEFAULT_SELA_PARAMS = {
        "sela_id": "",
        "identity_id": "",
        "private_key_path": "",
        "api_port": 8888,
        "role": "validator",
        "auto_mine": False,
        "mine_interval": 60,
        "activity_log_path": "data/activity_log.json",
        "validator_rotation": {
            "enabled": True,
            "min_etzem_score": 30,
            "required_badges": [
                "SELA_FOUNDER",
                "VALIDATOR_ELIGIBLE_BADGE"
            ]
        }
    }
    
    def __init__(self, config_dir: str = "config", data_dir: str = "data"):
        """
        Initialize the OnnyxConfig.

        Args:
            config_dir: Directory containing configuration files
            data_dir: Directory containing data files
        """
        self.config_dir = config_dir
        self.data_dir = data_dir

        # Set database path - use production database location
        self.db_path = os.path.join(data_dir, "db", "onnyx.db")

        # Initialize configuration dictionaries
        self.chain_params = self.DEFAULT_CHAIN_PARAMS.copy()
        self.network_params = self.DEFAULT_NETWORK_PARAMS.copy()
        self.node_params = self.DEFAULT_NODE_PARAMS.copy()
        self.sela_params = self.DEFAULT_SELA_PARAMS.copy()

        # Load configurations from files
        self.load_chain_params()
        self.load_network_params()
        self.load_node_params()
        self.load_sela_params()
    
    def load_chain_params(self, path: Optional[str] = None):
        """
        Load chain parameters from a file.
        
        Args:
            path: Path to the chain parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "chain_params.json")
        
        self._load_config(path, self.chain_params)
    
    def load_network_params(self, path: Optional[str] = None):
        """
        Load network parameters from a file.
        
        Args:
            path: Path to the network parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "network_params.json")
        
        self._load_config(path, self.network_params)
    
    def load_node_params(self, path: Optional[str] = None):
        """
        Load node parameters from a file.
        
        Args:
            path: Path to the node parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "node_params.json")
        
        self._load_config(path, self.node_params)
    
    def load_sela_params(self, path: Optional[str] = None):
        """
        Load Sela parameters from a file.
        
        Args:
            path: Path to the Sela parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "sela_params.json")
        
        self._load_config(path, self.sela_params)
    
    def _load_config(self, path: str, default_config: Dict[str, Any]):
        """
        Load configuration from a file and merge with defaults.
        
        Args:
            path: Path to the configuration file
            default_config: Default configuration to merge with
        """
        try:
            if os.path.exists(path) and os.path.getsize(path) > 0:
                with open(path, "r") as f:
                    if path.endswith(".json"):
                        loaded_config = json.load(f)
                    elif path.endswith(".yaml") or path.endswith(".yml"):
                        loaded_config = yaml.safe_load(f)
                    else:
                        logger.warning(f"Unsupported file format: {path}")
                        return
                    
                    # Update the default configuration with the loaded configuration
                    self._deep_update(default_config, loaded_config)
                    logger.info(f"Loaded configuration from {path}")
        except Exception as e:
            logger.error(f"Error loading configuration from {path}: {str(e)}")
    
    def _deep_update(self, d: Dict[str, Any], u: Dict[str, Any]):
        """
        Recursively update a dictionary.
        
        Args:
            d: Dictionary to update
            u: Dictionary with updates
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
    
    def save_chain_params(self, path: Optional[str] = None):
        """
        Save chain parameters to a file.
        
        Args:
            path: Path to the chain parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "chain_params.json")
        
        self._save_config(path, self.chain_params)
    
    def save_network_params(self, path: Optional[str] = None):
        """
        Save network parameters to a file.
        
        Args:
            path: Path to the network parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "network_params.json")
        
        self._save_config(path, self.network_params)
    
    def save_node_params(self, path: Optional[str] = None):
        """
        Save node parameters to a file.
        
        Args:
            path: Path to the node parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "node_params.json")
        
        self._save_config(path, self.node_params)
    
    def save_sela_params(self, path: Optional[str] = None):
        """
        Save Sela parameters to a file.
        
        Args:
            path: Path to the Sela parameters file (optional)
        """
        if path is None:
            path = os.path.join(self.config_dir, "sela_params.json")
        
        self._save_config(path, self.sela_params)
    
    def _save_config(self, path: str, config: Dict[str, Any]):
        """
        Save configuration to a file.
        
        Args:
            path: Path to the configuration file
            config: Configuration to save
        """
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(path), exist_ok=True)
            
            with open(path, "w") as f:
                if path.endswith(".json"):
                    json.dump(config, f, indent=2)
                elif path.endswith(".yaml") or path.endswith(".yml"):
                    yaml.dump(config, f, default_flow_style=False)
                else:
                    logger.warning(f"Unsupported file format: {path}")
                    return
                
                logger.info(f"Saved configuration to {path}")
        except Exception as e:
            logger.error(f"Error saving configuration to {path}: {str(e)}")
    
    def get_chain_param(self, key: str, default: Any = None) -> Any:
        """
        Get a chain parameter.
        
        Args:
            key: The parameter key
            default: The default value to return if the key is not found
        
        Returns:
            The parameter value
        """
        return self.chain_params.get(key, default)
    
    def set_chain_param(self, key: str, value: Any):
        """
        Set a chain parameter.
        
        Args:
            key: The parameter key
            value: The parameter value
        """
        self.chain_params[key] = value
        self.save_chain_params()
        logger.info(f"Set chain parameter {key} to {value}")
    
    def get_network_param(self, key: str, default: Any = None) -> Any:
        """
        Get a network parameter.
        
        Args:
            key: The parameter key
            default: The default value to return if the key is not found
        
        Returns:
            The parameter value
        """
        return self.network_params.get(key, default)
    
    def set_network_param(self, key: str, value: Any):
        """
        Set a network parameter.
        
        Args:
            key: The parameter key
            value: The parameter value
        """
        self.network_params[key] = value
        self.save_network_params()
        logger.info(f"Set network parameter {key} to {value}")
    
    def get_node_param(self, key: str, default: Any = None) -> Any:
        """
        Get a node parameter.
        
        Args:
            key: The parameter key
            default: The default value to return if the key is not found
        
        Returns:
            The parameter value
        """
        return self.node_params.get(key, default)
    
    def set_node_param(self, key: str, value: Any):
        """
        Set a node parameter.
        
        Args:
            key: The parameter key
            value: The parameter value
        """
        self.node_params[key] = value
        self.save_node_params()
        logger.info(f"Set node parameter {key} to {value}")
    
    def get_sela_param(self, key: str, default: Any = None) -> Any:
        """
        Get a Sela parameter.
        
        Args:
            key: The parameter key
            default: The default value to return if the key is not found
        
        Returns:
            The parameter value
        """
        return self.sela_params.get(key, default)
    
    def set_sela_param(self, key: str, value: Any):
        """
        Set a Sela parameter.
        
        Args:
            key: The parameter key
            value: The parameter value
        """
        self.sela_params[key] = value
        self.save_sela_params()
        logger.info(f"Set Sela parameter {key} to {value}")

# Create a global instance of the OnnyxConfig
# Use the shared/db directory for database path
import os
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
data_dir = os.path.join(project_root, "shared", "db")
onnyx_config = OnnyxConfig(data_dir=data_dir)
