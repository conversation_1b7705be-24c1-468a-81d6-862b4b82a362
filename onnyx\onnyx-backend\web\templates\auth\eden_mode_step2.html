{% extends "base.html" %}

{% block title %}Nations Hidden in Plain Sight - Eden Mode{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black relative overflow-hidden">
    <!-- Standardized Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
        <!-- Professional cyber grid background -->
        <div class="absolute inset-0 cyber-grid opacity-20"></div>
        <!-- Floating particles effect - Enhanced visibility -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-purple rounded-full animate-ping"></div>
            <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-cyan rounded-full animate-pulse"></div>
            <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
            <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyber-purple rounded-full animate-ping"></div>
            <div class="absolute top-1/2 left-1/2 w-1 h-1 bg-cyber-cyan rounded-full animate-pulse"></div>
            <div class="absolute bottom-1/3 right-1/2 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
            <div class="absolute top-3/4 left-1/5 w-1 h-1 bg-cyber-purple rounded-full animate-ping"></div>
            <div class="absolute bottom-1/5 right-1/5 w-1.5 h-1.5 bg-cyber-cyan rounded-full animate-pulse"></div>
        </div>
    </div>

    <!-- Progress Indicator - Non-sticky -->
    <div class="flex justify-center mb-8">
        <div class="glass-card-enhanced px-8 py-4 rounded-2xl">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-cyan"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-cyan flex items-center justify-center text-onyx-black font-bold">2</div>
                <div class="w-8 h-1 bg-glass-border"></div>
                <div class="w-8 h-8 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary">3</div>
                <div class="w-8 h-1 bg-glass-border"></div>
                <div class="w-8 h-8 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary">4</div>
                <div class="w-8 h-1 bg-glass-border"></div>
                <div class="w-8 h-8 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary">5</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-xl mx-auto px-6 pt-32 pb-16">
        <div class="max-w-6xl mx-auto">

            <!-- Header -->
            <div class="text-center mb-16 fade-in-up">
                <div class="mb-8">
                    <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                        <svg class="w-16 h-16 text-cyber-purple mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                  d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h1 class="text-6xl md:text-7xl font-orbitron font-bold text-cyber-purple mb-6 glow-text">
                        Nations Hidden in Plain Sight
                    </h1>
                    <p class="text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                        The twelve tribes were scattered to the four corners of the earth, but their bloodlines endure.
                        <span class="text-cyber-cyan font-semibold">Which nation calls to your spirit?</span>
                    </p>
                </div>
            </div>

            <!-- Nation Selection Interface -->
            <div class="space-y-12">
                <!-- Introduction -->
                <div class="glass-card-premium p-8 rounded-3xl fade-in-up" data-delay="300">
                    <div class="text-center">
                        <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                            🏛️ Choose Your Covenant Heritage
                        </h2>
                        <p class="text-lg text-text-secondary max-w-3xl mx-auto mb-8">
                            Each nation carries unique spiritual gifts, economic roles, and covenant responsibilities.
                            Listen to your heart—your ancestors will guide you to the right choice.
                        </p>
                        <div class="glass-card-enhanced p-6 rounded-xl bg-gradient-to-r from-cyber-purple/10 to-cyber-cyan/10">
                            <p class="text-cyber-cyan font-orbitron font-semibold">
                                "And ye shall be unto me a kingdom of priests, and an holy nation" — Exodus 19:6
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Two-Tier Selection System -->

                <!-- Tier 1: Nation Type Selection -->
                <div id="nationTypeSelection" class="fade-in-up" data-delay="600">
                    <div class="text-center mb-12">
                        <h3 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-4">Choose Your Covenant Path</h3>
                        <p class="text-lg text-text-secondary max-w-4xl mx-auto">
                            The Genesis Covenant encompasses both the original covenant tribes and witness nations.
                            Each path offers unique spiritual gifts and responsibilities in the covenant community.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
                        <!-- Covenant Tribes -->
                        <div class="nation-type-card glass-card-premium p-8 cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-lg hover:shadow-cyber-cyan/40"
                             data-type="covenant">
                            <div class="text-center">
                                <div class="text-8xl mb-6">👑</div>
                                <h3 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-4">Covenant Tribes</h3>
                                <p class="text-lg text-cyber-purple font-semibold mb-6">The Original 12 Tribes</p>
                                <p class="text-text-secondary leading-relaxed mb-6">
                                    The foundational tribes of the covenant, each with unique spiritual gifts,
                                    tribal roles, and covenant responsibilities. Called to leadership, priesthood,
                                    and the establishment of divine order.
                                </p>
                                <div class="text-sm text-cyber-cyan font-mono">
                                    12 Tribes Available
                                </div>
                            </div>
                        </div>

                        <!-- Witness Nations -->
                        <div class="nation-type-card glass-card-premium p-8 cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-lg hover:shadow-cyber-purple/40"
                             data-type="witness">
                            <div class="text-center">
                                <div class="text-8xl mb-6">👁️</div>
                                <h3 class="text-3xl font-orbitron font-bold text-cyber-purple mb-4">Witness Nations</h3>
                                <p class="text-lg text-cyber-blue font-semibold mb-6">The 35 Witness Nations</p>
                                <p class="text-text-secondary leading-relaxed mb-6">
                                    Nations called to witness the covenant in action, each contributing unique
                                    cultural wisdom, economic functions, and community gifts to the global
                                    covenant community.
                                </p>
                                <div class="text-sm text-cyber-purple font-mono">
                                    35 Nations Available
                                </div>
                                <div class="text-xs text-cyber-cyan mt-2 font-semibold">
                                    Two-Tier Ancestral Selection
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ancestral Heritage Selection (for Witness Nations) -->
                <div id="ancestralSelection" class="hidden fade-in-up" data-delay="1000">
                    <div class="text-center mb-8">
                        <button id="backToTypeSelection" class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 mb-6">
                            ← Back to Path Selection
                        </button>
                        <h3 class="text-3xl font-orbitron font-bold text-cyber-purple mb-4">
                            🌍 Discover Your Ancestral Roots
                        </h3>
                        <p class="text-text-secondary max-w-3xl mx-auto">
                            Before selecting your modern nation, connect with your ancient heritage. Choose the ancestral lineage that resonates with your family history and cultural background.
                        </p>
                    </div>

                    <div id="ancestralGroupsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Ancestral groups will be populated here -->
                    </div>
                </div>

                <!-- Tier 2: Specific Nation Selection -->
                <div id="specificNationSelection" class="hidden">
                    <div class="text-center mb-12">
                        <button id="backToTypes" class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 mb-6">
                            ← Back to Path Selection
                        </button>
                        <h3 id="selectionTitle" class="text-3xl font-orbitron font-bold text-cyber-cyan mb-4"></h3>
                        <p id="selectionDescription" class="text-lg text-text-secondary max-w-3xl mx-auto"></p>
                    </div>

                    <!-- Nations Grid -->
                    <div id="nationsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16">
                        <!-- Nations will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Hidden data for JavaScript -->
                <script type="application/json" id="nationsData">
                    {{ biblical_nations | tojson }}
                </script>

                <script type="application/json" id="ancestralGroupsData">
                    {{ ancestral_groups | tojson }}
                </script>

                <!-- Selected Nation Details -->
                <div id="selectedNationDetails" class="glass-card-premium p-8 rounded-3xl hidden fade-in-up">
                    <div class="text-center mb-8">
                        <h3 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-4">
                            Your Chosen Heritage
                        </h3>
                        <div id="nationDetails" class="space-y-6">
                            <!-- Dynamic content will be inserted here -->
                        </div>
                    </div>

                    <!-- Covenant Confirmation -->
                    <div class="glass-card-enhanced p-6 rounded-xl bg-gradient-to-r from-cyber-green/10 to-cyber-cyan/10">
                        <div class="text-center">
                            <h4 class="text-xl font-orbitron font-bold text-cyber-green mb-4">
                                🤝 Covenant Confirmation
                            </h4>
                            <p class="text-text-secondary mb-6">
                                By selecting this nation, you acknowledge your spiritual connection to this heritage
                                and commit to upholding its covenant responsibilities within the ONNYX ecosystem.
                            </p>

                            <div class="flex items-center justify-center space-x-4">
                                <label class="flex items-center space-x-3 cursor-pointer">
                                    <input type="checkbox" id="covenantAcceptance" class="form-checkbox h-5 w-5 text-cyber-green rounded">
                                    <span class="text-text-secondary">
                                        I accept my covenant heritage and responsibilities
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="flex justify-between items-center mt-16 fade-in-up" data-delay="900">
                <button id="backButton"
                        class="glass-button px-8 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    ← Back to Legacy
                </button>

                <button id="continueButton"
                        class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 opacity-50 cursor-not-allowed"
                        disabled>
                    Continue to Covenant →
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Eden Mode Step 2 Controller - Two-Tier Selection System
class EdenModeStep2 {
    constructor() {
        this.selectedNation = null;
        this.selectedTribe = null;
        this.selectedNationType = null;
        this.selectedAncestralGroup = null;
        this.covenantAccepted = false;
        this.allNations = [];
        this.ancestralGroups = [];

        this.init();
    }

    init() {
        this.loadNationsData();
        this.loadAncestralGroupsData();
        this.initializeAnimations();
        this.setupNationTypeSelection();
        this.setupAncestralSelection();
        this.setupNavigation();
        this.setupCovenantAcceptance();
    }

    loadNationsData() {
        const nationsDataElement = document.getElementById('nationsData');
        if (nationsDataElement) {
            this.allNations = JSON.parse(nationsDataElement.textContent);
        }
    }

    loadAncestralGroupsData() {
        const ancestralGroupsDataElement = document.getElementById('ancestralGroupsData');
        if (ancestralGroupsDataElement) {
            this.ancestralGroups = JSON.parse(ancestralGroupsDataElement.textContent);
        }
    }

    initializeAnimations() {
        const elements = document.querySelectorAll('.fade-in-up');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, delay);
                }
            });
        }, { threshold: 0.1 });

        elements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(40px)';
            element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            observer.observe(element);
        });
    }

    setupNationTypeSelection() {
        const typeCards = document.querySelectorAll('.nation-type-card');

        typeCards.forEach(card => {
            card.addEventListener('click', () => {
                this.selectNationType(card.dataset.type);
            });
        });

        // Setup back button
        const backButton = document.getElementById('backToTypes');
        if (backButton) {
            backButton.addEventListener('click', () => {
                if (this.selectedAncestralGroup) {
                    this.showAncestralSelection();
                } else {
                    this.showNationTypeSelection();
                }
            });
        }
    }

    setupAncestralSelection() {
        const backToTypeButton = document.getElementById('backToTypeSelection');
        if (backToTypeButton) {
            backToTypeButton.addEventListener('click', () => {
                this.showNationTypeSelection();
            });
        }
    }

    selectNationType(type) {
        this.selectedNationType = type;

        if (type === 'covenant') {
            // Direct selection for covenant tribes
            this.showSpecificNationSelection(type);
        } else {
            // Two-tier selection for witness nations
            this.showAncestralSelection();
        }
    }

    showNationTypeSelection() {
        document.getElementById('nationTypeSelection').classList.remove('hidden');
        document.getElementById('specificNationSelection').classList.add('hidden');
        document.getElementById('ancestralSelection').classList.add('hidden');

        // Reset selections
        this.selectedNation = null;
        this.selectedTribe = null;
        this.selectedNationType = null;
        this.selectedAncestralGroup = null;
        this.covenantAccepted = false;

        // Hide nation details
        const detailsContainer = document.getElementById('selectedNationDetails');
        if (detailsContainer) {
            detailsContainer.classList.add('hidden');
        }

        this.updateContinueButton();
    }

    showAncestralSelection() {
        document.getElementById('nationTypeSelection').classList.add('hidden');
        document.getElementById('specificNationSelection').classList.add('hidden');
        document.getElementById('ancestralSelection').classList.remove('hidden');

        this.populateAncestralGroupsGrid();
    }

    populateAncestralGroupsGrid() {
        const grid = document.getElementById('ancestralGroupsGrid');
        grid.innerHTML = '';

        this.ancestralGroups.forEach(group => {
            const groupCard = this.createAncestralGroupCard(group);
            grid.appendChild(groupCard);
        });
    }

    createAncestralGroupCard(group) {
        const card = document.createElement('div');
        card.className = 'ancestral-group-card glass-card-enhanced p-6 rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105';
        card.dataset.ancestralGroup = group.ancestral_group;

        // Get representative symbol for ancestral group
        const groupSymbols = {
            'Germanic Tribes': '⚔️',
            'Nordic Peoples': '🏔️',
            'Celtic Peoples': '🍀',
            'Celtic-Anglo-Saxon': '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
            'Celtic-Frankish': '⚜️',
            'Slavic Peoples': '🐻',
            'Slavic-Turkic': '🏹',
            'Daco-Roman': '🦅',
            'Magyar Peoples': '🏇',
            'Italic Peoples': '🏛️',
            'Iberian-Visigothic': '🏰',
            'Hellenic Peoples': '🏺',
            'Abrahamic Covenant': '🗿',
            'Armenian Peoples': '⛰️',
            'Persian Peoples': '👑',
            'Mesopotamian Peoples': '📜',
            'Semitic Peoples': '🐪',
            'Hamitic Peoples': '🏺',
            'Cushite Peoples': '🦁',
            'Sinic Peoples': '🐉',
            'Yamato Peoples': '🌸',
            'Korean Peoples': '🏔️',
            'Mongolic Peoples': '🐎',
            'Indo-Aryan Peoples': '🕉️',
            'Roman Peoples': '🦅',
            'Finno-Ugric Peoples': '🌲'
        };

        const symbol = groupSymbols[group.ancestral_group] || '🌍';

        card.innerHTML = `
            <div class="text-center">
                <div class="text-4xl mb-4">${symbol}</div>
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-3">
                    ${group.ancestral_group}
                </h3>
                <p class="text-text-secondary text-sm mb-4">
                    ${group.nation_count} nations available
                </p>
                <div class="text-xs text-cyber-cyan">
                    Click to explore nations
                </div>
            </div>
        `;

        card.addEventListener('click', () => {
            this.selectAncestralGroup(group.ancestral_group);
        });

        return card;
    }

    selectAncestralGroup(ancestralGroup) {
        this.selectedAncestralGroup = ancestralGroup;
        this.showSpecificNationSelection('witness', ancestralGroup);
    }

    showSpecificNationSelection(type, ancestralGroup = null) {
        document.getElementById('nationTypeSelection').classList.add('hidden');
        document.getElementById('ancestralSelection').classList.add('hidden');
        document.getElementById('specificNationSelection').classList.remove('hidden');

        // Update titles and descriptions
        const title = document.getElementById('selectionTitle');
        const description = document.getElementById('selectionDescription');

        if (type === 'covenant') {
            title.textContent = 'Select Your Covenant Tribe';
            title.className = 'text-3xl font-orbitron font-bold text-cyber-cyan mb-4';
            description.textContent = 'Choose from the 12 original covenant tribes, each with unique spiritual gifts and covenant responsibilities.';
        } else if (ancestralGroup) {
            title.textContent = `${ancestralGroup} Nations`;
            title.className = 'text-3xl font-orbitron font-bold text-cyber-purple mb-4';
            description.textContent = `Choose your modern nation from the ${ancestralGroup} ancestral lineage. Each nation carries the heritage and wisdom of your ancient forefathers.`;
        } else {
            title.textContent = 'Select Your Witness Nation';
            title.className = 'text-3xl font-orbitron font-bold text-cyber-purple mb-4';
            description.textContent = 'Choose from the 35 witness nations, each contributing unique cultural wisdom and community gifts.';
        }

        // Populate nations grid
        this.populateNationsGrid(type, ancestralGroup);
    }

    populateNationsGrid(type, ancestralGroup = null) {
        const grid = document.getElementById('nationsGrid');
        let filteredNations = this.allNations.filter(nation => nation.nation_type === type);

        // Further filter by ancestral group if specified
        if (ancestralGroup) {
            filteredNations = filteredNations.filter(nation => nation.ancestral_group === ancestralGroup);
        }

        grid.innerHTML = '';

        filteredNations.forEach(nation => {
            const nationCard = this.createNationCard(nation);
            grid.appendChild(nationCard);
        });
    }

    createNationCard(nation) {
        const card = document.createElement('div');
        card.className = 'nation-card glass-card-enhanced p-6 rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105';
        card.dataset.nation = nation.nation_code;
        card.dataset.tribe = nation.tribe_name;
        card.dataset.description = nation.description;
        card.dataset.type = nation.nation_type;

        // Special handling for Israel nation and ancestral heritage
        const isIsrael = nation.nation_code === 'ISRAEL';
        const specialText = isIsrael ?
            '"You shall be to Me a kingdom of priests and a holy nation" (Exodus 19:6)' :
            nation.description;

        // Show ancestral heritage for witness nations
        const ancestralInfo = nation.nation_type === 'witness' && nation.ancestral_group ?
            `<div class="text-xs text-cyber-cyan mb-2">Heritage: ${nation.ancestral_group}</div>` : '';

        card.innerHTML = `
            <div class="text-center mb-6">
                <div class="w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyber-cyan/20 to-cyber-purple/20 flex items-center justify-center text-4xl">
                    ${nation.flag_symbol || '🏛️'}
                </div>
                ${ancestralInfo}
                <h3 class="text-2xl font-orbitron font-bold ${nation.nation_type === 'covenant' ? 'text-cyber-cyan' : 'text-cyber-purple'} mb-2">
                    ${nation.nation_name}
                </h3>
                ${nation.tribe_name ? `<p class="text-cyber-purple font-semibold">Tribe of ${nation.tribe_name}</p>` : ''}
            </div>

            <div class="space-y-4">
                <p class="text-text-secondary text-sm leading-relaxed">
                    ${specialText.length > 120 ? specialText.substring(0, 120) + '...' : specialText}
                </p>
                ${nation.historical_connection ? `
                    <div class="text-xs text-cyber-purple bg-cyber-purple/10 p-2 rounded">
                        ${nation.historical_connection.length > 100 ? nation.historical_connection.substring(0, 100) + '...' : nation.historical_connection}
                    </div>
                ` : ''}
            </div>

            <div class="selection-indicator mt-6 text-center opacity-0 transition-opacity duration-300">
                <div class="w-8 h-8 mx-auto rounded-full bg-cyber-green flex items-center justify-center">
                    <svg class="w-5 h-5 text-onyx-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <p class="text-cyber-green font-semibold mt-2">Selected</p>
            </div>
        `;

        card.addEventListener('click', () => {
            this.selectNation(card);
        });

        return card;
    }

    selectNation(selectedCard) {
        // Remove previous selections
        document.querySelectorAll('.nation-card').forEach(card => {
            card.classList.remove('selected');
            const indicator = card.querySelector('.selection-indicator');
            if (indicator) indicator.style.opacity = '0';
        });

        // Select new nation
        selectedCard.classList.add('selected');
        selectedCard.querySelector('.selection-indicator').style.opacity = '1';

        // Store selection data
        this.selectedNation = selectedCard.dataset.nation;
        this.selectedTribe = selectedCard.dataset.tribe;

        // Show nation details
        this.showNationDetails(selectedCard);

        // Update continue button state
        this.updateContinueButton();
    }

    showNationDetails(card) {
        const detailsContainer = document.getElementById('selectedNationDetails');
        const nationDetails = document.getElementById('nationDetails');

        const nationName = card.querySelector('h3').textContent;
        const tribeName = this.selectedTribe;
        const description = card.dataset.description;

        nationDetails.innerHTML = `
            <div class="flex items-center justify-center space-x-4 mb-6">
                <div class="text-6xl">${card.querySelector('.text-4xl').textContent}</div>
                <div>
                    <h4 class="text-2xl font-orbitron font-bold text-cyber-cyan">${nationName}</h4>
                    ${tribeName ? `<p class="text-cyber-purple">Tribe of ${tribeName}</p>` : ''}
                </div>
            </div>
            <p class="text-text-secondary text-lg leading-relaxed max-w-3xl mx-auto">
                ${description}
            </p>
            <div class="mt-6">
                <p class="text-cyber-cyan font-semibold">
                    Your spiritual gifts and economic role within the ONNYX covenant will be aligned with this heritage.
                </p>
            </div>
        `;

        detailsContainer.classList.remove('hidden');

        // Scroll to details
        setTimeout(() => {
            detailsContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);
    }

    setupCovenantAcceptance() {
        const checkbox = document.getElementById('covenantAcceptance');

        checkbox.addEventListener('change', () => {
            this.covenantAccepted = checkbox.checked;
            this.updateContinueButton();
        });
    }

    updateContinueButton() {
        const continueButton = document.getElementById('continueButton');

        if (this.selectedNation && this.covenantAccepted) {
            continueButton.disabled = false;
            continueButton.classList.remove('opacity-50', 'cursor-not-allowed');
            continueButton.classList.add('glow-on-hover');
        } else {
            continueButton.disabled = true;
            continueButton.classList.add('opacity-50', 'cursor-not-allowed');
            continueButton.classList.remove('glow-on-hover');
        }
    }

    setupNavigation() {
        const backButton = document.getElementById('backButton');
        const continueButton = document.getElementById('continueButton');

        backButton.addEventListener('click', () => {
            window.location.href = '/auth/eden-mode/step1';
        });

        continueButton.addEventListener('click', () => {
            if (this.selectedNation && this.covenantAccepted) {
                // Store selection in session
                this.storeSelection();

                // Proceed to next step
                continueButton.innerHTML = '🌟 Awakening Covenant...';
                continueButton.disabled = true;

                setTimeout(() => {
                    window.location.href = '/auth/eden-mode/step3';
                }, 1500);
            }
        });
    }

    storeSelection() {
        // Store in sessionStorage for now
        sessionStorage.setItem('edenMode_selectedNation', this.selectedNation);
        sessionStorage.setItem('edenMode_selectedTribe', this.selectedTribe);
        sessionStorage.setItem('edenMode_covenantAccepted', this.covenantAccepted);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new EdenModeStep2();
});
</script>

<style>
.glow-text {
    text-shadow: 0 0 20px rgba(154, 0, 255, 0.5);
}

.nation-type-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 255, 247, 0.2);
}

.nation-type-card.selected {
    border: 2px solid var(--cyber-cyan);
    box-shadow: 0 0 30px rgba(0, 255, 247, 0.3);
}

.nation-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 255, 247, 0.2);
}

.nation-card.selected {
    border: 2px solid var(--cyber-green);
    box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
}

.tribal-patterns::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 40%, rgba(154, 0, 255, 0.1) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(0, 255, 247, 0.1) 50%, transparent 60%);
    background-size: 60px 60px;
    animation: tribalMove 30s linear infinite;
}

@keyframes tribalMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(60px) translateY(60px); }
}

.constellation-map::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 25% 25%, rgba(0, 255, 247, 0.4), transparent),
        radial-gradient(1px 1px at 75% 75%, rgba(154, 0, 255, 0.4), transparent),
        radial-gradient(1px 1px at 50% 10%, rgba(0, 255, 136, 0.4), transparent);
    background-size: 200px 200px;
    animation: constellationTwinkle 10s ease-in-out infinite;
}

@keyframes constellationTwinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}
</style>
{% endblock %}
