#!/usr/bin/env python3
"""
ONNYX Quick Genesis Reset Script
Simplified version for immediate testing and demonstration
"""

import sys
import os
import time
import json
import shutil
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db
from shared.models.identity import Identity
from shared.models.sela import Sela
from shared.models.transaction import Transaction

def backup_existing_data():
    """Create backup of existing data"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "data/backups"
    os.makedirs(backup_dir, exist_ok=True)
    
    backup_path = f"{backup_dir}/onnyx_backup_{timestamp}.db"
    
    if os.path.exists("data/onnyx.db"):
        shutil.copy2("data/onnyx.db", backup_path)
        print(f"✅ Backup created: {backup_path}")
    else:
        print("ℹ️ No existing database to backup")
    
    return backup_path

def reset_blockchain_data():
    """Clear all existing chain data"""
    print("🔄 Resetting blockchain data...")
    
    # Tables to reset
    tables_to_reset = [
        'blocks', 'transactions', 'mempool', 'identities', 'selas',
        'mining_rewards', 'activity_ledger', 'tokens', 'token_balances',
        'gleaning_pool', 'tribal_representatives', 'labor_records'
    ]
    
    for table in tables_to_reset:
        if db.table_exists(table):
            db.execute(f"DELETE FROM {table}")
            print(f"   ✅ Cleared {table}")
    
    # Reset auto-increment sequences
    db.execute("DELETE FROM sqlite_sequence")
    print("✅ All blockchain data cleared")

def create_genesis_block():
    """Create Genesis Block #0"""
    print("🌟 Creating Genesis Block...")
    
    # Create founding transaction
    founding_tx = Transaction(
        tx_id="genesis_covenant_founding",
        op="OP_COVENANT_FOUNDING",
        sender="SYSTEM",
        data=json.dumps({
            "covenant_name": "ONNYX Covenant Blockchain",
            "founding_date": datetime.now().isoformat(),
            "biblical_principles": ["anti_usury", "sabbath_observance", "gleaning_pools", "tribal_governance"],
            "genesis_miner": "<EMAIL>"
        }),
        timestamp=int(time.time()),
        status="confirmed"
    )
    founding_tx.save()
    
    print(f"✅ Genesis founding transaction created: {founding_tx.tx_id}")
    return founding_tx.tx_id

def create_founder_identity():
    """Create Jedidiah Israel as founder"""
    print("👑 Creating founder identity...")
    
    founder = Identity(
        identity_id="founder_jedidiah_israel",
        name="Jedidiah Israel",
        email="<EMAIL>",
        role_class="Citizen",
        verification_level=3,
        nation_code="JU",
        created_at=int(time.time())
    )
    founder.save()
    
    # Create founder's initial token balance
    db.execute("""
        INSERT INTO token_balances (identity_id, token_class, balance, last_updated)
        VALUES (?, ?, ?, ?)
    """, (founder.identity_id, "ONX", 1000.0, int(time.time())))
    
    print(f"✅ Founder created: {founder.name} ({founder.email})")
    print(f"   Verification Level: Tier {founder.verification_level}")
    print(f"   Initial Balance: 1000 ONX")
    
    return founder

def create_sample_participants():
    """Create sample participants"""
    print("👥 Creating sample participants...")
    
    biblical_names = [
        "Abraham ben David", "Sarah bat Rachel", "Isaac ben Abraham", "Rebecca bat Sarah",
        "Jacob ben Isaac", "Rachel bat Laban", "Joseph ben Jacob", "Benjamin ben Jacob",
        "Moses ben Amram", "Miriam bat Amram", "Aaron ben Amram", "Joshua ben Nun",
        "David ben Jesse", "Solomon ben David", "Daniel ben Judah", "Esther bat Abihail"
    ]
    
    tribal_codes = ["JU", "LE", "EP", "BE", "SI", "MA", "IS", "ZE", "NA", "GA", "AS", "RE"]
    participants = []
    
    for i, name in enumerate(biblical_names[:10]):  # Create 10 participants
        email = f"{name.split()[0].lower()}.{name.split()[2].lower()}@onnyx.covenant"
        tribal_code = tribal_codes[i % len(tribal_codes)]
        verification_level = [0, 1, 2, 3][i % 4]  # Cycle through tiers
        
        participant = Identity(
            identity_id=f"participant_{i+1:03d}_{name.split()[0].lower()}",
            name=name,
            email=email,
            role_class="Citizen",
            verification_level=verification_level,
            nation_code=tribal_code,
            created_at=int(time.time()) + i
        )
        participant.save()
        
        # Create initial token balance
        initial_balance = {0: 10.0, 1: 50.0, 2: 200.0, 3: 500.0}[verification_level]
        db.execute("""
            INSERT INTO token_balances (identity_id, token_class, balance, last_updated)
            VALUES (?, ?, ?, ?)
        """, (participant.identity_id, "ONX", initial_balance, int(time.time())))
        
        participants.append(participant)
        print(f"   ✅ {name} ({tribal_code}) - Tier {verification_level} - {initial_balance} ONX")
    
    print(f"✅ Created {len(participants)} participants")
    return participants

def create_sample_organizations(participants):
    """Create sample organizations"""
    print("🏢 Creating sample organizations...")
    
    # Create non-profit
    nonprofit = Sela(
        sela_id="nonprofit_covenant_community",
        name="Covenant Community Services",
        category="Non-Profit",
        description="Community services and gleaning pool management",
        identity_id=participants[0].identity_id,
        status="active",
        created_at=int(time.time())
    )
    nonprofit.save()
    print(f"   ✅ Non-profit: {nonprofit.name}")
    
    # Create businesses
    businesses = [
        ("Covenant Tech Solutions", "Technology Services"),
        ("Promised Land Agriculture", "Agricultural Cooperative"),
        ("Temple Builders Cooperative", "Construction & Building")
    ]
    
    organizations = [nonprofit]
    
    for i, (name, category) in enumerate(businesses):
        business = Sela(
            sela_id=f"business_{i+1:02d}_{category.lower().replace(' ', '_')}",
            name=name,
            category=category,
            description=f"Biblical business providing {category.lower()} services",
            identity_id=participants[i+1].identity_id,
            status="active",
            created_at=int(time.time()) + i + 1
        )
        business.save()
        organizations.append(business)
        print(f"   ✅ Business: {name} ({category})")
    
    print(f"✅ Created {len(organizations)} organizations")
    return organizations

def execute_genesis_mining(founder):
    """Execute genesis mining cycle"""
    print("⛏️ Executing genesis mining...")
    
    # Create mining transaction
    mining_tx = Transaction(
        tx_id="genesis_mining_block_1",
        op="OP_MINING_REWARD",
        sender="SYSTEM",
        data=json.dumps({
            "miner": founder.email,
            "block_height": 1,
            "reward_amount": 50.0,
            "gleaning_allocation": 1.0,
            "biblical_compliance_score": 1.0
        }),
        timestamp=int(time.time()),
        status="confirmed"
    )
    mining_tx.save()
    
    # Record mining reward
    db.execute("""
        INSERT INTO mining_rewards (identity_id, block_height, reward_amount, 
                                  gleaning_allocation, timestamp, biblical_compliance_score)
        VALUES (?, ?, ?, ?, ?, ?)
    """, (founder.identity_id, 1, 50.0, 1.0, int(time.time()), 1.0))
    
    # Update founder's balance
    db.execute("""
        UPDATE token_balances SET balance = balance + ?, last_updated = ?
        WHERE identity_id = ? AND token_class = ?
    """, (49.0, int(time.time()), founder.identity_id, "ONX"))
    
    # Add to gleaning pool
    db.execute("""
        INSERT INTO gleaning_pool (amount, source_transaction, timestamp)
        VALUES (?, ?, ?)
    """, (1.0, mining_tx.tx_id, int(time.time())))
    
    print(f"✅ Genesis mining completed")
    print(f"   Miner: {founder.email}")
    print(f"   Reward: 49 ONX (1 ONX to gleaning pool)")
    
    return mining_tx

def display_ecosystem_summary(founder, participants, organizations):
    """Display ecosystem summary"""
    print("\n🌟 ONNYX GENESIS ECOSYSTEM SUMMARY")
    print("=" * 60)
    
    # Blockchain status
    print("📊 BLOCKCHAIN STATUS:")
    print("   Genesis Block: ✅ Created")
    print("   Latest Block: #1 (Genesis mining completed)")
    print("   Total Transactions: 2 (founding + mining)")
    
    # Participants
    print(f"\n👥 PARTICIPANT ECOSYSTEM:")
    print(f"   Founder: {founder.name} ({founder.email})")
    print(f"   Participants: {len(participants)} created")
    
    # Organizations
    print(f"\n🏢 ORGANIZATIONS:")
    for org in organizations:
        print(f"   {org.category}: {org.name}")
    
    # Token distribution
    total_participant_balance = sum([10, 50, 200, 500] * 3)[:len(participants)]  # Approximate
    total_supply = 1000 + sum(total_participant_balance) + 49
    print(f"\n💰 TOKEN DISTRIBUTION:")
    print(f"   Total Supply: ~{total_supply} ONX")
    print(f"   Founder Balance: 1049 ONX")
    print(f"   Gleaning Pool: 1 ONX")
    
    # Biblical compliance
    print(f"\n📜 BIBLICAL COMPLIANCE:")
    print("   Anti-Usury: ✅ CONFIGURED")
    print("   Sabbath Observance: ✅ CONFIGURED")
    print("   Gleaning Pools: ✅ ACTIVE")
    print("   Tribal Governance: ✅ READY")
    
    # Access information
    print(f"\n🌐 ACCESS YOUR COVENANT BLOCKCHAIN:")
    print(f"   Web Interface: http://localhost:5000")
    print(f"   Blockchain Explorer: http://localhost:5000/explorer/")
    print(f"   Login as Founder: <EMAIL>")

def main():
    """Main execution function"""
    print("🔥 ONNYX QUICK GENESIS RESET")
    print("=" * 50)
    
    try:
        # Create necessary directories
        os.makedirs('data/backups', exist_ok=True)
        
        # Phase 1: Backup and reset
        backup_path = backup_existing_data()
        reset_blockchain_data()
        
        # Phase 2: Genesis initialization
        founding_tx = create_genesis_block()
        
        # Phase 3: Create ecosystem
        founder = create_founder_identity()
        participants = create_sample_participants()
        organizations = create_sample_organizations(participants)
        
        # Phase 4: Genesis mining
        mining_tx = execute_genesis_mining(founder)
        
        # Phase 5: Summary
        display_ecosystem_summary(founder, participants, organizations)
        
        print("\n🎉 QUICK GENESIS RESET COMPLETED SUCCESSFULLY!")
        print("Your ONNYX covenant blockchain is ready for testing!")
        
    except Exception as e:
        print(f"\n❌ Genesis reset failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
