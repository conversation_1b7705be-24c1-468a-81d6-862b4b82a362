#!/usr/bin/env python3
"""
Create a test identity for testing dashboard functionality
"""

import sys
import os
import time
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from shared.db.db import db
from shared.models.identity import Identity

def create_test_identity():
    """Create a test identity for dashboard testing"""
    try:
        print("Creating test identity...")

        # Check if test identity already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", ["<EMAIL>"])
        if existing:
            print(f"Test identity already exists: {existing['identity_id']}")
            return existing['identity_id']

        # Create test identity
        identity_id = "test_user_001"

        # Insert directly into database
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, nation_code, nation_name,
                role_class, verification_level, status, created_at, updated_at,
                metadata, covenant_accepted, vault_status, etzem_score
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            "Test User",
            "<EMAIL>",
            f"pubkey_{identity_id}",
            "JU",
            "Judah",
            "Community_Member",
            2,  # Tier 2
            "active",
            int(time.time()),
            int(time.time()),
            json.dumps({"test_user": True, "covenant_compliance": 85}),
            True,
            "Active",
            120
        ))

        # Create a test Sela for the user
        sela_id = "test_sela_001"
        db.execute("""
            INSERT INTO selas (
                sela_id, identity_id, name, category, description,
                status, created_at, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            sela_id,
            identity_id,
            "Test Business",
            "Technology",
            "Test business for dashboard testing",
            "active",
            int(time.time()),
            json.dumps({"test_sela": True, "business_type": "Technology Services"})
        ))

        # Create some test transactions
        for i in range(3):
            tx_id = f"test_tx_{i+1:03d}"
            db.execute("""
                INSERT INTO transactions (
                    tx_id, timestamp, op, data, sender, signature, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                tx_id,
                int(time.time()) - (i * 3600),  # 1 hour apart
                "TEST_TRANSACTION",
                json.dumps({"test": True, "amount": 10 * (i + 1)}),
                identity_id,
                f"sig_{tx_id}",
                "confirmed",
                int(time.time()) - (i * 3600)
            ))

        print(f"✅ Test identity created: {identity_id}")
        print(f"✅ Test Sela created: {sela_id}")
        print(f"✅ Test transactions created: 3")
        print(f"📧 Email: <EMAIL>")
        print(f"🔑 Password: Use any password (auth not enforced for test)")

        return identity_id

    except Exception as e:
        print(f"❌ Error creating test identity: {e}")
        return None

if __name__ == "__main__":
    create_test_identity()
