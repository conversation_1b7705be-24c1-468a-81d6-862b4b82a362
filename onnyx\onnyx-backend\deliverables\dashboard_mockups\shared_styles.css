/* ONNYX Platform Dashboard Styles */
/* Cyber-Tribal Aesthetic with Biblical Elements */

:root {
    /* ONNYX Color Palette */
    --cyber-cyan: #00fff7;
    --cyber-purple: #9a00ff;
    --onyx-black: #0a0a0a;
    --cyber-gold: #ffd700;
    --tribal-silver: #c0c0c0;
    --deep-space: #1a1a2e;
    --electric-blue: #16213e;
    --neon-green: #39ff14;

    /* Typography */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Arial', sans-serif;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* Borders & Shadows */
    --border-radius: 8px;
    --glow-cyan: 0 0 20px var(--cyber-cyan);
    --glow-purple: 0 0 20px var(--cyber-purple);
    --glow-gold: 0 0 20px var(--cyber-gold);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--onyx-black) 0%, var(--deep-space) 100%);
    color: var(--tribal-silver);
    min-height: 100vh;
    overflow-x: hidden;
}

.dashboard-body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles */
.dashboard-header {
    background: linear-gradient(90deg, var(--onyx-black) 0%, var(--electric-blue) 100%);
    border-bottom: 2px solid var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.spinning-n {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 900;
    color: var(--onyx-black);
    animation: spin 10s linear infinite;
    box-shadow: var(--glow-cyan);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.logo-section h1 {
    font-size: 28px;
    color: var(--cyber-cyan);
    text-shadow: var(--glow-cyan);
    margin: 0;
}

.logo-section p {
    font-size: 14px;
    color: var(--cyber-purple);
    margin: 0;
}

.main-nav {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--tribal-silver);
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.nav-link:hover {
    color: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
}

.nav-link.active {
    color: var(--cyber-gold);
    border-color: var(--cyber-gold);
    box-shadow: var(--glow-gold);
}

.user-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-info {
    text-align: right;
}

.user-name {
    display: block;
    font-weight: bold;
    color: var(--cyber-cyan);
}

.user-tribe {
    display: block;
    font-size: 12px;
    color: var(--cyber-purple);
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--onyx-black);
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Status Cards */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.status-card {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 1px solid var(--cyber-cyan);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--glow-cyan);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 247, 0.3);
}

.card-icon {
    font-size: 48px;
    opacity: 0.8;
}

.card-content h3 {
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-sm);
    font-size: 18px;
}

.status-text {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
}

.balance-amount {
    font-size: 24px;
    color: var(--cyber-gold);
    font-weight: bold;
}

.compliance-score, .tribal-score {
    margin-top: var(--spacing-sm);
}

.score-bar, .etzem-bar {
    width: 100%;
    height: 8px;
    background: var(--onyx-black);
    border-radius: 4px;
    overflow: hidden;
    margin-top: var(--spacing-xs);
}

.score-fill, .etzem-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    transition: width 0.3s ease;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-xxl);
}

.quick-actions h2 {
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-lg);
    font-size: 24px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.action-btn {
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-cyan));
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    color: var(--onyx-black);
    font-family: var(--font-primary);
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(154, 0, 255, 0.4);
}

.btn-icon {
    font-size: 20px;
}

/* Activity List */
.recent-activity {
    margin-bottom: var(--spacing-xxl);
}

.recent-activity h2 {
    color: var(--cyber-cyan);
    margin-bottom: var(--spacing-lg);
    font-size: 24px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--deep-space) 100%);
    border: 1px solid var(--cyber-purple);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.3s ease;
}

.activity-item:hover {
    border-color: var(--cyber-cyan);
    box-shadow: var(--glow-cyan);
}

.activity-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.activity-details {
    flex: 1;
}

.activity-title {
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: 12px;
    color: var(--tribal-silver);
    opacity: 0.7;
}

.activity-amount {
    font-weight: bold;
    color: var(--cyber-gold);
}

/* Footer */
.dashboard-footer {
    background: var(--onyx-black);
    border-top: 1px solid var(--cyber-cyan);
    padding: var(--spacing-lg);
    text-align: center;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--tribal-silver);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--cyber-cyan);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .main-nav {
        order: 3;
    }

    .status-cards {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* Theme Variations */
.sabbath-theme {
    --primary-color: var(--cyber-gold);
    --secondary-color: var(--cyber-purple);
}

.lending-theme {
    --primary-color: var(--neon-green);
    --secondary-color: var(--cyber-cyan);
}

.tribal-theme {
    --primary-color: var(--cyber-purple);
    --secondary-color: var(--cyber-gold);
}