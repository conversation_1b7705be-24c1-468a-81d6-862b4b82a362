#!/usr/bin/env python3
"""
ONNYX Genesis Block Generator
Creates the immutable genesis block with the sealed covenant and all 47 nations
"""

import sys
import os
import json
import time
import hashlib
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db
from blockchain.vm.opcodes import OP_COVENANT_FOUNDING

def calculate_covenant_hash():
    """Calculate SHA3-256 hash of the Genesis Covenant document."""
    print("📜 Calculating Genesis Covenant hash...")

    try:
        with open('docs/Genesis Covenant of Onnyx.md', 'r', encoding='utf-8') as f:
            covenant_text = f.read()

        # Calculate SHA3-256 hash
        covenant_hash = hashlib.sha3_256(covenant_text.encode('utf-8')).hexdigest()
        print(f"✅ Covenant hash calculated: {covenant_hash[:16]}...")

        return covenant_hash

    except Exception as e:
        print(f"❌ Error calculating covenant hash: {e}")
        return None

def load_genesis_nations():
    """Load all 47 nations from the genesis configuration."""
    print("🌍 Loading Genesis Nations configuration...")

    try:
        with open('data/genesis_nations.json', 'r', encoding='utf-8') as f:
            nations_config = json.load(f)

        # Extract all nation codes
        covenant_codes = [nation['nation_code'] for nation in nations_config['nations']['covenant_nations']]
        witness_codes = [nation['nation_code'] for nation in nations_config['nations']['witness_nations']]

        all_nation_codes = covenant_codes + witness_codes

        print(f"✅ Loaded {len(covenant_codes)} covenant nations")
        print(f"✅ Loaded {len(witness_codes)} witness nations")
        print(f"✅ Total nations: {len(all_nation_codes)}")

        return all_nation_codes, nations_config

    except Exception as e:
        print(f"❌ Error loading genesis nations: {e}")
        return None, None

def create_genesis_transaction(covenant_hash, nation_codes):
    """Create the OP_COVENANT_FOUNDING genesis transaction."""
    print("🔐 Creating Genesis Transaction...")

    genesis_timestamp = int(time.time())

    genesis_transaction = {
        "transaction_id": "0x0000000000000000000000000000000000000000000000000000000000000000",
        "op": OP_COVENANT_FOUNDING,
        "timestamp": genesis_timestamp,
        "from_id": "GENESIS_COUNCIL",
        "to_id": "ONNYX_CHAIN",
        "data": {
            "covenant_text_hash": covenant_hash,
            "founding_nations": nation_codes,
            "initial_council": ["GENESIS_COUNCIL"],
            "yovel_cycle": 1,
            "season": 1,
            "immutable": True,
            "sealed": True,
            "psalm_83_witness": True,
            "covenant_version": "1.0"
        },
        "signature": "GENESIS_SEAL",
        "block_height": 0,
        "status": "confirmed"
    }

    print(f"✅ Genesis transaction created with {len(nation_codes)} nations")
    return genesis_transaction

def create_genesis_block(genesis_transaction, covenant_hash):
    """Create the immutable genesis block."""
    print("📦 Creating Genesis Block...")

    genesis_timestamp = int(time.time())

    # Calculate merkle root (simplified for genesis block)
    merkle_data = json.dumps(genesis_transaction, sort_keys=True)
    merkle_root = hashlib.sha256(merkle_data.encode()).hexdigest()

    genesis_block = {
        "block_height": 0,
        "timestamp": genesis_timestamp,
        "previous_hash": "0x0000000000000000000000000000000000000000000000000000000000000000",
        "transactions": [genesis_transaction["transaction_id"]],
        "merkle_root": merkle_root,
        "covenant_seal": covenant_hash,
        "immutable_flag": True,
        "sealed": True,
        "miner": "GENESIS_COUNCIL",
        "difficulty": 0,
        "nonce": 0,
        "version": "1.0",
        "size": len(json.dumps(genesis_transaction))
    }

    # Calculate block hash
    block_data = {k: v for k, v in genesis_block.items() if k != 'block_hash'}
    block_string = json.dumps(block_data, sort_keys=True)
    genesis_block["block_hash"] = hashlib.sha256(block_string.encode()).hexdigest()

    print(f"✅ Genesis block created: {genesis_block['block_hash'][:16]}...")
    return genesis_block

def save_genesis_to_database(genesis_block, genesis_transaction, nations_config):
    """Save the genesis block and nations to the database."""
    print("💾 Saving Genesis Block to database...")

    try:
        # Save genesis block
        db.execute("""
            INSERT INTO blocks (
                block_height, timestamp, previous_hash, transactions,
                merkle_root, block_hash, miner, difficulty, nonce, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            genesis_block["block_height"],
            genesis_block["timestamp"],
            genesis_block["previous_hash"],
            json.dumps(genesis_block["transactions"]),
            genesis_block["merkle_root"],
            genesis_block["block_hash"],
            genesis_block["miner"],
            genesis_block["difficulty"],
            genesis_block["nonce"],
            genesis_block["timestamp"]
        ))

        # Save genesis transaction
        db.execute("""
            INSERT INTO transactions (
                transaction_id, op, timestamp, from_id, to_id,
                data, signature, block_height, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            genesis_transaction["transaction_id"],
            genesis_transaction["op"],
            genesis_transaction["timestamp"],
            genesis_transaction["from_id"],
            genesis_transaction["to_id"],
            json.dumps(genesis_transaction["data"]),
            genesis_transaction["signature"],
            genesis_transaction["block_height"],
            genesis_transaction["status"]
        ))

        # Save all nations to biblical_nations table
        covenant_nations = nations_config['nations']['covenant_nations']
        witness_nations = nations_config['nations']['witness_nations']

        for nation in covenant_nations + witness_nations:
            db.execute("""
                INSERT OR REPLACE INTO biblical_nations (
                    nation_code, name, full_name, type, category,
                    symbol, description, founding_status, governance_weight,
                    biblical_reference, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                nation["nation_code"],
                nation["name"],
                nation["full_name"],
                nation["type"],
                nation.get("category", "covenant"),
                nation["symbol"],
                nation["description"],
                nation["founding_status"],
                nation["governance_weight"],
                nation["biblical_reference"],
                genesis_block["timestamp"]
            ))

        print(f"✅ Genesis block saved to database")
        print(f"✅ Genesis transaction saved to database")
        print(f"✅ {len(covenant_nations + witness_nations)} nations saved to database")

        return True

    except Exception as e:
        print(f"❌ Error saving to database: {e}")
        return False

def verify_genesis_deployment():
    """Verify the genesis block deployment."""
    print("🔍 Verifying Genesis Block deployment...")

    try:
        # Check genesis block
        genesis_block = db.query_one("""
            SELECT * FROM blocks WHERE block_height = 0
        """)

        if not genesis_block:
            print("❌ Genesis block not found in database")
            return False

        print(f"✅ Genesis block verified: {genesis_block['block_hash'][:16]}...")

        # Check genesis transaction
        genesis_tx = db.query_one("""
            SELECT * FROM transactions WHERE block_height = 0
        """)

        if not genesis_tx:
            print("❌ Genesis transaction not found in database")
            return False

        print(f"✅ Genesis transaction verified: {genesis_tx['op']}")

        # Check nations
        nations = db.query("""
            SELECT COUNT(*) as count, type FROM biblical_nations
            GROUP BY type
        """)

        for nation_type in nations:
            print(f"✅ {nation_type['type']} nations: {nation_type['count']}")

        total_nations = db.query_one("""
            SELECT COUNT(*) as count FROM biblical_nations
        """)

        print(f"✅ Total nations registered: {total_nations['count']}")

        if total_nations['count'] == 47:
            print("🎉 All 47 nations successfully registered!")
            return True
        else:
            print(f"❌ Expected 47 nations, found {total_nations['count']}")
            return False

    except Exception as e:
        print(f"❌ Error verifying genesis deployment: {e}")
        return False

def generate_genesis_block():
    """Main function to generate the genesis block."""
    print("🌟 ONNYX GENESIS BLOCK GENERATION")
    print("=" * 50)

    # Step 1: Calculate covenant hash
    covenant_hash = calculate_covenant_hash()
    if not covenant_hash:
        return False

    # Step 2: Load genesis nations
    nation_codes, nations_config = load_genesis_nations()
    if not nation_codes:
        return False

    # Step 3: Create genesis transaction
    genesis_transaction = create_genesis_transaction(covenant_hash, nation_codes)

    # Step 4: Create genesis block
    genesis_block = create_genesis_block(genesis_transaction, covenant_hash)

    # Step 5: Save to database
    if not save_genesis_to_database(genesis_block, genesis_transaction, nations_config):
        return False

    # Step 6: Verify deployment
    if not verify_genesis_deployment():
        return False

    print("\n" + "=" * 50)
    print("🎉 GENESIS BLOCK GENERATION COMPLETE!")
    print("=" * 50)
    print(f"📦 Block Hash: {genesis_block['block_hash']}")
    print(f"📜 Covenant Hash: {covenant_hash}")
    print(f"🌍 Nations Registered: 47")
    print(f"⏰ Timestamp: {datetime.fromtimestamp(genesis_block['timestamp']).isoformat()}")
    print(f"🔒 Immutable: TRUE")
    print(f"🔐 Sealed: TRUE")

    return True

if __name__ == '__main__':
    success = generate_genesis_block()
    sys.exit(0 if success else 1)
