#!/usr/bin/env python3
"""
Test the dashboard fixes by simulating a logged-in user session
"""

import sys
import os
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from shared.db.db import db

def test_dashboard_access():
    """Test dashboard access with existing user"""
    try:
        print("🧪 Testing Dashboard Access...")
        
        # Check if test user exists
        test_user = db.query_one("SELECT * FROM identities WHERE email = ?", ["<EMAIL>"])
        if not test_user:
            print("❌ Test user not found. Please run create_test_identity.py first")
            return False
        
        print(f"✅ Test user found: {test_user['identity_id']}")
        print(f"   Name: {test_user['name']}")
        print(f"   Email: {test_user['email']}")
        print(f"   Nation: {test_user.get('nation_name', 'Unknown')}")
        
        # Test if we can access the dashboard route logic
        from web.routes.dashboard import dashboard_bp
        print(f"✅ Dashboard blueprint loaded successfully")
        
        # Test biblical tokenomics initialization
        try:
            from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
            bt = BiblicalTokenomics(db)
            print(f"✅ Biblical Tokenomics initialized: Cycle {bt.current_yovel_cycle}, Season {bt.current_season}")
        except Exception as e:
            print(f"⚠️ Biblical Tokenomics warning: {e}")
        
        # Test database queries that dashboard uses
        user_selas = db.query("""
            SELECT * FROM selas WHERE identity_id = ?
        """, (test_user['identity_id'],))
        print(f"✅ User Selas query: {len(user_selas)} selas found")
        
        user_transactions = db.query("""
            SELECT * FROM transactions WHERE sender = ? LIMIT 5
        """, (test_user['identity_id'],))
        print(f"✅ User Transactions query: {len(user_transactions)} transactions found")
        
        print("\n🎉 Dashboard access test completed successfully!")
        print("The fixes should resolve the 'Error loading dashboard' issue.")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
        return False

def test_eden_mode_completion():
    """Test Eden Mode completion flow"""
    try:
        print("\n🧪 Testing Eden Mode Completion Flow...")
        
        # Test the completion template exists
        template_path = "web/templates/auth/eden_mode_step5.html"
        if os.path.exists(template_path):
            print("✅ Eden Mode Step 5 template found")
            
            # Check for duplicate button issue
            with open(template_path, 'r') as f:
                content = f.read()
                
            # Count dashboard button references
            dashboard_button_count = content.count('Enter Your')
            print(f"✅ Dashboard button references: {dashboard_button_count}")
            
            # Check for correct URL
            if '/dashboard/' in content:
                print("✅ Correct dashboard URL found (/dashboard/)")
            else:
                print("⚠️ Dashboard URL may need verification")
                
        else:
            print("❌ Eden Mode Step 5 template not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Eden Mode test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ONNYX Dashboard Fix Testing")
    print("=" * 50)
    
    success = True
    success &= test_dashboard_access()
    success &= test_eden_mode_completion()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed! Dashboard fixes should be working.")
        print("\nNext steps:")
        print("1. Go through Eden Mode registration flow")
        print("2. Click 'Enter Your Covenant Dashboard' button")
        print("3. Verify dashboard loads without errors")
    else:
        print("❌ Some tests failed. Please check the errors above.")
