{% extends "base.html" %}

{% block title %}Business Validator Registration - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative py-20">
    <!-- Enhanced floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-3 h-3 bg-cyber-purple rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-2 h-2 bg-cyber-cyan rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-2.5 h-2.5 bg-green-400 rounded-full animate-bounce opacity-50"></div>
        <div class="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-pulse opacity-40"></div>
    </div>

    <div class="container-lg relative z-10">
        <div class="card">
            <!-- Enhanced Header -->
            <div class="card-header text-center">
                <!-- ONNYX Logo -->
                <div class="mb-8 flex justify-center">
                    <div class="flex items-center justify-center group">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <!-- Fallback symbol only if image fails -->
                        <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
                    </div>
                </div>

                <!-- Business Icon -->
                <div class="w-24 h-24 bg-gradient-to-br from-cyber-purple via-green-400 to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-6 glow-effect shadow-cyber animate-pulse">
                    <svg class="w-12 h-12 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                    </svg>
                </div>

                <h1 class="card-title text-4xl md:text-5xl mb-4">
                    <span class="hologram-text">Business Validator Registration</span>
                </h1>
                <p class="card-subtitle text-xl max-w-3xl mx-auto">
                    Register your business as a verified Sela validator on the ONNYX blockchain network
                </p>
            </div>

            <!-- Enhanced Form -->
            <div class="card-body">
                <form method="POST" x-data="selaForm()" @submit="handleSubmit" class="space-y-8">
                    <!-- Platform Founder Welcome -->
                    {% if session.get('is_platform_founder') %}
                    <div class="card border border-cyber-cyan/40 bg-cyber-cyan/10">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">👑 Platform Founder Registration</h3>
                                <p class="text-cyber-cyan/90 leading-relaxed">
                                    Welcome, Platform Founder! You're registering the first business validator on the ONNYX network. This will establish the foundation for the trusted business ecosystem.
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Business Name Field -->
                    <div class="form-group">
                        <label for="sela_name" class="form-label required">
                            🏢 Business Name
                        </label>
                        <input type="text"
                               id="sela_name"
                               name="sela_name"
                               required
                               x-model="form.sela_name"
                               @blur="validateBusinessName"
                               class="form-control"
                               placeholder="Enter your business name (e.g., GetTwisted Hair Studios)">
                        <div x-show="businessValidation.checking" class="form-help flex items-center gap-2">
                            <div class="spinner inline-block w-4 h-4"></div>
                            <span>Checking availability...</span>
                        </div>
                        <div x-show="businessValidation.message"
                             :class="businessValidation.valid ? 'form-success' : 'form-error'"
                             x-text="businessValidation.message">
                        </div>
                    </div>

                    <!-- Business Category Field -->
                    <div class="form-group">
                        <label for="category" class="form-label required">
                            📂 Business Category
                        </label>
                        <select id="category"
                                name="category"
                                required
                                x-model="form.category"
                                class="form-control">
                            <option value="">Select your business category</option>
                            {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-help">Choose the category that best describes your business</div>
                    </div>

                    <!-- Business Description Field -->
                    <div class="form-group">
                        <label for="description" class="form-label required">
                            📝 Business Description
                        </label>
                        <textarea id="description"
                                  name="description"
                                  required
                                  x-model="form.description"
                                  rows="4"
                                  class="form-control"
                                  placeholder="Describe your business, services, and what makes you unique..."></textarea>
                        <div class="form-help">This will be displayed on your public validator profile</div>
                    </div>

                    <!-- Business Address Field -->
                    <div class="form-group">
                        <label for="address" class="form-label">
                            📍 Business Address
                        </label>
                        <textarea id="address"
                                  name="address"
                                  x-model="form.address"
                                  rows="3"
                                  class="form-control"
                                  placeholder="Enter your business address (optional)"></textarea>
                        <div class="form-help">Optional: Physical location for customer reference</div>
                    </div>

                    <!-- Services Offered Field -->
                    <div class="form-group">
                        <label class="form-label">
                            🛠️ Services Offered
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mt-3">
                            <label class="form-check">
                                <input type="checkbox" name="services" value="Hair Styling" class="form-check-input">
                                <span class="form-check-label">Hair Styling</span>
                            </label>
                            <label class="form-check">
                                <input type="checkbox" name="services" value="Hair Cutting" class="form-check-input">
                                <span class="form-check-label">Hair Cutting</span>
                            </label>
                            <label class="form-check">
                                <input type="checkbox" name="services" value="Hair Coloring" class="form-check-input">
                                <span class="form-check-label">Hair Coloring</span>
                            </label>
                            <label class="form-check">
                                <input type="checkbox" name="services" value="Catering" class="form-check-input">
                                <span class="form-check-label">Catering</span>
                            </label>
                            <label class="form-check">
                                <input type="checkbox" name="services" value="Event Planning" class="form-check-input">
                                <span class="form-check-label">Event Planning</span>
                            </label>
                            <label class="form-check">
                                <input type="checkbox" name="services" value="Consulting" class="form-check-input">
                                <span class="form-check-label">Consulting</span>
                            </label>
                        </div>
                        <div class="form-help mt-3">Select all services your business provides</div>
                    </div>

                    <!-- Validator Information -->
                    <div class="card border border-cyber-purple/40 bg-cyber-purple/10">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-cyber-purple to-green-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">⛓️ Blockchain Validator Benefits</h3>
                                <ul class="text-cyber-purple/90 leading-relaxed space-y-2">
                                    <li>• <strong>Verified Business Identity</strong> on the blockchain</li>
                                    <li>• <strong>Mining and Validation Privileges</strong> to earn rewards</li>
                                    <li>• <strong>Token Minting Capabilities</strong> for loyalty programs</li>
                                    <li>• <strong>Public Validator Profile</strong> with trust metrics</li>
                                    <li>• <strong>Network Governance Participation</strong> in platform decisions</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox"
                                   id="acceptTerms"
                                   required
                                   x-model="form.acceptTerms"
                                   class="form-check-input">
                            <label for="acceptTerms" class="form-check-label">
                                I confirm that the business information provided is accurate, agree to operate as a trusted validator on the ONNYX network, and understand my responsibilities for maintaining network integrity and security.
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-group">
                        <button type="submit"
                                :disabled="!canSubmit"
                                :class="canSubmit ? 'btn btn-primary btn-lg w-full' : 'btn btn-primary btn-lg w-full opacity-50 cursor-not-allowed'"
                                class="transition-all duration-300">
                            <span x-show="!submitting" class="flex items-center justify-center gap-3">
                                <span class="text-xl">🏢</span>
                                <span>Register Business Validator</span>
                            </span>
                            <span x-show="submitting" class="flex items-center justify-center gap-3">
                                <div class="spinner inline-block w-5 h-5"></div>
                                <span>Registering on Blockchain...</span>
                            </span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Enhanced Footer -->
            <div class="card-footer justify-center">
                <a href="{{ url_for('dashboard.overview') }}" class="btn btn-ghost">
                    ← Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function selaForm() {
    return {
        form: {
            sela_name: '',
            category: '',
            description: '',
            address: '',
            acceptTerms: false
        },
        businessValidation: {
            valid: null,
            message: '',
            checking: false
        },
        submitting: false,

        get canSubmit() {
            return this.form.sela_name &&
                   this.form.category &&
                   this.form.description &&
                   this.form.acceptTerms &&
                   this.businessValidation.valid !== false &&
                   !this.submitting;
        },

        async validateBusinessName() {
            if (!this.form.sela_name) return;

            this.businessValidation.checking = true;
            this.businessValidation.message = '';

            try {
                const response = await fetch('/auth/api/validate/business', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ business_name: this.form.sela_name })
                });

                const result = await response.json();
                this.businessValidation.valid = result.valid;
                this.businessValidation.message = result.message;
            } catch (error) {
                this.businessValidation.valid = false;
                this.businessValidation.message = 'Validation failed';
            } finally {
                this.businessValidation.checking = false;
            }
        },

        handleSubmit(event) {
            if (!this.canSubmit) {
                event.preventDefault();
                return;
            }
            this.submitting = true;
        }
    }
}
</script>
{% endblock %}
