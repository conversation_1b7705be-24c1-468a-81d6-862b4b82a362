"""
Onnyx Voice Scroll Model

This module defines the VoiceScroll model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.voice_scroll")

class VoiceScroll(BaseModel):
    """
    VoiceScroll model for the Onnyx blockchain.

    A VoiceScroll represents a governance proposal in the Onnyx ecosystem.
    """

    TABLE_NAME = "voice_scrolls"
    PRIMARY_KEY = "scroll_id"

    def __init__(
        self,
        scroll_id: str,
        creator_id: str,
        title: str,
        description: str,
        category: str,
        status: str,
        created_at: int,
        expires_at: int,
        effect: Optional[Dict[str, Any]] = None,
        votes: Optional[Dict[str, str]] = None,
        outcome: Optional[str] = None,
        resolved_at: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a VoiceScroll.

        Args:
            scroll_id: The scroll ID
            creator_id: The identity ID of the creator
            title: The title of the scroll
            description: The description of the scroll
            category: The category of the scroll
            status: The status of the scroll
            created_at: The timestamp when the scroll was created
            expires_at: The timestamp when the scroll expires
            effect: The effect of the scroll (optional)
            votes: The votes on the scroll (optional)
            outcome: The outcome of the scroll (optional)
            resolved_at: The timestamp when the scroll was resolved (optional)
            metadata: Additional metadata for the scroll (optional)
        """
        self.scroll_id = scroll_id
        self.creator_id = creator_id
        self.title = title
        self.description = description
        self.category = category
        self.status = status
        self.created_at = created_at
        self.expires_at = expires_at
        self.effect = effect or {}
        self.votes = votes or {}
        self.outcome = outcome
        self.resolved_at = resolved_at
        self.metadata = metadata or {}

    @classmethod
    def create_table(cls) -> None:
        """Create the VoiceScroll table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            creator_id TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            category TEXT NOT NULL,
            status TEXT NOT NULL,
            created_at INTEGER NOT NULL,
            expires_at INTEGER NOT NULL,
            effect TEXT,
            votes TEXT,
            outcome TEXT,
            resolved_at INTEGER,
            metadata TEXT
        )
        """)

        conn.commit()
        conn.close()

    @classmethod
    def create(
        cls,
        scroll_id: str,
        creator_id: str,
        title: str,
        description: str,
        category: str,
        expiry_days: int = 7,
        effect: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "VoiceScroll":
        """
        Create a new VoiceScroll.

        Args:
            scroll_id: The scroll ID
            creator_id: The identity ID of the creator
            title: The title of the scroll
            description: The description of the scroll
            category: The category of the scroll
            expiry_days: The number of days until the scroll expires (default: 7)
            effect: The effect of the scroll (optional)
            metadata: Additional metadata for the scroll (optional)

        Returns:
            The created VoiceScroll
        """
        # Create the table if it doesn't exist
        cls.create_table()

        # Calculate timestamps
        created_at = int(time.time())
        expires_at = created_at + (expiry_days * 86400)

        # Create the VoiceScroll
        scroll = cls(
            scroll_id=scroll_id,
            creator_id=creator_id,
            title=title,
            description=description,
            category=category,
            status="active",
            created_at=created_at,
            expires_at=expires_at,
            effect=effect,
            metadata=metadata
        )

        # Save the VoiceScroll to the database
        scroll.save()

        return scroll

    def save(self) -> None:
        """Save the VoiceScroll to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.scroll_id,
            self.creator_id,
            self.title,
            self.description,
            self.category,
            self.status,
            self.created_at,
            self.expires_at,
            json.dumps(self.effect),
            json.dumps(self.votes),
            self.outcome,
            self.resolved_at,
            json.dumps(self.metadata)
        ))

        conn.commit()
        conn.close()

    @classmethod
    def get_by_id(cls, scroll_id: str) -> Optional["VoiceScroll"]:
        """
        Get a VoiceScroll by ID.

        Args:
            scroll_id: The scroll ID

        Returns:
            The VoiceScroll, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (scroll_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return cls(
                scroll_id=row[0],
                creator_id=row[1],
                title=row[2],
                description=row[3],
                category=row[4],
                status=row[5],
                created_at=row[6],
                expires_at=row[7],
                effect=json.loads(row[8]) if row[8] else {},
                votes=json.loads(row[9]) if row[9] else {},
                outcome=row[10],
                resolved_at=row[11],
                metadata=json.loads(row[12]) if row[12] else {}
            )

        return None

    @classmethod
    def get_all(cls) -> List["VoiceScroll"]:
        """
        Get all VoiceScrolls.

        Returns:
            A list of all VoiceScrolls
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        FROM {cls.TABLE_NAME}
        """)

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                scroll_id=row[0],
                creator_id=row[1],
                title=row[2],
                description=row[3],
                category=row[4],
                status=row[5],
                created_at=row[6],
                expires_at=row[7],
                effect=json.loads(row[8]) if row[8] else {},
                votes=json.loads(row[9]) if row[9] else {},
                outcome=row[10],
                resolved_at=row[11],
                metadata=json.loads(row[12]) if row[12] else {}
            )
            for row in rows
        ]

    @classmethod
    def find_by_status(cls, status: str) -> List["VoiceScroll"]:
        """
        Find VoiceScrolls by status.

        Args:
            status: The status to search for

        Returns:
            A list of VoiceScrolls with the status
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE status = ?
        """, (status,))

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                scroll_id=row[0],
                creator_id=row[1],
                title=row[2],
                description=row[3],
                category=row[4],
                status=row[5],
                created_at=row[6],
                expires_at=row[7],
                effect=json.loads(row[8]) if row[8] else {},
                votes=json.loads(row[9]) if row[9] else {},
                outcome=row[10],
                resolved_at=row[11],
                metadata=json.loads(row[12]) if row[12] else {}
            )
            for row in rows
        ]

    @classmethod
    def find_by_category(cls, category: str) -> List["VoiceScroll"]:
        """
        Find VoiceScrolls by category.

        Args:
            category: The category to search for

        Returns:
            A list of VoiceScrolls in the category
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE category = ?
        """, (category,))

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                scroll_id=row[0],
                creator_id=row[1],
                title=row[2],
                description=row[3],
                category=row[4],
                status=row[5],
                created_at=row[6],
                expires_at=row[7],
                effect=json.loads(row[8]) if row[8] else {},
                votes=json.loads(row[9]) if row[9] else {},
                outcome=row[10],
                resolved_at=row[11],
                metadata=json.loads(row[12]) if row[12] else {}
            )
            for row in rows
        ]

    @classmethod
    def find_by_creator(cls, creator_id: str) -> List["VoiceScroll"]:
        """
        Find VoiceScrolls by creator.

        Args:
            creator_id: The creator's identity ID

        Returns:
            A list of VoiceScrolls created by the identity
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE creator_id = ?
        """, (creator_id,))

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                scroll_id=row[0],
                creator_id=row[1],
                title=row[2],
                description=row[3],
                category=row[4],
                status=row[5],
                created_at=row[6],
                expires_at=row[7],
                effect=json.loads(row[8]) if row[8] else {},
                votes=json.loads(row[9]) if row[9] else {},
                outcome=row[10],
                resolved_at=row[11],
                metadata=json.loads(row[12]) if row[12] else {}
            )
            for row in rows
        ]

    @classmethod
    def find_by_outcome(cls, outcome: str) -> List["VoiceScroll"]:
        """
        Find VoiceScrolls by outcome.

        Args:
            outcome: The outcome to search for

        Returns:
            A list of VoiceScrolls with the outcome
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            creator_id,
            title,
            description,
            category,
            status,
            created_at,
            expires_at,
            effect,
            votes,
            outcome,
            resolved_at,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE outcome = ?
        """, (outcome,))

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                scroll_id=row[0],
                creator_id=row[1],
                title=row[2],
                description=row[3],
                category=row[4],
                status=row[5],
                created_at=row[6],
                expires_at=row[7],
                effect=json.loads(row[8]) if row[8] else {},
                votes=json.loads(row[9]) if row[9] else {},
                outcome=row[10],
                resolved_at=row[11],
                metadata=json.loads(row[12]) if row[12] else {}
            )
            for row in rows
        ]

    def add_vote(self, identity_id: str, decision: str) -> None:
        """
        Add a vote to the scroll.

        Args:
            identity_id: The identity ID of the voter
            decision: The decision ("yes", "no", or "abstain")
        """
        self.votes[identity_id] = decision
        self.save()

    def tally_votes(self) -> Dict[str, Any]:
        """
        Tally the votes on the scroll.

        Returns:
            The tally results
        """
        yes_votes = sum(1 for decision in self.votes.values() if decision == "yes")
        no_votes = sum(1 for decision in self.votes.values() if decision == "no")
        abstain_votes = sum(1 for decision in self.votes.values() if decision == "abstain")
        total_votes = len(self.votes)

        return {
            "yes": yes_votes,
            "no": no_votes,
            "abstain": abstain_votes,
            "total": total_votes
        }

    def resolve(self, outcome: str) -> None:
        """
        Resolve the scroll.

        Args:
            outcome: The outcome of the scroll
        """
        self.status = "resolved"
        self.outcome = outcome
        self.resolved_at = int(time.time())
        self.save()

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the VoiceScroll to a dictionary.

        Returns:
            The VoiceScroll as a dictionary
        """
        return {
            "scroll_id": self.scroll_id,
            "creator_id": self.creator_id,
            "title": self.title,
            "description": self.description,
            "category": self.category,
            "status": self.status,
            "created_at": self.created_at,
            "expires_at": self.expires_at,
            "effect": self.effect,
            "votes": self.votes,
            "outcome": self.outcome,
            "resolved_at": self.resolved_at,
            "metadata": self.metadata
        }
