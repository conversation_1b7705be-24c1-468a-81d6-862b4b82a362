{% extends "base.html" %}

{% block title %}Community Mining - ONNYX Platform{% endblock %}

{% block head %}
<style>
.mining-container {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    min-height: 100vh;
    padding: 2rem 0;
}

.mining-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.mining-button {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    font-weight: bold;
    padding: 1rem 2rem;
    border-radius: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mining-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 255, 247, 0.3);
}

.mining-button:disabled {
    background: linear-gradient(135deg, #666, #888);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.mining-button.mining {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(0, 255, 247, 0.1);
    border: 1px solid rgba(0, 255, 247, 0.3);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.compliance-status {
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.compliance-allowed {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.5);
    color: #22c55e;
}

.compliance-restricted {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.5);
    color: #ef4444;
}

.leaderboard {
    background: rgba(154, 0, 255, 0.1);
    border: 1px solid rgba(154, 0, 255, 0.3);
    border-radius: 1rem;
    padding: 1.5rem;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.mining-log {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0.75rem;
    padding: 1rem;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.log-entry {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.log-success {
    color: #22c55e;
}

.log-error {
    color: #ef4444;
}

.mining-progress {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    width: 0%;
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="mining-container">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-orbitron font-bold text-white mb-4">Community Mining</h1>
            <p class="text-xl text-text-secondary max-w-3xl mx-auto">
                Participate in lite mining to earn ONNYX tokens while supporting the covenant network
            </p>
        </div>

        <!-- Biblical Compliance Status -->
        <div class="compliance-status {% if biblical_status.mining_allowed %}compliance-allowed{% else %}compliance-restricted{% endif %}">
            <div class="flex items-center justify-center gap-2">
                <span class="text-2xl">
                    {% if biblical_status.mining_allowed %}✅{% else %}⏸️{% endif %}
                </span>
                <span class="font-semibold">{{ biblical_status.compliance_message }}</span>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Mining Control Panel -->
            <div class="lg:col-span-2">
                <div class="mining-card p-6">
                    <h2 class="text-2xl font-bold text-white mb-6 flex items-center gap-2">
                        <span>⛏️</span>
                        Mining Control
                    </h2>

                    <!-- Mining Stats -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{{ mining_stats.total_rewards|round(2) }}</div>
                            <div class="stat-label">Total ONX Earned</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{{ mining_stats.successful_attempts }}</div>
                            <div class="stat-label">Successful Attempts</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{{ mining_stats.efficiency }}%</div>
                            <div class="stat-label">Mining Efficiency</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{{ mining_stats.current_balance|round(2) }}</div>
                            <div class="stat-label">Current Balance</div>
                        </div>
                    </div>

                    <!-- Mining Controls -->
                    <div class="text-center mb-6">
                        {% if current_session %}
                        <div class="mb-4">
                            <div class="text-cyber-cyan font-semibold mb-2">Mining Session Active</div>
                            <div class="mining-progress">
                                <div class="progress-bar" style="width: 75%"></div>
                            </div>
                            <div class="text-sm text-text-secondary">
                                Attempts: <span id="session-attempts">{{ current_session.attempts or 0 }}</span>
                            </div>
                        </div>
                        <button id="mine-button" class="mining-button mining" onclick="attemptMining()">
                            ⚡ Mine Block
                        </button>
                        <button id="stop-button" class="mining-button ml-4" onclick="stopMining()">
                            ⏹️ Stop Mining
                        </button>
                        {% else %}
                        <button id="start-button" class="mining-button" onclick="startMining()" 
                                {% if not biblical_status.mining_allowed %}disabled{% endif %}>
                            🚀 Start Mining
                        </button>
                        {% endif %}
                    </div>

                    <!-- Mining Log -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-3">Mining Log</h3>
                        <div id="mining-log" class="mining-log">
                            <div class="log-entry">Ready to start community mining...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Community Leaderboard -->
                <div class="leaderboard">
                    <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-2">
                        <span>🏆</span>
                        Community Leaders
                    </h3>
                    {% for leader in leaderboard[:5] %}
                    <div class="leaderboard-item">
                        <div>
                            <div class="font-semibold text-white">{{ leader.name }}</div>
                            <div class="text-sm text-text-secondary">{{ leader.successful_attempts }} successful</div>
                        </div>
                        <div class="text-cyber-cyan font-bold">{{ leader.total_rewards|round(2) }} ONX</div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Mining Info -->
                <div class="mining-card p-4">
                    <h3 class="text-lg font-bold text-white mb-3">Mining Info</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Mining Power:</span>
                            <span class="text-cyber-cyan">{{ mining_stats.mining_power }}x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Base Reward:</span>
                            <span class="text-cyber-cyan">0.5 ONX</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Success Rate:</span>
                            <span class="text-cyber-cyan">~5-15%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Network Type:</span>
                            <span class="text-cyber-cyan">Community Lite</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mining-card p-4">
                    <h3 class="text-lg font-bold text-white mb-3">Quick Actions</h3>
                    <div class="space-y-2">
                        <a href="{{ url_for('dashboard.overview') }}" class="block w-full text-center py-2 px-4 bg-glass-bg border border-glass-border rounded-lg text-cyber-cyan hover:bg-glass-hover transition-colors">
                            📊 Dashboard
                        </a>
                        <a href="{{ url_for('explorer.index') }}" class="block w-full text-center py-2 px-4 bg-glass-bg border border-glass-border rounded-lg text-cyber-cyan hover:bg-glass-hover transition-colors">
                            🔍 Explorer
                        </a>
                        <a href="{{ url_for('sela.directory') }}" class="block w-full text-center py-2 px-4 bg-glass-bg border border-glass-border rounded-lg text-cyber-cyan hover:bg-glass-hover transition-colors">
                            🏢 Sela Directory
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let miningActive = {{ 'true' if current_session else 'false' }};
let sessionAttempts = {{ current_session.attempts or 0 if current_session else 0 }};

function addLogEntry(message, type = 'info') {
    const log = document.getElementById('mining-log');
    const timestamp = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.className = `log-entry ${type === 'success' ? 'log-success' : type === 'error' ? 'log-error' : ''}`;
    entry.textContent = `[${timestamp}] ${message}`;
    log.appendChild(entry);
    log.scrollTop = log.scrollHeight;
}

async function startMining() {
    try {
        const response = await fetch('/community-mining/api/start-mining', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            miningActive = true;
            sessionAttempts = 0;
            updateUI();
            addLogEntry('Mining session started successfully!', 'success');
        } else {
            addLogEntry(`Failed to start mining: ${result.error}`, 'error');
        }
    } catch (error) {
        addLogEntry(`Error starting mining: ${error.message}`, 'error');
    }
}

async function attemptMining() {
    if (!miningActive) return;
    
    try {
        addLogEntry('Attempting to mine block...');
        
        const response = await fetch('/community-mining/api/mine-block', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            addLogEntry(`Block mined successfully! Reward: ${result.reward} ONX`, 'success');
            // Update stats
            location.reload();
        } else {
            addLogEntry(result.message || 'Mining attempt unsuccessful');
        }
        
        sessionAttempts = result.total_attempts || sessionAttempts + 1;
        document.getElementById('session-attempts').textContent = sessionAttempts;
        
    } catch (error) {
        addLogEntry(`Mining error: ${error.message}`, 'error');
    }
}

async function stopMining() {
    try {
        const response = await fetch('/community-mining/api/stop-mining', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            miningActive = false;
            updateUI();
            addLogEntry('Mining session ended', 'success');
            addLogEntry(`Session stats: ${result.session_stats.successful_attempts}/${result.session_stats.total_attempts} successful`);
            
            // Reload to update stats
            setTimeout(() => location.reload(), 2000);
        } else {
            addLogEntry(`Failed to stop mining: ${result.error}`, 'error');
        }
    } catch (error) {
        addLogEntry(`Error stopping mining: ${error.message}`, 'error');
    }
}

function updateUI() {
    const startButton = document.getElementById('start-button');
    const mineButton = document.getElementById('mine-button');
    const stopButton = document.getElementById('stop-button');
    
    if (miningActive) {
        if (startButton) startButton.style.display = 'none';
        if (mineButton) mineButton.style.display = 'inline-block';
        if (stopButton) stopButton.style.display = 'inline-block';
    } else {
        if (startButton) startButton.style.display = 'inline-block';
        if (mineButton) mineButton.style.display = 'none';
        if (stopButton) stopButton.style.display = 'none';
    }
}

// Auto-mine every 10 seconds when active
setInterval(() => {
    if (miningActive && {{ biblical_status.mining_allowed|lower }}) {
        attemptMining();
    }
}, 10000);

// Initialize UI
updateUI();
</script>
{% endblock %}
