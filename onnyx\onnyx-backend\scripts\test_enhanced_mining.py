#!/usr/bin/env python3
"""
Enhanced Mining System Test Script
Validates the implementation of biblical tokenomics mining features
"""

import os
import sys
import logging
from datetime import datetime, timezone

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

logger = logging.getLogger(__name__)

def test_enhanced_mining_system():
    """Test the enhanced biblical mining system."""
    
    print("🛡️ ONNYX Enhanced Mining System Test")
    print("=" * 50)
    
    try:
        from shared.models.tokenomics import biblical_tokenomics
        print("✅ Biblical tokenomics module loaded successfully")
    except ImportError as e:
        print(f"❌ Failed to import biblical tokenomics: {e}")
        return False
    
    # Test 1: Mining Schedule Enforcement
    print("\n📅 Testing 24/6 Mining Schedule:")
    try:
        mining_allowed = biblical_tokenomics.is_mining_allowed()
        is_sabbath = biblical_tokenomics.is_sabbath_period()
        is_new_moon = biblical_tokenomics.is_new_moon_period()
        is_feast_day = biblical_tokenomics.is_feast_day()
        
        print(f"   Mining Allowed: {mining_allowed}")
        print(f"   Sabbath Period: {is_sabbath}")
        print(f"   New Moon Period: {is_new_moon}")
        print(f"   Feast Day: {is_feast_day}")
        print("✅ Mining schedule enforcement working")
    except Exception as e:
        print(f"❌ Mining schedule test failed: {e}")
        return False
    
    # Test 2: Annual Emission Caps
    print("\n💰 Testing Annual Emission Caps:")
    try:
        annual_cap = biblical_tokenomics.ANNUAL_MINING_CAP
        daily_emission = biblical_tokenomics.DAILY_EMISSION
        hourly_emission = biblical_tokenomics.HOURLY_EMISSION
        
        print(f"   Annual Cap: {annual_cap:,.0f} ONX")
        print(f"   Daily Target: {daily_emission:,.0f} ONX")
        print(f"   Hourly Target: {hourly_emission:,.0f} ONX")
        
        # Test cap enforcement
        test_reward = biblical_tokenomics._enforce_annual_cap(1000.0)
        print(f"   Test Reward (1000 ONX): {test_reward}")
        print("✅ Annual emission caps configured")
    except Exception as e:
        print(f"❌ Annual emission test failed: {e}")
        return False
    
    # Test 3: Tier Multiplier System
    print("\n🏆 Testing Tier Multiplier System:")
    try:
        tier_multipliers = biblical_tokenomics.TIER_MULTIPLIERS
        print("   Tier Multipliers:")
        for tier, multiplier in tier_multipliers.items():
            print(f"     {tier}: {multiplier}×")
        
        # Test tier calculation for a test identity
        test_identity = "test_identity_123"
        tier_multiplier = biblical_tokenomics._get_tier_multiplier(test_identity)
        print(f"   Test Identity Tier: {tier_multiplier}×")
        print("✅ Tier multiplier system working")
    except Exception as e:
        print(f"❌ Tier multiplier test failed: {e}")
        return False
    
    # Test 4: Righteous Bonus System
    print("\n🌟 Testing Righteous Bonus System:")
    try:
        righteous_bonus_rate = biblical_tokenomics.RIGHTEOUS_BONUS_RATE
        penalty_multiplier = biblical_tokenomics.PENALTY_MULTIPLIER
        
        print(f"   Righteous Bonus Rate: +{righteous_bonus_rate*100}%")
        print(f"   Penalty Multiplier: {penalty_multiplier}×")
        
        # Test bonus eligibility
        test_identity = "test_identity_123"
        bonus_eligible = biblical_tokenomics._check_righteous_bonus(test_identity)
        penalty_status = biblical_tokenomics._check_penalty_status(test_identity)
        
        print(f"   Test Identity Bonus Eligible: {bonus_eligible}")
        print(f"   Test Identity Penalty Status: {penalty_status}×")
        print("✅ Righteous bonus system working")
    except Exception as e:
        print(f"❌ Righteous bonus test failed: {e}")
        return False
    
    # Test 5: Enhanced Mining Reward Calculation
    print("\n⚡ Testing Enhanced Mining Reward Calculation:")
    try:
        test_identity = "test_identity_123"
        base_reward = 10.0
        
        effective_reward, gleaning_allocation, righteous_bonus = biblical_tokenomics.calculate_enhanced_mining_reward(
            test_identity, base_reward
        )
        
        print(f"   Base Reward: {base_reward} ONX")
        print(f"   Effective Reward: {effective_reward} ONX")
        print(f"   Gleaning Allocation: {gleaning_allocation} ONX")
        print(f"   Righteous Bonus Applied: {righteous_bonus}")
        print("✅ Enhanced mining reward calculation working")
    except Exception as e:
        print(f"❌ Enhanced mining reward test failed: {e}")
        return False
    
    # Test 6: API Integration
    print("\n🔌 Testing API Integration:")
    try:
        # Test if we can import the API routes
        from web.routes.api import api_bp
        print("   API blueprint loaded successfully")
        
        # Check if enhanced endpoints are registered
        endpoints = [rule.rule for rule in api_bp.url_map.iter_rules()]
        enhanced_endpoints = [
            '/api/tokenomics/mining/status',
            '/api/tokenomics/mining/tier/<identity_id>'
        ]
        
        for endpoint in enhanced_endpoints:
            if any(endpoint.replace('<identity_id>', '') in ep for ep in endpoints):
                print(f"   ✅ {endpoint} endpoint available")
            else:
                print(f"   ⚠️ {endpoint} endpoint not found")
        
        print("✅ API integration test completed")
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False
    
    # Summary
    print("\n📊 Test Summary:")
    print("=" * 50)
    print("✅ All enhanced mining system tests passed!")
    print("\n🎯 System Ready For:")
    print("   • 24/6 mining schedule enforcement")
    print("   • Annual emission cap monitoring")
    print("   • Tier-based reward multipliers")
    print("   • Righteous bonus calculations")
    print("   • Biblical compliance tracking")
    
    return True

def test_mining_scenarios():
    """Test specific mining scenarios."""
    
    print("\n🎮 Testing Mining Scenarios:")
    print("-" * 30)
    
    try:
        from shared.models.tokenomics import biblical_tokenomics
        
        scenarios = [
            ("Citizen (1 Sela)", "citizen_identity", 1.0),
            ("Basic Sela Owner", "basic_sela_identity", 2.5),
            ("Dual Sela Owner", "dual_sela_identity", 5.5),
            ("Pro Validator", "pro_validator_identity", 9.0)
        ]
        
        base_reward = 10.0
        
        for scenario_name, identity_id, expected_tier in scenarios:
            print(f"\n   {scenario_name}:")
            
            # Mock tier calculation (in real system, this would query database)
            tier_multiplier = expected_tier
            effective_reward = base_reward * tier_multiplier
            
            print(f"     Base Reward: {base_reward} ONX")
            print(f"     Tier Multiplier: {tier_multiplier}×")
            print(f"     Effective Reward: {effective_reward} ONX")
            
            # Calculate annual potential
            daily_potential = effective_reward * 24  # 24 hours
            annual_potential = daily_potential * 313  # 313 active days
            
            print(f"     Daily Potential: {daily_potential:,.0f} ONX")
            print(f"     Annual Potential: {annual_potential:,.0f} ONX")
        
        print("\n✅ Mining scenarios tested successfully")
        
    except Exception as e:
        print(f"❌ Mining scenarios test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Starting Enhanced Mining System Tests...")
    
    # Run main tests
    main_success = test_enhanced_mining_system()
    
    # Run scenario tests
    scenario_success = test_mining_scenarios()
    
    if main_success and scenario_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The enhanced mining system is ready for deployment.")
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("Please review the errors above before deployment.")
        sys.exit(1)
