"""
Security Headers Middleware for ONNYX Platform
Implements XSS protection, CSP, and other security headers
"""

from flask import request, make_response
import html
import re

class SecurityHeaders:
    """Security headers and XSS protection middleware"""
    
    @staticmethod
    def add_security_headers(response):
        """Add comprehensive security headers to response"""
        
        # Content Security Policy - Strict policy to prevent XSS
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; "
            "font-src 'self' https://fonts.gstatic.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
        
        # Security headers
        security_headers = {
            'Content-Security-Policy': csp_policy,
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response
    
    @staticmethod
    def sanitize_html_output(text):
        """Sanitize HTML output to prevent XSS"""
        if not isinstance(text, str):
            return text
        
        # HTML escape dangerous characters
        text = html.escape(text)
        
        # Remove any remaining script tags
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove javascript: URLs
        text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)
        
        # Remove on* event handlers
        text = re.sub(r'\son\w+\s*=', '', text, flags=re.IGNORECASE)
        
        return text
    
    @staticmethod
    def sanitize_user_input(data):
        """Sanitize user input to prevent XSS and injection attacks"""
        if isinstance(data, str):
            # Remove potentially dangerous characters
            data = re.sub(r'[<>"\']', '', data)
            
            # Remove script tags
            data = re.sub(r'<script[^>]*>.*?</script>', '', data, flags=re.IGNORECASE | re.DOTALL)
            
            # Remove javascript: URLs
            data = re.sub(r'javascript:', '', data, flags=re.IGNORECASE)
            
            # Remove SQL injection patterns
            sql_patterns = [
                r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
                r'(--|#|/\*|\*/)',
                r'(\bOR\b.*=.*\bOR\b)',
                r'(\bAND\b.*=.*\bAND\b)',
                r'(\'.*\bOR\b.*\')',
                r'(\".*\bOR\b.*\")'
            ]
            
            for pattern in sql_patterns:
                data = re.sub(pattern, '', data, flags=re.IGNORECASE)
            
            # Limit length
            data = data[:1000]
            
            return data.strip()
        
        elif isinstance(data, dict):
            return {k: SecurityHeaders.sanitize_user_input(v) for k, v in data.items()}
        
        elif isinstance(data, list):
            return [SecurityHeaders.sanitize_user_input(item) for item in data]
        
        else:
            return data
    
    @staticmethod
    def validate_file_upload(file):
        """Validate file uploads for security"""
        if not file:
            return False, "No file provided"
        
        # Check file size (max 10MB)
        if len(file.read()) > 10 * 1024 * 1024:
            return False, "File too large (max 10MB)"
        
        file.seek(0)  # Reset file pointer
        
        # Check file extension
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt', '.csv'}
        filename = file.filename.lower()
        
        if not any(filename.endswith(ext) for ext in allowed_extensions):
            return False, "File type not allowed"
        
        # Check for dangerous content in filename
        dangerous_patterns = ['..', '/', '\\', '<', '>', '|', ':', '*', '?', '"']
        if any(pattern in filename for pattern in dangerous_patterns):
            return False, "Invalid filename"
        
        return True, "File is valid"
    
    @staticmethod
    def log_security_violation(violation_type, details, user_id=None):
        """Log security violations for monitoring"""
        from web.auth_decorators import log_security_event
        
        log_security_event(f'SECURITY_VIOLATION_{violation_type}', user_id, {
            'violation_type': violation_type,
            'details': details,
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', '')[:100],
            'endpoint': request.endpoint,
            'method': request.method
        })

def init_security_middleware(app):
    """Initialize security middleware for Flask app"""
    
    @app.after_request
    def add_security_headers(response):
        """Add security headers to all responses"""
        return SecurityHeaders.add_security_headers(response)
    
    @app.before_request
    def security_checks():
        """Perform security checks on incoming requests"""
        
        # Check for common attack patterns in URL
        dangerous_url_patterns = [
            '../', '..\\', '<script', 'javascript:', 'vbscript:',
            'onload=', 'onerror=', 'onclick=', 'eval(', 'alert(',
            'document.cookie', 'document.write'
        ]
        
        request_url = request.url.lower()
        for pattern in dangerous_url_patterns:
            if pattern in request_url:
                SecurityHeaders.log_security_violation('MALICIOUS_URL', {
                    'url': request.url,
                    'pattern': pattern
                })
                # Don't block, just log for now
                break
        
        # Check User-Agent for known attack patterns
        user_agent = request.headers.get('User-Agent', '').lower()
        malicious_ua_patterns = [
            'sqlmap', 'nikto', 'nessus', 'openvas', 'nmap',
            'masscan', 'zap', 'burp', 'w3af', 'skipfish'
        ]
        
        for pattern in malicious_ua_patterns:
            if pattern in user_agent:
                SecurityHeaders.log_security_violation('MALICIOUS_USER_AGENT', {
                    'user_agent': user_agent,
                    'pattern': pattern
                })
                break
        
        # Check for oversized requests
        if request.content_length and request.content_length > 50 * 1024 * 1024:  # 50MB
            SecurityHeaders.log_security_violation('OVERSIZED_REQUEST', {
                'content_length': request.content_length
            })
            return "Request too large", 413
    
    return app

# Template filters for XSS protection
def safe_output(text):
    """Template filter for safe HTML output"""
    return SecurityHeaders.sanitize_html_output(str(text))

def safe_json(data):
    """Template filter for safe JSON output"""
    import json
    sanitized_data = SecurityHeaders.sanitize_user_input(data)
    return json.dumps(sanitized_data)
