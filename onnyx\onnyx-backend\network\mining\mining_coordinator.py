"""
ONNYX Mining Coordinator
Coordinates mining activities across the P2P network with biblical governance
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class NetworkMiningStatus(Enum):
    ACTIVE = "active"
    SABBATH_REST = "sabbath_rest"
    MAINTENANCE = "maintenance"
    EMERGENCY_HALT = "emergency_halt"

@dataclass
class MinerRegistration:
    miner_id: str
    tribal_code: str
    node_type: str
    mining_power: float
    biblical_compliance_score: float
    last_active: int
    blocks_mined: int = 0
    total_rewards: float = 0.0

@dataclass
class MiningRound:
    round_id: str
    block_height: int
    eligible_miners: List[str]
    selected_miner: Optional[str]
    proposals_received: Dict[str, dict]
    start_time: int
    end_time: Optional[int] = None
    status: str = "active"

class MiningCoordinator:
    """
    Coordinates mining activities across the ONNYX P2P network
    Implements biblical governance and fair distribution
    """

    def __init__(self, peer_manager, consensus_engine):
        self.peer_manager = peer_manager
        self.consensus = consensus_engine

        # Network mining state
        self.network_status = NetworkMiningStatus.ACTIVE
        self.registered_miners: Dict[str, MinerRegistration] = {}
        self.active_mining_rounds: Dict[int, MiningRound] = {}
        self.mining_history: List[dict] = []

        # Biblical governance parameters
        self.sabbath_enforcement = True
        self.gleaning_pool_mandatory = True
        self.anti_usury_enforcement = True
        self.concentration_limits = True

        # Mining coordination parameters
        self.block_time_target = 600  # 10 minutes
        self.max_miners_per_round = 12  # One per tribe
        self.mining_rotation_enabled = True
        self.difficulty_adjustment_blocks = 144  # Adjust every 144 blocks (24 hours)

        # Performance tracking
        self.total_blocks_coordinated = 0
        self.total_miners_registered = 0
        self.average_block_time = 600
        self.network_hash_rate = 0.0

        # Register coordination message handlers
        self._register_coordination_handlers()

        # Start background coordination tasks
        asyncio.create_task(self._mining_coordination_loop())
        asyncio.create_task(self._sabbath_monitoring_loop())
        asyncio.create_task(self._difficulty_adjustment_loop())

        logger.info("Mining Coordinator initialized")

    def _register_coordination_handlers(self):
        """Register message handlers for mining coordination"""
        handlers = {
            'miner_registration': self._handle_miner_registration,
            'mining_round_start': self._handle_mining_round_start,
            'mining_proposal_submission': self._handle_proposal_submission,
            'mining_round_completion': self._handle_round_completion,
            'network_status_request': self._handle_network_status_request,
            'difficulty_adjustment': self._handle_difficulty_adjustment,
            'sabbath_enforcement': self._handle_sabbath_enforcement
        }

        for msg_type, handler in handlers.items():
            self.peer_manager.register_message_handler(msg_type, handler)

    async def start_coordination(self) -> bool:
        """Start mining coordination across the network"""
        try:
            logger.info("Starting mining coordination...")

            # Check network readiness
            tribal_elders = self.peer_manager.get_tribal_elders()
            if len(tribal_elders) < 8:
                logger.warning("Insufficient tribal elders for mining coordination")
                return False

            # Initialize mining parameters
            await self._initialize_mining_parameters()

            # Start first mining round
            await self._start_new_mining_round()

            logger.info("Mining coordination started successfully")
            return True

        except Exception as e:
            logger.error(f"Error starting mining coordination: {e}")
            return False

    async def register_miner(self, miner_id: str, tribal_code: str,
                           node_type: str, mining_power: float = 1.0) -> bool:
        """Register a new miner in the network"""
        try:
            # Validate miner eligibility
            if not await self._validate_miner_eligibility(miner_id, tribal_code):
                logger.warning(f"Miner {miner_id} failed eligibility validation")
                return False

            # Calculate biblical compliance score
            compliance_score = await self._calculate_miner_compliance(miner_id)

            # Create miner registration
            registration = MinerRegistration(
                miner_id=miner_id,
                tribal_code=tribal_code,
                node_type=node_type,
                mining_power=mining_power,
                biblical_compliance_score=compliance_score,
                last_active=int(time.time())
            )

            self.registered_miners[miner_id] = registration
            self.total_miners_registered += 1

            # Broadcast miner registration
            await self._broadcast_miner_registration(registration)

            logger.info(f"Registered miner {miner_id} from tribe {tribal_code}")
            return True

        except Exception as e:
            logger.error(f"Error registering miner {miner_id}: {e}")
            return False

    async def _start_new_mining_round(self) -> Optional[MiningRound]:
        """Start a new mining round"""
        try:
            # Get current block height
            current_height = await self._get_current_block_height()
            next_height = current_height + 1

            # Check if mining is allowed (Sabbath compliance)
            if not await self._is_mining_allowed():
                logger.info("Mining not allowed - Sabbath period or other restrictions")
                return None

            # Select eligible miners for this round
            eligible_miners = await self._select_eligible_miners(next_height)

            if not eligible_miners:
                logger.warning("No eligible miners for mining round")
                return None

            # Create mining round
            round_id = f"round_{next_height}_{int(time.time())}"
            mining_round = MiningRound(
                round_id=round_id,
                block_height=next_height,
                eligible_miners=eligible_miners,
                selected_miner=None,
                proposals_received={},
                start_time=int(time.time())
            )

            self.active_mining_rounds[next_height] = mining_round

            # Broadcast mining round start
            await self._broadcast_mining_round_start(mining_round)

            logger.info(f"Started mining round {round_id} for block {next_height}")
            return mining_round

        except Exception as e:
            logger.error(f"Error starting new mining round: {e}")
            return None

    async def _select_eligible_miners(self, block_height: int) -> List[str]:
        """Select eligible miners for a mining round"""
        try:
            eligible = []

            # Filter miners by biblical compliance
            for miner_id, registration in self.registered_miners.items():
                # Check compliance score
                if registration.biblical_compliance_score < 0.8:
                    continue

                # Check if miner is active
                if int(time.time()) - registration.last_active > 3600:  # 1 hour
                    continue

                # Check tribal rotation if enabled
                if self.mining_rotation_enabled:
                    if not await self._is_miner_turn(miner_id, block_height):
                        continue

                eligible.append(miner_id)

            # Limit to max miners per round
            if len(eligible) > self.max_miners_per_round:
                # Prioritize by compliance score and tribal diversity
                eligible = await self._prioritize_miners(eligible)
                eligible = eligible[:self.max_miners_per_round]

            return eligible

        except Exception as e:
            logger.error(f"Error selecting eligible miners: {e}")
            return []

    async def _prioritize_miners(self, miners: List[str]) -> List[str]:
        """Prioritize miners based on biblical principles"""
        try:
            # Create priority list with scoring
            miner_scores = []

            for miner_id in miners:
                registration = self.registered_miners[miner_id]

                # Base score from compliance
                score = registration.biblical_compliance_score * 100

                # Bonus for tribal diversity
                tribal_code = registration.tribal_code
                tribal_count = len([m for m in miners
                                  if self.registered_miners[m].tribal_code == tribal_code])
                if tribal_count == 1:  # Only miner from this tribe
                    score += 20

                # Bonus for gleaning pool participation
                if await self._has_gleaning_participation(miner_id):
                    score += 10

                # Penalty for recent mining (encourage rotation)
                recent_blocks = await self._get_recent_blocks_by_miner(miner_id, 10)
                score -= len(recent_blocks) * 5

                miner_scores.append((miner_id, score))

            # Sort by score (highest first)
            miner_scores.sort(key=lambda x: x[1], reverse=True)

            return [miner_id for miner_id, score in miner_scores]

        except Exception as e:
            logger.error(f"Error prioritizing miners: {e}")
            return miners

    async def _broadcast_mining_round_start(self, mining_round: MiningRound):
        """Broadcast mining round start to eligible miners"""
        try:
            message = self.peer_manager.NetworkMessage(
                message_type="mining_round_start",
                sender_id=self.peer_manager.node_id,
                recipient_id=None,
                data={
                    "round_id": mining_round.round_id,
                    "block_height": mining_round.block_height,
                    "eligible_miners": mining_round.eligible_miners,
                    "start_time": mining_round.start_time,
                    "target_time": self.block_time_target
                },
                timestamp=int(time.time())
            )

            await self.peer_manager.broadcast_message(message)

        except Exception as e:
            logger.error(f"Error broadcasting mining round start: {e}")

    async def _complete_mining_round(self, mining_round: MiningRound, winning_proposal: dict):
        """Complete a mining round with the winning proposal"""
        try:
            mining_round.end_time = int(time.time())
            mining_round.status = "completed"
            mining_round.selected_miner = winning_proposal["miner_id"]

            # Update miner statistics
            winner_id = winning_proposal["miner_id"]
            if winner_id in self.registered_miners:
                self.registered_miners[winner_id].blocks_mined += 1
                self.registered_miners[winner_id].last_active = int(time.time())

            # Calculate block time
            block_time = mining_round.end_time - mining_round.start_time
            self._update_average_block_time(block_time)

            # Record mining history
            self.mining_history.append({
                "round_id": mining_round.round_id,
                "block_height": mining_round.block_height,
                "winning_miner": winner_id,
                "proposals_count": len(mining_round.proposals_received),
                "block_time": block_time,
                "timestamp": mining_round.end_time
            })

            self.total_blocks_coordinated += 1

            # Broadcast round completion
            await self._broadcast_round_completion(mining_round, winning_proposal)

            # Start next mining round
            await asyncio.sleep(1)  # Brief pause
            await self._start_new_mining_round()

            logger.info(f"Completed mining round {mining_round.round_id}, winner: {winner_id}")

        except Exception as e:
            logger.error(f"Error completing mining round: {e}")

    # Message Handlers
    async def _handle_miner_registration(self, message):
        """Handle miner registration requests"""
        try:
            data = message.data
            success = await self.register_miner(
                miner_id=data["miner_id"],
                tribal_code=data["tribal_code"],
                node_type=data["node_type"],
                mining_power=data.get("mining_power", 1.0)
            )

            # Send registration response
            response = self.peer_manager.NetworkMessage(
                message_type="miner_registration_response",
                sender_id=self.peer_manager.node_id,
                recipient_id=message.sender_id,
                data={
                    "miner_id": data["miner_id"],
                    "success": success,
                    "network_status": self.network_status.value
                },
                timestamp=int(time.time())
            )

            await self.peer_manager.send_to_peer(message.sender_id, response)

        except Exception as e:
            logger.error(f"Error handling miner registration: {e}")

    async def _handle_mining_round_start(self, message):
        """Handle mining round start notifications"""
        logger.info(f"Mining round started: {message.data['round_id']}")

    async def _handle_proposal_submission(self, message):
        """Handle mining proposal submissions"""
        try:
            data = message.data
            round_id = data["round_id"]
            block_height = data["block_height"]

            if block_height in self.active_mining_rounds:
                mining_round = self.active_mining_rounds[block_height]
                miner_id = data["miner_id"]

                # Validate proposal
                if await self._validate_mining_proposal(data["proposal"], mining_round):
                    mining_round.proposals_received[miner_id] = data["proposal"]
                    logger.info(f"Received valid proposal from {miner_id} for round {round_id}")

                    # Check if we have enough proposals to select winner
                    await self._check_proposal_threshold(mining_round)
                else:
                    logger.warning(f"Invalid proposal from {miner_id} for round {round_id}")

        except Exception as e:
            logger.error(f"Error handling proposal submission: {e}")

    async def _handle_round_completion(self, message):
        """Handle mining round completion notifications"""
        logger.info(f"Mining round completed: {message.data['round_id']}")

    async def _handle_network_status_request(self, message):
        """Handle network status requests"""
        try:
            status = self.get_network_mining_status()

            response = self.peer_manager.NetworkMessage(
                message_type="network_status_response",
                sender_id=self.peer_manager.node_id,
                recipient_id=message.sender_id,
                data=status,
                timestamp=int(time.time())
            )

            await self.peer_manager.send_to_peer(message.sender_id, response)

        except Exception as e:
            logger.error(f"Error handling network status request: {e}")

    async def _handle_difficulty_adjustment(self, message):
        """Handle difficulty adjustment notifications"""
        try:
            data = message.data
            new_difficulty = data["new_difficulty"]

            logger.info(f"Network difficulty adjusted to {new_difficulty}")

        except Exception as e:
            logger.error(f"Error handling difficulty adjustment: {e}")

    async def _handle_sabbath_enforcement(self, message):
        """Handle Sabbath enforcement notifications"""
        try:
            data = message.data
            is_sabbath = data["is_sabbath"]

            if is_sabbath and self.sabbath_enforcement:
                logger.info("Sabbath period - halting mining coordination")
                self.network_status = NetworkMiningStatus.SABBATH_REST
            elif not is_sabbath and self.network_status == NetworkMiningStatus.SABBATH_REST:
                logger.info("Sabbath period ended - resuming mining coordination")
                self.network_status = NetworkMiningStatus.ACTIVE
                await self._start_new_mining_round()

        except Exception as e:
            logger.error(f"Error handling Sabbath enforcement: {e}")

    # Background Tasks
    async def _mining_coordination_loop(self):
        """Main mining coordination loop"""
        while True:
            try:
                if self.network_status == NetworkMiningStatus.ACTIVE:
                    # Check for stalled mining rounds
                    await self._check_stalled_rounds()

                    # Update miner activity
                    await self._update_miner_activity()

                    # Clean up old rounds
                    await self._cleanup_old_rounds()

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Mining coordination loop error: {e}")
                await asyncio.sleep(30)

    async def _sabbath_monitoring_loop(self):
        """Monitor Sabbath periods and enforce mining restrictions"""
        while True:
            try:
                if self.sabbath_enforcement:
                    is_sabbath = await self._is_sabbath_period()

                    if is_sabbath and self.network_status == NetworkMiningStatus.ACTIVE:
                        logger.info("Sabbath period detected - halting mining")
                        self.network_status = NetworkMiningStatus.SABBATH_REST
                        await self._broadcast_sabbath_announcement(True)
                    elif not is_sabbath and self.network_status == NetworkMiningStatus.SABBATH_REST:
                        logger.info("Sabbath period ended - resuming mining")
                        self.network_status = NetworkMiningStatus.ACTIVE
                        await self._broadcast_sabbath_announcement(False)
                        await self._start_new_mining_round()

                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                logger.error(f"Sabbath monitoring loop error: {e}")
                await asyncio.sleep(300)

    async def _difficulty_adjustment_loop(self):
        """Monitor and adjust mining difficulty"""
        while True:
            try:
                if self.total_blocks_coordinated % self.difficulty_adjustment_blocks == 0:
                    await self._adjust_mining_difficulty()

                await asyncio.sleep(3600)  # Check every hour

            except Exception as e:
                logger.error(f"Difficulty adjustment loop error: {e}")
                await asyncio.sleep(3600)

    # Utility Methods
    async def _validate_miner_eligibility(self, miner_id: str, tribal_code: str) -> bool:
        """Validate miner eligibility for registration"""
        # Check if miner is already registered
        if miner_id in self.registered_miners:
            return False

        # Validate tribal code
        valid_tribes = {"JU", "LE", "EP", "BE", "SI", "MA", "IS", "ZE", "NA", "GA", "AS", "RE", "COMMUNITY"}
        if tribal_code not in valid_tribes:
            return False

        return True

    async def _calculate_miner_compliance(self, miner_id: str) -> float:
        """Calculate biblical compliance score for a miner"""
        # This would integrate with the biblical tokenomics system
        return 0.95  # Default high compliance

    async def _is_mining_allowed(self) -> bool:
        """Check if mining is currently allowed"""
        if self.network_status != NetworkMiningStatus.ACTIVE:
            return False

        if self.sabbath_enforcement and await self._is_sabbath_period():
            return False

        return True

    async def _is_miner_turn(self, miner_id: str, block_height: int) -> bool:
        """Check if it's a miner's turn based on rotation"""
        # Simple rotation based on block height and miner ID
        if not self.mining_rotation_enabled:
            return True

        # Allow mining every 12 blocks (one per tribe)
        return (block_height + hash(miner_id)) % 12 == 0

    async def _has_gleaning_participation(self, miner_id: str) -> bool:
        """Check if miner participates in gleaning pools"""
        # This would check the miner's gleaning pool contributions
        return True  # Default to true

    async def _get_recent_blocks_by_miner(self, miner_id: str, count: int) -> List[dict]:
        """Get recent blocks mined by a specific miner"""
        # This would query the blockchain for recent blocks
        return []  # Simplified

    async def _is_sabbath_period(self) -> bool:
        """Check if it's currently Sabbath period"""
        import time
        day_of_week = time.gmtime().tm_wday
        return day_of_week == 5  # Saturday

    async def _get_current_block_height(self) -> int:
        """Get current blockchain height"""
        try:
            from shared.db.db import db
            result = db.query_one("SELECT MAX(block_number) as height FROM blocks")
            return result['height'] if result['height'] is not None else 0
        except Exception as e:
            logger.error(f"Error getting current block height: {e}")
            return 0

    def _update_average_block_time(self, block_time: int):
        """Update average block time calculation"""
        # Simple moving average
        self.average_block_time = (self.average_block_time * 0.9) + (block_time * 0.1)

    async def _validate_mining_proposal(self, proposal: dict, mining_round: MiningRound) -> bool:
        """Validate a mining proposal"""
        # Check if miner is eligible for this round
        miner_id = proposal.get("miner_id")
        if miner_id not in mining_round.eligible_miners:
            return False

        # Check biblical compliance
        compliance_score = proposal.get("biblical_compliance_score", 0)
        if compliance_score < 0.8:
            return False

        return True

    async def _check_proposal_threshold(self, mining_round: MiningRound):
        """Check if enough proposals received to select winner"""
        # Select winner when we have at least 3 proposals or after timeout
        current_time = int(time.time())
        time_elapsed = current_time - mining_round.start_time

        if len(mining_round.proposals_received) >= 3 or time_elapsed > 300:  # 5 minutes
            await self._select_winning_proposal(mining_round)

    async def _select_winning_proposal(self, mining_round: MiningRound):
        """Select winning proposal from received proposals"""
        if not mining_round.proposals_received:
            logger.warning(f"No proposals received for round {mining_round.round_id}")
            return

        # Select proposal with highest biblical compliance score
        best_proposal = None
        best_score = 0

        for miner_id, proposal in mining_round.proposals_received.items():
            score = proposal.get("biblical_compliance_score", 0)
            if score > best_score:
                best_score = score
                best_proposal = proposal

        if best_proposal:
            await self._complete_mining_round(mining_round, best_proposal)

    async def _check_stalled_rounds(self):
        """Check for stalled mining rounds and restart if needed"""
        current_time = int(time.time())

        for block_height, mining_round in list(self.active_mining_rounds.items()):
            if current_time - mining_round.start_time > 1800:  # 30 minutes
                logger.warning(f"Mining round {mining_round.round_id} stalled, restarting")
                del self.active_mining_rounds[block_height]
                await self._start_new_mining_round()

    async def _update_miner_activity(self):
        """Update miner activity status"""
        current_time = int(time.time())

        for miner_id, registration in self.registered_miners.items():
            if current_time - registration.last_active > 7200:  # 2 hours
                logger.info(f"Miner {miner_id} marked as inactive")

    async def _cleanup_old_rounds(self):
        """Clean up old mining rounds"""
        current_time = int(time.time())

        for block_height, mining_round in list(self.active_mining_rounds.items()):
            if mining_round.end_time and current_time - mining_round.end_time > 3600:  # 1 hour
                del self.active_mining_rounds[block_height]

    async def _adjust_mining_difficulty(self):
        """Adjust mining difficulty based on block times"""
        if self.average_block_time > self.block_time_target * 1.2:
            # Blocks too slow, decrease difficulty
            logger.info("Decreasing mining difficulty")
        elif self.average_block_time < self.block_time_target * 0.8:
            # Blocks too fast, increase difficulty
            logger.info("Increasing mining difficulty")

    async def _broadcast_miner_registration(self, registration: MinerRegistration):
        """Broadcast miner registration to network"""
        message = self.peer_manager.NetworkMessage(
            message_type="miner_registered",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "miner_id": registration.miner_id,
                "tribal_code": registration.tribal_code,
                "node_type": registration.node_type,
                "compliance_score": registration.biblical_compliance_score
            },
            timestamp=int(time.time())
        )

        await self.peer_manager.broadcast_message(message)

    async def _broadcast_round_completion(self, mining_round: MiningRound, winning_proposal: dict):
        """Broadcast mining round completion"""
        message = self.peer_manager.NetworkMessage(
            message_type="mining_round_completed",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "round_id": mining_round.round_id,
                "block_height": mining_round.block_height,
                "winning_miner": mining_round.selected_miner,
                "proposals_count": len(mining_round.proposals_received),
                "block_time": mining_round.end_time - mining_round.start_time
            },
            timestamp=int(time.time())
        )

        await self.peer_manager.broadcast_message(message)

    async def _broadcast_sabbath_announcement(self, is_sabbath: bool):
        """Broadcast Sabbath period announcement"""
        message = self.peer_manager.NetworkMessage(
            message_type="sabbath_announcement",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "is_sabbath": is_sabbath,
                "timestamp": int(time.time())
            },
            timestamp=int(time.time())
        )

        await self.peer_manager.broadcast_message(message)

    # Public API Methods
    def get_network_mining_status(self) -> dict:
        """Get comprehensive network mining status"""
        return {
            "network_status": self.network_status.value,
            "total_miners_registered": len(self.registered_miners),
            "active_miners": len([m for m in self.registered_miners.values()
                                if int(time.time()) - m.last_active < 3600]),
            "active_mining_rounds": len(self.active_mining_rounds),
            "total_blocks_coordinated": self.total_blocks_coordinated,
            "average_block_time": self.average_block_time,
            "target_block_time": self.block_time_target,
            "sabbath_enforcement": self.sabbath_enforcement,
            "mining_rotation_enabled": self.mining_rotation_enabled,
            "last_updated": int(time.time())
        }

    def get_registered_miners(self) -> List[dict]:
        """Get list of registered miners"""
        return [
            {
                "miner_id": reg.miner_id,
                "tribal_code": reg.tribal_code,
                "node_type": reg.node_type,
                "mining_power": reg.mining_power,
                "compliance_score": reg.biblical_compliance_score,
                "blocks_mined": reg.blocks_mined,
                "total_rewards": reg.total_rewards,
                "last_active": reg.last_active
            }
            for reg in self.registered_miners.values()
        ]

    def get_mining_history(self) -> List[dict]:
        """Get mining coordination history"""
        return self.mining_history.copy()
