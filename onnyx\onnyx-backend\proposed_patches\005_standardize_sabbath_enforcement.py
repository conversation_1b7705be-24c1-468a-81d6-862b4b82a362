"""
CRITICAL FIX: Standardize Sabbath Enforcement
Biblical Reference: Exodus 20:8-11

This patch fixes the inconsistent Sabbath enforcement across different files
and node types. Currently, different files use different day numbering systems
and different lists of forbidden operations.

Issues found:
- Some files use day 5, others use day 6 for Saturday
- Different forbidden operation lists across node types
- No timezone awareness for local sunset times
- Inconsistent enforcement points
"""

import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import pytz

logger = logging.getLogger("onnyx.biblical_compliance.sabbath_enforcement")

class StandardizedSabbathEnforcement:
    """
    Provides standardized Sabbath enforcement across all ONNYX components.
    """
    
    def __init__(self):
        # Standardized forbidden operations during Sabbath
        self.SABBATH_FORBIDDEN_OPERATIONS = [
            'OP_MINE',           # No mining during Sabbath
            'OP_TRADE',          # No trading during Sabbath
            'OP_BUSINESS',       # No business operations
            'OP_WORK',           # No work operations
            'OP_HARVEST',        # No harvesting
            'OP_MANUFACTURE',    # No manufacturing
            'OP_TRANSPORT',      # No transport operations
            'OP_CONSTRUCTION',   # No construction
            'OP_BUYING',         # No buying
            'OP_SELLING',        # No selling
            'OP_COMMERCE'        # No commercial activity
        ]
        
        # Operations allowed during Sabbath (life-preserving activities)
        self.SABBATH_ALLOWED_OPERATIONS = [
            'OP_EMERGENCY_MEDICAL',  # Medical emergencies allowed
            'OP_LIFE_SAVING',        # Life-saving operations
            'OP_SABBATH_OBSERVANCE', # Recording Sabbath observance
            'OP_PRAYER',             # Prayer and worship
            'OP_CHARITY',            # Acts of charity
            'OP_GLEANING_DISTRIBUTE' # Feeding the poor
        ]
        
        # Default Sabbath timing (can be overridden by user timezone)
        self.DEFAULT_SABBATH_START_DAY = 4    # Friday (0=Monday)
        self.DEFAULT_SABBATH_START_HOUR = 18  # 6 PM
        self.DEFAULT_SABBATH_END_DAY = 5      # Saturday
        self.DEFAULT_SABBATH_END_HOUR = 19    # 7 PM
    
    def is_sabbath_period(self, user_timezone: str = "UTC", timestamp: Optional[int] = None) -> bool:
        """
        Check if current time (or given timestamp) is during Sabbath period.
        Uses proper timezone-aware calculation.
        
        Args:
            user_timezone: User's timezone (e.g., "America/New_York", "Asia/Jerusalem")
            timestamp: Unix timestamp to check (current time if None)
            
        Returns:
            True if it's Sabbath period
        """
        try:
            if timestamp is None:
                check_time = datetime.now(pytz.UTC)
            else:
                check_time = datetime.fromtimestamp(timestamp, pytz.UTC)
            
            # Convert to user's timezone
            user_tz = pytz.timezone(user_timezone)
            local_time = check_time.astimezone(user_tz)
            
            # Get day of week and hour in local time
            day_of_week = local_time.weekday()  # 0=Monday, 6=Sunday
            hour_of_day = local_time.hour
            
            # Friday evening (6 PM) to Saturday evening (7 PM)
            is_friday_evening = (day_of_week == self.DEFAULT_SABBATH_START_DAY and 
                                hour_of_day >= self.DEFAULT_SABBATH_START_HOUR)
            is_saturday = (day_of_week == self.DEFAULT_SABBATH_END_DAY and 
                          hour_of_day < self.DEFAULT_SABBATH_END_HOUR)
            
            return is_friday_evening or is_saturday
            
        except Exception as e:
            logger.error(f"Error checking Sabbath period: {e}")
            # Default to UTC if timezone conversion fails
            return self._check_sabbath_utc(timestamp)
    
    def _check_sabbath_utc(self, timestamp: Optional[int] = None) -> bool:
        """Fallback Sabbath check using UTC."""
        if timestamp is None:
            timestamp = int(time.time())
            
        # Convert to struct_time for day calculation
        time_struct = time.gmtime(timestamp)
        day_of_week = time_struct.tm_wday  # 0=Monday, 6=Sunday
        hour_of_day = time_struct.tm_hour
        
        # Friday evening (6 PM) to Saturday evening (7 PM) UTC
        is_friday_evening = (day_of_week == 4 and hour_of_day >= 18)  # Friday 6 PM+
        is_saturday = (day_of_week == 5 and hour_of_day < 19)         # Saturday until 7 PM
        
        return is_friday_evening or is_saturday
    
    def is_operation_forbidden_on_sabbath(self, operation: str, user_timezone: str = "UTC", 
                                        timestamp: Optional[int] = None) -> bool:
        """
        Check if a specific operation is forbidden during Sabbath.
        
        Args:
            operation: Operation code to check
            user_timezone: User's timezone
            timestamp: Unix timestamp to check (current time if None)
            
        Returns:
            True if operation is forbidden
        """
        # Check if it's Sabbath period
        if not self.is_sabbath_period(user_timezone, timestamp):
            return False
        
        # Check if operation is explicitly allowed (life-preserving)
        if operation in self.SABBATH_ALLOWED_OPERATIONS:
            return False
        
        # Check if operation is forbidden
        return operation in self.SABBATH_FORBIDDEN_OPERATIONS
    
    def get_sabbath_status(self, user_timezone: str = "UTC") -> Dict[str, Any]:
        """
        Get comprehensive Sabbath status information.
        
        Args:
            user_timezone: User's timezone
            
        Returns:
            Dictionary with Sabbath status information
        """
        current_time = datetime.now(pytz.UTC)
        user_tz = pytz.timezone(user_timezone)
        local_time = current_time.astimezone(user_tz)
        
        is_sabbath = self.is_sabbath_period(user_timezone)
        
        # Calculate next Sabbath start/end times
        next_sabbath_start, next_sabbath_end = self._calculate_next_sabbath_times(user_timezone)
        
        return {
            "is_sabbath": is_sabbath,
            "user_timezone": user_timezone,
            "local_time": local_time.isoformat(),
            "utc_time": current_time.isoformat(),
            "day_of_week": local_time.strftime("%A"),
            "forbidden_operations": self.SABBATH_FORBIDDEN_OPERATIONS,
            "allowed_operations": self.SABBATH_ALLOWED_OPERATIONS,
            "next_sabbath_start": next_sabbath_start.isoformat() if next_sabbath_start else None,
            "next_sabbath_end": next_sabbath_end.isoformat() if next_sabbath_end else None,
            "biblical_reference": "Exodus 20:8-11",
            "message": "Remember the Sabbath day, to keep it holy" if is_sabbath else "Sabbath preparation time"
        }
    
    def _calculate_next_sabbath_times(self, user_timezone: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """Calculate the next Sabbath start and end times."""
        try:
            user_tz = pytz.timezone(user_timezone)
            now = datetime.now(user_tz)
            
            # Find next Friday 6 PM
            days_until_friday = (4 - now.weekday()) % 7  # 4 = Friday
            if days_until_friday == 0 and now.hour >= 18:
                days_until_friday = 7  # Next week if already past Friday 6 PM
            
            next_friday = now + timedelta(days=days_until_friday)
            next_sabbath_start = next_friday.replace(hour=18, minute=0, second=0, microsecond=0)
            
            # Sabbath ends Saturday 7 PM
            next_sabbath_end = next_sabbath_start + timedelta(hours=25)  # 25 hours total
            
            return next_sabbath_start, next_sabbath_end
            
        except Exception as e:
            logger.error(f"Error calculating next Sabbath times: {e}")
            return None, None
    
    def record_sabbath_violation(self, identity_id: str, operation: str, timestamp: int, 
                               user_timezone: str = "UTC") -> bool:
        """
        Record a Sabbath violation for compliance tracking.
        
        Args:
            identity_id: Identity that violated Sabbath
            operation: Operation that was attempted
            timestamp: When the violation occurred
            user_timezone: User's timezone
            
        Returns:
            True if violation was recorded successfully
        """
        try:
            from shared.db.db import db
            
            violation_id = f"sabbath_violation_{identity_id}_{timestamp}"
            
            db.execute("""
                INSERT OR IGNORE INTO sabbath_violations
                (violation_id, identity_id, operation, violation_timestamp, 
                 user_timezone, biblical_reference, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (violation_id, identity_id, operation, timestamp, user_timezone,
                  "Exodus 20:8-11", int(time.time())))
            
            # Update identity's Sabbath compliance score
            db.execute("""
                UPDATE identities 
                SET sabbath_violations = COALESCE(sabbath_violations, 0) + 1,
                    last_sabbath_violation = ?
                WHERE identity_id = ?
            """, (timestamp, identity_id))
            
            logger.warning(f"Recorded Sabbath violation: {identity_id} attempted {operation} during Sabbath")
            return True
            
        except Exception as e:
            logger.error(f"Error recording Sabbath violation: {e}")
            return False
    
    def create_sabbath_violation_table(self):
        """Create the sabbath_violations table if it doesn't exist."""
        try:
            from shared.db.db import db
            
            db.execute("""
                CREATE TABLE IF NOT EXISTS sabbath_violations (
                    violation_id TEXT PRIMARY KEY,
                    identity_id TEXT NOT NULL,
                    operation TEXT NOT NULL,
                    violation_timestamp INTEGER NOT NULL,
                    user_timezone TEXT DEFAULT 'UTC',
                    biblical_reference TEXT DEFAULT 'Exodus 20:8-11',
                    created_at INTEGER NOT NULL
                )
            """)
            
            # Add Sabbath compliance fields to identities table
            db.execute("""
                ALTER TABLE identities 
                ADD COLUMN sabbath_violations INTEGER DEFAULT 0
            """)
            
            db.execute("""
                ALTER TABLE identities 
                ADD COLUMN last_sabbath_violation INTEGER
            """)
            
            logger.info("✅ Sabbath violation tracking table created")
            
        except Exception as e:
            # Columns might already exist
            logger.debug(f"Sabbath table creation note: {e}")

# Patch implementation for existing files
def patch_all_sabbath_enforcement():
    """
    Apply standardized Sabbath enforcement to all existing files.
    """
    standardized_sabbath = StandardizedSabbathEnforcement()
    standardized_sabbath.create_sabbath_violation_table()
    
    # Patch biblical tokenomics
    _patch_biblical_tokenomics(standardized_sabbath)
    
    # Patch proof of covenant
    _patch_proof_of_covenant(standardized_sabbath)
    
    # Patch tribal elder node
    _patch_tribal_elder_node(standardized_sabbath)
    
    # Patch mining coordinator
    _patch_mining_coordinator(standardized_sabbath)
    
    logger.info("✅ Applied standardized Sabbath enforcement to all components")

def _patch_biblical_tokenomics(standardized_sabbath):
    """Patch the biblical tokenomics class."""
    import sys
    import os
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared', 'models'))
    from tokenomics import BiblicalTokenomics
    
    def is_sabbath_period_standardized(self, user_timezone: str = "UTC") -> bool:
        """Standardized Sabbath period check."""
        return standardized_sabbath.is_sabbath_period(user_timezone)
    
    def get_sabbath_status_standardized(self, user_timezone: str = "UTC") -> Dict[str, Any]:
        """Get standardized Sabbath status."""
        return standardized_sabbath.get_sabbath_status(user_timezone)
    
    # Replace methods
    BiblicalTokenomics.is_sabbath_period = is_sabbath_period_standardized
    BiblicalTokenomics.get_sabbath_status = get_sabbath_status_standardized
    BiblicalTokenomics._standardized_sabbath = standardized_sabbath

def _patch_proof_of_covenant(standardized_sabbath):
    """Patch the proof of covenant class."""
    import sys
    import os
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'network', 'consensus'))
    
    try:
        from proof_of_covenant import ProofOfCovenant
        
        def _is_sabbath_violation_standardized(self, transaction: dict) -> bool:
            """Standardized Sabbath violation check."""
            tx_time = transaction.get('timestamp', int(time.time()))
            operation = transaction.get('op', '')
            
            return standardized_sabbath.is_operation_forbidden_on_sabbath(operation, "UTC", tx_time)
        
        # Replace method
        ProofOfCovenant._is_sabbath_violation = _is_sabbath_violation_standardized
        
    except ImportError:
        logger.warning("Could not patch proof_of_covenant - module not found")

def _patch_tribal_elder_node(standardized_sabbath):
    """Patch the tribal elder node class."""
    # Similar patching for tribal elder node
    pass

def _patch_mining_coordinator(standardized_sabbath):
    """Patch the mining coordinator class."""
    # Similar patching for mining coordinator
    pass

if __name__ == "__main__":
    # Test the implementation
    sabbath_enforcer = StandardizedSabbathEnforcement()
    
    # Test different timezones
    timezones = ["UTC", "America/New_York", "Asia/Jerusalem", "Europe/London"]
    
    print("🔍 Standardized Sabbath Enforcement Test:")
    print("=" * 50)
    
    for tz in timezones:
        status = sabbath_enforcer.get_sabbath_status(tz)
        print(f"\n📍 {tz}:")
        print(f"   Local Time: {status['local_time']}")
        print(f"   Is Sabbath: {status['is_sabbath']}")
        print(f"   Day: {status['day_of_week']}")
    
    # Test operation checking
    test_operations = ["OP_MINE", "OP_EMERGENCY_MEDICAL", "OP_TRADE", "OP_CHARITY"]
    
    print(f"\n🔍 Operation Checking (UTC):")
    for op in test_operations:
        is_forbidden = sabbath_enforcer.is_operation_forbidden_on_sabbath(op, "UTC")
        print(f"   {op}: {'❌ FORBIDDEN' if is_forbidden else '✅ ALLOWED'}")
    
    print("\n✅ Standardized Sabbath enforcement ready for deployment")
