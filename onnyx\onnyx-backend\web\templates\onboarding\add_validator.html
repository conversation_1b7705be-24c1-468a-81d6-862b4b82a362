{% extends "base.html" %}

{% block title %}Add Business Validator - ONNYX Onboarding{% endblock %}

{% block head %}
<style>
.onboarding-container {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    min-height: 100vh;
    padding: 2rem 0;
}

.glass-card-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 2rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.glass-bg {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.glass-border {
    border-color: rgba(255, 255, 255, 0.2);
}

.glass-hover:hover {
    background: rgba(255, 255, 255, 0.08);
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 50%;
    opacity: 0.6;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

.form-section {
    background: rgba(0, 255, 247, 0.05);
    border: 1px solid rgba(0, 255, 247, 0.2);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.section-title {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mining-tier-card {
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.1), rgba(0, 255, 247, 0.1));
    border: 1px solid rgba(154, 0, 255, 0.3);
    border-radius: 0.75rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mining-tier-card:hover {
    border-color: var(--cyber-purple);
    transform: translateY(-2px);
}

.mining-tier-card.selected {
    border-color: var(--cyber-cyan);
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.2), rgba(154, 0, 255, 0.2));
}
</style>
{% endblock %}

{% block content %}
<div class="onboarding-container">
    <div class="floating-particles"></div>
    
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-cyber-purple to-purple-600 rounded-2xl mb-6">
                <span class="text-3xl">🏢</span>
            </div>
            <h1 class="text-4xl font-orbitron font-bold text-white mb-4">Add Business Validator</h1>
            <p class="text-xl text-text-secondary max-w-2xl mx-auto">
                Register a new Sela (business validator) with mining capabilities and covenant compliance
            </p>
        </div>

        <!-- Form -->
        <div class="glass-card-premium p-8 relative">
            <form id="validator-form" method="POST" class="space-y-8">
                <!-- Business Information -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span>🏪</span>
                        Business Information
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="business_name" class="block text-sm font-semibold text-text-primary mb-3">Business Name *</label>
                            <input type="text" id="business_name" name="business_name" required
                                   class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-muted focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px]"
                                   placeholder="Enter business name">
                        </div>

                        <div>
                            <label for="identity_id" class="block text-sm font-semibold text-text-primary mb-3">Owner Identity ID *</label>
                            <select id="identity_id" name="identity_id" required
                                    class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px]">
                                <option value="">Select Identity</option>
                                {% for identity in available_identities %}
                                <option value="{{ identity.identity_id }}">{{ identity.name }} ({{ identity.identity_id }})</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-semibold text-text-primary mb-3">Business Description</label>
                            <textarea id="description" name="description" rows="3"
                                      class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-muted focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover"
                                      placeholder="Describe the business and its services"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Security Information -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span>🔐</span>
                        Security Setup
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="password" class="block text-sm font-semibold text-text-primary mb-3">Password *</label>
                            <div class="relative">
                                <input type="password" id="password" name="password" required
                                       class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-muted focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px]"
                                       placeholder="Enter secure password" minlength="8">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <button type="button" onclick="togglePassword('password')" class="text-text-muted hover:text-cyber-purple transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-1 text-xs text-text-muted">Minimum 8 characters, secure password for business access</div>
                        </div>

                        <div>
                            <label for="confirm_password" class="block text-sm font-semibold text-text-primary mb-3">Confirm Password *</label>
                            <div class="relative">
                                <input type="password" id="confirm_password" name="confirm_password" required
                                       class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-muted focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px]"
                                       placeholder="Confirm your password" minlength="8">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <button type="button" onclick="togglePassword('confirm_password')" class="text-text-muted hover:text-cyber-purple transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-1 text-xs text-text-muted">Re-enter password to confirm</div>
                            <div id="password-match-error" class="mt-1 text-xs text-red-400 hidden">Passwords do not match</div>
                        </div>
                    </div>
                </div>

                <!-- Mining Configuration -->
                <div class="form-section">
                    <h3 class="section-title">
                        <span>⛏️</span>
                        Mining Configuration
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {% for tier_value, tier_name in mining_tiers %}
                        <div class="mining-tier-card" onclick="selectMiningTier('{{ tier_value }}')">
                            <input type="radio" name="mining_tier" value="{{ tier_value }}" id="tier_{{ tier_value }}" class="hidden">
                            <div class="text-center">
                                <div class="text-2xl mb-2">
                                    {% if tier_value == 'bronze' %}⚡
                                    {% elif tier_value == 'silver' %}💎
                                    {% elif tier_value == 'gold' %}🏆
                                    {% elif tier_value == 'dual' %}🔥
                                    {% elif tier_value == 'triple' %}⭐
                                    {% elif tier_value == 'pro' %}👑
                                    {% endif %}
                                </div>
                                <div class="font-semibold text-cyber-purple">{{ tier_name.split('(')[0].strip() }}</div>
                                <div class="text-sm text-text-muted">{{ tier_name.split('(')[1].replace(')', '') if '(' in tier_name else '' }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-center pt-6">
                    <button type="submit" class="bg-gradient-to-r from-cyber-purple to-purple-600 hover:from-purple-600 hover:to-cyber-purple text-white font-bold py-4 px-12 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl focus:outline-none focus:ring-4 focus:ring-purple-500/50 min-h-[56px] text-lg">
                        🏢 Register Business Validator
                    </button>
                </div>
            </form>
        </div>

        <!-- Back Link -->
        <div class="text-center mt-8">
            <a href="{{ url_for('onboarding.dashboard') }}" class="inline-flex items-center text-cyber-cyan hover:text-cyan-400 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Onboarding Dashboard
            </a>
        </div>
    </div>
</div>

<script>
// Password validation and form handling
document.addEventListener('DOMContentLoaded', function() {
    addFloatingParticles();
    setupPasswordValidation();
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
    field.setAttribute('type', type);
}

function setupPasswordValidation() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const errorDiv = document.getElementById('password-match-error');
    const form = document.getElementById('validator-form');

    function validatePasswords() {
        if (confirmPassword.value && password.value !== confirmPassword.value) {
            errorDiv.classList.remove('hidden');
            confirmPassword.classList.add('border-red-400');
            return false;
        } else {
            errorDiv.classList.add('hidden');
            confirmPassword.classList.remove('border-red-400');
            return true;
        }
    }

    confirmPassword.addEventListener('input', validatePasswords);
    password.addEventListener('input', validatePasswords);

    form.addEventListener('submit', function(e) {
        if (!validatePasswords()) {
            e.preventDefault();
            alert('Please ensure passwords match before submitting.');
            return false;
        }
        
        if (password.value.length < 8) {
            e.preventDefault();
            alert('Password must be at least 8 characters long.');
            return false;
        }

        // Check if mining tier is selected
        const selectedTier = document.querySelector('input[name="mining_tier"]:checked');
        if (!selectedTier) {
            e.preventDefault();
            alert('Please select a mining tier.');
            return false;
        }
    });
}

function selectMiningTier(tierValue) {
    // Remove selected class from all cards
    document.querySelectorAll('.mining-tier-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Add selected class to clicked card
    event.currentTarget.classList.add('selected');
    
    // Select the radio button
    document.getElementById('tier_' + tierValue).checked = true;
}

function addFloatingParticles() {
    const container = document.querySelector('.glass-card-premium');
    if (container) {
        for (let i = 0; i < 3; i++) {
            const particle = document.createElement('div');
            particle.className = 'absolute w-1 h-1 bg-cyber-purple rounded-full opacity-30 animate-ping';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particle.style.animationDuration = (2 + Math.random() * 2) + 's';
            container.appendChild(particle);
        }
    }
}
</script>
{% endblock %}
