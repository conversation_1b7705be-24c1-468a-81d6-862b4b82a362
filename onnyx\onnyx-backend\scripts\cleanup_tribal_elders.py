#!/usr/bin/env python3
"""
Cleanup Tribal Elders Database
Removes excessive tribal elder records and ensures only 12 proper elders exist
"""

import sys
import os
import json
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.db.database import Database

# The Twelve Tribes that should exist
TWELVE_TRIBES = [
    {"code": "JU", "name": "<PERSON>", "voting_weight": 2, "role": "<PERSON>"},
    {"code": "LE", "name": "<PERSON>", "voting_weight": 2, "role": "Priestly"},
    {"code": "EP", "name": "<PERSON><PERSON>rai<PERSON>", "voting_weight": 2, "role": "Fruitful"},
    {"code": "BE", "name": "<PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "SI", "name": "Simeon", "voting_weight": 1, "role": "Standard"},
    {"code": "MA", "name": "<PERSON><PERSON><PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "GA", "name": "Gad", "voting_weight": 1, "role": "Standard"},
    {"code": "AS", "name": "<PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "NA", "name": "Naphtali", "voting_weight": 1, "role": "Standard"},
    {"code": "ZE", "name": "Zebulun", "voting_weight": 1, "role": "Standard"},
    {"code": "IS", "name": "Issachar", "voting_weight": 1, "role": "Standard"},
    {"code": "DA", "name": "Dan", "voting_weight": 1, "role": "Standard"}
]

def cleanup_tribal_elders():
    """Remove all tribal elders and related configurations."""
    print("🧹 CLEANING UP TRIBAL ELDERS DATABASE")
    print("=" * 50)
    
    db = Database()
    
    try:
        # Check current state
        current_elders = db.query("""
            SELECT COUNT(*) as count 
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
        """)
        current_count = current_elders[0]['count'] if current_elders else 0
        
        print(f"📊 Current tribal elders: {current_count}")
        
        if current_count == 0:
            print("✅ No tribal elders to clean up")
            return True
        
        # Show what will be deleted
        elders_to_delete = db.query("""
            SELECT identity_id, name, nation_code, created_at
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
            ORDER BY nation_code, created_at
        """)
        
        print(f"\n📋 Tribal elders to be deleted:")
        for elder in elders_to_delete:
            created_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(elder['created_at']))
            print(f"   {elder['nation_code']}: {elder['name']} (created: {created_date})")
        
        # Confirm deletion
        response = input(f"\n⚠️ Delete all {current_count} tribal elders? (y/N): ")
        if response.lower() != 'y':
            print("Cleanup cancelled")
            return False
        
        # Delete all tribal elders
        print(f"\n🗑️ Deleting tribal elders...")
        deleted_count = db.execute("""
            DELETE FROM identities 
            WHERE role_class = 'Tribal_Elder'
        """)
        
        print(f"✅ Deleted {deleted_count} tribal elder records")
        
        # Clear tribal council configuration
        print(f"🗑️ Clearing tribal council configuration...")
        db.execute("""
            DELETE FROM system_config 
            WHERE key = 'tribal_council'
        """)
        
        print(f"✅ Cleared tribal council configuration")
        
        # Verify cleanup
        remaining_elders = db.query("""
            SELECT COUNT(*) as count 
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
        """)
        remaining_count = remaining_elders[0]['count'] if remaining_elders else 0
        
        if remaining_count == 0:
            print(f"\n🎉 CLEANUP SUCCESSFUL!")
            print(f"✅ All tribal elders removed")
            print(f"✅ Council configuration cleared")
            print(f"✅ Database ready for fresh tribal elder registration")
            return True
        else:
            print(f"\n❌ Cleanup incomplete - {remaining_count} elders remain")
            return False
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        return False

def cleanup_duplicates_only():
    """Remove duplicate tribal elders, keeping only one per tribe."""
    print("🧹 CLEANING UP DUPLICATE TRIBAL ELDERS")
    print("=" * 50)
    
    db = Database()
    
    try:
        # Find duplicates
        duplicates = db.query("""
            SELECT nation_code, COUNT(*) as count
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
            GROUP BY nation_code
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        if not duplicates:
            print("✅ No duplicate tribal elders found")
            return True
        
        print(f"📊 Found duplicates for {len(duplicates)} tribes:")
        total_to_delete = 0
        for dup in duplicates:
            extra_count = dup['count'] - 1
            total_to_delete += extra_count
            print(f"   {dup['nation_code']}: {dup['count']} elders ({extra_count} to delete)")
        
        response = input(f"\n⚠️ Delete {total_to_delete} duplicate elders? (y/N): ")
        if response.lower() != 'y':
            print("Cleanup cancelled")
            return False
        
        # Delete duplicates (keep the oldest one for each tribe)
        deleted_total = 0
        for dup in duplicates:
            tribe_code = dup['nation_code']
            
            # Get all elders for this tribe, ordered by creation date
            tribe_elders = db.query("""
                SELECT identity_id, name, created_at
                FROM identities 
                WHERE role_class = 'Tribal_Elder' AND nation_code = ?
                ORDER BY created_at ASC
            """, (tribe_code,))
            
            # Keep the first one, delete the rest
            if len(tribe_elders) > 1:
                to_keep = tribe_elders[0]
                to_delete = tribe_elders[1:]
                
                print(f"\n🏛️ {tribe_code} tribe:")
                print(f"   Keeping: {to_keep['name']} (created: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(to_keep['created_at']))})")
                
                for elder in to_delete:
                    print(f"   Deleting: {elder['name']} (created: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(elder['created_at']))})")
                    db.execute("""
                        DELETE FROM identities 
                        WHERE identity_id = ?
                    """, (elder['identity_id'],))
                    deleted_total += 1
        
        print(f"\n🎉 DUPLICATE CLEANUP SUCCESSFUL!")
        print(f"✅ Deleted {deleted_total} duplicate elders")
        print(f"✅ Each tribe now has exactly 1 elder")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during duplicate cleanup: {e}")
        return False

def main():
    """Main cleanup function."""
    print("🌟 ONNYX TRIBAL ELDERS CLEANUP TOOL")
    print("=" * 60)
    
    print("Choose cleanup option:")
    print("1. Remove ALL tribal elders (complete reset)")
    print("2. Remove only DUPLICATE elders (keep one per tribe)")
    print("3. Cancel")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == '1':
        success = cleanup_tribal_elders()
    elif choice == '2':
        success = cleanup_duplicates_only()
    elif choice == '3':
        print("Cleanup cancelled")
        return
    else:
        print("Invalid choice")
        return
    
    if success:
        print(f"\n📋 NEXT STEPS:")
        print("1. Run tribal elder registration script to create proper elders")
        print("2. Verify the blockchain explorer shows correct data")
        print("3. Test the tribal governance system")

if __name__ == '__main__':
    main()
