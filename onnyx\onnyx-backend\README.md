# ONNYX Backend - Production Blockchain Platform

The Digital Backbone of Trustworthy Commerce - A blockchain-powered verification platform built upon biblical principles of justice, truth, and integrity.

## 🚀 Live Production Platform

**Deployment**: https://onnyx-backend.onrender.com
**Status**: Phase 1 Production Launch Complete ✅

## Biblical Foundation

ONNYX serves as a righteous alternative to corrupt worldly systems, built upon Scripture as our cornerstone.

**📜 Essential Reading**: [Code of Ethics and Foundational Principles](docs/ONNYX_CODE_OF_ETHICS.md) - The complete biblical framework guiding all ONNYX development.

*"But seek first the kingdom of God and his righteousness, and all these things will be added to you."* - Matthew 6:33

## Current Blockchain State

### Production Network Status
- **Genesis Identity**: Platform Founder (<PERSON><PERSON><PERSON><PERSON>) ✅
- **Active Validators**: 3 real business validators ✅
- **Mining System**: Hybrid Proof-of-Trust + Performance Boost ✅
- **Token Economy**: ONNX (22,222,200 total supply) ✅
- **Auto-Mining**: Operational with tiered rewards ✅

### Live Validators
1. **ONNYX Foundation** (Platform Founder)
   - Mining Tier: Pro (10x multiplier)
   - ONNX Balance: 1,000,000 ONNX
   - Status: Genesis validator

2. **<PERSON><PERSON> Hair Replacement**
   - Mining Tier: Optimized (3x multiplier)
   - ONNX Balance: 100,000 ONNX
   - Status: Active business validator

3. **GetTwisted Hair Studios**
   - Mining Tier: Optimized (3x multiplier)
   - ONNX Balance: 100,000 ONNX
   - Status: Active business validator

### Blockchain Metrics
- **Total Blocks**: 4+ (actively growing)
- **Total Transactions**: 5+ (real business operations)
- **Network Uptime**: 99.9% target
- **Mining Rewards**: Distributed based on tier multipliers

## 🚀 Quick Start

### One-Command Startup (Recommended)

The easiest way to start the complete ONNYX platform:

**Windows:**
```bash
start_onnyx.bat
```

**Linux/Mac:**
```bash
python start_onnyx.py
```

This automatically starts:
- ✅ Database initialization
- ✅ P2P blockchain network
- ✅ Mining network
- ✅ Web interface (http://localhost:5000)

### Manual Startup Options

**Web App Only** (Database backend):
```bash
cd web
python app.py
```

**Web App + Blockchain** (Full platform):
```bash
python start_onnyx.py
```

**Blockchain Network Only**:
```bash
python scripts/deploy_p2p_network.py
```

## Blockchain Integration

### Automatic Startup
When you start the Flask web app, the blockchain backend now **automatically starts** with:

1. **P2P Network**: Bootstrap node + Tribal Elder nodes + Community nodes
2. **Mining Network**: Distributed mining with biblical compliance
3. **Consensus Engine**: Proof-of-Trust validation
4. **Token Economy**: ONNX token management

### Real-Time Status
The web interface shows live blockchain status:
- 🟢 **Network Online**: P2P network is running
- 🔵 **Blockchain Active**: Consensus and validation active
- 🟡 **Mining Active**: Mining network operational

### Architecture Overview

```
onnyx-backend/
├── web/                    # 🎯 Flask Application (Single Source of Truth)
│   ├── app.py             # Main application entry point
│   ├── routes/            # API and web routes
│   ├── templates/         # Unified frontend templates
│   └── static/            # CSS, JS, images (Onyx Stone theme)
├── shared/                # Core platform components
│   ├── models/            # Database models (Identity, Sela, etc.)
│   ├── db/                # Database layer and schema
│   └── utils/             # Shared utilities
├── blockchain/            # Blockchain core components
│   ├── consensus/         # Mining and validation
│   ├── node/              # Blockchain node operations
│   ├── vm/                # Virtual machine and validation
│   └── wallet/            # Cryptographic wallet functions
└── docs/                  # Essential documentation
```

## Key Features

### 🔐 Identity Management
- **Cryptographic Identities**: ECDSA key generation and management
- **Web Registration**: Complete user onboarding workflow
- **Key Download**: Secure private key distribution
- **Session Management**: Flask-based authentication

### 🏢 Business Validation (Sela Network)
- **Validator Registration**: Business entity onboarding
- **Mining Tiers**: Basic CPU (1x), ONNYX Optimized (2x-5x), ONNYX Pro (5x+)
- **Trust Scoring**: Etzem trust protocol integration
- **Real Business Data**: Production validators with actual business information

### ⚡ Auto-Mining System
- **Scheduled Mining**: 9 AM - 5 PM business hours
- **Background Processes**: Daemon-based mining operations
- **Performance Monitoring**: Real-time uptime and earnings tracking
- **Restart Protection**: Automatic failure recovery
- **Web Dashboard**: Complete mining management interface

### 💰 Token Economy
- **ONNX Token**: Native platform currency
- **Genesis Allocation**: Fair distribution to founder and validators
- **Mining Rewards**: Performance-based with tier multipliers
- **Real-time Tracking**: Live balance and transaction monitoring

### 📊 Production Dashboard
- **Live Data**: Real-time blockchain and validator statistics
- **User Management**: Identity and business profile management
- **Mining Analytics**: Performance metrics and earnings tracking
- **Blockchain Explorer**: Transaction and block browsing

## Quick Start

### Prerequisites
```bash
Python 3.9+
SQLite3
```

### Local Development
```bash
# Navigate to backend
cd onnyx-backend

# Install dependencies
pip install -r requirements.txt

# Initialize database
python init_db.py

# Start application
python web/app.py
```

**Local URL**: http://localhost:5000

### Production Database
The platform uses SQLite with production data:
- **Location**: `shared/data/onnyx.db`
- **Schema**: Complete production schema with all tables
- **Data**: Real identities, validators, blocks, and transactions

## API Endpoints

### Core APIs
- **Identity Management**: `/api/identity/`
- **Validator Operations**: `/api/sela/`
- **Blockchain Data**: `/api/explorer/`
- **Network Statistics**: `/api/network/stats`

### Auto-Mining APIs
- **Dashboard**: `/auto-mining/`
- **Control**: `/auto-mining/api/`
- **Configuration**: `/auto-mining/configure/<sela_id>`
- **Performance**: `/auto-mining/performance`

### Web Interface
- **Landing Page**: `/`
- **Authentication**: `/auth/register`, `/auth/login`
- **Dashboard**: `/dashboard/`
- **Explorer**: `/explorer/`
- **Validator Directory**: `/sela/`

## User Journey

### Complete Onboarding Flow
1. **Visit Platform** → https://onnyx-backend.onrender.com
2. **Register Identity** → Cryptographic identity creation
3. **Download Keys** → Secure private key file
4. **Register Business** → Sela validator creation
5. **Configure Mining** → Auto-mining schedule setup
6. **Start Earning** → ONNX token accumulation
7. **Monitor Performance** → Real-time analytics

### Mining Tiers
- **Basic CPU**: 1x mining multiplier
- **ONNYX Optimized**: 2x-5x mining multiplier
- **ONNYX Pro**: 5x+ mining multiplier

## Development Commands

### Database Management
```bash
# Fresh database setup
python init_db.py

# Production setup
python ../scripts/phase1_production_setup.py

# Health check
python ../scripts/health_check.py
```

### Auto-Mining Operations
```bash
# Start auto-mining system
python ../scripts/auto_mining_manager.py start --daemon

# Check status
python ../scripts/auto_mining_manager.py status

# Monitor operations
python ../scripts/monitor_mining.py
```

### Testing
```bash
# Run test suite
python -m pytest ../tests/

# Test specific components
python ../scripts/test_phase1_launch_readiness.py
```

## Deployment

See [Deployment Guide](docs/DEPLOYMENT.md) for complete production deployment instructions.

### Environment Variables
```bash
DATABASE_URL=sqlite:///shared/data/onnyx.db
FLASK_ENV=production
PORT=5000
SECRET_KEY=<secure-key>
```

## Biblical Principles in Code

Every aspect of this platform reflects our commitment to biblical principles:

- **Truth and Transparency**: Open-source development and honest communication
- **Justice and Fairness**: Anti-usury principles and fair reward distribution
- **Stewardship**: Responsible resource management and sustainable practices
- **Community**: Mutual aid features and collaborative governance
- **Independence**: Maximum separation from corrupt worldly systems

## Support

- **Platform**: https://onnyx-backend.onrender.com
- **Documentation**: Available in `/docs` directory
- **Health Check**: `/health` endpoint
- **API Status**: `/api/status` endpoint

---

**ONNYX Backend** - The technical foundation of righteous commerce, built upon the Rock of biblical principles.

*"Unless the Lord builds the house, those who build it labor in vain."* - Psalm 127:1
