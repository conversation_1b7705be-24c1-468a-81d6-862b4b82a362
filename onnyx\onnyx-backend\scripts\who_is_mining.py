#!/usr/bin/env python3
"""
ONNYX Mining Detection Script
Quick script to identify who's currently mining on your system
"""

import sys
import os
import time
import psutil
import subprocess

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def check_gpu_usage():
    """Check GPU usage to detect mining activity"""
    try:
        # Try nvidia-smi first
        result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            gpu_usage = int(result.stdout.strip())
            return gpu_usage
    except:
        pass
    
    return None

def check_cpu_usage():
    """Check CPU usage"""
    return psutil.cpu_percent(interval=1)

def find_mining_processes():
    """Find processes that might be mining"""
    mining_keywords = ['miner', 'mining', 'onnyx', 'blockchain', 'crypto', 'bitcoin', 'ethereum']
    mining_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
        try:
            proc_info = proc.info
            proc_name = proc_info['name'].lower()
            
            # Check if process name contains mining keywords
            if any(keyword in proc_name for keyword in mining_keywords):
                mining_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_info['name'],
                    'cpu': proc_info['cpu_percent'],
                    'memory': proc_info['memory_percent']
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return mining_processes

def check_database_mining():
    """Check database for recent mining activity"""
    try:
        # Check recent blocks (last hour)
        recent_time = int(time.time()) - 3600
        recent_miners = db.query('''
            SELECT miner, COUNT(*) as blocks, MAX(timestamp) as last_time, MAX(block_height) as latest_block
            FROM blocks 
            WHERE timestamp > ?
            GROUP BY miner 
            ORDER BY last_time DESC
        ''', (recent_time,))
        
        return recent_miners
    except Exception as e:
        print(f"Database error: {e}")
        return []

def check_network_mining():
    """Check if P2P mining network is active"""
    try:
        import requests
        response = requests.get('http://localhost:5000/explorer/api/mining-proposals', timeout=5)
        if response.status_code == 200:
            data = response.json()
            return data.get('proposals', [])
    except:
        pass
    return []

def main():
    print("🔍 ONNYX MINING DETECTION REPORT")
    print("=" * 60)
    print(f"⏰ Scan Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check GPU usage
    gpu_usage = check_gpu_usage()
    if gpu_usage is not None:
        status = "🔥 HIGH" if gpu_usage > 80 else "📊 NORMAL" if gpu_usage > 20 else "💤 LOW"
        print(f"🎮 GPU Usage: {status} ({gpu_usage}%)")
    else:
        print("🎮 GPU Usage: ❓ Unable to detect")
    
    # Check CPU usage
    cpu_usage = check_cpu_usage()
    cpu_status = "🔥 HIGH" if cpu_usage > 80 else "📊 NORMAL" if cpu_usage > 20 else "💤 LOW"
    print(f"🖥️  CPU Usage: {cpu_status} ({cpu_usage:.1f}%)")
    print()
    
    # Check for mining processes
    print("🔍 MINING PROCESSES:")
    mining_procs = find_mining_processes()
    if mining_procs:
        for proc in mining_procs:
            print(f"   🔥 {proc['name']} (PID: {proc['pid']}) - CPU: {proc['cpu']:.1f}% | Memory: {proc['memory']:.1f}%")
    else:
        print("   💤 No obvious mining processes found")
    print()
    
    # Check database for mining activity
    print("📊 BLOCKCHAIN MINING ACTIVITY:")
    db_miners = check_database_mining()
    if db_miners:
        for miner in db_miners:
            time_ago = int(time.time()) - miner['last_time']
            status = "🔥 ACTIVE" if time_ago < 600 else "💤 IDLE"
            print(f"   {status} {miner['miner']}")
            print(f"      Blocks: {miner['blocks']} | Latest: #{miner['latest_block']} | {time_ago//60}min ago")
    else:
        print("   💤 No recent mining activity in database")
    print()
    
    # Check P2P network mining
    print("🌐 P2P NETWORK MINING:")
    network_proposals = check_network_mining()
    if network_proposals:
        for proposal in network_proposals:
            status_icon = "🔥" if proposal.get('status') == 'active' else "✅" if proposal.get('status') == 'approved' else "⏳"
            print(f"   {status_icon} {proposal.get('proposer', 'Unknown')} ({proposal.get('tribal_code', 'N/A')}) - {proposal.get('status', 'unknown').upper()}")
    else:
        print("   💤 No active P2P mining proposals")
    print()
    
    # Summary and recommendations
    print("📋 SUMMARY:")
    if gpu_usage and gpu_usage > 90:
        print("   🚨 HIGH GPU USAGE DETECTED!")
        print("   🔍 Check the processes above to identify the miner")
        if db_miners:
            active_miners = [m for m in db_miners if (int(time.time()) - m['last_time']) < 600]
            if active_miners:
                print(f"   ⛏️  Most likely miner: {active_miners[0]['miner']}")
    elif db_miners:
        print("   ✅ Mining activity detected in blockchain")
        print("   📊 Check the blockchain mining activity section above")
    else:
        print("   💤 No significant mining activity detected")
    
    print()
    print("🔧 ACTIONS:")
    print("   • Visit http://localhost:5000/explorer/ for real-time monitoring")
    print("   • Use Task Manager to see detailed process information")
    print("   • Check ONNYX web interface for mining controls")
    print()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Scan interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during scan: {e}")
