"""
ONNYX Blockchain Synchronization
Multi-node blockchain state synchronization for covenant network
"""

import asyncio
import json
import time
import hashlib
import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from network.p2p.peer_manager import NetworkMessage

logger = logging.getLogger(__name__)

class SyncStatus(Enum):
    SYNCED = "synced"
    SYNCING = "syncing"
    OUT_OF_SYNC = "out_of_sync"
    ERROR = "error"

@dataclass
class BlockSyncRequest:
    start_height: int
    end_height: int
    requester_id: str
    timestamp: int

@dataclass
class BlockSyncResponse:
    blocks: List[dict]
    start_height: int
    end_height: int
    total_blocks: int
    sender_id: str

class BlockchainSync:
    """
    Handles blockchain synchronization across the ONNYX covenant network
    """
    
    def __init__(self, peer_manager, blockchain_manager=None):
        self.peer_manager = peer_manager
        self.blockchain = blockchain_manager
        self.sync_status = SyncStatus.SYNCED
        self.sync_progress = 0.0
        self.last_sync_time = 0
        
        # Sync parameters
        self.sync_batch_size = 50  # Blocks per sync request
        self.sync_timeout = 30  # seconds
        self.sync_interval = 60  # seconds between sync checks
        self.max_concurrent_syncs = 3
        
        # Active sync operations
        self.active_syncs: Dict[str, BlockSyncRequest] = {}
        self.sync_peers: Set[str] = set()
        
        # Register sync message handlers
        self._register_sync_handlers()
        
        # Start background sync monitoring
        asyncio.create_task(self._sync_monitoring_loop())
    
    def _register_sync_handlers(self):
        """Register blockchain sync message handlers"""
        handlers = {
            'sync_request': self._handle_sync_request,
            'sync_response': self._handle_sync_response,
            'block_announcement': self._handle_block_announcement,
            'chain_status_request': self._handle_chain_status_request,
            'chain_status_response': self._handle_chain_status_response
        }
        
        for msg_type, handler in handlers.items():
            self.peer_manager.register_message_handler(msg_type, handler)
    
    async def start_sync(self) -> bool:
        """Start blockchain synchronization process"""
        try:
            logger.info("Starting blockchain synchronization...")
            
            # Get current chain status
            local_height = await self._get_local_chain_height()
            
            # Query peers for their chain status
            peer_heights = await self._query_peer_chain_heights()
            
            if not peer_heights:
                logger.warning("No peers available for synchronization")
                return False
            
            # Find the highest chain height
            max_height = max(peer_heights.values())
            
            if max_height <= local_height:
                logger.info("Local chain is up to date")
                self.sync_status = SyncStatus.SYNCED
                return True
            
            # Start synchronization
            self.sync_status = SyncStatus.SYNCING
            success = await self._sync_to_height(max_height, peer_heights)
            
            if success:
                self.sync_status = SyncStatus.SYNCED
                self.last_sync_time = int(time.time())
                logger.info(f"Synchronization completed to height {max_height}")
            else:
                self.sync_status = SyncStatus.ERROR
                logger.error("Synchronization failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Sync start error: {e}")
            self.sync_status = SyncStatus.ERROR
            return False
    
    async def _sync_to_height(self, target_height: int, peer_heights: Dict[str, int]) -> bool:
        """Synchronize blockchain to target height"""
        try:
            local_height = await self._get_local_chain_height()
            
            if local_height >= target_height:
                return True
            
            # Select best peers for sync (those with target height)
            sync_peers = [peer_id for peer_id, height in peer_heights.items() 
                         if height >= target_height]
            
            if not sync_peers:
                logger.error("No suitable peers for synchronization")
                return False
            
            # Sync in batches
            current_height = local_height + 1
            
            while current_height <= target_height:
                batch_end = min(current_height + self.sync_batch_size - 1, target_height)
                
                # Update progress
                self.sync_progress = (current_height - local_height) / (target_height - local_height)
                
                # Request blocks from peers
                success = await self._sync_block_batch(current_height, batch_end, sync_peers)
                
                if not success:
                    logger.error(f"Failed to sync blocks {current_height}-{batch_end}")
                    return False
                
                current_height = batch_end + 1
                
                # Brief pause between batches
                await asyncio.sleep(0.1)
            
            self.sync_progress = 1.0
            return True
            
        except Exception as e:
            logger.error(f"Sync to height error: {e}")
            return False
    
    async def _sync_block_batch(self, start_height: int, end_height: int, 
                               sync_peers: List[str]) -> bool:
        """Sync a batch of blocks from peers"""
        try:
            # Try each peer until successful
            for peer_id in sync_peers:
                try:
                    blocks = await self._request_blocks_from_peer(
                        peer_id, start_height, end_height
                    )
                    
                    if blocks:
                        # Validate and add blocks
                        success = await self._validate_and_add_blocks(blocks)
                        if success:
                            logger.debug(f"Synced blocks {start_height}-{end_height} from {peer_id}")
                            return True
                        else:
                            logger.warning(f"Block validation failed from peer {peer_id}")
                    
                except Exception as e:
                    logger.warning(f"Sync error with peer {peer_id}: {e}")
                    continue
            
            logger.error(f"Failed to sync blocks {start_height}-{end_height} from any peer")
            return False
            
        except Exception as e:
            logger.error(f"Block batch sync error: {e}")
            return False
    
    async def _request_blocks_from_peer(self, peer_id: str, start_height: int, 
                                       end_height: int) -> Optional[List[dict]]:
        """Request blocks from a specific peer"""
        try:
            request_id = hashlib.sha256(f"{peer_id}{start_height}{end_height}{time.time()}".encode()).hexdigest()[:16]
            
            # Create sync request
            sync_request = BlockSyncRequest(
                start_height=start_height,
                end_height=end_height,
                requester_id=self.peer_manager.node_id,
                timestamp=int(time.time())
            )
            
            self.active_syncs[request_id] = sync_request
            
            # Send request message
            message = NetworkMessage(
                message_type="sync_request",
                sender_id=self.peer_manager.node_id,
                recipient_id=peer_id,
                data={
                    "request_id": request_id,
                    "start_height": start_height,
                    "end_height": end_height
                },
                timestamp=int(time.time())
            )
            
            await self.peer_manager.send_to_peer(peer_id, message)
            
            # Wait for response
            timeout_time = time.time() + self.sync_timeout
            
            while time.time() < timeout_time:
                if request_id not in self.active_syncs:
                    # Response received and processed
                    return getattr(self, f'_sync_result_{request_id}', None)
                
                await asyncio.sleep(0.1)
            
            # Timeout
            if request_id in self.active_syncs:
                del self.active_syncs[request_id]
            
            logger.warning(f"Sync request timeout for peer {peer_id}")
            return None
            
        except Exception as e:
            logger.error(f"Block request error: {e}")
            return None
    
    async def _validate_and_add_blocks(self, blocks: List[dict]) -> bool:
        """Validate and add blocks to local chain"""
        try:
            for block in blocks:
                # Basic block validation
                if not await self._validate_block(block):
                    logger.error(f"Block validation failed: {block.get('block_height')}")
                    return False
                
                # Add to blockchain
                success = await self._add_block_to_chain(block)
                if not success:
                    logger.error(f"Failed to add block: {block.get('block_height')}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Block validation/addition error: {e}")
            return False
    
    async def _validate_block(self, block: dict) -> bool:
        """Validate a block before adding to chain"""
        try:
            # Check required fields
            required_fields = ['block_height', 'block_hash', 'previous_hash', 'timestamp', 'transactions']
            for field in required_fields:
                if field not in block:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Validate block height sequence
            local_height = await self._get_local_chain_height()
            if block['block_height'] != local_height + 1:
                logger.error(f"Invalid block height: expected {local_height + 1}, got {block['block_height']}")
                return False
            
            # Validate previous hash
            if local_height > 0:
                previous_block = await self._get_block_by_height(local_height)
                if previous_block and block['previous_hash'] != previous_block['block_hash']:
                    logger.error("Previous hash mismatch")
                    return False
            
            # Validate block hash
            calculated_hash = self._calculate_block_hash(block)
            if calculated_hash != block['block_hash']:
                logger.error("Block hash validation failed")
                return False
            
            # Validate transactions (biblical compliance)
            for tx in block.get('transactions', []):
                if not await self._validate_transaction(tx):
                    logger.error(f"Transaction validation failed: {tx.get('transaction_id')}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Block validation error: {e}")
            return False
    
    async def _validate_transaction(self, transaction: dict) -> bool:
        """Validate transaction for biblical compliance"""
        try:
            # Check for usury violations
            if transaction.get('op') == 'OP_LEND':
                interest_rate = transaction.get('data', {}).get('interest_rate', 0)
                if interest_rate > 0:
                    logger.warning("Usury violation detected in transaction")
                    return False
            
            # Check Sabbath compliance
            tx_time = transaction.get('timestamp', 0)
            if self._is_sabbath_violation(transaction, tx_time):
                logger.warning("Sabbath violation detected in transaction")
                return False
            
            # Validate signature
            if not self._verify_transaction_signature(transaction):
                logger.error("Transaction signature validation failed")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Transaction validation error: {e}")
            return False
    
    def _is_sabbath_violation(self, transaction: dict, timestamp: int) -> bool:
        """Check if transaction violates Sabbath principles"""
        # Simplified Sabbath check
        day_of_week = time.gmtime(timestamp).tm_wday
        
        # Saturday (5) is Sabbath - restrict certain operations
        if day_of_week == 5:
            forbidden_ops = ['OP_MINE', 'OP_TRADE', 'OP_BUSINESS']
            if transaction.get('op') in forbidden_ops:
                return True
        
        return False
    
    def _verify_transaction_signature(self, transaction: dict) -> bool:
        """Verify transaction signature (simplified)"""
        # In production, this would use proper cryptographic verification
        return transaction.get('signature') is not None
    
    def _calculate_block_hash(self, block: dict) -> str:
        """Calculate block hash"""
        # Create hash from block data (excluding the hash field itself)
        block_data = {k: v for k, v in block.items() if k != 'block_hash'}
        block_string = json.dumps(block_data, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()
    
    # Message Handlers
    async def _handle_sync_request(self, message):
        """Handle blockchain sync requests from peers"""
        try:
            data = message.data
            start_height = data['start_height']
            end_height = data['end_height']
            request_id = data['request_id']
            
            # Get requested blocks
            blocks = await self._get_blocks_range(start_height, end_height)
            
            # Send response
            response = NetworkMessage(
                message_type="sync_response",
                sender_id=self.peer_manager.node_id,
                recipient_id=message.sender_id,
                data={
                    "request_id": request_id,
                    "blocks": blocks,
                    "start_height": start_height,
                    "end_height": end_height,
                    "total_blocks": len(blocks)
                },
                timestamp=int(time.time())
            )
            
            await self.peer_manager.send_to_peer(message.sender_id, response)
            logger.debug(f"Sent {len(blocks)} blocks to {message.sender_id}")
            
        except Exception as e:
            logger.error(f"Sync request handler error: {e}")
    
    async def _handle_sync_response(self, message):
        """Handle blockchain sync responses"""
        try:
            data = message.data
            request_id = data['request_id']
            blocks = data['blocks']
            
            if request_id in self.active_syncs:
                # Store result for retrieval
                setattr(self, f'_sync_result_{request_id}', blocks)
                del self.active_syncs[request_id]
                
                logger.debug(f"Received {len(blocks)} blocks from {message.sender_id}")
            
        except Exception as e:
            logger.error(f"Sync response handler error: {e}")
    
    async def _handle_block_announcement(self, message):
        """Handle new block announcements"""
        try:
            block_data = message.data
            block_height = block_data.get('block_height')
            
            local_height = await self._get_local_chain_height()
            
            if block_height == local_height + 1:
                # This is the next block we need
                if await self._validate_block(block_data):
                    await self._add_block_to_chain(block_data)
                    logger.info(f"Added new block {block_height} from announcement")
                else:
                    logger.warning(f"Invalid block announcement from {message.sender_id}")
            elif block_height > local_height + 1:
                # We're behind, trigger sync
                logger.info(f"Behind by {block_height - local_height} blocks, triggering sync")
                asyncio.create_task(self.start_sync())
            
        except Exception as e:
            logger.error(f"Block announcement handler error: {e}")
    
    async def _handle_chain_status_request(self, message):
        """Handle chain status requests"""
        try:
            local_height = await self._get_local_chain_height()
            latest_block = await self._get_block_by_height(local_height) if local_height > 0 else None
            
            response = NetworkMessage(
                message_type="chain_status_response",
                sender_id=self.peer_manager.node_id,
                recipient_id=message.sender_id,
                data={
                    "chain_height": local_height,
                    "latest_block_hash": latest_block['block_hash'] if latest_block else None,
                    "sync_status": self.sync_status.value
                },
                timestamp=int(time.time())
            )
            
            await self.peer_manager.send_to_peer(message.sender_id, response)
            
        except Exception as e:
            logger.error(f"Chain status request handler error: {e}")
    
    async def _handle_chain_status_response(self, message):
        """Handle chain status responses"""
        try:
            data = message.data
            peer_height = data['chain_height']
            
            # Store peer height for sync decisions
            setattr(self, f'_peer_height_{message.sender_id}', peer_height)
            
        except Exception as e:
            logger.error(f"Chain status response handler error: {e}")
    
    # Background Tasks
    async def _sync_monitoring_loop(self):
        """Monitor sync status and trigger sync when needed"""
        while True:
            try:
                if self.sync_status == SyncStatus.SYNCED:
                    # Periodically check if we're still in sync
                    if int(time.time()) - self.last_sync_time > self.sync_interval:
                        await self._check_sync_status()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Sync monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def _check_sync_status(self):
        """Check if we're still in sync with the network"""
        try:
            local_height = await self._get_local_chain_height()
            peer_heights = await self._query_peer_chain_heights()
            
            if peer_heights:
                max_peer_height = max(peer_heights.values())
                
                if max_peer_height > local_height:
                    logger.info(f"Out of sync: local={local_height}, network={max_peer_height}")
                    self.sync_status = SyncStatus.OUT_OF_SYNC
                    await self.start_sync()
                else:
                    self.last_sync_time = int(time.time())
            
        except Exception as e:
            logger.error(f"Sync status check error: {e}")
    
    # Utility Methods
    async def _get_local_chain_height(self) -> int:
        """Get local blockchain height"""
        try:
            # This would integrate with the actual blockchain storage
            from shared.db.db import db
            result = db.query_one("SELECT MAX(block_height) as height FROM blocks")
            return result['height'] if result['height'] is not None else 0
        except Exception as e:
            logger.error(f"Error getting local chain height: {e}")
            return 0
    
    async def _get_block_by_height(self, height: int) -> Optional[dict]:
        """Get block by height"""
        try:
            from shared.db.db import db
            result = db.query_one("SELECT * FROM blocks WHERE block_height = ?", (height,))
            return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error getting block by height: {e}")
            return None
    
    async def _get_blocks_range(self, start_height: int, end_height: int) -> List[dict]:
        """Get blocks in height range"""
        try:
            from shared.db.db import db
            results = db.query(
                "SELECT * FROM blocks WHERE block_height >= ? AND block_height <= ? ORDER BY block_height",
                (start_height, end_height)
            )
            return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Error getting blocks range: {e}")
            return []
    
    async def _add_block_to_chain(self, block: dict) -> bool:
        """Add block to local blockchain"""
        try:
            from shared.db.db import db
            
            # Parse transactions if they're JSON strings
            transactions = block.get('transactions', [])
            if isinstance(transactions, str):
                transactions = json.loads(transactions)
            
            db.execute("""
                INSERT INTO blocks (
                    block_height, timestamp, previous_hash, transactions,
                    merkle_root, block_hash, miner, difficulty, nonce, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                block['block_height'],
                block['timestamp'],
                block['previous_hash'],
                json.dumps(transactions),
                block.get('merkle_root', ''),
                block['block_hash'],
                block.get('miner', 'network'),
                block.get('difficulty', 1),
                block.get('nonce', 0),
                int(time.time())
            ))
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding block to chain: {e}")
            return False
    
    async def _query_peer_chain_heights(self) -> Dict[str, int]:
        """Query all peers for their chain heights"""
        try:
            peer_heights = {}
            connected_peers = self.peer_manager.get_connected_peers()
            
            # Send status requests to all peers
            for peer in connected_peers:
                message = NetworkMessage(
                    message_type="chain_status_request",
                    sender_id=self.peer_manager.node_id,
                    recipient_id=peer.peer_id,
                    data={},
                    timestamp=int(time.time())
                )
                
                await self.peer_manager.send_to_peer(peer.peer_id, message)
            
            # Wait for responses
            await asyncio.sleep(5)
            
            # Collect responses
            for peer in connected_peers:
                height = getattr(self, f'_peer_height_{peer.peer_id}', None)
                if height is not None:
                    peer_heights[peer.peer_id] = height
            
            return peer_heights
            
        except Exception as e:
            logger.error(f"Error querying peer heights: {e}")
            return {}
    
    # Public API
    def get_sync_status(self) -> dict:
        """Get current synchronization status"""
        return {
            "status": self.sync_status.value,
            "progress": self.sync_progress,
            "last_sync": self.last_sync_time,
            "active_syncs": len(self.active_syncs),
            "sync_peers": len(self.sync_peers)
        }
    
    async def announce_new_block(self, block: dict):
        """Announce a new block to the network"""
        try:
            message = NetworkMessage(
                message_type="block_announcement",
                sender_id=self.peer_manager.node_id,
                recipient_id=None,
                data=block,
                timestamp=int(time.time())
            )
            
            await self.peer_manager.broadcast_message(message)
            logger.info(f"Announced new block {block['block_height']} to network")
            
        except Exception as e:
            logger.error(f"Block announcement error: {e}")
    
    async def force_sync(self) -> bool:
        """Force immediate synchronization"""
        logger.info("Forcing blockchain synchronization...")
        return await self.start_sync()
