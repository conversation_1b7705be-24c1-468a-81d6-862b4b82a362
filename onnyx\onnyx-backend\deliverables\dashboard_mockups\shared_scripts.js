// ONNYX Platform Dashboard JavaScript
// Handles interactive elements and real-time updates

// Initialize dashboard functionality
function initializeDashboard() {
    console.log('🚀 ONNYX Dashboard Initialized');

    // Add hover effects to cards
    addCardHoverEffects();

    // Initialize navigation
    initializeNavigation();

    // Add click handlers
    addClickHandlers();
}

// Add hover effects to status cards
function addCardHoverEffects() {
    const cards = document.querySelectorAll('.status-card, .activity-item, .action-btn');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Initialize navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Navigate to appropriate dashboard
            const target = this.getAttribute('href').substring(1);
            navigateToDashboard(target);
        });
    });
}

// Navigate to different dashboard sections
function navigateToDashboard(section) {
    console.log(`Navigating to ${section} dashboard`);

    // In a real application, this would handle routing
    switch(section) {
        case 'dashboard':
            window.location.href = 'main_dashboard.html';
            break;
        case 'sabbath':
            window.location.href = 'sabbath_dashboard.html';
            break;
        case 'lending':
            window.location.href = 'lending_dashboard.html';
            break;
        case 'tribal':
            window.location.href = 'tribal_dashboard.html';
            break;
        default:
            console.log('Unknown section:', section);
    }
}

// Add click handlers for interactive elements
function addClickHandlers() {
    // Action buttons
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            handleActionClick(action);
        });
    });

    // Tab buttons
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.getAttribute('data-tab');
            switchTab(tab);
        });
    });
}

// Handle action button clicks
function handleActionClick(action) {
    console.log(`Action clicked: ${action}`);

    // Show modal or navigate based on action
    switch(action) {
        case 'Send ONX':
            showSendModal();
            break;
        case 'Receive ONX':
            showReceiveModal();
            break;
        case 'Create Loan':
            showLoanModal();
            break;
        case 'Tribal Vote':
            showVoteModal();
            break;
        default:
            console.log('Unknown action:', action);
    }
}

// Switch between tabs
function switchTab(tabName) {
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to clicked tab
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Show/hide content based on tab
    console.log(`Switched to ${tabName} tab`);
}

// Update real-time data
function updateRealTimeData() {
    console.log('📊 Updating real-time data');

    // Update balance
    updateBalance();

    // Update sabbath countdown
    updateSabbathCountdown();

    // Update compliance score
    updateComplianceScore();
}

// Update ONX balance
function updateBalance() {
    const balanceElement = document.querySelector('.balance-amount');
    if (balanceElement) {
        // Simulate real-time balance updates
        const currentBalance = parseFloat(balanceElement.textContent.replace(/[^0-9.]/g, ''));
        const fluctuation = (Math.random() - 0.5) * 10; // ±5 ONX fluctuation
        const newBalance = Math.max(0, currentBalance + fluctuation);

        balanceElement.textContent = `${newBalance.toFixed(2)} ONX`;
    }
}

// Update sabbath countdown
function updateSabbathCountdown() {
    const countdownElements = document.querySelectorAll('.countdown-number');

    if (countdownElements.length > 0) {
        // Calculate time until next Friday 6 PM
        const now = new Date();
        const nextFriday = new Date();

        // Find next Friday
        const daysUntilFriday = (5 - now.getDay() + 7) % 7;
        if (daysUntilFriday === 0 && now.getHours() >= 18) {
            nextFriday.setDate(now.getDate() + 7);
        } else {
            nextFriday.setDate(now.getDate() + daysUntilFriday);
        }

        nextFriday.setHours(18, 0, 0, 0); // 6 PM

        const timeDiff = nextFriday - now;
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

        if (countdownElements[0]) countdownElements[0].textContent = days;
        if (countdownElements[1]) countdownElements[1].textContent = hours;
        if (countdownElements[2]) countdownElements[2].textContent = minutes;
    }
}

// Update compliance score
function updateComplianceScore() {
    const scoreElements = document.querySelectorAll('.score-fill');

    scoreElements.forEach(element => {
        // Simulate slight compliance score changes
        const currentWidth = parseFloat(element.style.width || '98%');
        const change = (Math.random() - 0.5) * 0.5; // ±0.25% change
        const newWidth = Math.max(0, Math.min(100, currentWidth + change));

        element.style.width = `${newWidth}%`;
    });
}

// Initialize sabbath-specific dashboard
function initializeSabbathDashboard() {
    console.log('🕯️ Sabbath Dashboard Initialized');

    // Initialize clock animation
    initializeSabbathClock();

    // Update sabbath status
    updateSabbathStatus();
}

// Initialize sabbath clock
function initializeSabbathClock() {
    const hourHand = document.querySelector('.hour-hand');
    const minuteHand = document.querySelector('.minute-hand');

    if (hourHand && minuteHand) {
        function updateClock() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();

            const hourAngle = (hours * 30) + (minutes * 0.5);
            const minuteAngle = minutes * 6;

            hourHand.style.transform = `rotate(${hourAngle}deg)`;
            minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
        }

        updateClock();
        setInterval(updateClock, 60000); // Update every minute
    }
}

// Update sabbath status
function updateSabbathStatus() {
    const statusElement = document.querySelector('.status-text');

    if (statusElement) {
        const now = new Date();
        const dayOfWeek = now.getDay();
        const hour = now.getHours();

        // Check if it's sabbath (Friday 6 PM to Saturday 7 PM)
        const isSabbath = (dayOfWeek === 5 && hour >= 18) ||
                         (dayOfWeek === 6 && hour < 19);

        if (isSabbath) {
            statusElement.textContent = 'Currently Sabbath';
            statusElement.className = 'status-text in-sabbath';
        } else {
            statusElement.textContent = 'Not Currently Sabbath';
            statusElement.className = 'status-text not-sabbath';
        }
    }
}

// Modal functions (placeholders for full implementation)
function showSendModal() {
    alert('Send ONX Modal - Would open transaction form');
}

function showReceiveModal() {
    alert('Receive ONX Modal - Would show QR code and address');
}

function showLoanModal() {
    alert('Create Loan Modal - Would open biblical lending form');
}

function showVoteModal() {
    alert('Tribal Vote Modal - Would show governance proposals');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check which dashboard we're on and initialize accordingly
    if (document.body.classList.contains('sabbath-theme')) {
        initializeSabbathDashboard();
    } else {
        initializeDashboard();
    }

    // Start real-time updates
    updateRealTimeData();
    setInterval(updateRealTimeData, 30000); // Update every 30 seconds
});