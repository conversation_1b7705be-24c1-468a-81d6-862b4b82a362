"""
Onnyx Database-Backed Mempool Module

This module provides a SQLite-backed mempool implementation.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.chain.db_mempool")

class DBMempool:
    """
    DB<PERSON><PERSON><PERSON> manages the mempool data stored in a SQLite database.
    """
    
    def __init__(self):
        """Initialize the DBMempool."""
        # Create the mempool table if it doesn't exist
        self._create_table()
    
    def _create_table(self):
        """Create the mempool table if it doesn't exist."""
        try:
            # Create the mempool table
            db.execute("""
                CREATE TABLE IF NOT EXISTS mempool (
                    txid TEXT PRIMARY KEY,
                    timestamp INTEGER NOT NULL,
                    type TEXT NOT NULL,
                    op TEXT NOT NULL,
                    sender TEXT,
                    recipient TEXT,
                    token_id TEXT,
                    amount REAL,
                    data TEXT
                )
            """)
            
            # Create indexes
            db.execute("CREATE INDEX IF NOT EXISTS idx_mempool_timestamp ON mempool(timestamp)")
            db.execute("CREATE INDEX IF NOT EXISTS idx_mempool_type ON mempool(type)")
            db.execute("CREATE INDEX IF NOT EXISTS idx_mempool_op ON mempool(op)")
            db.execute("CREATE INDEX IF NOT EXISTS idx_mempool_sender ON mempool(sender)")
            db.execute("CREATE INDEX IF NOT EXISTS idx_mempool_recipient ON mempool(recipient)")
            db.execute("CREATE INDEX IF NOT EXISTS idx_mempool_token_id ON mempool(token_id)")
            
            logger.info("Created mempool table")
        except Exception as e:
            logger.error(f"Error creating mempool table: {str(e)}")
    
    def add(self, tx: Dict[str, Any]) -> bool:
        """
        Add a transaction to the mempool.
        
        Args:
            tx: The transaction to add
        
        Returns:
            True if the transaction was added, False otherwise
        """
        try:
            # Ensure the transaction has a txid
            if "txid" not in tx:
                tx["txid"] = f"tx_{int(time.time())}_{hash(json.dumps(tx, sort_keys=True))}"
            
            # Ensure the transaction has a timestamp
            if "timestamp" not in tx:
                tx["timestamp"] = int(time.time())
            
            # Insert the transaction into the database
            db.insert("mempool", {
                "txid": tx["txid"],
                "timestamp": tx["timestamp"],
                "type": tx.get("type", ""),
                "op": tx.get("op", ""),
                "sender": tx.get("from", None),
                "recipient": tx.get("to", None),
                "token_id": tx.get("token_id", None),
                "amount": tx.get("amount", None),
                "data": json.dumps(tx.get("data", {}))
            })
            
            logger.info(f"Added transaction {tx['txid']} to mempool")
            return True
        except Exception as e:
            logger.error(f"Error adding transaction to mempool: {str(e)}")
            return False
    
    def get(self, txid: str) -> Optional[Dict[str, Any]]:
        """
        Get a transaction from the mempool.
        
        Args:
            txid: The transaction ID
        
        Returns:
            The transaction or None if not found
        """
        try:
            # Query the database for the transaction
            tx = db.query_one("SELECT * FROM mempool WHERE txid = ?", (txid,))
            
            if tx:
                # Parse the data field
                tx["data"] = json.loads(tx["data"]) if tx["data"] else {}
                
                # Rename fields to match the expected format
                if "sender" in tx:
                    tx["from"] = tx["sender"]
                    del tx["sender"]
                
                if "recipient" in tx:
                    tx["to"] = tx["recipient"]
                    del tx["recipient"]
            
            return tx
        except Exception as e:
            logger.error(f"Error getting transaction from mempool: {str(e)}")
            return None
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all transactions from the mempool.
        
        Args:
            limit: The maximum number of transactions to return
            offset: The number of transactions to skip
        
        Returns:
            A list of transactions
        """
        try:
            # Query the database for the transactions
            txs = db.query(
                "SELECT * FROM mempool ORDER BY timestamp DESC LIMIT ? OFFSET ?",
                (limit, offset)
            )
            
            # Process the transactions
            for tx in txs:
                # Parse the data field
                tx["data"] = json.loads(tx["data"]) if tx["data"] else {}
                
                # Rename fields to match the expected format
                if "sender" in tx:
                    tx["from"] = tx["sender"]
                    del tx["sender"]
                
                if "recipient" in tx:
                    tx["to"] = tx["recipient"]
                    del tx["recipient"]
            
            return txs
        except Exception as e:
            logger.error(f"Error getting transactions from mempool: {str(e)}")
            return []
    
    def get_by_type(self, tx_type: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get transactions of a specific type from the mempool.
        
        Args:
            tx_type: The transaction type
            limit: The maximum number of transactions to return
            offset: The number of transactions to skip
        
        Returns:
            A list of transactions
        """
        try:
            # Query the database for the transactions
            txs = db.query(
                "SELECT * FROM mempool WHERE type = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?",
                (tx_type, limit, offset)
            )
            
            # Process the transactions
            for tx in txs:
                # Parse the data field
                tx["data"] = json.loads(tx["data"]) if tx["data"] else {}
                
                # Rename fields to match the expected format
                if "sender" in tx:
                    tx["from"] = tx["sender"]
                    del tx["sender"]
                
                if "recipient" in tx:
                    tx["to"] = tx["recipient"]
                    del tx["recipient"]
            
            return txs
        except Exception as e:
            logger.error(f"Error getting transactions from mempool: {str(e)}")
            return []
    
    def get_by_op(self, op: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get transactions with a specific operation from the mempool.
        
        Args:
            op: The operation
            limit: The maximum number of transactions to return
            offset: The number of transactions to skip
        
        Returns:
            A list of transactions
        """
        try:
            # Query the database for the transactions
            txs = db.query(
                "SELECT * FROM mempool WHERE op = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?",
                (op, limit, offset)
            )
            
            # Process the transactions
            for tx in txs:
                # Parse the data field
                tx["data"] = json.loads(tx["data"]) if tx["data"] else {}
                
                # Rename fields to match the expected format
                if "sender" in tx:
                    tx["from"] = tx["sender"]
                    del tx["sender"]
                
                if "recipient" in tx:
                    tx["to"] = tx["recipient"]
                    del tx["recipient"]
            
            return txs
        except Exception as e:
            logger.error(f"Error getting transactions from mempool: {str(e)}")
            return []
    
    def get_count(self) -> int:
        """
        Get the number of transactions in the mempool.
        
        Returns:
            The number of transactions
        """
        try:
            # Query the database for the number of transactions
            result = db.query_one("SELECT COUNT(*) as count FROM mempool")
            return result["count"] if result else 0
        except Exception as e:
            logger.error(f"Error getting transaction count from mempool: {str(e)}")
            return 0
    
    def remove(self, txid: str) -> bool:
        """
        Remove a transaction from the mempool.
        
        Args:
            txid: The transaction ID
        
        Returns:
            True if the transaction was removed, False otherwise
        """
        try:
            # Delete the transaction from the database
            db.execute("DELETE FROM mempool WHERE txid = ?", (txid,))
            
            logger.info(f"Removed transaction {txid} from mempool")
            return True
        except Exception as e:
            logger.error(f"Error removing transaction from mempool: {str(e)}")
            return False
    
    def clear(self) -> bool:
        """
        Clear the mempool.
        
        Returns:
            True if the mempool was cleared, False otherwise
        """
        try:
            # Delete all transactions from the database
            db.execute("DELETE FROM mempool")
            
            logger.info("Cleared mempool")
            return True
        except Exception as e:
            logger.error(f"Error clearing mempool: {str(e)}")
            return False

# Create a global instance of the DBMempool
db_mempool = DBMempool()
