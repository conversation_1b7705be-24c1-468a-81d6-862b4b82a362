# Eden Mode Integration Guide

## Overview

Eden Mode is an immersive, narrative-driven identity onboarding experience for the ONNYX blockchain platform that transforms standard user registration into a spiritual awakening journey. This guide provides complete integration instructions and testing procedures.

## 🌟 What Eden Mode Delivers

### Spiritual Narrative Framework
- **Step 1**: "The Forgotten Legacy" - Introduction to covenant awakening
- **Step 2**: "Nations Hidden in Plain Sight" - Interactive tribal/nation selection  
- **Step 3**: "The Covenant Reawakens" - Role and labor identification
- **Step 4**: "Eden Mode Activation" - Business/Sela creation or joining
- **Step 5**: "Blockchain Inscription" - Final identity creation with ceremony

### Technical Integration
- Complete Flask route system (`/auth/eden-mode/step1-5`)
- JavaScript controller class with state management
- CSS animations and cyberpunk aesthetic integration
- Biblical tokenomics and CIPP system integration
- Mobile-responsive design with accessibility compliance

## 📁 Files Created/Modified

### New Templates
- `web/templates/auth/eden_mode_step1.html` - The Forgotten Legacy
- `web/templates/auth/eden_mode_step2.html` - Nations Hidden in Plain Sight
- `web/templates/auth/eden_mode_step3.html` - The Covenant Reawakens
- `web/templates/auth/eden_mode_step4.html` - Eden Mode Activation
- `web/templates/auth/eden_mode_step5.html` - Blockchain Inscription

### New JavaScript
- `web/static/js/onnyx-immersive-controller.js` - Main controller class

### New Routes
- `web/routes/eden_mode.py` - Complete Flask blueprint

### Modified Files
- `web/app.py` - Added Eden Mode blueprint registration
- `web/static/css/main.css` - Added Eden Mode specific animations and styles

## 🚀 Installation Instructions

### 1. Verify Prerequisites
Ensure your ONNYX platform has these components:
- Biblical tokenomics system operational
- CIPP (Covenant Identity Protection Protocol) active
- Database schema with required tables:
  - `identities`
  - `biblical_nations`
  - `selas`
  - `sela_relationships`
  - `covenant_transactions`

### 2. Database Setup
Run this SQL to ensure required tables exist:

```sql
-- Covenant transactions table for Eden Mode
CREATE TABLE IF NOT EXISTS covenant_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    transaction_type TEXT NOT NULL,
    transaction_data TEXT,
    block_number INTEGER,
    created_at TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(id)
);

-- Ensure biblical nations are populated
INSERT OR IGNORE INTO biblical_nations (nation_code, nation_name, tribe_name, description, is_active) VALUES
('JUDAH', 'Judah', 'Judah', 'The royal tribe, leadership and governance', 1),
('BENJAMIN', 'Benjamin', 'Benjamin', 'Warriors and protectors of the covenant', 1),
('LEVI', 'Levi', 'Levi', 'Priests and teachers of the law', 1),
('EPHRAIM', 'Ephraim', 'Ephraim', 'Fruitful builders and agriculturalists', 1),
('MANASSEH', 'Manasseh', 'Manasseh', 'Commerce and craftsmanship', 1);
```

### 3. Flask Integration
The Eden Mode blueprint is automatically registered in `web/app.py`. Restart your Flask application to activate the new routes.

### 4. Static Assets
Ensure the new JavaScript controller is loaded by adding to your base template:

```html
<script src="{{ url_for('static', filename='js/onnyx-immersive-controller.js') }}"></script>
```

## 🧪 Testing Checklist

### Pre-Testing Setup
- [ ] Flask application starts without errors
- [ ] All Eden Mode routes accessible (`/auth/eden-mode/step1` through `/step5`)
- [ ] Database tables exist and are populated
- [ ] Biblical tokenomics system operational

### Step 1 Testing: The Forgotten Legacy
- [ ] Page loads with cyberpunk aesthetic
- [ ] Ambient background effects animate smoothly
- [ ] Progress indicator shows step 1 active
- [ ] "Begin the Journey" button transitions to step 2
- [ ] Audio toggle functions (optional)
- [ ] Mobile responsive design works
- [ ] Accessibility: keyboard navigation functional

### Step 2 Testing: Nations Hidden in Plain Sight
- [ ] Biblical nations load from database
- [ ] Nation cards display with proper icons and descriptions
- [ ] Nation selection updates UI with visual feedback
- [ ] Covenant acceptance checkbox functions
- [ ] Continue button enables only when nation selected and covenant accepted
- [ ] Session data stored correctly
- [ ] Back button returns to step 1

### Step 3 Testing: The Covenant Reawakens
- [ ] Previous selections display correctly (heritage summary)
- [ ] Personal information form validates properly
- [ ] Labor category selection works with visual feedback
- [ ] Form validation prevents progression with incomplete data
- [ ] Email validation functions
- [ ] Biblical tokenomics preview displays correctly
- [ ] Session data accumulates properly

### Step 4 Testing: Eden Mode Activation
- [ ] Journey summary displays all previous selections
- [ ] Sela path selection (Create vs Join) functions
- [ ] Create Sela form validates business information
- [ ] Available Selas load from API endpoint
- [ ] Sela selection in join mode works
- [ ] Form validation appropriate for selected path
- [ ] Business type dropdown populated
- [ ] Continue button state management correct

### Step 5 Testing: Blockchain Inscription
- [ ] Covenant summary displays complete journey
- [ ] Cryptographic key generation simulates properly
- [ ] Inscription process animates through all steps
- [ ] API call to create identity succeeds
- [ ] Biblical tokenomics initialization occurs
- [ ] Sela creation/joining processes correctly
- [ ] Completion ceremony displays
- [ ] Session data cleared after completion
- [ ] Redirect to dashboard functions

### API Testing
Test these endpoints:
- [ ] `GET /auth/eden-mode/api/selas/available` - Returns available Selas
- [ ] `POST /auth/eden-mode/create-identity` - Creates complete identity
- [ ] `POST /auth/eden-mode/validate-nation` - Validates nation selection
- [ ] `POST /auth/eden-mode/check-email` - Checks email availability

### Integration Testing
- [ ] Biblical tokenomics integration works
- [ ] CIPP tier assignment functions
- [ ] Sela validator creation operational
- [ ] Worker relationship creation works
- [ ] Gleaning pool participation enabled
- [ ] Sabbath enforcement activated
- [ ] Mining rewards system recognizes new identity

### Performance Testing
- [ ] Page load times under 3 seconds
- [ ] Animations smooth on mobile devices
- [ ] JavaScript controller memory usage reasonable
- [ ] Database queries optimized
- [ ] Image and asset loading efficient

### Cross-Browser Testing
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers (iOS Safari, Android Chrome)

### Accessibility Testing
- [ ] Screen reader compatibility
- [ ] Keyboard-only navigation
- [ ] Color contrast compliance
- [ ] Focus indicators visible
- [ ] Alt text for images
- [ ] ARIA labels where appropriate

## 🔧 Troubleshooting

### Common Issues

**Issue**: Eden Mode routes return 404
**Solution**: Ensure `eden_mode_bp` is registered in `web/app.py` and Flask app restarted

**Issue**: Biblical nations not loading
**Solution**: Check database connection and ensure `biblical_nations` table populated

**Issue**: JavaScript controller not initializing
**Solution**: Verify script inclusion in base template and check browser console for errors

**Issue**: Session data not persisting
**Solution**: Ensure Flask session configuration correct and browser allows cookies

**Issue**: API endpoints failing
**Solution**: Check database schema matches expected structure and verify import paths

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export FLASK_DEBUG=1
```

## 🎯 Success Criteria

Eden Mode is successfully integrated when:

1. **User Experience**: Complete 5-step journey flows smoothly with spiritual narrative
2. **Technical Function**: All API endpoints work and data persists correctly
3. **Design Consistency**: Maintains ONNYX cyberpunk aesthetic throughout
4. **Biblical Integration**: Properly integrates with tokenomics and CIPP systems
5. **Mobile Compatibility**: Responsive design works across all breakpoints
6. **Performance**: Loads quickly and animates smoothly
7. **Accessibility**: Meets WCAG guidelines for inclusive design

## 📞 Support

For issues or questions:
1. Check browser console for JavaScript errors
2. Review Flask application logs
3. Verify database schema and data
4. Test API endpoints individually
5. Validate session management

The Eden Mode experience represents a sophisticated integration of spiritual narrative with blockchain technology, providing users with a meaningful onboarding journey that aligns with ONNYX's biblical tokenomics mission.
