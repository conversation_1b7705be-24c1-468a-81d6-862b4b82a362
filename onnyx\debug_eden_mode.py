#!/usr/bin/env python3
"""
Test script to debug Eden Mode database issues
"""
import sys
import os

# Add the backend directory to the Python path
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'onnyx-backend')
sys.path.insert(0, backend_dir)

from onnyx-backend.shared.db.db import db
import json

def test_database_connection():
    """Test database connection and basic operations"""
    try:
        print("=== Testing Database Connection ===")
        
        # Test connection
        print("✓ Database connection successful")
        
        # Test getting tables
        tables = db.get_tables()
        print(f"✓ Found {len(tables)} tables")
        
        # Test identities table specifically
        result = db.query_all("SELECT name FROM sqlite_master WHERE type='table' AND name='identities';")
        if result:
            print("✓ Identities table exists")
        else:
            print("❌ Identities table not found")
            return False
            
        # Test columns in identities table
        result = db.query_all("PRAGMA table_info(identities);")
        columns = [col[1] for col in result]
        print(f"✓ Identities table has {len(columns)} columns")
        print(f"Columns: {', '.join(columns)}")
        
        # Test a simple insert (simulate what Eden Mode does)
        print("\n=== Testing Identity Creation ===")
        
        test_data = {
            'identity_id': 'TEST_ID_123',
            'name': 'Test User',
            'public_key': 'test_public_key_123',
            'metadata': json.dumps({'test': True}),
            'created_at': 1749159999,
            'updated_at': 1749159999,
            'role_class': 'israelite',
            'tribal_affiliation': 'judah',
            'verification_level': 1,
            'covenant_accepted': True,
            'vault_status': 'active',
            'etzem_score': 100,
            'status': 'active',
            'email': '<EMAIL>',
            'protection_tier': 'Basic',
            'zeman_count': 0,
            'cipp_tier': 0,
            'nation_code': 'USA',
            'nation_name': 'United States',
            'full_name': 'Test User',
            'tribe_name': 'Judah',
            'labor_category': 'priest',
            'eden_mode_completed': True,
            'sabbath_observer': True,
            'deeds_score': 50.0,
            'timezone': 'UTC'
        }
        
        # Clean up any existing test data
        db.query("DELETE FROM identities WHERE identity_id = ?", [test_data['identity_id']])
        
        # Test insert
        try:
            db.insert('identities', test_data)
            print("✓ Identity insert successful")
            
            # Verify the insert
            result = db.query_one("SELECT identity_id, name, email FROM identities WHERE identity_id = ?", 
                                [test_data['identity_id']])
            if result:
                print(f"✓ Identity verified: {result[0]}, {result[1]}, {result[2]}")
            else:
                print("❌ Identity not found after insert")
                
        except Exception as e:
            print(f"❌ Identity insert failed: {e}")
            print("This is likely the cause of the 'Internal server error'")
            
            # Show the specific error details
            print("\nError details:")
            print(f"Error type: {type(e).__name__}")
            print(f"Error message: {str(e)}")
            
            # Try to identify which columns are missing
            try:
                # Get the actual columns in the table
                result = db.query_all("PRAGMA table_info(identities);")
                actual_columns = [col[1] for col in result]
                provided_columns = list(test_data.keys())
                
                missing_columns = set(provided_columns) - set(actual_columns)
                extra_columns = set(actual_columns) - set(provided_columns)
                
                if missing_columns:
                    print(f"Columns in data but not in table: {missing_columns}")
                if extra_columns:
                    print(f"Columns in table but not in data: {extra_columns}")
                    
            except Exception as debug_e:
                print(f"Error during debugging: {debug_e}")
        
        # Clean up
        db.query("DELETE FROM identities WHERE identity_id = ?", [test_data['identity_id']])
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    print("Eden Mode Database Debugging")
    print("=" * 50)
    
    if test_database_connection():
        print("\n✅ Database tests completed")
    else:
        print("\n❌ Database tests failed")

if __name__ == "__main__":
    main()
