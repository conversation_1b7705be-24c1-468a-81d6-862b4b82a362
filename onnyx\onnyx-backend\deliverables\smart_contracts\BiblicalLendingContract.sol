// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title BiblicalLendingContract
 * @dev Implements Torah-compliant lending rules for the ONNYX platform
 * @notice This contract enforces biblical lending principles based on tribal affiliation
 */

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract BiblicalLendingContract is ReentrancyGuard, Ownable {
    
    // ONNYX token interface
    IERC20 public immutable onnyxToken;
    
    // Tribal affiliations
    enum TribalAffiliation {
        ISRAELITE,
        WITNESS_NATION,
        UNKNOWN
    }
    
    // Loan status
    enum LoanStatus {
        ACTIVE,
        REPAID,
        FORGIVEN,
        DEFAULTED
    }
    
    // Loan structure
    struct Loan {
        uint256 loanId;
        address borrower;
        address lender;
        uint256 principal;
        uint256 interestRate; // Basis points (0 for interest-free)
        uint256 amountRepaid;
        uint256 createdAt;
        uint256 dueDate;
        LoanStatus status;
        TribalAffiliation borrowerAffiliation;
        TribalAffiliation lenderAffiliation;
        string biblicalReference;
        bool autoForgiveness;
    }
    
    // State variables
    mapping(uint256 => Loan) public loans;
    mapping(address => TribalAffiliation) public tribalAffiliations;
    mapping(address => uint256[]) public userLoans;
    
    uint256 public nextLoanId = 1;
    uint256 public constant FORGIVENESS_THRESHOLD = 8000; // 80% in basis points
    uint256 public constant GRACE_PERIOD = 10 days;
    uint256 public constant SHMITA_CYCLE = 7 * 365 days; // 7 years
    
    // Events
    event LoanCreated(
        uint256 indexed loanId,
        address indexed borrower,
        address indexed lender,
        uint256 principal,
        uint256 interestRate,
        string biblicalReference
    );
    
    event LoanRepayment(
        uint256 indexed loanId,
        uint256 amount,
        uint256 totalRepaid,
        bool forgiven
    );
    
    event LoanForgiven(
        uint256 indexed loanId,
        uint256 forgivenAmount,
        string reason
    );
    
    event TribalAffiliationSet(
        address indexed user,
        TribalAffiliation affiliation
    );
    
    /**
     * @dev Constructor
     * @param _onnyxToken Address of the ONNYX token contract
     */
    constructor(address _onnyxToken) {
        onnyxToken = IERC20(_onnyxToken);
    }
    
    /**
     * @dev Set tribal affiliation for a user
     * @param user Address of the user
     * @param affiliation Tribal affiliation
     */
    function setTribalAffiliation(
        address user,
        TribalAffiliation affiliation
    ) external onlyOwner {
        tribalAffiliations[user] = affiliation;
        emit TribalAffiliationSet(user, affiliation);
    }
    
    /**
     * @dev Create a new loan with biblical compliance
     * @param borrower Address of the borrower
     * @param principal Loan amount
     * @param durationDays Loan duration in days
     */
    function createLoan(
        address borrower,
        uint256 principal,
        uint256 durationDays
    ) external nonReentrant returns (uint256) {
        require(borrower != msg.sender, "Cannot lend to yourself");
        require(principal > 0, "Principal must be greater than 0");
        require(durationDays > 0, "Duration must be greater than 0");
        
        // Get tribal affiliations
        TribalAffiliation lenderAffiliation = tribalAffiliations[msg.sender];
        TribalAffiliation borrowerAffiliation = tribalAffiliations[borrower];
        
        // Calculate interest rate based on biblical rules
        (uint256 interestRate, string memory biblicalRef) = calculateBiblicalInterestRate(
            lenderAffiliation,
            borrowerAffiliation
        );
        
        // Transfer tokens from lender to contract
        require(
            onnyxToken.transferFrom(msg.sender, address(this), principal),
            "Token transfer failed"
        );
        
        // Create loan
        uint256 loanId = nextLoanId++;
        loans[loanId] = Loan({
            loanId: loanId,
            borrower: borrower,
            lender: msg.sender,
            principal: principal,
            interestRate: interestRate,
            amountRepaid: 0,
            createdAt: block.timestamp,
            dueDate: block.timestamp + (durationDays * 1 days),
            status: LoanStatus.ACTIVE,
            borrowerAffiliation: borrowerAffiliation,
            lenderAffiliation: lenderAffiliation,
            biblicalReference: biblicalRef,
            autoForgiveness: (interestRate == 0) // Auto-forgiveness for interest-free loans
        });
        
        // Add to user loan lists
        userLoans[borrower].push(loanId);
        userLoans[msg.sender].push(loanId);
        
        // Transfer principal to borrower
        require(
            onnyxToken.transfer(borrower, principal),
            "Principal transfer failed"
        );
        
        emit LoanCreated(
            loanId,
            borrower,
            msg.sender,
            principal,
            interestRate,
            biblicalRef
        );
        
        return loanId;
    }
    
    /**
     * @dev Calculate biblical interest rate based on tribal affiliations
     * @param lenderAffiliation Lender's tribal affiliation
     * @param borrowerAffiliation Borrower's tribal affiliation
     * @return interestRate Interest rate in basis points
     * @return biblicalReference Relevant biblical reference
     */
    function calculateBiblicalInterestRate(
        TribalAffiliation lenderAffiliation,
        TribalAffiliation borrowerAffiliation
    ) internal pure returns (uint256 interestRate, string memory biblicalReference) {
        
        if (lenderAffiliation == TribalAffiliation.ISRAELITE && 
            borrowerAffiliation == TribalAffiliation.ISRAELITE) {
            // Israelite to Israelite: 0% interest (mandatory)
            return (0, "Exodus 22:25 - Thou shalt not be to him as an usurer");
        }
        
        if (lenderAffiliation == TribalAffiliation.ISRAELITE && 
            borrowerAffiliation == TribalAffiliation.WITNESS_NATION) {
            // Israelite to Witness Nation: Interest allowed
            return (500, "Deuteronomy 23:20 - Unto a stranger thou mayest lend upon usury");
        }
        
        if (lenderAffiliation == TribalAffiliation.WITNESS_NATION && 
            borrowerAffiliation == TribalAffiliation.ISRAELITE) {
            // Witness Nation to Israelite: Interest allowed (negotiate wisely)
            return (300, "Deuteronomy 23:20 - Negotiate with wisdom");
        }
        
        // Witness Nation to Witness Nation: Standard market practice
        return (1000, "Standard market practice");
    }
    
    /**
     * @dev Repay a loan
     * @param loanId ID of the loan to repay
     * @param amount Amount to repay
     */
    function repayLoan(uint256 loanId, uint256 amount) external nonReentrant {
        Loan storage loan = loans[loanId];
        require(loan.borrower == msg.sender, "Not the borrower");
        require(loan.status == LoanStatus.ACTIVE, "Loan not active");
        require(amount > 0, "Amount must be greater than 0");
        
        // Calculate total amount due (principal + interest)
        uint256 totalDue = calculateTotalDue(loanId);
        uint256 remainingDue = totalDue - loan.amountRepaid;
        
        // Ensure repayment doesn't exceed what's due
        if (amount > remainingDue) {
            amount = remainingDue;
        }
        
        // Transfer repayment from borrower
        require(
            onnyxToken.transferFrom(msg.sender, address(this), amount),
            "Repayment transfer failed"
        );
        
        loan.amountRepaid += amount;
        
        // Check for automatic forgiveness (80% threshold)
        bool forgiven = false;
        if (loan.autoForgiveness && 
            (loan.amountRepaid * 10000) >= (loan.principal * FORGIVENESS_THRESHOLD)) {
            
            loan.status = LoanStatus.FORGIVEN;
            forgiven = true;
            
            uint256 forgivenAmount = totalDue - loan.amountRepaid;
            emit LoanForgiven(loanId, forgivenAmount, "80% threshold reached");
        } else if (loan.amountRepaid >= totalDue) {
            loan.status = LoanStatus.REPAID;
        }
        
        // Transfer repayment to lender
        require(
            onnyxToken.transfer(loan.lender, amount),
            "Lender transfer failed"
        );
        
        emit LoanRepayment(loanId, amount, loan.amountRepaid, forgiven);
    }
    
    /**
     * @dev Calculate total amount due for a loan
     * @param loanId ID of the loan
     * @return Total amount due including interest
     */
    function calculateTotalDue(uint256 loanId) public view returns (uint256) {
        Loan memory loan = loans[loanId];
        
        if (loan.interestRate == 0) {
            return loan.principal; // Interest-free loan
        }
        
        uint256 timeElapsed = block.timestamp - loan.createdAt;
        uint256 interest = (loan.principal * loan.interestRate * timeElapsed) / 
                          (10000 * 365 days); // Annual interest calculation
        
        return loan.principal + interest;
    }
    
    /**
     * @dev Get loan details
     * @param loanId ID of the loan
     * @return Loan struct
     */
    function getLoan(uint256 loanId) external view returns (Loan memory) {
        return loans[loanId];
    }
    
    /**
     * @dev Get user's loan IDs
     * @param user Address of the user
     * @return Array of loan IDs
     */
    function getUserLoans(address user) external view returns (uint256[] memory) {
        return userLoans[user];
    }
    
    /**
     * @dev Emergency function to forgive a loan (Shmita/Yovel)
     * @param loanId ID of the loan to forgive
     * @param reason Reason for forgiveness
     */
    function forgiveLoan(uint256 loanId, string calldata reason) external onlyOwner {
        Loan storage loan = loans[loanId];
        require(loan.status == LoanStatus.ACTIVE, "Loan not active");
        
        uint256 totalDue = calculateTotalDue(loanId);
        uint256 forgivenAmount = totalDue - loan.amountRepaid;
        
        loan.status = LoanStatus.FORGIVEN;
        
        emit LoanForgiven(loanId, forgivenAmount, reason);
    }
}
