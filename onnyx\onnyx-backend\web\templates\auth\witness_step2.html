{% extends "base.html" %}

{% block title %}Witness Nation Selection - ONNYX Eden Mode{% endblock %}
{% block meta_description %}Choose your ancestral heritage from the biblical witness nations and begin your covenant registration journey.{% endblock %}

{% block head %}
<style>
    .nation-selection-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .ancestral-groups {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .ancestral-group {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .ancestral-group:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(139, 92, 246, 0.5);
    }
    
    .ancestral-group.selected {
        border-color: #8B5CF6;
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(79, 70, 229, 0.1));
    }
    
    .group-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .group-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
    }
    
    .nations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 2rem;
        display: none;
    }
    
    .nation-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }
    
    .nation-card:hover {
        transform: translateY(-3px);
        border-color: rgba(34, 197, 94, 0.5);
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.05));
    }
    
    .nation-card.selected {
        border-color: #22C55E;
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.1));
    }
    
    .nation-flag {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .nation-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .nation-description {
        font-size: 0.875rem;
        color: #D1D5DB;
        line-height: 1.4;
    }
    
    .progress-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
    }
    
    .progress-step {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 0.5rem;
        font-weight: bold;
        color: white;
    }
    
    .progress-step.completed {
        background: linear-gradient(135deg, #10B981, #34D399);
    }
    
    .progress-step.current {
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        animation: pulse 2s infinite;
    }
    
    .progress-step.pending {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.6);
    }
    
    .progress-connector {
        width: 3rem;
        height: 3px;
        background: rgba(255, 255, 255, 0.2);
        margin: 0 0.5rem;
    }
    
    .progress-connector.completed {
        background: linear-gradient(90deg, #10B981, #34D399);
    }
    
    .witness-info {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
        border: 1px solid rgba(34, 197, 94, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .search-box {
        width: 100%;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .search-box::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }
    
    .search-box:focus {
        outline: none;
        border-color: #8B5CF6;
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
    
    .selection-summary {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-48 h-48 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float-delayed"></div>
        <div class="absolute bottom-20 left-1/3 w-40 h-40 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
    </div>

    <div class="container mx-auto px-4 py-8 relative z-10">
        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step completed">1</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step current">2</div>
            <div class="progress-connector pending"></div>
            <div class="progress-step pending">3</div>
            <div class="progress-connector pending"></div>
            <div class="progress-step pending">4</div>
        </div>

        <div class="nation-selection-container">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">
                    <span class="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 bg-clip-text text-transparent">
                        Witness Nation Heritage
                    </span>
                </h1>
                <p class="text-gray-300 max-w-2xl mx-auto">
                    Choose your ancestral heritage from the nations that witnessed God's covenant with Israel. 
                    Your heritage connects you to the broader biblical narrative.
                </p>
            </div>

            <!-- Witness Nation Information -->
            <div class="witness-info">
                <div class="flex items-center mb-3">
                    <span class="text-2xl mr-3">🌍</span>
                    <h3 class="text-xl font-bold text-emerald-100">Witness Nation Covenant</h3>
                </div>
                <p class="text-emerald-200 mb-3">
                    As a Witness Nation member, you join the covenant community as one who supports and participates 
                    in the restoration of biblical order. No Gate Keeper verification is required.
                </p>
                <div class="text-sm text-emerald-300">
                    <strong>Your Role:</strong> Support the covenant community, participate in biblical commerce, 
                    and contribute to the restoration of righteous governance.
                </div>

                <!-- Developer Bypass Section (Hidden by default) -->
                <div class="mt-6">
                    <button onclick="toggleDeveloperBypass()" 
                            class="text-xs text-gray-500 hover:text-emerald-400 transition-colors duration-300"
                            style="opacity: 0.3;">
                        Developer Access
                    </button>
                    <div id="developerBypass" class="hidden mt-4 max-w-md mx-auto">
                        <div class="bg-emerald-600/10 border border-emerald-500/20 rounded-lg p-6">
                            <h4 class="text-lg font-orbitron font-semibold text-emerald-400 mb-4">
                                🔑 Developer Bypass
                            </h4>
                            <p class="text-sm text-emerald-200 mb-4">
                                Enter the covenant phrase to bypass nation selection
                            </p>
                            <input type="password" 
                                   id="bypassPassword" 
                                   placeholder="Enter covenant phrase..."
                                   class="w-full px-4 py-3 bg-gray-800/50 border border-emerald-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 mb-4">
                            <button onclick="validateBypass()" 
                                    class="w-full px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-lg font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                🚀 Bypass Selection
                            </button>
                            <p class="text-xs text-emerald-300 mt-2 text-center">
                                This will mark your identity as developer-verified
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Box -->
            <input type="text" id="nationSearch" class="search-box" 
                   placeholder="Search for your ancestral nation or heritage...">

            <!-- Ancestral Groups -->
            <div class="ancestral-groups" id="ancestralGroups">
                <!-- Groups will be populated by JavaScript -->
            </div>

            <!-- Nations Grid -->
            <div class="nations-grid" id="nationsGrid">
                <!-- Nations will be populated by JavaScript -->
            </div>

            <!-- Selection Summary -->
            <div class="selection-summary" id="selectionSummary">
                <h3 class="text-xl font-bold text-white mb-4">Selected Heritage</h3>
                <div id="selectedNationInfo"></div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between items-center mt-8">
                <a href="{{ url_for('eden_mode.step1') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                    ← Back to Path Selection
                </a>
                
                <button id="continueToNext" disabled
                        class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100">
                    Continue to Confirmation →
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Simulate biblical nations data (in production, this would come from the backend)
    const biblicalNations = [
        // Japhethite Nations (Europeans, Indo-Europeans)
        { nation_code: 'GRC', nation_name: 'Greece', tribe_name: 'Javan', ancestral_group: 'Japhethite', flag_symbol: '🇬🇷', description: 'Descendants of Javan, known for philosophy and learning' },
        { nation_code: 'ITA', nation_name: 'Italy', tribe_name: 'Javan', ancestral_group: 'Japhethite', flag_symbol: '🇮🇹', description: 'Roman heritage, center of early Christianity' },
        { nation_code: 'DEU', nation_name: 'Germany', tribe_name: 'Ashkenaz', ancestral_group: 'Japhethite', flag_symbol: '🇩🇪', description: 'Germanic tribes, reformation heritage' },
        { nation_code: 'FRA', nation_name: 'France', tribe_name: 'Gomer', ancestral_group: 'Japhethite', flag_symbol: '🇫🇷', description: 'Gallic heritage, daughter of Zion' },
        { nation_code: 'GBR', nation_name: 'Britain', tribe_name: 'Tarshish', ancestral_group: 'Japhethite', flag_symbol: '🇬🇧', description: 'Maritime nation, ships of Tarshish' },
        { nation_code: 'RUS', nation_name: 'Russia', tribe_name: 'Magog', ancestral_group: 'Japhethite', flag_symbol: '🇷🇺', description: 'Northern kingdom, land of Magog' },
        
        // Hamitic Nations (Africans, Egyptians)
        { nation_code: 'EGY', nation_name: 'Egypt', tribe_name: 'Mizraim', ancestral_group: 'Hamitic', flag_symbol: '🇪🇬', description: 'Land of bondage and deliverance' },
        { nation_code: 'ETH', nation_name: 'Ethiopia', tribe_name: 'Cush', ancestral_group: 'Hamitic', flag_symbol: '🇪🇹', description: 'Land of Cush, ancient kingdom' },
        { nation_code: 'LBY', nation_name: 'Libya', tribe_name: 'Put', ancestral_group: 'Hamitic', flag_symbol: '🇱🇾', description: 'Western African heritage' },
        { nation_code: 'ZAF', nation_name: 'South Africa', tribe_name: 'Ham', ancestral_group: 'Hamitic', flag_symbol: '🇿🇦', description: 'Southern African nations' },
        
        // Shemitic Nations (Middle Eastern, non-Israelite)
        { nation_code: 'IRQ', nation_name: 'Iraq', tribe_name: 'Asshur', ancestral_group: 'Shemitic', flag_symbol: '🇮🇶', description: 'Ancient Assyria, Mesopotamia' },
        { nation_code: 'IRN', nation_name: 'Iran', tribe_name: 'Elam', ancestral_group: 'Shemitic', flag_symbol: '🇮🇷', description: 'Persian empire, Elamite heritage' },
        { nation_code: 'SYR', nation_name: 'Syria', tribe_name: 'Aram', ancestral_group: 'Shemitic', flag_symbol: '🇸🇾', description: 'Aramean heritage, Damascus' },
        { nation_code: 'JOR', nation_name: 'Jordan', tribe_name: 'Ammon', ancestral_group: 'Shemitic', flag_symbol: '🇯🇴', description: 'Ammonite and Moabite heritage' },
        { nation_code: 'SAU', nation_name: 'Arabia', tribe_name: 'Ishmael', ancestral_group: 'Shemitic', flag_symbol: '🇸🇦', description: 'Ishmaelite heritage, desert dwelling' },
        
        // Other Nations
        { nation_code: 'USA', nation_name: 'United States', tribe_name: 'Mixed', ancestral_group: 'Mixed Heritage', flag_symbol: '🇺🇸', description: 'Nation of many peoples and tribes' },
        { nation_code: 'CAN', nation_name: 'Canada', tribe_name: 'Mixed', ancestral_group: 'Mixed Heritage', flag_symbol: '🇨🇦', description: 'Northern confederation of peoples' },
        { nation_code: 'AUS', nation_name: 'Australia', tribe_name: 'Mixed', ancestral_group: 'Mixed Heritage', flag_symbol: '🇦🇺', description: 'Island nation, diverse heritage' },
        { nation_code: 'CHN', nation_name: 'China', tribe_name: 'Sinim', ancestral_group: 'Asiatic', flag_symbol: '🇨🇳', description: 'Land of Sinim, eastern kingdom' },
        { nation_code: 'JPN', nation_name: 'Japan', tribe_name: 'Eastern Isles', ancestral_group: 'Asiatic', flag_symbol: '🇯🇵', description: 'Island nation of the east' },
        { nation_code: 'IND', nation_name: 'India', tribe_name: 'Eastern Peoples', ancestral_group: 'Asiatic', flag_symbol: '🇮🇳', description: 'Subcontinent of diverse peoples' }
    ];

    const ancestralGroups = [
        { name: 'Japhethite', icon: '🌊', description: 'European and Indo-European peoples, sons of Japheth' },
        { name: 'Hamitic', icon: '🌍', description: 'African peoples, sons of Ham' },
        { name: 'Shemitic', icon: '🏜️', description: 'Middle Eastern peoples, sons of Shem (non-Israelite)' },
        { name: 'Asiatic', icon: '🏔️', description: 'Eastern Asian peoples and island nations' },
        { name: 'Mixed Heritage', icon: '🌐', description: 'Modern nations with diverse ancestral heritage' }
    ];

    let selectedNation = null;
    let selectedGroup = null;

    // Initialize ancestral groups
    function initializeAncestralGroups() {
        const groupsContainer = document.getElementById('ancestralGroups');
        groupsContainer.innerHTML = '';

        ancestralGroups.forEach(group => {
            const nationCount = biblicalNations.filter(n => n.ancestral_group === group.name).length;
            const groupElement = document.createElement('div');
            groupElement.className = 'ancestral-group';
            groupElement.innerHTML = `
                <div class="group-header">
                    <div class="group-icon">${group.icon}</div>
                    <div>
                        <h3 class="text-lg font-bold text-white">${group.name}</h3>
                        <p class="text-sm text-gray-400">${nationCount} nations</p>
                    </div>
                </div>
                <p class="text-gray-300 text-sm">${group.description}</p>
            `;

            groupElement.addEventListener('click', () => selectAncestralGroup(group.name));
            groupsContainer.appendChild(groupElement);
        });
    }

    function selectAncestralGroup(groupName) {
        selectedGroup = groupName;
        
        // Update group selection UI
        document.querySelectorAll('.ancestral-group').forEach(el => {
            el.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');

        // Show nations for this group
        showNationsForGroup(groupName);
    }

    function showNationsForGroup(groupName) {
        const nationsGrid = document.getElementById('nationsGrid');
        const nations = biblicalNations.filter(nation => nation.ancestral_group === groupName);
        
        nationsGrid.innerHTML = '';
        nationsGrid.style.display = 'grid';

        nations.forEach(nation => {
            const nationElement = document.createElement('div');
            nationElement.className = 'nation-card';
            nationElement.innerHTML = `
                <div class="nation-flag">${nation.flag_symbol}</div>
                <div class="nation-name">${nation.nation_name}</div>
                <div class="text-sm text-purple-300 mb-2">Tribe of ${nation.tribe_name}</div>
                <div class="nation-description">${nation.description}</div>
            `;

            nationElement.addEventListener('click', () => selectNation(nation));
            nationsGrid.appendChild(nationElement);
        });
    }

    function selectNation(nation) {
        selectedNation = nation;
        
        // Update nation selection UI
        document.querySelectorAll('.nation-card').forEach(el => {
            el.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');

        // Show selection summary
        showSelectionSummary(nation);
        
        // Enable continue button
        document.getElementById('continueToNext').disabled = false;
    }

    function showSelectionSummary(nation) {
        const summaryContainer = document.getElementById('selectionSummary');
        const summaryInfo = document.getElementById('selectedNationInfo');
        
        summaryInfo.innerHTML = `
            <div class="flex items-center mb-4">
                <div class="text-4xl mr-4">${nation.flag_symbol}</div>
                <div>
                    <h4 class="text-xl font-bold text-white">${nation.nation_name}</h4>
                    <p class="text-emerald-300">Tribe of ${nation.tribe_name} (${nation.ancestral_group})</p>
                </div>
            </div>
            <p class="text-gray-300">${nation.description}</p>
        `;
        
        summaryContainer.style.display = 'block';
    }

    // Search functionality
    document.getElementById('nationSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        
        if (searchTerm.length > 2) {
            const filteredNations = biblicalNations.filter(nation => 
                nation.nation_name.toLowerCase().includes(searchTerm) ||
                nation.tribe_name.toLowerCase().includes(searchTerm) ||
                nation.description.toLowerCase().includes(searchTerm)
            );
            
            showSearchResults(filteredNations);
        } else if (selectedGroup) {
            showNationsForGroup(selectedGroup);
        } else {
            document.getElementById('nationsGrid').style.display = 'none';
        }
    });

    function showSearchResults(nations) {
        const nationsGrid = document.getElementById('nationsGrid');
        nationsGrid.innerHTML = '';
        nationsGrid.style.display = 'grid';

        if (nations.length === 0) {
            nationsGrid.innerHTML = '<div class="text-gray-400 text-center col-span-full py-8">No nations found matching your search.</div>';
            return;
        }

        nations.forEach(nation => {
            const nationElement = document.createElement('div');
            nationElement.className = 'nation-card';
            nationElement.innerHTML = `
                <div class="nation-flag">${nation.flag_symbol}</div>
                <div class="nation-name">${nation.nation_name}</div>
                <div class="text-sm text-purple-300 mb-2">Tribe of ${nation.tribe_name}</div>
                <div class="nation-description">${nation.description}</div>
            `;

            nationElement.addEventListener('click', () => selectNation(nation));
            nationsGrid.appendChild(nationElement);
        });
    }

    // Continue button handler
    document.getElementById('continueToNext').addEventListener('click', function() {
        if (!selectedNation) return;
        
        // Store selection in session
        sessionStorage.setItem('edenMode_selectedNation', selectedNation.nation_code);
        sessionStorage.setItem('edenMode_selectedNationName', selectedNation.nation_name);
        sessionStorage.setItem('edenMode_selectedTribe', selectedNation.tribe_name);
        sessionStorage.setItem('edenMode_ancestralGroup', selectedNation.ancestral_group);
        
        // Navigate to next step
        window.location.href = `/auth/eden-mode/witness-step3?nation=${selectedNation.nation_code}`;
    });

    // Initialize the page
    initializeAncestralGroups();
    
    // Restore selection if returning to this page
    const savedNation = sessionStorage.getItem('edenMode_selectedNation');
    if (savedNation) {
        const nation = biblicalNations.find(n => n.nation_code === savedNation);
        if (nation) {
            selectAncestralGroup(nation.ancestral_group);
            setTimeout(() => {
                const nationCards = document.querySelectorAll('.nation-card');
                const targetCard = Array.from(nationCards).find(card => 
                    card.textContent.includes(nation.nation_name)
                );
                if (targetCard) {
                    targetCard.click();
                }
            }, 100);
        }
    }
});

// Developer Bypass Functions
function toggleDeveloperBypass() {
    const bypassSection = document.getElementById('developerBypass');
    bypassSection.classList.toggle('hidden');
    
    // Focus on password input if showing
    if (!bypassSection.classList.contains('hidden')) {
        setTimeout(() => {
            document.getElementById('bypassPassword').focus();
        }, 100);
    }
}

function validateBypass() {
    const password = document.getElementById('bypassPassword').value.trim();
    const correctPassword = "Israel United In Christ";
    
    if (password === correctPassword) {
        // Store bypass flag
        sessionStorage.setItem('developerBypass', 'true');
        sessionStorage.setItem('bypassType', 'developer');
        sessionStorage.setItem('bypassTimestamp', Date.now().toString());
        sessionStorage.setItem('covenant_path', 'witness');
        
        // Show success message
        const button = document.querySelector('button[onclick="validateBypass()"]');
        button.innerHTML = '✅ Bypass Activated';
        button.disabled = true;
        button.classList.remove('bg-gradient-to-r', 'from-emerald-600', 'to-green-600');
        button.classList.add('bg-emerald-500');
        
        // Hide the regular nation selection
        const ancestralGroups = document.querySelector('.ancestral-groups');
        const nationsGrid = document.querySelector('.nations-grid');
        if (ancestralGroups) {
            ancestralGroups.style.opacity = '0.5';
            ancestralGroups.style.pointerEvents = 'none';
        }
        if (nationsGrid) {
            nationsGrid.style.opacity = '0.5';
            nationsGrid.style.pointerEvents = 'none';
        }
        
        // Show bypass success message
        setTimeout(() => {
            showBypassSuccess();
        }, 1000);
        
    } else {
        // Show error
        const input = document.getElementById('bypassPassword');
        input.style.borderColor = '#ef4444';
        input.style.boxShadow = '0 0 10px rgba(239, 68, 68, 0.3)';
        input.value = '';
        input.placeholder = 'Incorrect phrase - try again';
        
        setTimeout(() => {
            input.style.borderColor = '';
            input.style.boxShadow = '';
            input.placeholder = 'Enter covenant phrase...';
        }, 2000);
    }
}

function showBypassSuccess() {
    // Create success overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center';
    overlay.innerHTML = `
        <div class="bg-gray-900/95 border border-emerald-500/30 rounded-3xl p-12 max-w-lg mx-4 text-center">
            <div class="text-6xl mb-6">🚀</div>
            <h3 class="text-3xl font-orbitron font-bold text-emerald-400 mb-4">
                Developer Bypass Activated
            </h3>
            <p class="text-gray-300 mb-6 leading-relaxed">
                Your identity has been marked as developer-verified. You will proceed directly to covenant inscription.
            </p>
            <div class="bg-emerald-500/10 border border-emerald-500/20 rounded-lg p-4 mb-6">
                <p class="text-sm font-orbitron font-semibold text-emerald-400">
                    ✅ Proceeding as Witness Nation developer
                </p>
            </div>
            <button onclick="proceedWithBypass()" 
                    class="px-8 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                Continue to Inscription
            </button>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function proceedWithBypass() {
    // Store that we're bypassing and proceed to final step
    sessionStorage.setItem('skipNationSelection', 'true');
    sessionStorage.setItem('verificationStatus', 'developer-bypassed');
    sessionStorage.setItem('edenMode_selectedNation', 'DEV');
    sessionStorage.setItem('edenMode_selectedNationName', 'Developer Nation');
    sessionStorage.setItem('edenMode_selectedTribe', 'Developer');
    sessionStorage.setItem('edenMode_ancestralGroup', 'Developer');
    
    // Go directly to step 4 (covenant inscription) for witness nations
    window.location.href = '/auth/eden-mode/witness-step4?bypass=developer';
}
</script>
{% endblock %}
