#!/usr/bin/env python3
"""
Test Dashboard Role Column Fix
"""

import requests
import sys

def test_dashboard():
    """Test dashboard loading for role column errors"""
    print("Testing dashboard loading...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/dashboard', timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("Dashboard loaded successfully")
            content = response.text.lower()
            
            if 'no such column: role' in content:
                print("ERROR: Still has 'no such column: role' error")
                return False
            elif 'error loading dashboard' in content:
                print("WARNING: Dashboard has other errors")
                return False
            else:
                print("SUCCESS: No role column errors detected")
                return True
                
        elif response.status_code == 302:
            print("Dashboard redirected (likely to login)")
            print("SUCCESS: This is expected for protected routes")
            return True
            
        elif response.status_code == 500:
            print("ERROR: Server error (500)")
            return False
            
        else:
            print(f"Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error testing dashboard: {e}")
        return False

def test_role_column():
    """Test that role column exists and works"""
    print("\nTesting role column in database...")
    
    try:
        import sys
        sys.path.append('.')
        from shared.db.db import db
        
        # Test role column query
        result = db.query("SELECT identity_id, name, role, role_class FROM identities LIMIT 1")
        print(f"SUCCESS: Role column query worked ({len(result)} results)")
        
        if result:
            user = result[0]
            print(f"Sample user role: {user.get('role', 'None')}")
            print(f"Sample user role_class: {user.get('role_class', 'None')}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Role column test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Dashboard Role Column Fix")
    print("=" * 40)
    
    role_success = test_role_column()
    dashboard_success = test_dashboard()
    
    print("\nResults:")
    if role_success:
        print("✅ Role column working")
    else:
        print("❌ Role column issues")
        
    if dashboard_success:
        print("✅ Dashboard loading correctly")
    else:
        print("❌ Dashboard has issues")
    
    overall_success = role_success and dashboard_success
    print(f"\n{'SUCCESS' if overall_success else 'FAILURE'}: Dashboard role column fix")
    
    sys.exit(0 if overall_success else 1)
