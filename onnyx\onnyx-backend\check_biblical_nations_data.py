#!/usr/bin/env python3
"""
Check Biblical Nations Data
"""

import sqlite3
import sys
import os

def check_biblical_nations_table():
    """Check if biblical_nations table exists and has data"""
    
    print("🔍 Checking Biblical Nations Table")
    print("=" * 40)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='biblical_nations'")
        if not cursor.fetchone():
            print("❌ biblical_nations table does not exist")
            conn.close()
            return False
        
        print("✅ biblical_nations table exists")
        
        # Check table structure
        cursor.execute('PRAGMA table_info(biblical_nations)')
        columns = cursor.fetchall()
        print(f"✅ Table has {len(columns)} columns:")
        for col in columns:
            print(f"   - {col[1]} {col[2]}")
        
        # Check data counts
        cursor.execute("SELECT COUNT(*) FROM biblical_nations")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 Total records: {total_count}")
        
        if total_count == 0:
            print("⚠️ No data in biblical_nations table")
            conn.close()
            return False
        
        # Check covenant nations (12 Tribes of Israel)
        cursor.execute("SELECT COUNT(*) FROM biblical_nations WHERE nation_type = 'covenant'")
        covenant_count = cursor.fetchone()[0]
        print(f"   - Covenant nations (Israel): {covenant_count}")
        
        # Check Edom dukes
        cursor.execute("SELECT COUNT(*) FROM biblical_nations WHERE ancestral_group = 'Edom'")
        edom_count = cursor.fetchone()[0]
        print(f"   - Edom dukes: {edom_count}")
        
        # Check Ishmael princes
        cursor.execute("SELECT COUNT(*) FROM biblical_nations WHERE ancestral_group = 'Ishmael'")
        ishmael_count = cursor.fetchone()[0]
        print(f"   - Ishmael princes: {ishmael_count}")
        
        # Show sample data
        print("\n📋 Sample covenant nations:")
        cursor.execute("SELECT nation_code, nation_name, tribe_name FROM biblical_nations WHERE nation_type = 'covenant' LIMIT 5")
        covenant_samples = cursor.fetchall()
        for nation in covenant_samples:
            print(f"   - {nation[0]}: {nation[1]} ({nation[2]})")
        
        print("\n📋 Sample Edom dukes:")
        cursor.execute("SELECT nation_code, nation_name, tribe_name FROM biblical_nations WHERE ancestral_group = 'Edom' LIMIT 5")
        edom_samples = cursor.fetchall()
        for duke in edom_samples:
            print(f"   - {duke[0]}: {duke[1]} ({duke[2]})")
        
        conn.close()
        
        # Return True if we have reasonable data
        return covenant_count >= 10 and edom_count >= 10  # Should have at least 10 of each
        
    except Exception as e:
        print(f"❌ Error checking biblical_nations table: {e}")
        return False

def test_tribes_route_directly():
    """Test the tribes route directly to see what's happening"""
    
    print("\n🧪 Testing Tribes Route Directly")
    print("=" * 35)
    
    try:
        import requests
        
        # Test the overview route
        response = requests.get('http://127.0.0.1:5000/tribes/overview', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"Content Length: {len(content)} characters")
            
            # Check for key elements
            if 'Biblical Tribes & Nations' in content:
                print("✅ Main title found")
            else:
                print("❌ Main title missing")
            
            if 'covenant_nations' in content:
                print("✅ Template variable reference found")
            else:
                print("❌ Template variable reference missing")
            
            # Check for template loops
            if '{% for nation in covenant_nations %}' in content:
                print("✅ Covenant nations loop found")
            else:
                print("❌ Covenant nations loop missing")
            
            if '{% for duke in edom_dukes %}' in content:
                print("✅ Edom dukes loop found")
            else:
                print("❌ Edom dukes loop missing")
            
            # Look for error indicators
            if 'error' in content.lower() or 'exception' in content.lower():
                print("⚠️ Possible error in content")
                # Find error context
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'error' in line.lower() or 'exception' in line.lower():
                        print(f"   Line {i}: {line.strip()}")
            
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            if response.text:
                print(f"Error content: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error testing tribes route: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Checking Biblical Nations Data and Tribes Route")
    print("=" * 60)
    
    # Check database
    data_ok = check_biblical_nations_table()
    
    # Test route
    route_ok = test_tribes_route_directly()
    
    print("\n📊 Results:")
    print("=" * 20)
    
    if data_ok:
        print("✅ Biblical nations data: Available")
    else:
        print("❌ Biblical nations data: Missing or insufficient")
    
    if route_ok:
        print("✅ Tribes route: Accessible")
    else:
        print("❌ Tribes route: Issues found")
    
    overall_success = data_ok and route_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: Biblical nations data and tribes route are working!")
    else:
        print("\n⚠️ Issues found - may need to populate biblical_nations table")
        if not data_ok:
            print("   - Run biblical nations population script")
        if not route_ok:
            print("   - Check server logs for route errors")
    
    sys.exit(0 if overall_success else 1)
