"""
Enhanced Onnyx Block Model

This module provides the enhanced Block model for the Onnyx blockchain with comprehensive
block header structure including biblical compliance fields, witness logs,
and covenant-specific metadata as specified in the requirements.
"""

import time
import json
import hashlib
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class WitnessLog:
    """Witness log for transaction verification"""
    witness_id: str
    signature: str
    timestamp: int
    proof_url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "witness_id": self.witness_id,
            "signature": self.signature,
            "timestamp": self.timestamp,
            "proof_url": self.proof_url
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WitnessLog':
        """Create from dictionary"""
        return cls(
            witness_id=data["witness_id"],
            signature=data["signature"],
            timestamp=data["timestamp"],
            proof_url=data.get("proof_url")
        )


@dataclass
class ChainParameters:
    """Chain parameters snapshot for the block"""
    sabbath_cycle: int = 7
    yovel_year: int = 50
    gleaning_pool_percentage: float = 0.02
    biblical_compliance_required: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "sabbath_cycle": self.sabbath_cycle,
            "yovel_year": self.yovel_year,
            "gleaning_pool_percentage": self.gleaning_pool_percentage,
            "biblical_compliance_required": self.biblical_compliance_required
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChainParameters':
        """Create from dictionary"""
        return cls(
            sabbath_cycle=data.get("sabbath_cycle", 7),
            yovel_year=data.get("yovel_year", 50),
            gleaning_pool_percentage=data.get("gleaning_pool_percentage", 0.02),
            biblical_compliance_required=data.get("biblical_compliance_required", True)
        )


@dataclass
class BlockMetadata:
    """Enhanced metadata for covenant blockchain"""
    chain_parameters: ChainParameters
    trust_score_changes: List[Dict[str, Any]] = field(default_factory=list)
    disputes: List[Dict[str, Any]] = field(default_factory=list)
    biblical_compliance_score: float = 1.0
    yovel_cycle: int = 7
    sabbath_compliant: bool = True
    gleaning_allocation: float = 0.02
    tribal_signatures: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "chain_parameters": self.chain_parameters.to_dict(),
            "trust_score_changes": self.trust_score_changes,
            "disputes": self.disputes,
            "biblical_compliance_score": self.biblical_compliance_score,
            "yovel_cycle": self.yovel_cycle,
            "sabbath_compliant": self.sabbath_compliant,
            "gleaning_allocation": self.gleaning_allocation,
            "tribal_signatures": self.tribal_signatures
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BlockMetadata':
        """Create from dictionary"""
        chain_params = ChainParameters.from_dict(data.get("chain_parameters", {}))
        return cls(
            chain_parameters=chain_params,
            trust_score_changes=data.get("trust_score_changes", []),
            disputes=data.get("disputes", []),
            biblical_compliance_score=data.get("biblical_compliance_score", 1.0),
            yovel_cycle=data.get("yovel_cycle", 7),
            sabbath_compliant=data.get("sabbath_compliant", True),
            gleaning_allocation=data.get("gleaning_allocation", 0.02),
            tribal_signatures=data.get("tribal_signatures", {})
        )


@dataclass
class CovenantTransaction:
    """Enhanced transaction structure for covenant blockchain"""
    tx_id: str
    type: str  # identity_registration, token_transfer, voice_scroll_proposal, etc.
    timestamp: int
    sender: str
    data: Dict[str, Any]
    witness_log: Optional[WitnessLog] = None
    signature: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            "tx_id": self.tx_id,
            "type": self.type,
            "timestamp": self.timestamp,
            "sender": self.sender,
            "data": self.data,
            "signature": self.signature
        }
        if self.witness_log:
            result["witness_log"] = self.witness_log.to_dict()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CovenantTransaction':
        """Create from dictionary"""
        witness_log = None
        if "witness_log" in data:
            witness_log = WitnessLog.from_dict(data["witness_log"])
        
        return cls(
            tx_id=data["tx_id"],
            type=data["type"],
            timestamp=data["timestamp"],
            sender=data["sender"],
            data=data["data"],
            witness_log=witness_log,
            signature=data.get("signature", "")
        )


@dataclass
class EnhancedBlock:
    """
    Enhanced Block model for the Onnyx blockchain with comprehensive header structure.
    
    This model implements the complete block structure as specified in the requirements:
    - Complete block header with all required fields
    - Support for covenant-specific transactions
    - Witness logs and multi-signature verification
    - Biblical compliance metadata
    - Chain parameters snapshot
    """
    
    # 1️⃣ Block Header - Core Fields
    block_hash: str
    previous_hash: str
    block_height: int
    timestamp: int
    merkle_root: str
    proposer_id: str
    signature: str
    witness_signatures: List[str] = field(default_factory=list)
    
    # 2️⃣ Block Body - Transactions
    transactions: List[CovenantTransaction] = field(default_factory=list)
    
    # 3️⃣ Block Metadata - Enhanced Fields
    metadata: BlockMetadata = field(default_factory=lambda: BlockMetadata(
        chain_parameters=ChainParameters()
    ))
    
    # Additional fields for compatibility
    nonce: int = 0
    difficulty: int = 1
    size: int = 0
    version: str = "1.0"
    
    def calculate_merkle_root(self) -> str:
        """Calculate the Merkle root of all transactions"""
        if not self.transactions:
            return hashlib.sha256(b"").hexdigest()
        
        # Get transaction hashes
        tx_hashes = []
        for tx in self.transactions:
            tx_string = json.dumps(tx.to_dict(), sort_keys=True)
            tx_hash = hashlib.sha256(tx_string.encode()).hexdigest()
            tx_hashes.append(tx_hash)
        
        # Build Merkle tree
        while len(tx_hashes) > 1:
            next_level = []
            for i in range(0, len(tx_hashes), 2):
                left = tx_hashes[i]
                right = tx_hashes[i + 1] if i + 1 < len(tx_hashes) else left
                combined = left + right
                next_level.append(hashlib.sha256(combined.encode()).hexdigest())
            tx_hashes = next_level
        
        return tx_hashes[0]
    
    def calculate_block_hash(self) -> str:
        """Calculate the block hash"""
        header_data = {
            "previous_hash": self.previous_hash,
            "block_height": self.block_height,
            "timestamp": self.timestamp,
            "merkle_root": self.merkle_root,
            "proposer_id": self.proposer_id,
            "nonce": self.nonce
        }
        header_string = json.dumps(header_data, sort_keys=True)
        return hashlib.sha256(header_string.encode()).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "block_hash": self.block_hash,
            "previous_hash": self.previous_hash,
            "block_height": self.block_height,
            "timestamp": self.timestamp,
            "merkle_root": self.merkle_root,
            "proposer_id": self.proposer_id,
            "signature": self.signature,
            "witness_signatures": self.witness_signatures,
            "transactions": [tx.to_dict() for tx in self.transactions],
            "metadata": self.metadata.to_dict(),
            "nonce": self.nonce,
            "difficulty": self.difficulty,
            "size": self.size,
            "version": self.version
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedBlock':
        """Create from dictionary"""
        transactions = [
            CovenantTransaction.from_dict(tx_data) 
            for tx_data in data.get("transactions", [])
        ]
        
        metadata = BlockMetadata.from_dict(data.get("metadata", {}))
        
        return cls(
            block_hash=data["block_hash"],
            previous_hash=data["previous_hash"],
            block_height=data["block_height"],
            timestamp=data["timestamp"],
            merkle_root=data["merkle_root"],
            proposer_id=data["proposer_id"],
            signature=data["signature"],
            witness_signatures=data.get("witness_signatures", []),
            transactions=transactions,
            metadata=metadata,
            nonce=data.get("nonce", 0),
            difficulty=data.get("difficulty", 1),
            size=data.get("size", 0),
            version=data.get("version", "1.0")
        )
