#!/usr/bin/env python3
"""
Fix existing table schemas to match production schema
"""

import sqlite3
import sys
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_connection():
    """Get database connection."""
    try:
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        if backend_dir.endswith('migrations'):
            backend_dir = os.path.dirname(backend_dir)
        
        db_path = os.path.join(backend_dir, 'data', 'onnyx.db')
        logger.info(f"Connecting to database: {db_path}")
        
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
        
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return None

def check_table_schema(conn, table_name):
    """Check current table schema."""
    try:
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        logger.info(f"Current schema for {table_name}:")
        for col in columns:
            logger.info(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
        
        return [col[1] for col in columns]  # Return column names
        
    except Exception as e:
        logger.error(f"Error checking schema for {table_name}: {e}")
        return []

def fix_jubilee_pools_table(conn):
    """Fix jubilee_pools table to match production schema."""
    try:
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='jubilee_pools'")
        if not cursor.fetchone():
            logger.info("jubilee_pools table doesn't exist, creating it...")
            cursor.execute("""
                CREATE TABLE jubilee_pools (
                    pool_id TEXT PRIMARY KEY,
                    pool_type TEXT NOT NULL,
                    total_amount REAL NOT NULL DEFAULT 0.0,
                    token_id TEXT NOT NULL DEFAULT 'ONX',
                    created_at INTEGER NOT NULL,
                    last_distribution INTEGER DEFAULT 0,
                    metadata TEXT NOT NULL DEFAULT '{}'
                )
            """)
            conn.commit()
            logger.info("✅ Created jubilee_pools table")
            return True
        
        # Check current columns
        current_columns = check_table_schema(conn, 'jubilee_pools')
        
        # Add missing columns
        missing_columns = []
        required_columns = {
            'token_id': 'TEXT NOT NULL DEFAULT "ONX"',
            'metadata': 'TEXT NOT NULL DEFAULT "{}"'
        }
        
        for col_name, col_def in required_columns.items():
            if col_name not in current_columns:
                missing_columns.append((col_name, col_def))
        
        if missing_columns:
            for col_name, col_def in missing_columns:
                try:
                    cursor.execute(f"ALTER TABLE jubilee_pools ADD COLUMN {col_name} {col_def}")
                    logger.info(f"✅ Added column {col_name} to jubilee_pools")
                except Exception as e:
                    logger.error(f"❌ Failed to add column {col_name}: {e}")
            
            conn.commit()
        else:
            logger.info("✅ jubilee_pools table schema is correct")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix jubilee_pools table: {e}")
        return False

def fix_dormant_accounts_table(conn):
    """Fix dormant_accounts table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dormant_accounts (
                identity_id TEXT PRIMARY KEY,
                last_activity INTEGER NOT NULL,
                dormant_since INTEGER NOT NULL,
                reclaimed_amount REAL DEFAULT 0.0,
                status TEXT DEFAULT 'DORMANT',
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        conn.commit()
        logger.info("✅ Fixed dormant_accounts table")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix dormant_accounts table: {e}")
        return False

def fix_deeds_ledger_table(conn):
    """Fix deeds_ledger table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS deeds_ledger (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                deed_type TEXT NOT NULL,
                deed_value REAL NOT NULL,
                description TEXT,
                timestamp INTEGER NOT NULL,
                block_height INTEGER,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        conn.commit()
        logger.info("✅ Fixed deeds_ledger table")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix deeds_ledger table: {e}")
        return False

def run_schema_fixes():
    """Run all schema fixes."""
    logger.info("🔧 Starting Table Schema Fixes")
    logger.info("=" * 50)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        fixes = [
            ("jubilee_pools", fix_jubilee_pools_table),
            ("dormant_accounts", fix_dormant_accounts_table),
            ("deeds_ledger", fix_deeds_ledger_table)
        ]
        
        success_count = 0
        
        for table_name, fix_function in fixes:
            logger.info(f"📋 Fixing {table_name} table...")
            if fix_function(conn):
                success_count += 1
        
        logger.info(f"\n📊 Schema Fix Results: {success_count}/{len(fixes)} tables fixed")
        
        return success_count == len(fixes)
        
    except Exception as e:
        logger.error(f"❌ Schema fixes failed: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = run_schema_fixes()
    sys.exit(0 if success else 1)
