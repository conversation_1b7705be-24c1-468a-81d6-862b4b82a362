#!/usr/bin/env python3
"""
Check if Genesis Admin has a Sela registered
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from shared.db.db import db

def check_genesis_sela():
    """Check if genesis admin has a Sela registered"""
    
    genesis_identity_id = "ONX4d0fb6bb0dda45a7"
    
    print("🔍 Checking Genesis Admin Sela Status")
    print("=" * 40)
    
    try:
        # Check identity
        identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (genesis_identity_id,))
        if identity:
            print(f"✅ Identity: {identity['name']} ({identity['email']})")
            print(f"   Role: {identity['role_class']}")
        else:
            print("❌ Genesis identity not found")
            return False
        
        # Check for existing Sela
        existing_sela = db.query_one("""
            SELECT sela_id, name, category, status, created_at 
            FROM selas 
            WHERE identity_id = ?
        """, (genesis_identity_id,))
        
        if existing_sela:
            print(f"✅ Existing Sela Found:")
            print(f"   Sela ID: {existing_sela['sela_id']}")
            print(f"   Name: {existing_sela['name']}")
            print(f"   Category: {existing_sela['category']}")
            print(f"   Status: {existing_sela['status']}")
            print(f"   Created: {existing_sela['created_at']}")
            return True
        else:
            print("❌ No Sela found for genesis admin")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Sela: {e}")
        return False

if __name__ == "__main__":
    check_genesis_sela()
