"""
Biblical Cycles Routes - Sabbath & Jubilee Cycle Visualization
Handles cycle tracking displays, countdowns, and covenant cycle information.

Routes:
- /cycles/ - Main cycles dashboard with all biblical cycles
- /cycles/sabbath - Weekly Sabbath tracking and countdown
- /cycles/jubilee - Jubilee cycle status and countdown
- /cycles/sabbatical - Sabbatical year (<PERSON>hmita) tracking
- /cycles/calendar - Biblical calendar with all observances
- /cycles/api/status - API endpoint for real-time cycle data
"""

from flask import Blueprint, render_template, request, jsonify, session
from shared.models.cycle_tracker import BiblicalCycleTracker, CycleType
from shared.db.db import db
import json
import logging
import time

logger = logging.getLogger(__name__)

cycles_bp = Blueprint('cycles', __name__, url_prefix='/cycles')

@cycles_bp.route('/')
def dashboard():
    """Main biblical cycles dashboard."""
    try:
        # Get user timezone if logged in
        user_timezone = "UTC"
        user_name = "Visitor"
        
        if 'identity_id' in session:
            user = db.query_one("""
                SELECT name, timezone FROM identities WHERE identity_id = ?
            """, (session['identity_id'],))
            
            if user:
                user_name = user['name']
                user_timezone = user.get('timezone', 'UTC')
        
        # Get cycle tracker
        tracker = BiblicalCycleTracker()
        
        # Get all cycle statuses
        cycle_statuses = tracker.get_all_cycle_statuses(user_timezone)
        
        # Get dynamic covenant message
        covenant_message = tracker.get_cycle_message(user_timezone)
        
        # Format time deltas for display
        for status in cycle_statuses.values():
            if status.time_until_next:
                status.formatted_time_until = tracker.format_time_until(status.time_until_next)
            else:
                status.formatted_time_until = "N/A"
        
        # Get recent cycle events from database
        recent_events = db.query("""
            SELECT 'sabbath' as event_type, 'Weekly Sabbath' as event_name, 
                   start_timestamp, end_timestamp
            FROM sabbath_enforcement 
            WHERE period_type = 'weekly' 
            ORDER BY start_timestamp DESC 
            LIMIT 5
        """)
        
        # Get upcoming biblical holidays
        upcoming_holidays = [
            {
                "name": "Passover (Pesach)",
                "date": "April 2025",
                "description": "Feast of Unleavened Bread",
                "reference": "Leviticus 23:5-8"
            },
            {
                "name": "Pentecost (Shavuot)", 
                "date": "June 2025",
                "description": "Feast of Weeks",
                "reference": "Leviticus 23:15-21"
            },
            {
                "name": "Trumpets (Rosh Hashanah)",
                "date": "September 2025", 
                "description": "Day of Trumpets",
                "reference": "Leviticus 23:23-25"
            }
        ]
        
        return render_template('cycles/dashboard.html',
                             user_name=user_name,
                             user_timezone=user_timezone,
                             cycle_statuses=cycle_statuses,
                             covenant_message=covenant_message,
                             recent_events=recent_events,
                             upcoming_holidays=upcoming_holidays)
        
    except Exception as e:
        logger.error(f"Error in cycles dashboard: {e}")
        return render_template('error.html', error="Failed to load cycles dashboard"), 500

@cycles_bp.route('/sabbath')
def sabbath_tracker():
    """Weekly Sabbath tracking page."""
    try:
        user_timezone = request.args.get('tz', 'UTC')
        
        if 'identity_id' in session:
            user = db.query_one("""
                SELECT timezone FROM identities WHERE identity_id = ?
            """, (session['identity_id'],))
            
            if user and user.get('timezone'):
                user_timezone = user['timezone']
        
        tracker = BiblicalCycleTracker()
        sabbath_status = tracker.get_current_sabbath_status(user_timezone)
        
        # Get Sabbath observance history
        sabbath_history = db.query("""
            SELECT start_timestamp, end_timestamp, observers_count
            FROM sabbath_periods 
            ORDER BY start_timestamp DESC 
            LIMIT 10
        """)
        
        # Get Sabbath compliance data if user is logged in
        compliance_data = None
        if 'identity_id' in session:
            compliance_data = db.query_one("""
                SELECT COUNT(*) as total_sabbaths,
                       SUM(CASE WHEN compliance_status = 'compliant' THEN 1 ELSE 0 END) as compliant_count
                FROM sabbath_enforcement 
                WHERE identity_id = ?
            """, (session['identity_id'],))
        
        return render_template('cycles/sabbath.html',
                             sabbath_status=sabbath_status,
                             user_timezone=user_timezone,
                             sabbath_history=sabbath_history,
                             compliance_data=compliance_data)
        
    except Exception as e:
        logger.error(f"Error in Sabbath tracker: {e}")
        return render_template('error.html', error="Failed to load Sabbath tracker"), 500

@cycles_bp.route('/jubilee')
def jubilee_tracker():
    """Jubilee cycle tracking page."""
    try:
        tracker = BiblicalCycleTracker()
        jubilee_status = tracker.get_jubilee_cycle_status()
        
        # Get Jubilee cycle history from database
        jubilee_cycles = db.query("""
            SELECT cycle_number, start_timestamp, end_timestamp, 
                   reset_completed, land_redistribution_completed, 
                   debt_forgiveness_completed
            FROM yovel_cycles 
            ORDER BY cycle_number DESC
        """)
        
        # Get land ownership data for Jubilee context
        land_stats = db.query_one("""
            SELECT COUNT(*) as total_properties,
                   COUNT(CASE WHEN pledged_as_collateral = 1 THEN 1 END) as pledged_properties,
                   COUNT(CASE WHEN can_be_sold = 0 THEN 1 END) as tribal_inheritance_properties
            FROM land_ownership
        """)
        
        # Get debt forgiveness statistics
        debt_stats = db.query_one("""
            SELECT COUNT(*) as total_debts,
                   COUNT(CASE WHEN status = 'FORGIVEN' THEN 1 END) as forgiven_debts,
                   SUM(CASE WHEN status = 'ACTIVE' THEN current_balance ELSE 0 END) as active_debt_total
            FROM debt_records
        """)
        
        return render_template('cycles/jubilee.html',
                             jubilee_status=jubilee_status,
                             jubilee_cycles=jubilee_cycles,
                             land_stats=land_stats,
                             debt_stats=debt_stats)
        
    except Exception as e:
        logger.error(f"Error in Jubilee tracker: {e}")
        return render_template('error.html', error="Failed to load Jubilee tracker"), 500

@cycles_bp.route('/sabbatical')
def sabbatical_tracker():
    """Sabbatical year (Shmita) tracking page."""
    try:
        tracker = BiblicalCycleTracker()
        sabbatical_status = tracker.get_sabbatical_year_status()
        
        # Get Sabbatical year history
        sabbatical_years = db.query("""
            SELECT year_number, start_timestamp, end_timestamp,
                   debt_forgiveness_completed, land_rest_enforced
            FROM sabbatical_years 
            ORDER BY year_number DESC
        """)
        
        # Get agricultural activity data (if any)
        agricultural_stats = db.query_one("""
            SELECT COUNT(*) as total_land_records,
                   COUNT(CASE WHEN land_type = 'TRIBAL_INHERITANCE' THEN 1 END) as tribal_lands
            FROM land_ownership
        """)
        
        # Get debt forgiveness data for Sabbatical context
        sabbatical_debt_stats = db.query_one("""
            SELECT COUNT(*) as total_forgiveness_events,
                   SUM(forgiven_amount) as total_forgiven_amount
            FROM sabbatical_debt_forgiveness
        """)
        
        return render_template('cycles/sabbatical.html',
                             sabbatical_status=sabbatical_status,
                             sabbatical_years=sabbatical_years,
                             agricultural_stats=agricultural_stats,
                             sabbatical_debt_stats=sabbatical_debt_stats)
        
    except Exception as e:
        logger.error(f"Error in Sabbatical tracker: {e}")
        return render_template('error.html', error="Failed to load Sabbatical tracker"), 500

@cycles_bp.route('/calendar')
def biblical_calendar():
    """Biblical calendar with all observances."""
    try:
        user_timezone = request.args.get('tz', 'UTC')
        
        if 'identity_id' in session:
            user = db.query_one("""
                SELECT timezone FROM identities WHERE identity_id = ?
            """, (session['identity_id'],))
            
            if user and user.get('timezone'):
                user_timezone = user['timezone']
        
        tracker = BiblicalCycleTracker()
        
        # Get all cycle statuses
        all_statuses = tracker.get_all_cycle_statuses(user_timezone)
        
        # Get biblical holidays and observances
        biblical_observances = [
            {
                "name": "Weekly Sabbath",
                "frequency": "Weekly",
                "next_occurrence": all_statuses["weekly_sabbath"].next_start,
                "description": "Rest and worship from Friday evening to Saturday evening",
                "reference": "Exodus 20:8-11",
                "type": "sabbath"
            },
            {
                "name": "New Moon",
                "frequency": "Monthly", 
                "next_occurrence": all_statuses["new_moon"].next_start,
                "description": "Monthly celebration of the new moon",
                "reference": "Numbers 28:11",
                "type": "new_moon"
            },
            {
                "name": "Passover",
                "frequency": "Annual",
                "next_occurrence": None,  # Would calculate based on Hebrew calendar
                "description": "Feast of Unleavened Bread",
                "reference": "Leviticus 23:5-8",
                "type": "feast"
            },
            {
                "name": "Sabbatical Year",
                "frequency": "Every 7 years",
                "next_occurrence": all_statuses["sabbatical_year"].next_start,
                "description": "Year of land rest and debt forgiveness",
                "reference": "Deuteronomy 15:1-2",
                "type": "sabbatical"
            },
            {
                "name": "Jubilee Year",
                "frequency": "Every 50 years",
                "next_occurrence": all_statuses["jubilee_cycle"].next_start,
                "description": "Year of liberty and land restoration",
                "reference": "Leviticus 25:8-13",
                "type": "jubilee"
            }
        ]
        
        return render_template('cycles/calendar.html',
                             user_timezone=user_timezone,
                             all_statuses=all_statuses,
                             biblical_observances=biblical_observances)
        
    except Exception as e:
        logger.error(f"Error in biblical calendar: {e}")
        return render_template('error.html', error="Failed to load biblical calendar"), 500

@cycles_bp.route('/api/status')
def api_cycle_status():
    """API endpoint for real-time cycle status data."""
    try:
        user_timezone = request.args.get('tz', 'UTC')
        
        tracker = BiblicalCycleTracker()
        statuses = tracker.get_all_cycle_statuses(user_timezone)
        covenant_message = tracker.get_cycle_message(user_timezone)
        
        # Convert to JSON-serializable format
        result = {
            "covenant_message": covenant_message,
            "timestamp": int(time.time()),
            "cycles": {}
        }
        
        for key, status in statuses.items():
            result["cycles"][key] = {
                "cycle_type": status.cycle_type.value,
                "is_active": status.is_active,
                "current_period": status.current_period,
                "next_start": status.next_start.isoformat() if status.next_start else None,
                "next_end": status.next_end.isoformat() if status.next_end else None,
                "time_until_next_seconds": int(status.time_until_next.total_seconds()) if status.time_until_next else None,
                "progress_percentage": status.progress_percentage,
                "description": status.description,
                "biblical_reference": status.biblical_reference,
                "formatted_time_until": tracker.format_time_until(status.time_until_next) if status.time_until_next else "N/A"
            }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in cycle status API: {e}")
        return jsonify({"error": str(e)}), 500

@cycles_bp.route('/api/countdown/<cycle_type>')
def api_countdown(cycle_type):
    """API endpoint for specific cycle countdown."""
    try:
        user_timezone = request.args.get('tz', 'UTC')
        
        tracker = BiblicalCycleTracker()
        
        if cycle_type == 'sabbath':
            status = tracker.get_current_sabbath_status(user_timezone)
        elif cycle_type == 'jubilee':
            status = tracker.get_jubilee_cycle_status()
        elif cycle_type == 'sabbatical':
            status = tracker.get_sabbatical_year_status()
        elif cycle_type == 'new_moon':
            status = tracker.get_new_moon_status(user_timezone)
        else:
            return jsonify({"error": "Invalid cycle type"}), 400
        
        countdown_data = {
            "cycle_type": status.cycle_type.value,
            "is_active": status.is_active,
            "current_period": status.current_period,
            "time_until_next_seconds": int(status.time_until_next.total_seconds()) if status.time_until_next else None,
            "formatted_time_until": tracker.format_time_until(status.time_until_next) if status.time_until_next else "N/A",
            "progress_percentage": status.progress_percentage,
            "description": status.description,
            "biblical_reference": status.biblical_reference
        }
        
        return jsonify(countdown_data)
        
    except Exception as e:
        logger.error(f"Error in countdown API: {e}")
        return jsonify({"error": str(e)}), 500
