#!/usr/bin/env python3
"""
Database migration to add mining control columns to selas table
"""

import os
import sys
import sqlite3
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.db.db import db

logger = logging.getLogger(__name__)

def add_mining_columns():
    """Add mining control columns to selas table"""
    try:
        # Check if columns already exist
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(selas)")
        columns = [column[1] for column in cursor.fetchall()]

        migrations_needed = []

        # Check for missing columns
        if 'mining_active' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN mining_active INTEGER DEFAULT 0")

        if 'mining_intensity' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN mining_intensity TEXT DEFAULT 'medium'")

        if 'last_updated' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN last_updated TEXT")

        if 'description' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN description TEXT")

        if 'address' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN address TEXT")

        if 'phone' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN phone TEXT")

        if 'website' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN website TEXT")

        if 'services' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN services TEXT")

        if 'trust_score' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN trust_score REAL DEFAULT 0.0")

        if 'mining_tier' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN mining_tier TEXT DEFAULT 'basic'")

        if 'mining_power' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN mining_power INTEGER DEFAULT 1")

        if 'mining_rewards_earned' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN mining_rewards_earned REAL DEFAULT 0.0")

        if 'blocks_mined' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN blocks_mined INTEGER DEFAULT 0")

        if 'onx_balance' not in columns:
            migrations_needed.append("ALTER TABLE selas ADD COLUMN onx_balance REAL DEFAULT 0.0")

        # Execute migrations
        if migrations_needed:
            print(f"Applying {len(migrations_needed)} migrations to selas table...")

            for migration in migrations_needed:
                print(f"Executing: {migration}")
                db.execute(migration)

            print("✅ All migrations applied successfully!")

            # Update existing records with default values
            db.execute("""
                UPDATE selas
                SET last_updated = datetime('now')
                WHERE last_updated IS NULL
            """)

            print("✅ Updated existing records with default values")

        else:
            print("✅ No migrations needed - all columns already exist")

        # Verify the schema
        cursor.execute("PRAGMA table_info(selas)")
        columns = cursor.fetchall()

        print("\n📋 Current selas table schema:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")

        return True

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"❌ Migration failed: {e}")
        return False

def main():
    """Run the migration"""
    print("🚀 Starting mining columns migration...")

    if add_mining_columns():
        print("✅ Migration completed successfully!")
        return 0
    else:
        print("❌ Migration failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
