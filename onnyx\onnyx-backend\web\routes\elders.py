"""
Elder Validator Dashboard Routes
Handles elder validation interfaces, tribal rotation displays, and governance oversight.

Routes:
- /elders/ - Main elder dashboard
- /elders/validation - Block validation interface
- /elders/rotation - Tribal rotation schedule
- /elders/governance - Governance oversight
- /elders/signatures - Signature tracking
- /elders/api/status - Real-time elder status API
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from shared.db.db import db
from shared.models.wisdom_engine import WisdomEngine
import json
import logging
import time
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

elders_bp = Blueprint('elders', __name__, url_prefix='/elders')

@elders_bp.route('/')
def dashboard():
    """Main elder validator dashboard."""
    try:
        # Check authentication and elder privileges
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        identity_id = session['identity_id']
        
        # Get user info and check elder status
        user = db.query_one("""
            SELECT name, verification_level, etzem_score, nation_code, nation_name, role_class
            FROM identities WHERE identity_id = ?
        """, (identity_id,))
        
        if not user:
            return redirect(url_for('auth.login'))
        
        # Check if user has elder privileges (Tier 3+ or Tribal_Elder role)
        is_elder = (user['verification_level'] >= 3 or 
                   user.get('role_class') == 'Tribal_Elder')
        
        if not is_elder:
            return render_template('elders/access_denied.html', 
                                 required_role="Tribal Elder",
                                 current_tier=user['verification_level'])
        
        # Get tribal elder information
        tribal_elders = db.query("""
            SELECT identity_id, name, nation_code, nation_name, 
                   verification_level, etzem_score, created_at,
                   last_active_timestamp
            FROM identities 
            WHERE role_class = 'Tribal_Elder' OR verification_level >= 3
            ORDER BY nation_code, etzem_score DESC
        """)
        
        # Get recent block validations
        recent_validations = db.query("""
            SELECT b.block_hash, b.block_height, b.proposer_id, b.timestamp,
                   b.witness_signatures, i.name as proposer_name
            FROM blocks b
            LEFT JOIN identities i ON b.proposer_id = i.identity_id
            ORDER BY b.block_height DESC
            LIMIT 10
        """)
        
        # Get governance activity
        governance_activity = db.query("""
            SELECT vs.scroll_id, vs.title, vs.status, vs.created_at,
                   i.name as creator_name, i.nation_code
            FROM voice_scrolls vs
            LEFT JOIN identities i ON vs.creator_id = i.identity_id
            WHERE vs.status IN ('PROPOSED', 'VOTING', 'PASSED')
            ORDER BY vs.created_at DESC
            LIMIT 5
        """)
        
        # Calculate elder statistics
        elder_stats = {
            'total_elders': len(tribal_elders),
            'active_elders': len([e for e in tribal_elders if e.get('last_active_timestamp', 0) > (time.time() - 86400)]),
            'recent_validations': len(recent_validations),
            'pending_governance': len([g for g in governance_activity if g['status'] in ['PROPOSED', 'VOTING']])
        }
        
        # Get tribal rotation status
        rotation_status = _get_tribal_rotation_status()
        
        # Get validation consensus health
        consensus_health = _calculate_consensus_health()
        
        return render_template('elders/dashboard.html',
                             user=user,
                             tribal_elders=tribal_elders,
                             recent_validations=recent_validations,
                             governance_activity=governance_activity,
                             elder_stats=elder_stats,
                             rotation_status=rotation_status,
                             consensus_health=consensus_health)
        
    except Exception as e:
        logger.error(f"Error in elder dashboard: {e}")
        return render_template('error.html', error="Failed to load elder dashboard"), 500

@elders_bp.route('/validation')
def validation_interface():
    """Block validation interface for elders."""
    try:
        if not _check_elder_access():
            return redirect(url_for('auth.login'))
        
        # Get pending blocks for validation
        pending_blocks = db.query("""
            SELECT b.block_hash, b.block_height, b.proposer_id, b.timestamp,
                   b.transactions, b.metadata, i.name as proposer_name
            FROM blocks b
            LEFT JOIN identities i ON b.proposer_id = i.identity_id
            WHERE b.witness_signatures = '[]' OR json_array_length(b.witness_signatures) < 7
            ORDER BY b.timestamp DESC
            LIMIT 20
        """)
        
        # Get validation history for current elder
        validation_history = db.query("""
            SELECT block_hash, validation_decision, timestamp, biblical_justification
            FROM elder_validations 
            WHERE elder_id = ?
            ORDER BY timestamp DESC
            LIMIT 50
        """, (session['identity_id'],))
        
        # Get biblical compliance metrics
        compliance_metrics = _get_compliance_metrics()
        
        return render_template('elders/validation.html',
                             pending_blocks=pending_blocks,
                             validation_history=validation_history,
                             compliance_metrics=compliance_metrics)
        
    except Exception as e:
        logger.error(f"Error in validation interface: {e}")
        return render_template('error.html', error="Failed to load validation interface"), 500

@elders_bp.route('/rotation')
def rotation_schedule():
    """Tribal rotation schedule and management."""
    try:
        if not _check_elder_access():
            return redirect(url_for('auth.login'))
        
        # Get current rotation status
        rotation_data = _get_detailed_rotation_status()
        
        # Get rotation history
        rotation_history = db.query("""
            SELECT rr.*, i.name as validator_name, i.nation_code
            FROM rotation_registry rr
            LEFT JOIN identities i ON rr.identity_id = i.identity_id
            ORDER BY rr.updated_at DESC
            LIMIT 100
        """)
        
        # Get tribal participation statistics
        tribal_stats = _get_tribal_participation_stats()
        
        return render_template('elders/rotation.html',
                             rotation_data=rotation_data,
                             rotation_history=rotation_history,
                             tribal_stats=tribal_stats)
        
    except Exception as e:
        logger.error(f"Error in rotation schedule: {e}")
        return render_template('error.html', error="Failed to load rotation schedule"), 500

@elders_bp.route('/governance')
def governance_oversight():
    """Governance oversight and Voice Scroll management."""
    try:
        if not _check_elder_access():
            return redirect(url_for('auth.login'))
        
        # Get all Voice Scrolls with voting status
        voice_scrolls = db.query("""
            SELECT vs.*, i.name as creator_name, i.nation_code,
                   COUNT(v.vote_id) as total_votes,
                   SUM(CASE WHEN v.vote = 'yes' THEN 1 ELSE 0 END) as yes_votes,
                   SUM(CASE WHEN v.vote = 'no' THEN 1 ELSE 0 END) as no_votes
            FROM voice_scrolls vs
            LEFT JOIN identities i ON vs.creator_id = i.identity_id
            LEFT JOIN votes v ON vs.scroll_id = v.scroll_id
            GROUP BY vs.scroll_id
            ORDER BY vs.created_at DESC
        """)
        
        # Get elder voting participation
        elder_participation = db.query("""
            SELECT i.identity_id, i.name, i.nation_code,
                   COUNT(v.vote_id) as votes_cast,
                   COUNT(DISTINCT v.scroll_id) as scrolls_participated
            FROM identities i
            LEFT JOIN votes v ON i.identity_id = v.identity_id
            WHERE i.role_class = 'Tribal_Elder' OR i.verification_level >= 3
            GROUP BY i.identity_id
            ORDER BY votes_cast DESC
        """)
        
        # Get governance statistics
        governance_stats = {
            'total_scrolls': len(voice_scrolls),
            'active_scrolls': len([vs for vs in voice_scrolls if vs['status'] == 'VOTING']),
            'passed_scrolls': len([vs for vs in voice_scrolls if vs['status'] == 'PASSED']),
            'average_participation': sum([ep['votes_cast'] for ep in elder_participation]) / max(len(elder_participation), 1)
        }
        
        return render_template('elders/governance.html',
                             voice_scrolls=voice_scrolls,
                             elder_participation=elder_participation,
                             governance_stats=governance_stats)
        
    except Exception as e:
        logger.error(f"Error in governance oversight: {e}")
        return render_template('error.html', error="Failed to load governance oversight"), 500

@elders_bp.route('/signatures')
def signature_tracking():
    """Signature tracking and validation history."""
    try:
        if not _check_elder_access():
            return redirect(url_for('auth.login'))
        
        # Get recent block signatures
        block_signatures = db.query("""
            SELECT b.block_hash, b.block_height, b.timestamp,
                   b.witness_signatures, b.proposer_id,
                   i.name as proposer_name
            FROM blocks b
            LEFT JOIN identities i ON b.proposer_id = i.identity_id
            WHERE b.witness_signatures != '[]'
            ORDER BY b.block_height DESC
            LIMIT 50
        """)
        
        # Parse witness signatures and get signer details
        signature_details = []
        for block in block_signatures:
            try:
                signatures = json.loads(block['witness_signatures'] or '[]')
                for sig in signatures:
                    signer = db.query_one("""
                        SELECT name, nation_code FROM identities WHERE identity_id = ?
                    """, (sig.get('signer_id', ''),))
                    
                    signature_details.append({
                        'block_hash': block['block_hash'],
                        'block_height': block['block_height'],
                        'timestamp': block['timestamp'],
                        'signer_id': sig.get('signer_id'),
                        'signer_name': signer['name'] if signer else 'Unknown',
                        'signer_tribe': signer['nation_code'] if signer else 'Unknown',
                        'signature': sig.get('signature', '')[:20] + '...',
                        'signature_timestamp': sig.get('timestamp')
                    })
            except (json.JSONDecodeError, KeyError):
                continue
        
        # Get signature statistics by tribe
        tribal_signature_stats = {}
        for sig in signature_details:
            tribe = sig['signer_tribe']
            if tribe not in tribal_signature_stats:
                tribal_signature_stats[tribe] = {'count': 0, 'recent_activity': 0}
            tribal_signature_stats[tribe]['count'] += 1
            if sig['timestamp'] > (time.time() - 86400):  # Last 24 hours
                tribal_signature_stats[tribe]['recent_activity'] += 1
        
        return render_template('elders/signatures.html',
                             signature_details=signature_details,
                             tribal_signature_stats=tribal_signature_stats)
        
    except Exception as e:
        logger.error(f"Error in signature tracking: {e}")
        return render_template('error.html', error="Failed to load signature tracking"), 500

@elders_bp.route('/api/status')
def api_elder_status():
    """Real-time elder status API."""
    try:
        if not _check_elder_access():
            return jsonify({'error': 'Access denied'}), 403
        
        # Get current elder activity
        elder_activity = db.query("""
            SELECT identity_id, name, nation_code, last_active_timestamp,
                   etzem_score, verification_level
            FROM identities 
            WHERE role_class = 'Tribal_Elder' OR verification_level >= 3
        """)
        
        # Get recent validation activity
        recent_validations = db.query("""
            SELECT COUNT(*) as count, elder_id
            FROM elder_validations 
            WHERE timestamp > ?
            GROUP BY elder_id
        """, (time.time() - 3600,))  # Last hour
        
        validation_counts = {v['elder_id']: v['count'] for v in recent_validations}
        
        # Format response
        elders_status = []
        for elder in elder_activity:
            status = "active" if elder.get('last_active_timestamp', 0) > (time.time() - 3600) else "inactive"
            
            elders_status.append({
                'identity_id': elder['identity_id'],
                'name': elder['name'],
                'tribe_code': elder['nation_code'],
                'status': status,
                'etzem_score': elder['etzem_score'],
                'recent_validations': validation_counts.get(elder['identity_id'], 0),
                'last_active': elder.get('last_active_timestamp', 0)
            })
        
        return jsonify({
            'elders': elders_status,
            'consensus_health': _calculate_consensus_health(),
            'rotation_status': _get_tribal_rotation_status(),
            'timestamp': int(time.time())
        })
        
    except Exception as e:
        logger.error(f"Error in elder status API: {e}")
        return jsonify({'error': str(e)}), 500

def _check_elder_access():
    """Check if current user has elder access."""
    if 'identity_id' not in session:
        return False
    
    user = db.query_one("""
        SELECT verification_level, role_class FROM identities WHERE identity_id = ?
    """, (session['identity_id'],))
    
    if not user:
        return False
    
    return (user['verification_level'] >= 3 or 
            user.get('role_class') == 'Tribal_Elder')

def _get_tribal_rotation_status():
    """Get current tribal rotation status."""
    try:
        current_rotation = db.query_one("""
            SELECT * FROM rotation_registry 
            ORDER BY updated_at DESC 
            LIMIT 1
        """)
        
        if current_rotation:
            validator = db.query_one("""
                SELECT name, nation_code FROM identities WHERE identity_id = ?
            """, (current_rotation['identity_id'],))
            
            return {
                'current_validator': validator['name'] if validator else 'Unknown',
                'current_tribe': validator['nation_code'] if validator else 'Unknown',
                'rotation_time': current_rotation['next_rotation_time'],
                'eligible_count': len(json.loads(current_rotation.get('eligible_validators', '[]')))
            }
    except Exception as e:
        logger.error(f"Error getting rotation status: {e}")
    
    return {
        'current_validator': 'Unknown',
        'current_tribe': 'Unknown',
        'rotation_time': 0,
        'eligible_count': 0
    }

def _calculate_consensus_health():
    """Calculate consensus health metrics."""
    try:
        # Get recent blocks and their validation status
        recent_blocks = db.query("""
            SELECT witness_signatures FROM blocks 
            WHERE timestamp > ?
            ORDER BY block_height DESC
            LIMIT 50
        """, (time.time() - 86400,))  # Last 24 hours
        
        total_blocks = len(recent_blocks)
        if total_blocks == 0:
            return {'health_score': 0, 'participation_rate': 0, 'consensus_rate': 0}
        
        validated_blocks = 0
        total_signatures = 0
        
        for block in recent_blocks:
            try:
                signatures = json.loads(block['witness_signatures'] or '[]')
                if len(signatures) >= 7:  # Minimum required signatures
                    validated_blocks += 1
                total_signatures += len(signatures)
            except json.JSONDecodeError:
                continue
        
        consensus_rate = (validated_blocks / total_blocks) * 100
        avg_signatures = total_signatures / total_blocks if total_blocks > 0 else 0
        participation_rate = (avg_signatures / 12) * 100  # 12 tribes
        
        # Calculate overall health score
        health_score = (consensus_rate * 0.6) + (participation_rate * 0.4)
        
        return {
            'health_score': round(health_score, 1),
            'participation_rate': round(participation_rate, 1),
            'consensus_rate': round(consensus_rate, 1),
            'total_blocks': total_blocks,
            'validated_blocks': validated_blocks
        }
        
    except Exception as e:
        logger.error(f"Error calculating consensus health: {e}")
        return {'health_score': 0, 'participation_rate': 0, 'consensus_rate': 0}

def _get_detailed_rotation_status():
    """Get detailed rotation status information."""
    # Implementation would get comprehensive rotation data
    return _get_tribal_rotation_status()

def _get_tribal_participation_stats():
    """Get tribal participation statistics."""
    try:
        stats = db.query("""
            SELECT i.nation_code, COUNT(*) as elder_count,
                   AVG(i.etzem_score) as avg_etzem,
                   COUNT(CASE WHEN i.last_active_timestamp > ? THEN 1 END) as active_count
            FROM identities i
            WHERE i.role_class = 'Tribal_Elder' OR i.verification_level >= 3
            GROUP BY i.nation_code
            ORDER BY elder_count DESC
        """, (time.time() - 86400,))
        
        return list(stats)
        
    except Exception as e:
        logger.error(f"Error getting tribal participation stats: {e}")
        return []

def _get_compliance_metrics():
    """Get biblical compliance metrics."""
    try:
        # Get recent compliance scores
        compliance_data = db.query("""
            SELECT AVG(biblical_compliance_score) as avg_score,
                   COUNT(*) as total_validations
            FROM elder_validations 
            WHERE timestamp > ?
        """, (time.time() - 86400,))
        
        return {
            'average_compliance': compliance_data[0]['avg_score'] if compliance_data else 0,
            'total_validations': compliance_data[0]['total_validations'] if compliance_data else 0
        }
        
    except Exception as e:
        logger.error(f"Error getting compliance metrics: {e}")
        return {'average_compliance': 0, 'total_validations': 0}
