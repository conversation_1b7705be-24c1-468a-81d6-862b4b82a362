{% extends "base.html" %}

{% block title %}Biblical Mining Reward Calculator - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .calculator-card {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.3);
    }
    
    .parameter-card {
        transition: all 0.3s ease;
    }
    
    .parameter-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 247, 0.15);
    }
    
    .slider-container {
        position: relative;
        margin: 1rem 0;
    }
    
    .slider {
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: linear-gradient(90deg, #2a2a2a 0%, #00fff7 100%);
        outline: none;
        -webkit-appearance: none;
    }
    
    .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #00fff7;
        cursor: pointer;
        box-shadow: 0 0 10px rgba(0, 255, 247, 0.5);
    }
    
    .slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #00fff7;
        cursor: pointer;
        border: none;
        box-shadow: 0 0 10px rgba(0, 255, 247, 0.5);
    }
    
    .reward-display {
        background: linear-gradient(135deg, rgba(154, 0, 255, 0.2) 0%, rgba(0, 255, 247, 0.2) 100%);
        border: 2px solid rgba(0, 255, 247, 0.5);
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
    }
    
    .sabbath-warning {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
        border: 1px solid rgba(255, 215, 0, 0.3);
    }
    
    .breakdown-item {
        border-left: 3px solid;
        padding-left: 1rem;
        margin: 0.5rem 0;
    }
    
    .breakdown-base { border-left-color: #00fff7; }
    .breakdown-deed { border-left-color: #9a00ff; }
    .breakdown-concentration { border-left-color: #ff6b35; }
    .breakdown-gleaning { border-left-color: #ffd700; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                    Biblical Mining Reward Calculator
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Calculate your mining rewards based on biblical economic principles
            </p>
            
            {% if params.is_sabbath %}
            <div class="sabbath-warning glass-card p-4 mb-6 max-w-2xl mx-auto">
                <div class="flex items-center justify-center space-x-3">
                    <span class="text-2xl">🕊️</span>
                    <p class="text-yellow-300 font-semibold">Sabbath Period Active - Mining Rewards Paused</p>
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Calculator Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input Parameters -->
            <div class="calculator-card glass-card p-6">
                <h2 class="text-2xl font-semibold mb-6">Adjust Parameters</h2>
                
                <!-- Base Reward -->
                <div class="parameter-card glass-card p-4 mb-4">
                    <label class="block text-sm font-medium mb-2">Base Mining Reward</label>
                    <div class="slider-container">
                        <input type="range" 
                               id="baseReward" 
                               class="slider" 
                               min="{{ params.min_reward }}" 
                               max="{{ params.max_reward }}" 
                               value="10" 
                               step="0.1">
                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                            <span>{{ params.min_reward }} ONX</span>
                            <span id="baseRewardValue">10.0 ONX</span>
                            <span>{{ params.max_reward }} ONX</span>
                        </div>
                    </div>
                </div>
                
                <!-- Deed Score -->
                <div class="parameter-card glass-card p-4 mb-4">
                    <label class="block text-sm font-medium mb-2">Deed Score (Righteousness Level)</label>
                    <div class="slider-container">
                        <input type="range" 
                               id="deedScore" 
                               class="slider" 
                               min="0" 
                               max="10" 
                               value="2" 
                               step="0.1">
                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                            <span>0 (No deeds)</span>
                            <span id="deedScoreValue">2.0</span>
                            <span>10 (Highly righteous)</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        Deed score increases through community service, firstfruits offerings, and righteous acts
                    </p>
                </div>
                
                <!-- Total Balance -->
                <div class="parameter-card glass-card p-4 mb-4">
                    <label class="block text-sm font-medium mb-2">Total Token Balance</label>
                    <div class="slider-container">
                        <input type="range" 
                               id="totalBalance" 
                               class="slider" 
                               min="0" 
                               max="{{ params.concentration_threshold * 2 }}" 
                               value="50000" 
                               step="1000">
                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                            <span>0 ONX</span>
                            <span id="totalBalanceValue">50,000 ONX</span>
                            <span>{{ "{:,}".format(params.concentration_threshold * 2) }} ONX</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        Concentration threshold: {{ "{:,}".format(params.concentration_threshold) }} ONX
                    </p>
                </div>
                
                <!-- Sabbath Observer -->
                <div class="parameter-card glass-card p-4 mb-4">
                    <label class="flex items-center space-x-3">
                        <input type="checkbox" id="sabbathObserver" class="form-checkbox h-5 w-5 text-cyber-cyan">
                        <span class="text-sm font-medium">Sabbath Observer</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-2">
                        Sabbath observers receive bonus rewards for faithful rest observance
                    </p>
                </div>
                
                <!-- Reset Button -->
                <button id="resetButton" 
                        class="w-full py-2 border border-gray-600 text-gray-300 rounded hover:bg-gray-700 transition-colors">
                    Reset to Defaults
                </button>
            </div>
            
            <!-- Results Display -->
            <div class="space-y-6">
                <!-- Main Reward Display -->
                <div class="reward-display">
                    <h3 class="text-lg font-semibold mb-2">Calculated Mining Reward</h3>
                    <div class="text-5xl font-bold text-cyber-cyan mb-2">
                        <span id="finalReward">10.0</span> <span class="text-2xl">ONX</span>
                    </div>
                    <p class="text-sm text-gray-400">Per block mined</p>
                    
                    <!-- Gleaning Pool Contribution -->
                    <div class="mt-4 pt-4 border-t border-gray-600">
                        <p class="text-sm text-gray-400">Gleaning Pool Contribution</p>
                        <p class="text-xl font-bold text-yellow-400">
                            <span id="gleaningContribution">0.2</span> ONX
                        </p>
                        <p class="text-xs text-gray-500">{{ (params.gleaning_percentage * 100)|round(1) }}% automatically allocated</p>
                    </div>
                </div>
                
                <!-- Reward Breakdown -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">Reward Breakdown</h3>
                    
                    <div id="rewardBreakdown" class="space-y-3">
                        <!-- Base Reward -->
                        <div class="breakdown-item breakdown-base">
                            <div class="flex justify-between">
                                <span class="text-sm">Base Reward</span>
                                <span class="font-semibold text-cyber-cyan" id="breakdownBase">10.0 ONX</span>
                            </div>
                        </div>
                        
                        <!-- Deed Bonus -->
                        <div class="breakdown-item breakdown-deed">
                            <div class="flex justify-between">
                                <span class="text-sm">Deed Score Bonus</span>
                                <span class="font-semibold text-cyber-purple" id="breakdownDeed">+0.0 ONX</span>
                            </div>
                        </div>
                        
                        <!-- Concentration Penalty -->
                        <div class="breakdown-item breakdown-concentration">
                            <div class="flex justify-between">
                                <span class="text-sm">Concentration Penalty</span>
                                <span class="font-semibold text-orange-400" id="breakdownConcentration">-0.0 ONX</span>
                            </div>
                        </div>
                        
                        <!-- Sabbath Bonus -->
                        <div class="breakdown-item breakdown-gleaning">
                            <div class="flex justify-between">
                                <span class="text-sm">Sabbath Observer Bonus</span>
                                <span class="font-semibold text-yellow-400" id="breakdownSabbath">+0.0 ONX</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Parameters -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">System Parameters</h3>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-gray-400">Min Reward</p>
                            <p class="font-semibold">{{ params.min_reward }} ONX</p>
                        </div>
                        <div>
                            <p class="text-gray-400">Max Reward</p>
                            <p class="font-semibold">{{ params.max_reward }} ONX</p>
                        </div>
                        <div>
                            <p class="text-gray-400">Deed Multiplier</p>
                            <p class="font-semibold">{{ (params.deed_multiplier * 100)|round(1) }}%</p>
                        </div>
                        <div>
                            <p class="text-gray-400">Gleaning Pool</p>
                            <p class="font-semibold">{{ (params.gleaning_percentage * 100)|round(1) }}%</p>
                        </div>
                        <div>
                            <p class="text-gray-400">Concentration Threshold</p>
                            <p class="font-semibold">{{ "{:,}".format(params.concentration_threshold) }} ONX</p>
                        </div>
                        <div>
                            <p class="text-gray-400">Concentration Penalty</p>
                            <p class="font-semibold">{{ (params.concentration_penalty * 100)|round(1) }}%</p>
                        </div>
                    </div>
                </div>
                
                <!-- Educational Information -->
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">How Biblical Mining Works</h3>
                    
                    <div class="space-y-3 text-sm">
                        <div>
                            <h4 class="font-semibold text-cyber-cyan">Base Reward</h4>
                            <p class="text-gray-300">Standard mining reward based on network difficulty and block value.</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-cyber-purple">Deed Score Bonus</h4>
                            <p class="text-gray-300">Up to {{ (params.deed_multiplier * 100)|round(1) }}% bonus for righteous acts like community service and firstfruits offerings.</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-orange-400">Concentration Penalty</h4>
                            <p class="text-gray-300">Reduced rewards for those holding more than {{ "{:,}".format(params.concentration_threshold) }} ONX to prevent wealth concentration.</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-yellow-400">Sabbath Observance</h4>
                            <p class="text-gray-300">Faithful Sabbath observers receive bonus rewards and mining is paused during Sabbath periods.</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-cyber-cyan">Gleaning Pool</h4>
                            <p class="text-gray-300">{{ (params.gleaning_percentage * 100)|round(1) }}% of all mining rewards automatically go to support community members in need.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // System parameters from backend
    const params = {
        minReward: {{ params.min_reward }},
        maxReward: {{ params.max_reward }},
        deedMultiplier: {{ params.deed_multiplier }},
        gleaningPercentage: {{ params.gleaning_percentage }},
        concentrationThreshold: {{ params.concentration_threshold }},
        concentrationPenalty: {{ params.concentration_penalty }},
        isSabbath: {{ params.is_sabbath|tojson }}
    };
    
    // Get DOM elements
    const baseRewardSlider = document.getElementById('baseReward');
    const deedScoreSlider = document.getElementById('deedScore');
    const totalBalanceSlider = document.getElementById('totalBalance');
    const sabbathObserverCheckbox = document.getElementById('sabbathObserver');
    const resetButton = document.getElementById('resetButton');
    
    const baseRewardValue = document.getElementById('baseRewardValue');
    const deedScoreValue = document.getElementById('deedScoreValue');
    const totalBalanceValue = document.getElementById('totalBalanceValue');
    
    const finalReward = document.getElementById('finalReward');
    const gleaningContribution = document.getElementById('gleaningContribution');
    
    const breakdownBase = document.getElementById('breakdownBase');
    const breakdownDeed = document.getElementById('breakdownDeed');
    const breakdownConcentration = document.getElementById('breakdownConcentration');
    const breakdownSabbath = document.getElementById('breakdownSabbath');
    
    // Update display values
    function updateDisplayValues() {
        baseRewardValue.textContent = parseFloat(baseRewardSlider.value).toFixed(1) + ' ONX';
        deedScoreValue.textContent = parseFloat(deedScoreSlider.value).toFixed(1);
        totalBalanceValue.textContent = parseInt(totalBalanceSlider.value).toLocaleString() + ' ONX';
    }
    
    // Calculate rewards
    function calculateRewards() {
        if (params.isSabbath) {
            // No mining during Sabbath
            finalReward.textContent = '0.0';
            gleaningContribution.textContent = '0.0';
            breakdownBase.textContent = '0.0 ONX';
            breakdownDeed.textContent = '+0.0 ONX';
            breakdownConcentration.textContent = '-0.0 ONX';
            breakdownSabbath.textContent = '+0.0 ONX';
            return;
        }
        
        const baseReward = parseFloat(baseRewardSlider.value);
        const deedScore = parseFloat(deedScoreSlider.value);
        const totalBalance = parseInt(totalBalanceSlider.value);
        const isSabbathObserver = sabbathObserverCheckbox.checked;
        
        // Calculate deed bonus
        const deedBonus = Math.min(deedScore * params.deedMultiplier, params.deedMultiplier) * baseReward;
        
        // Calculate concentration penalty
        let concentrationPenalty = 0;
        if (totalBalance > params.concentrationThreshold) {
            const excessRatio = (totalBalance - params.concentrationThreshold) / params.concentrationThreshold;
            concentrationPenalty = Math.min(excessRatio * params.concentrationPenalty, 0.9) * (baseReward + deedBonus);
        }
        
        // Calculate sabbath bonus
        const sabbathBonus = isSabbathObserver ? baseReward * 0.05 : 0; // 5% bonus for observers
        
        // Calculate final reward
        let reward = baseReward + deedBonus - concentrationPenalty + sabbathBonus;
        reward = Math.max(reward, params.minReward); // Ensure minimum
        reward = Math.min(reward, params.maxReward); // Ensure maximum
        
        // Calculate gleaning contribution
        const gleaning = reward * params.gleaningPercentage;
        
        // Update display
        finalReward.textContent = reward.toFixed(1);
        gleaningContribution.textContent = gleaning.toFixed(2);
        
        breakdownBase.textContent = baseReward.toFixed(1) + ' ONX';
        breakdownDeed.textContent = '+' + deedBonus.toFixed(1) + ' ONX';
        breakdownConcentration.textContent = '-' + concentrationPenalty.toFixed(1) + ' ONX';
        breakdownSabbath.textContent = '+' + sabbathBonus.toFixed(1) + ' ONX';
    }
    
    // Event listeners
    baseRewardSlider.addEventListener('input', function() {
        updateDisplayValues();
        calculateRewards();
    });
    
    deedScoreSlider.addEventListener('input', function() {
        updateDisplayValues();
        calculateRewards();
    });
    
    totalBalanceSlider.addEventListener('input', function() {
        updateDisplayValues();
        calculateRewards();
    });
    
    sabbathObserverCheckbox.addEventListener('change', calculateRewards);
    
    resetButton.addEventListener('click', function() {
        baseRewardSlider.value = 10;
        deedScoreSlider.value = 2;
        totalBalanceSlider.value = 50000;
        sabbathObserverCheckbox.checked = false;
        
        updateDisplayValues();
        calculateRewards();
    });
    
    // Initial calculation
    updateDisplayValues();
    calculateRewards();
});
</script>
{% endblock %}
