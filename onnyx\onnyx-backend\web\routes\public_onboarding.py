"""
ONNYX Public Onboarding Routes
Handles public registration, covenant comprehension testing, and tier advancement
"""

from flask import Blueprint, render_template, request, jsonify, session
import json
import time
import uuid
import hashlib
from datetime import datetime

from shared.db.db import db

public_bp = Blueprint('public', __name__, url_prefix='/public')

@public_bp.route('/')
def public_landing():
    """Public landing page for covenant community registration."""
    return render_template('public/landing.html')

@public_bp.route('/register')
def public_register():
    """Public registration form."""
    return render_template('public/register.html')

@public_bp.route('/covenant-test')
def covenant_test():
    """Covenant comprehension test interface."""
    return render_template('public/covenant_test.html')

@public_bp.route('/api/register', methods=['POST'])
def api_public_register():
    """Handle public registration requests."""
    try:
        data = request.get_json()
        name = data.get('name')
        email = data.get('email')
        nation_code = data.get('nation_code')
        
        if not all([name, email, nation_code]):
            return jsonify({"error": "Missing required fields"}), 400
        
        # Validate nation code (must be one of the 12 tribes)
        valid_tribes = ['JU', 'BE', 'LE', 'SI', 'EP', 'MA', 'IS', 'ZE', 'NA', 'GA', 'AS', 'RE']
        if nation_code not in valid_tribes:
            return jsonify({"error": "Invalid nation code. Must select one of the 12 tribes."}), 400
        
        # Check if email already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", (email,))
        if existing:
            return jsonify({"error": "Email already registered"}), 400
        
        # Generate identity
        identity_id = str(uuid.uuid4())
        public_key = hashlib.sha256(f"{identity_id}:{nation_code}".encode()).hexdigest()
        
        # Get nation name
        nation_names = {
            'JU': 'Judah', 'BE': 'Benjamin', 'LE': 'Levi', 'SI': 'Simeon',
            'EP': 'Ephraim', 'MA': 'Manasseh', 'IS': 'Issachar', 'ZE': 'Zebulun',
            'NA': 'Naphtali', 'GA': 'Gad', 'AS': 'Asher', 'RE': 'Reuben'
        }
        
        # Create metadata for Tier 0 member
        metadata = {
            "tribe_code": nation_code,
            "tribe_name": nation_names[nation_code],
            "tribal_role": "Member",
            "council_member": False,
            "voting_weight": 0,
            "badges": ["Community_Member"],
            "reputation": 100,  # Starting reputation
            "covenant_comprehension_score": 0,  # Not tested yet
            "onboarding_completed": False
        }
        
        current_time = int(time.time())
        
        # Insert into identities table (Tier 0)
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, nation_id, metadata,
                status, created_at, updated_at, nation_of_origin, role_class,
                etzem_score, verification_level, covenant_accepted, vault_status,
                nation_code, nation_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            name,
            email,
            public_key,
            nation_code,
            json.dumps(metadata),
            "active",
            current_time,
            current_time,
            nation_code,
            "Community_Member",
            100,  # Starting Etzem score
            0,    # Tier 0 verification
            False,  # Covenant not yet accepted
            "Active",
            nation_code,
            nation_names[nation_code]
        ))
        
        # Create verification progress record
        db.execute("""
            INSERT INTO verification_progress (
                identity_id, current_tier, tier_0_completed, 
                tier_1_completed, tier_2_completed, tier_3_completed,
                last_updated
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id, 0, True, False, False, False, current_time
        ))
        
        return jsonify({
            "success": True,
            "identity_id": identity_id,
            "tribe": nation_names[nation_code],
            "verification_level": 0,
            "next_step": "Take covenant comprehension test to advance to Tier 1"
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@public_bp.route('/api/covenant-test', methods=['GET'])
def api_get_covenant_test():
    """Get the covenant comprehension test questions."""
    try:
        # Load test questions
        with open('docs/onboarding_questions.json', 'r', encoding='utf-8') as f:
            test_config = json.load(f)
        
        test = test_config['covenant_comprehension_test']
        
        # Return test structure without correct answers
        questions = []
        for q in test['questions']:
            question = {
                "question_id": q['question_id'],
                "category": q['category'],
                "difficulty": q['difficulty'],
                "question": q['question'],
                "type": q['type'],
                "options": []
            }
            
            # Include options but not correct answers
            if q['type'] == 'single_choice':
                for option in q['options']:
                    question['options'].append({
                        "option_id": option['option_id'],
                        "text": option['text']
                    })
            elif q['type'] == 'multiple_choice':
                for option in q['options']:
                    question['options'].append({
                        "option_id": option['option_id'],
                        "text": option['text']
                    })
            elif q['type'] == 'essay':
                question['prompt'] = q['prompt']
                question['grading_criteria'] = q['grading_criteria']
            
            questions.append(question)
        
        return jsonify({
            "test_info": {
                "total_questions": test['total_questions'],
                "passing_score": test['passing_score'],
                "tier_advancement": test['tier_advancement']
            },
            "questions": questions
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@public_bp.route('/api/submit-test', methods=['POST'])
def api_submit_covenant_test():
    """Submit and grade covenant comprehension test."""
    try:
        data = request.get_json()
        identity_id = data.get('identity_id')
        answers = data.get('answers', {})
        
        if not identity_id:
            return jsonify({"error": "Missing identity_id"}), 400
        
        # Verify identity exists and is Tier 0
        identity = db.query_one("""
            SELECT * FROM identities WHERE identity_id = ? AND verification_level = 0
        """, (identity_id,))
        
        if not identity:
            return jsonify({"error": "Invalid identity or already advanced beyond Tier 0"}), 400
        
        # Load test with correct answers
        with open('docs/onboarding_questions.json', 'r', encoding='utf-8') as f:
            test_config = json.load(f)
        
        test = test_config['covenant_comprehension_test']
        score = 0
        total_questions = len(test['questions'])
        
        # Grade the test
        for question in test['questions']:
            q_id = question['question_id']
            user_answer = answers.get(q_id)
            
            if question['type'] == 'single_choice':
                if user_answer == question['correct_answer']:
                    score += 1
            elif question['type'] == 'multiple_choice':
                correct_answers = set(question['correct_answers'])
                user_answers = set(user_answer) if isinstance(user_answer, list) else set()
                if user_answers == correct_answers:
                    score += 1
            elif question['type'] == 'essay':
                # Simple essay grading (in production, this would be more sophisticated)
                if user_answer and len(user_answer) > 100:  # Basic length check
                    score += 0.8  # Partial credit for essay attempts
        
        # Determine if passed
        passed = score >= test['passing_score']
        
        # Record test results
        test_results = {
            "score": score,
            "total": total_questions,
            "passed": passed,
            "answers": answers,
            "submitted_at": int(time.time())
        }
        
        if passed:
            # Advance to Tier 1
            current_time = int(time.time())
            
            # Update identity verification level
            db.execute("""
                UPDATE identities 
                SET verification_level = 1, covenant_accepted = TRUE, updated_at = ?
                WHERE identity_id = ?
            """, (current_time, identity_id))
            
            # Update verification progress
            db.execute("""
                UPDATE verification_progress 
                SET current_tier = 1, tier_1_completed = TRUE, last_updated = ?
                WHERE identity_id = ?
            """, (current_time, identity_id))
            
            # Record covenant acceptance
            covenant_hash = "genesis_covenant_v1.0"  # Simplified for now
            db.execute("""
                INSERT OR REPLACE INTO covenant_acceptances (
                    identity_id, covenant_version, accepted_at, covenant_hash,
                    comprehension_score, test_results
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                identity_id, "1.0", current_time, covenant_hash,
                score, json.dumps(test_results)
            ))
        
        return jsonify({
            "success": True,
            "score": score,
            "total": total_questions,
            "passed": passed,
            "new_tier": 1 if passed else 0,
            "message": "Congratulations! You have advanced to Tier 1 and can now participate in governance." if passed else "Test not passed. Please study the covenant materials and try again in 7 days."
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@public_bp.route('/api/stats')
def api_public_stats():
    """Get public onboarding statistics."""
    try:
        # Get registration stats
        total_members = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
        
        # Get tier distribution
        tier_stats = db.query("""
            SELECT verification_level, COUNT(*) as count 
            FROM identities 
            GROUP BY verification_level
        """)
        
        # Get tribal distribution
        tribal_stats = db.query("""
            SELECT nation_code, COUNT(*) as count 
            FROM identities 
            WHERE role_class = 'Community_Member'
            GROUP BY nation_code
        """)
        
        # Get test pass rate
        test_attempts = db.query_one("SELECT COUNT(*) as count FROM covenant_acceptances")['count']
        
        return jsonify({
            "total_members": total_members,
            "tier_distribution": {tier['verification_level']: tier['count'] for tier in tier_stats},
            "tribal_distribution": {tribe['nation_code']: tribe['count'] for tribe in tribal_stats},
            "test_attempts": test_attempts,
            "covenant_acceptance_rate": round((test_attempts / max(total_members, 1)) * 100, 1)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
