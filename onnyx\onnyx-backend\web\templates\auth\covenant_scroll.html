{% extends "base.html" %}

{% block title %}Digital Scroll of Rights - ONNYX Covenant | ONNYX Platform{% endblock %}

{% block description %}The Digital Scroll of Rights - A covenant-based framework for digital sovereignty, privacy protection, and righteous commerce in the ONNYX network.{% endblock %}

{% block head %}
<style>
    .scroll-container {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        border: 2px solid rgba(0, 212, 255, 0.2);
        border-radius: 24px;
        position: relative;
        overflow: hidden;
    }
    
    .scroll-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
        animation: scroll-shimmer 4s infinite;
    }
    
    @keyframes scroll-shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }
    
    .scroll-header {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border-bottom: 2px solid rgba(0, 212, 255, 0.3);
        padding: 32px;
        text-align: center;
        position: relative;
    }
    
    .scroll-seal {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #00d4ff, #8b5cf6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        margin: 0 auto 16px;
        border: 4px solid rgba(255, 255, 255, 0.1);
        animation: seal-glow 3s infinite;
    }
    
    @keyframes seal-glow {
        0%, 100% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
        50% { box-shadow: 0 0 40px rgba(139, 92, 246, 0.5); }
    }
    
    .scroll-article {
        padding: 24px 32px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
    }
    
    .scroll-article:last-child {
        border-bottom: none;
    }
    
    .article-number {
        position: absolute;
        left: -8px;
        top: 24px;
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #00d4ff, #8b5cf6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
        color: #1f2937;
    }
    
    .covenant-signature {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 16px;
        padding: 24px;
        margin-top: 32px;
        text-align: center;
    }
    
    .signature-line {
        border-bottom: 2px dashed rgba(0, 212, 255, 0.5);
        margin: 16px 0;
        height: 2px;
    }
    
    .acceptance-checkbox {
        appearance: none;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(0, 212, 255, 0.5);
        border-radius: 4px;
        background: transparent;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .acceptance-checkbox:checked {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6);
        border-color: #00d4ff;
    }
    
    .acceptance-checkbox:checked::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #1f2937;
        font-weight: bold;
        font-size: 14px;
    }
    
    .scroll-text {
        line-height: 1.8;
        font-size: 16px;
        color: #e5e7eb;
    }
    
    .scroll-text strong {
        color: #00d4ff;
        font-weight: 600;
    }
    
    .scroll-text em {
        color: #8b5cf6;
        font-style: normal;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Scroll Container -->
        <div class="scroll-container">
            
            <!-- Scroll Header -->
            <div class="scroll-header">
                <div class="scroll-seal">
                    📜
                </div>
                <h1 class="text-4xl font-orbitron font-bold text-primary mb-4">Digital Scroll of Rights</h1>
                <h2 class="text-xl font-medium text-cyber-cyan mb-2">ONNYX Covenant Identity Protection Protocol</h2>
                <div class="text-sm text-secondary">Version 1.0 • Established in the Genesis Block</div>
            </div>
            
            <!-- Preamble -->
            <div class="scroll-article">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">Preamble</h3>
                <div class="scroll-text">
                    <p class="mb-4">
                        We, the covenant community of ONNYX, establish this <strong>Digital Scroll of Rights</strong> to protect the 
                        fundamental liberties of all participants in our biblical economic network. These rights are 
                        <em>immutable</em>, <em>inalienable</em>, and <em>divinely inspired</em> by principles of righteous commerce 
                        and digital sovereignty.
                    </p>
                    <p>
                        By accepting this covenant, you join a community dedicated to <strong>economic justice</strong>, 
                        <strong>privacy protection</strong>, and <strong>mutual prosperity</strong> under the guidance of 
                        biblical wisdom and technological innovation.
                    </p>
                </div>
            </div>
            
            <!-- Article I -->
            <div class="scroll-article">
                <div class="article-number">I</div>
                <h3 class="text-xl font-orbitron font-bold text-primary mb-4 ml-8">Right to Digital Anonymity</h3>
                <div class="scroll-text ml-8">
                    <p class="mb-3">
                        <strong>You have the absolute right to remain unnamed</strong> outside the ONNYX network. 
                        Your identity within our covenant community shall be protected from external surveillance, 
                        data harvesting, and unauthorized disclosure.
                    </p>
                    <ul class="list-disc list-inside space-y-2 text-secondary">
                        <li>No external entity may compel disclosure of your ONNYX identity</li>
                        <li>Your personal data remains encrypted and under your sovereign control</li>
                        <li>Cross-platform tracking and profiling are strictly prohibited</li>
                        <li>Your right to pseudonymous participation is guaranteed</li>
                    </ul>
                </div>
            </div>
            
            <!-- Article II -->
            <div class="scroll-article">
                <div class="article-number">II</div>
                <h3 class="text-xl font-orbitron font-bold text-primary mb-4 ml-8">Right to Private Commerce</h3>
                <div class="scroll-text ml-8">
                    <p class="mb-3">
                        <strong>You have the right to trade without external surveillance.</strong> 
                        Your commercial activities within the ONNYX network are protected by cryptographic privacy 
                        and biblical principles of honest dealing.
                    </p>
                    <ul class="list-disc list-inside space-y-2 text-secondary">
                        <li>Financial transactions remain private between covenant parties</li>
                        <li>No central authority may freeze or confiscate your rightful assets</li>
                        <li>Anti-usury protections ensure fair lending practices</li>
                        <li>Gleaning pools provide community support without shame</li>
                    </ul>
                </div>
            </div>
            
            <!-- Article III -->
            <div class="scroll-article">
                <div class="article-number">III</div>
                <h3 class="text-xl font-orbitron font-bold text-primary mb-4 ml-8">Right to Prove Labor Without Selling Privacy</h3>
                <div class="scroll-text ml-8">
                    <p class="mb-3">
                        <strong>You have the right to demonstrate your contributions</strong> without surrendering 
                        personal privacy. The ONNYX Etzem system recognizes righteous deeds while protecting 
                        your digital sovereignty.
                    </p>
                    <ul class="list-disc list-inside space-y-2 text-secondary">
                        <li>Zero-knowledge proofs validate your work without exposure</li>
                        <li>Reputation building occurs through cryptographic verification</li>
                        <li>Your labor history remains under your control</li>
                        <li>Merit-based advancement without invasive monitoring</li>
                    </ul>
                </div>
            </div>
            
            <!-- Article IV -->
            <div class="scroll-article">
                <div class="article-number">IV</div>
                <h3 class="text-xl font-orbitron font-bold text-primary mb-4 ml-8">Protection Against Digital Exile</h3>
                <div class="scroll-text ml-8">
                    <p class="mb-3">
                        <strong>You are protected against unjust digital exile</strong> and asset seizure. 
                        The covenant community provides sanctuary against external persecution and 
                        maintains your economic rights even under pressure.
                    </p>
                    <ul class="list-disc list-inside space-y-2 text-secondary">
                        <li>Emergency vault protection against external threats</li>
                        <li>Community defense against coordinated attacks</li>
                        <li>Exile mode preserves your assets during persecution</li>
                        <li>Restoration protocols for returning members</li>
                    </ul>
                </div>
            </div>
            
            <!-- Article V -->
            <div class="scroll-article">
                <div class="article-number">V</div>
                <h3 class="text-xl font-orbitron font-bold text-primary mb-4 ml-8">Right to Economic Justice</h3>
                <div class="scroll-text ml-8">
                    <p class="mb-3">
                        <strong>You have the right to participate in biblical economic principles</strong> 
                        including jubilee resets, gleaning provisions, and anti-usury protections. 
                        Economic inequality is actively addressed through covenant mechanisms.
                    </p>
                    <ul class="list-disc list-inside space-y-2 text-secondary">
                        <li>Jubilee wealth redistribution every seven years</li>
                        <li>Gleaning pools for community mutual aid</li>
                        <li>Interest-free lending with forgiveness provisions</li>
                        <li>Sabbath rest protections for sustainable participation</li>
                    </ul>
                </div>
            </div>
            
            <!-- Article VI -->
            <div class="scroll-article">
                <div class="article-number">VI</div>
                <h3 class="text-xl font-orbitron font-bold text-primary mb-4 ml-8">Right to Covenant Governance</h3>
                <div class="scroll-text ml-8">
                    <p class="mb-3">
                        <strong>You have the right to participate in covenant governance</strong> through 
                        the submission of scrolls, voting on community matters, and contributing to 
                        the evolution of our biblical economic system.
                    </p>
                    <ul class="list-disc list-inside space-y-2 text-secondary">
                        <li>Democratic participation in network governance</li>
                        <li>Proposal submission through the scroll system</li>
                        <li>Transparent voting with cryptographic verification</li>
                        <li>Community consensus on protocol changes</li>
                    </ul>
                </div>
            </div>
            
            <!-- Covenant Signature -->
            <div class="covenant-signature">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">Covenant Acceptance</h3>
                
                <div class="scroll-text mb-6">
                    <p class="mb-4">
                        By accepting this <strong>Digital Scroll of Rights</strong>, I covenant to uphold these 
                        principles and participate in the ONNYX community with <em>integrity</em>, <em>righteousness</em>, 
                        and <em>mutual respect</em>.
                    </p>
                    <p>
                        I understand that these rights come with responsibilities to protect the covenant 
                        community and advance the cause of digital sovereignty and economic justice.
                    </p>
                </div>
                
                <div class="flex items-center justify-center gap-4 mb-6">
                    <input type="checkbox" id="covenant-acceptance" class="acceptance-checkbox" required>
                    <label for="covenant-acceptance" class="text-primary font-medium cursor-pointer">
                        I accept the Digital Scroll of Rights and covenant to uphold these principles
                    </label>
                </div>
                
                <div class="signature-line"></div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-secondary mt-6">
                    <div class="text-center">
                        <div class="font-medium text-primary">Witness</div>
                        <div>ONNYX Network</div>
                    </div>
                    <div class="text-center">
                        <div class="font-medium text-primary">Date</div>
                        <div id="current-date"></div>
                    </div>
                    <div class="text-center">
                        <div class="font-medium text-primary">Version</div>
                        <div>1.0</div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <button id="accept-covenant" class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        Accept Covenant & Continue
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Back to Registration -->
        <div class="text-center mt-8">
            <a href="{{ url_for('auth.register_identity') }}" class="glass-button px-6 py-3 rounded-lg font-medium">
                ← Back to Registration
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set current date
    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    document.getElementById('current-date').textContent = currentDate;
    
    // Handle covenant acceptance
    const checkbox = document.getElementById('covenant-acceptance');
    const acceptButton = document.getElementById('accept-covenant');
    
    checkbox.addEventListener('change', function() {
        acceptButton.disabled = !this.checked;
    });
    
    acceptButton.addEventListener('click', function() {
        if (checkbox.checked) {
            // Store acceptance in session storage
            sessionStorage.setItem('covenant_accepted', 'true');
            sessionStorage.setItem('covenant_version', '1.0');
            sessionStorage.setItem('covenant_date', Date.now());
            
            // Redirect back to registration
            window.location.href = "{{ url_for('auth.register_identity') }}";
        }
    });
    
    // Check if coming from registration
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('from') === 'registration') {
        // Auto-scroll to acceptance section
        document.querySelector('.covenant-signature').scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
        });
    }
});
</script>
{% endblock %}
