"""
Mining Fairness Audit Routes
Handles mining fairness monitoring, biblical balance checks, and anti-gaming oversight.

Routes:
- /mining-audit/ - Main mining fairness dashboard
- /mining-audit/miner/<identity_id> - Individual miner fairness report
- /mining-audit/network-report - Network-wide fairness analysis
- /mining-audit/violations - Fairness violations tracking
- /mining-audit/api/fairness-metrics - Real-time fairness metrics API
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from shared.db.db import db
from shared.models.mining_fairness import mining_fairness_auditor, FairnessViolationType
import json
import logging
import time

logger = logging.getLogger(__name__)

mining_audit_bp = Blueprint('mining_audit', __name__, url_prefix='/mining-audit')

@mining_audit_bp.route('/')
def dashboard():
    """Main mining fairness audit dashboard."""
    try:
        # Check authentication and admin privileges
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("""
            SELECT name, verification_level, role_class FROM identities WHERE identity_id = ?
        """, (session['identity_id'],))
        
        if not user:
            return redirect(url_for('auth.login'))
        
        # Check if user has audit access (Tier 3+ or Admin role)
        has_access = (user['verification_level'] >= 3 or 
                     user.get('role_class') in ['System_Admin', 'Tribal_Elder'])
        
        if not has_access:
            return render_template('mining_audit/access_denied.html', 
                                 required_role="System Admin or Tribal Elder",
                                 current_tier=user['verification_level'])
        
        # Get network fairness report
        network_report = mining_fairness_auditor.get_network_fairness_report()
        
        # Get recent mining activity
        recent_miners = db.query("""
            SELECT b.proposer_id, i.name, i.nation_code,
                   COUNT(*) as block_count,
                   MAX(b.timestamp) as last_block,
                   AVG(CAST(JSON_EXTRACT(b.metadata, '$.reward_amount') AS REAL)) as avg_reward
            FROM blocks b
            LEFT JOIN identities i ON b.proposer_id = i.identity_id
            WHERE b.timestamp > ?
            GROUP BY b.proposer_id
            ORDER BY block_count DESC
            LIMIT 20
        """, (time.time() - (7 * 86400),))  # Last 7 days
        
        # Get fairness violations
        recent_violations = []
        for miner in recent_miners:
            metrics = mining_fairness_auditor.audit_mining_fairness(miner['proposer_id'])
            if metrics.violations:
                recent_violations.append({
                    'identity_id': miner['proposer_id'],
                    'name': miner['name'],
                    'tribe': miner['nation_code'],
                    'violations': [v.value for v in metrics.violations],
                    'fairness_score': metrics.overall_fairness_score
                })
        
        # Calculate dashboard statistics
        audit_stats = {
            'total_miners': len(recent_miners),
            'network_fairness_score': network_report.get('network_fairness_score', 0),
            'total_violations': len(recent_violations),
            'max_concentration': network_report.get('max_concentration_ratio', 0),
            'recommendations_count': len(network_report.get('recommendations', []))
        }
        
        return render_template('mining_audit/dashboard.html',
                             user=user,
                             network_report=network_report,
                             recent_miners=recent_miners,
                             recent_violations=recent_violations,
                             audit_stats=audit_stats)
        
    except Exception as e:
        logger.error(f"Error in mining audit dashboard: {e}")
        return render_template('error.html', error="Failed to load mining audit dashboard"), 500

@mining_audit_bp.route('/miner/<identity_id>')
def miner_report(identity_id):
    """Individual miner fairness report."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        # Check access permissions
        user = db.query_one("""
            SELECT verification_level, role_class FROM identities WHERE identity_id = ?
        """, (session['identity_id'],))
        
        has_access = (user['verification_level'] >= 3 or 
                     user.get('role_class') in ['System_Admin', 'Tribal_Elder'] or
                     session['identity_id'] == identity_id)  # Users can view their own reports
        
        if not has_access:
            return render_template('mining_audit/access_denied.html', required_role="Authorized User")
        
        # Get miner information
        miner_info = db.query_one("""
            SELECT name, nation_code, nation_name, verification_level, 
                   etzem_score, created_at, role_class
            FROM identities WHERE identity_id = ?
        """, (identity_id,))
        
        if not miner_info:
            return render_template('error.html', error="Miner not found"), 404
        
        # Get comprehensive fairness metrics
        fairness_metrics = mining_fairness_auditor.audit_mining_fairness(identity_id)
        
        # Get mining history
        mining_history = db.query("""
            SELECT block_hash, block_height, timestamp,
                   CAST(JSON_EXTRACT(metadata, '$.reward_amount') AS REAL) as reward_amount
            FROM blocks 
            WHERE proposer_id = ?
            ORDER BY timestamp DESC
            LIMIT 50
        """, (identity_id,))
        
        # Get deed history for context
        deed_history = db.query("""
            SELECT deed_type, deed_value, description, timestamp
            FROM deeds_ledger 
            WHERE identity_id = ?
            ORDER BY timestamp DESC
            LIMIT 20
        """, (identity_id,))
        
        # Calculate mining statistics
        mining_stats = {
            'total_blocks': len(mining_history),
            'total_rewards': sum([m['reward_amount'] for m in mining_history if m['reward_amount']]),
            'avg_reward': sum([m['reward_amount'] for m in mining_history if m['reward_amount']]) / max(len(mining_history), 1),
            'first_block': min([m['timestamp'] for m in mining_history]) if mining_history else None,
            'last_block': max([m['timestamp'] for m in mining_history]) if mining_history else None
        }
        
        return render_template('mining_audit/miner_report.html',
                             miner_info=miner_info,
                             fairness_metrics=fairness_metrics,
                             mining_history=mining_history,
                             deed_history=deed_history,
                             mining_stats=mining_stats)
        
    except Exception as e:
        logger.error(f"Error in miner report for {identity_id}: {e}")
        return render_template('error.html', error="Failed to load miner report"), 500

@mining_audit_bp.route('/network-report')
def network_report():
    """Network-wide fairness analysis."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("""
            SELECT verification_level, role_class FROM identities WHERE identity_id = ?
        """, (session['identity_id'],))
        
        has_access = (user['verification_level'] >= 3 or 
                     user.get('role_class') in ['System_Admin', 'Tribal_Elder'])
        
        if not has_access:
            return render_template('mining_audit/access_denied.html', required_role="System Admin or Tribal Elder")
        
        # Get comprehensive network report
        network_report = mining_fairness_auditor.get_network_fairness_report()
        
        # Get tribal distribution analysis
        tribal_distribution = db.query("""
            SELECT i.nation_code, i.nation_name,
                   COUNT(DISTINCT b.proposer_id) as unique_miners,
                   COUNT(b.block_hash) as total_blocks,
                   SUM(CAST(JSON_EXTRACT(b.metadata, '$.reward_amount') AS REAL)) as total_rewards
            FROM blocks b
            LEFT JOIN identities i ON b.proposer_id = i.identity_id
            WHERE b.timestamp > ?
            GROUP BY i.nation_code, i.nation_name
            ORDER BY total_blocks DESC
        """, (time.time() - (30 * 86400),))  # Last 30 days
        
        # Get concentration analysis
        concentration_analysis = db.query("""
            SELECT b.proposer_id, i.name, i.nation_code,
                   COUNT(*) as block_count,
                   COUNT(*) * 100.0 / (SELECT COUNT(*) FROM blocks WHERE timestamp > ?) as percentage
            FROM blocks b
            LEFT JOIN identities i ON b.proposer_id = i.identity_id
            WHERE b.timestamp > ?
            GROUP BY b.proposer_id
            ORDER BY block_count DESC
            LIMIT 10
        """, (time.time() - (30 * 86400), time.time() - (30 * 86400)))
        
        # Get fairness trends over time
        fairness_trends = _calculate_fairness_trends()
        
        return render_template('mining_audit/network_report.html',
                             network_report=network_report,
                             tribal_distribution=tribal_distribution,
                             concentration_analysis=concentration_analysis,
                             fairness_trends=fairness_trends)
        
    except Exception as e:
        logger.error(f"Error in network report: {e}")
        return render_template('error.html', error="Failed to load network report"), 500

@mining_audit_bp.route('/violations')
def violations_tracking():
    """Fairness violations tracking."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))
        
        user = db.query_one("""
            SELECT verification_level, role_class FROM identities WHERE identity_id = ?
        """, (session['identity_id'],))
        
        has_access = (user['verification_level'] >= 3 or 
                     user.get('role_class') in ['System_Admin', 'Tribal_Elder'])
        
        if not has_access:
            return render_template('mining_audit/access_denied.html', required_role="System Admin or Tribal Elder")
        
        # Get all miners with violations
        recent_miners = db.query("""
            SELECT DISTINCT proposer_id FROM blocks 
            WHERE timestamp > ?
        """, (time.time() - (30 * 86400),))
        
        violations_data = []
        
        for miner in recent_miners:
            metrics = mining_fairness_auditor.audit_mining_fairness(miner['proposer_id'])
            if metrics.violations:
                miner_info = db.query_one("""
                    SELECT name, nation_code FROM identities WHERE identity_id = ?
                """, (miner['proposer_id'],))
                
                violations_data.append({
                    'identity_id': miner['proposer_id'],
                    'name': miner_info['name'] if miner_info else 'Unknown',
                    'tribe': miner_info['nation_code'] if miner_info else 'Unknown',
                    'violations': metrics.violations,
                    'fairness_score': metrics.overall_fairness_score,
                    'mining_power': metrics.mining_power_score,
                    'gaming_penalty': metrics.gaming_penalty
                })
        
        # Group violations by type
        violation_summary = {}
        for violation_type in FairnessViolationType:
            violation_summary[violation_type.value] = len([
                v for v in violations_data 
                if violation_type in v['violations']
            ])
        
        return render_template('mining_audit/violations.html',
                             violations_data=violations_data,
                             violation_summary=violation_summary)
        
    except Exception as e:
        logger.error(f"Error in violations tracking: {e}")
        return render_template('error.html', error="Failed to load violations tracking"), 500

@mining_audit_bp.route('/api/fairness-metrics')
def api_fairness_metrics():
    """Real-time fairness metrics API."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        
        # Get network fairness report
        network_report = mining_fairness_auditor.get_network_fairness_report()
        
        # Get recent activity summary
        recent_activity = db.query_one("""
            SELECT COUNT(DISTINCT proposer_id) as active_miners,
                   COUNT(*) as total_blocks,
                   AVG(CAST(JSON_EXTRACT(metadata, '$.reward_amount') AS REAL)) as avg_reward
            FROM blocks 
            WHERE timestamp > ?
        """, (time.time() - (24 * 3600),))  # Last 24 hours
        
        return jsonify({
            'network_fairness_score': network_report.get('network_fairness_score', 0),
            'max_concentration_ratio': network_report.get('max_concentration_ratio', 0),
            'total_violations': network_report.get('total_violations', 0),
            'active_miners': recent_activity['active_miners'] if recent_activity else 0,
            'total_blocks_24h': recent_activity['total_blocks'] if recent_activity else 0,
            'avg_reward_24h': recent_activity['avg_reward'] if recent_activity else 0,
            'recommendations': network_report.get('recommendations', []),
            'timestamp': int(time.time())
        })
        
    except Exception as e:
        logger.error(f"Error in fairness metrics API: {e}")
        return jsonify({'error': str(e)}), 500

@mining_audit_bp.route('/api/miner-metrics/<identity_id>')
def api_miner_metrics(identity_id):
    """Individual miner metrics API."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        
        # Check access permissions
        user = db.query_one("""
            SELECT verification_level, role_class FROM identities WHERE identity_id = ?
        """, (session['identity_id'],))
        
        has_access = (user['verification_level'] >= 3 or 
                     user.get('role_class') in ['System_Admin', 'Tribal_Elder'] or
                     session['identity_id'] == identity_id)
        
        if not has_access:
            return jsonify({'error': 'Access denied'}), 403
        
        # Get fairness metrics
        metrics = mining_fairness_auditor.audit_mining_fairness(identity_id)
        
        return jsonify({
            'identity_id': metrics.identity_id,
            'mining_power_score': metrics.mining_power_score,
            'humility_weight': metrics.humility_weight,
            'community_contribution': metrics.community_contribution,
            'tribal_balance_factor': metrics.tribal_balance_factor,
            'last_shall_be_first_bonus': metrics.last_shall_be_first_bonus,
            'gaming_penalty': metrics.gaming_penalty,
            'overall_fairness_score': metrics.overall_fairness_score,
            'violations': [v.value for v in metrics.violations],
            'timestamp': int(time.time())
        })
        
    except Exception as e:
        logger.error(f"Error in miner metrics API for {identity_id}: {e}")
        return jsonify({'error': str(e)}), 500

def _calculate_fairness_trends():
    """Calculate fairness trends over time."""
    try:
        # This would implement trend analysis over time
        # For now, return placeholder data
        return {
            'fairness_scores': [0.8, 0.82, 0.79, 0.85, 0.83],
            'concentration_ratios': [0.15, 0.14, 0.16, 0.12, 0.13],
            'violation_counts': [5, 3, 7, 2, 4],
            'timestamps': [
                time.time() - (4 * 86400),
                time.time() - (3 * 86400),
                time.time() - (2 * 86400),
                time.time() - (1 * 86400),
                time.time()
            ]
        }
    except Exception as e:
        logger.error(f"Error calculating fairness trends: {e}")
        return {}
