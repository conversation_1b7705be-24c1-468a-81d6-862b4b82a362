// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title SabbathEnforcementContract
 * @dev Implements biblical sabbath enforcement for the ONNYX platform
 * @notice This contract enforces sabbath observance with timezone locking
 */

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract SabbathEnforcementContract is Ownable, ReentrancyGuard {
    
    // Sabbath types
    enum SabbathType {
        WEEKLY,
        NEW_MOON,
        BIBLICAL_FEAST,
        HIGH_SABBATH
    }
    
    // Sabbath period structure
    struct SabbathPeriod {
        uint256 periodId;
        uint256 startTimestamp;
        uint256 endTimestamp;
        SabbathType sabbathType;
        string hebrewName;
        string biblicalReference;
        bool active;
        uint256 violationPenalty; // Basis points
        uint256 gracePeriodMinutes;
    }
    
    // User sabbath profile
    struct SabbathObserver {
        address userAddress;
        bool isObserver;
        string lockedTimezone;
        uint256 timezoneLockedAt;
        bool canOverrideTimezone;
        uint256 complianceScore; // Out of 10000 basis points
        uint256 totalViolations;
        uint256 lastViolationTime;
    }
    
    // Sabbath violation record
    struct SabbathViolation {
        uint256 violationId;
        address violator;
        uint256 sabbathPeriodId;
        uint256 violationTimestamp;
        string activityType;
        uint256 penaltyAmount;
        string biblicalReference;
        bool penaltyPaid;
    }
    
    // State variables
    mapping(uint256 => SabbathPeriod) public sabbathPeriods;
    mapping(address => SabbathObserver) public sabbathObservers;
    mapping(uint256 => SabbathViolation) public sabbathViolations;
    mapping(address => uint256[]) public userViolations;
    
    uint256 public nextPeriodId = 1;
    uint256 public nextViolationId = 1;
    uint256 public constant MAX_COMPLIANCE_SCORE = 10000;
    uint256 public constant VIOLATION_PENALTY = 100; // 1% default penalty
    
    // Events
    event SabbathPeriodCreated(
        uint256 indexed periodId,
        uint256 startTimestamp,
        uint256 endTimestamp,
        SabbathType sabbathType,
        string biblicalReference
    );
    
    event SabbathObserverRegistered(
        address indexed observer,
        string lockedTimezone,
        uint256 lockedAt
    );
    
    event SabbathViolationRecorded(
        uint256 indexed violationId,
        address indexed violator,
        uint256 sabbathPeriodId,
        string activityType,
        uint256 penaltyAmount
    );
    
    event ComplianceScoreUpdated(
        address indexed observer,
        uint256 oldScore,
        uint256 newScore
    );
    
    /**
     * @dev Register a sabbath observer with timezone locking
     * @param observer Address of the observer
     * @param timezone Timezone to lock (e.g., "America/New_York")
     */
    function registerSabbathObserver(
        address observer,
        string calldata timezone
    ) external onlyOwner {
        require(observer != address(0), "Invalid observer address");
        require(bytes(timezone).length > 0, "Timezone required");
        
        sabbathObservers[observer] = SabbathObserver({
            userAddress: observer,
            isObserver: true,
            lockedTimezone: timezone,
            timezoneLockedAt: block.timestamp,
            canOverrideTimezone: false,
            complianceScore: MAX_COMPLIANCE_SCORE, // Start with perfect score
            totalViolations: 0,
            lastViolationTime: 0
        });
        
        emit SabbathObserverRegistered(observer, timezone, block.timestamp);
    }
    
    /**
     * @dev Create a new sabbath period
     * @param startTimestamp Start time of sabbath (Unix timestamp)
     * @param endTimestamp End time of sabbath (Unix timestamp)
     * @param sabbathType Type of sabbath
     * @param hebrewName Hebrew name of the sabbath
     * @param biblicalReference Biblical reference for the sabbath
     */
    function createSabbathPeriod(
        uint256 startTimestamp,
        uint256 endTimestamp,
        SabbathType sabbathType,
        string calldata hebrewName,
        string calldata biblicalReference
    ) external onlyOwner returns (uint256) {
        require(startTimestamp < endTimestamp, "Invalid time range");
        require(startTimestamp > block.timestamp, "Cannot create past sabbath");
        
        uint256 periodId = nextPeriodId++;
        
        sabbathPeriods[periodId] = SabbathPeriod({
            periodId: periodId,
            startTimestamp: startTimestamp,
            endTimestamp: endTimestamp,
            sabbathType: sabbathType,
            hebrewName: hebrewName,
            biblicalReference: biblicalReference,
            active: true,
            violationPenalty: VIOLATION_PENALTY,
            gracePeriodMinutes: 30 // 30-minute grace period
        });
        
        emit SabbathPeriodCreated(
            periodId,
            startTimestamp,
            endTimestamp,
            sabbathType,
            biblicalReference
        );
        
        return periodId;
    }
    
    /**
     * @dev Check if currently in sabbath period
     * @return isActive True if currently in sabbath
     * @return currentPeriodId ID of current sabbath period (0 if none)
     * @return sabbathType Type of current sabbath
     */
    function getCurrentSabbathStatus() external view returns (
        bool isActive,
        uint256 currentPeriodId,
        SabbathType sabbathType
    ) {
        uint256 currentTime = block.timestamp;
        
        // Check all active sabbath periods
        for (uint256 i = 1; i < nextPeriodId; i++) {
            SabbathPeriod memory period = sabbathPeriods[i];
            
            if (period.active && 
                currentTime >= period.startTimestamp && 
                currentTime <= period.endTimestamp) {
                return (true, period.periodId, period.sabbathType);
            }
        }
        
        return (false, 0, SabbathType.WEEKLY);
    }
    
    /**
     * @dev Check if an activity is allowed during sabbath
     * @param user Address of the user
     * @param activityType Type of activity (e.g., "transaction", "mining")
     * @return allowed True if activity is allowed
     * @return reason Reason for the decision
     */
    function isActivityAllowed(
        address user,
        string calldata activityType
    ) external view returns (bool allowed, string memory reason) {
        
        // Check if user is a sabbath observer
        SabbathObserver memory observer = sabbathObservers[user];
        if (!observer.isObserver) {
            return (true, "Not a sabbath observer");
        }
        
        // Check if currently in sabbath
        (bool isActive, uint256 periodId, ) = this.getCurrentSabbathStatus();
        if (!isActive) {
            return (true, "Not currently sabbath");
        }
        
        // Check allowed activities during sabbath
        if (keccak256(bytes(activityType)) == keccak256(bytes("prayer")) ||
            keccak256(bytes(activityType)) == keccak256(bytes("study")) ||
            keccak256(bytes(activityType)) == keccak256(bytes("charity")) ||
            keccak256(bytes(activityType)) == keccak256(bytes("emergency"))) {
            return (true, "Activity permitted during sabbath");
        }
        
        // Check grace period
        SabbathPeriod memory period = sabbathPeriods[periodId];
        uint256 graceEndTime = period.startTimestamp + (period.gracePeriodMinutes * 60);
        
        if (block.timestamp <= graceEndTime) {
            return (true, "Within grace period");
        }
        
        return (false, "Activity not permitted during sabbath");
    }
    
    /**
     * @dev Record a sabbath violation
     * @param violator Address of the violator
     * @param activityType Type of prohibited activity
     */
    function recordSabbathViolation(
        address violator,
        string calldata activityType
    ) external onlyOwner nonReentrant {
        
        // Get current sabbath period
        (bool isActive, uint256 periodId, ) = this.getCurrentSabbathStatus();
        require(isActive, "Not currently sabbath");
        
        SabbathObserver storage observer = sabbathObservers[violator];
        require(observer.isObserver, "Not a sabbath observer");
        
        SabbathPeriod memory period = sabbathPeriods[periodId];
        
        // Calculate penalty based on compliance score
        uint256 penaltyAmount = (observer.complianceScore * period.violationPenalty) / 10000;
        
        // Create violation record
        uint256 violationId = nextViolationId++;
        sabbathViolations[violationId] = SabbathViolation({
            violationId: violationId,
            violator: violator,
            sabbathPeriodId: periodId,
            violationTimestamp: block.timestamp,
            activityType: activityType,
            penaltyAmount: penaltyAmount,
            biblicalReference: period.biblicalReference,
            penaltyPaid: false
        });
        
        // Update observer's record
        observer.totalViolations++;
        observer.lastViolationTime = block.timestamp;
        userViolations[violator].push(violationId);
        
        // Update compliance score (decrease by 1% per violation)
        uint256 oldScore = observer.complianceScore;
        uint256 scoreDecrease = MAX_COMPLIANCE_SCORE / 100; // 1%
        
        if (observer.complianceScore > scoreDecrease) {
            observer.complianceScore -= scoreDecrease;
        } else {
            observer.complianceScore = 0;
        }
        
        emit SabbathViolationRecorded(
            violationId,
            violator,
            periodId,
            activityType,
            penaltyAmount
        );
        
        emit ComplianceScoreUpdated(
            violator,
            oldScore,
            observer.complianceScore
        );
    }
    
    /**
     * @dev Get upcoming sabbath periods
     * @param count Number of periods to return
     * @return Array of upcoming sabbath periods
     */
    function getUpcomingSabbaths(uint256 count) external view returns (SabbathPeriod[] memory) {
        require(count > 0 && count <= 50, "Invalid count");
        
        SabbathPeriod[] memory upcoming = new SabbathPeriod[](count);
        uint256 found = 0;
        uint256 currentTime = block.timestamp;
        
        for (uint256 i = 1; i < nextPeriodId && found < count; i++) {
            SabbathPeriod memory period = sabbathPeriods[i];
            
            if (period.active && period.startTimestamp > currentTime) {
                upcoming[found] = period;
                found++;
            }
        }
        
        // Resize array to actual found count
        assembly {
            mstore(upcoming, found)
        }
        
        return upcoming;
    }
    
    /**
     * @dev Get user's sabbath compliance information
     * @param user Address of the user
     * @return observer Sabbath observer information
     * @return violationCount Number of violations
     * @return recentViolations Recent violation IDs
     */
    function getUserCompliance(address user) external view returns (
        SabbathObserver memory observer,
        uint256 violationCount,
        uint256[] memory recentViolations
    ) {
        observer = sabbathObservers[user];
        violationCount = observer.totalViolations;
        
        uint256[] memory allViolations = userViolations[user];
        uint256 recentCount = allViolations.length > 10 ? 10 : allViolations.length;
        
        recentViolations = new uint256[](recentCount);
        for (uint256 i = 0; i < recentCount; i++) {
            recentViolations[i] = allViolations[allViolations.length - 1 - i];
        }
        
        return (observer, violationCount, recentViolations);
    }
    
    /**
     * @dev Batch create weekly sabbaths for a year
     * @param startDate Unix timestamp of first Friday evening
     */
    function createWeeklySabbaths(uint256 startDate) external onlyOwner {
        require(startDate > block.timestamp, "Start date must be in future");
        
        uint256 weekInSeconds = 7 * 24 * 60 * 60; // 1 week
        uint256 sabbathDuration = 25 * 60 * 60; // 25 hours (sunset to sunset + buffer)
        
        for (uint256 i = 0; i < 52; i++) {
            uint256 sabbathStart = startDate + (i * weekInSeconds);
            uint256 sabbathEnd = sabbathStart + sabbathDuration;
            
            this.createSabbathPeriod(
                sabbathStart,
                sabbathEnd,
                SabbathType.WEEKLY,
                "Shabbat",
                "Exodus 20:8-11 - Remember the sabbath day, to keep it holy"
            );
        }
    }
}
