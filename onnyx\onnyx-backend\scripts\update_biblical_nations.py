#!/usr/bin/env python3
"""
Update Biblical Nations with Proper Biblical Ancestral Groupings
Implements the biblical tribal system as requested by the user:
1. <PERSON><PERSON> (12 Dukes)
2. <PERSON><PERSON><PERSON> (12 Princes) 
3. Hamitic Nations (Cush, Mizraim, Canaan, Put)
4. Japhethic Nations (7 sons with their branches)
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
import time

def update_biblical_nations():
    """Update biblical nations with proper biblical ancestral groupings."""
    print("📜 UPDATING BIBLICAL NATIONS WITH BIBLICAL ANCESTRAL GROUPINGS")
    print("=" * 70)
    print("Implementing proper biblical tribal system as specified in KJV Bible")
    
    try:
        current_time = int(time.time())
        
        # First, let's add any missing Edom dukes
        print("\n🏔️ UPDATING EDOM DUKES (Genesis 36:40-43)")
        print("-" * 50)
        
        missing_edom_dukes = [
            {
                'nation_code': 'TI',
                'nation_name': '<PERSON><PERSON>',
                'tribe_name': '<PERSON>',
                'nation_type': 'witness',
                'flag_symbol': '🏛️',
                'description': 'Duke of Edom, the restraint'
            },
            {
                'nation_code': 'AL',
                'nation_name': 'Alvah',
                'tribe_name': 'Duke Alvah',
                'nation_type': 'witness',
                'flag_symbol': '⚡',
                'description': 'Duke of Edom, the sublime'
            },
            {
                'nation_code': 'JT',
                'nation_name': 'Jetheth',
                'tribe_name': 'Duke Jetheth',
                'nation_type': 'witness',
                'flag_symbol': '🔨',
                'description': 'Duke of Edom, the nail'
            },
            {
                'nation_code': 'AO',
                'nation_name': 'Aholibamah',
                'tribe_name': 'Duke Aholibamah',
                'nation_type': 'witness',
                'flag_symbol': '⛺',
                'description': 'Duke of Edom, tent of the high place'
            },
            {
                'nation_code': 'EL',
                'nation_name': 'Elah',
                'tribe_name': 'Duke Elah',
                'nation_type': 'witness',
                'flag_symbol': '🌳',
                'description': 'Duke of Edom, the oak'
            },
            {
                'nation_code': 'PI',
                'nation_name': 'Pinon',
                'tribe_name': 'Duke Pinon',
                'nation_type': 'witness',
                'flag_symbol': '💎',
                'description': 'Duke of Edom, the pearl'
            },
            {
                'nation_code': 'MB',
                'nation_name': 'Mibzar',
                'tribe_name': 'Duke Mibzar',
                'nation_type': 'witness',
                'flag_symbol': '🏰',
                'description': 'Duke of Edom, the fortress'
            },
            {
                'nation_code': 'MG',
                'nation_name': 'Magdiel',
                'tribe_name': 'Duke Magdiel',
                'nation_type': 'witness',
                'flag_symbol': '🗡️',
                'description': 'Duke of Edom, God is noble'
            },
            {
                'nation_code': 'IR',
                'nation_name': 'Iram',
                'tribe_name': 'Duke Iram',
                'nation_type': 'witness',
                'flag_symbol': '🏙️',
                'description': 'Duke of Edom, the city'
            }
        ]
        
        # Add missing Edom dukes
        for duke in missing_edom_dukes:
            existing = db.query_one("SELECT nation_code FROM biblical_nations WHERE nation_code = ?", (duke['nation_code'],))
            if not existing:
                db.execute("""
                    INSERT INTO biblical_nations (
                        nation_code, nation_name, tribe_name, nation_type,
                        flag_symbol, description
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    duke["nation_code"], duke["nation_name"], duke["tribe_name"],
                    duke["nation_type"], duke["flag_symbol"], duke["description"]
                ))
                print(f"   ✅ Added {duke['nation_name']} ({duke['nation_code']})")
            else:
                print(f"   ⚠️  {duke['nation_name']} already exists")
        
        # Now add Japhethic nations
        print("\n🌍 ADDING JAPHETHIC NATIONS (Genesis 10:2-4)")
        print("-" * 50)
        
        japhethic_nations = [
            # The 7 sons of Japheth
            {
                'nation_code': 'GO',
                'nation_name': 'Gomer',
                'tribe_name': 'House of Gomer',
                'nation_type': 'witness',
                'flag_symbol': '🏔️',
                'description': 'Son of Japheth, father of northern peoples'
            },
            {
                'nation_code': 'MO',
                'nation_name': 'Magog',
                'tribe_name': 'House of Magog',
                'nation_type': 'witness',
                'flag_symbol': '🗻',
                'description': 'Son of Japheth, the land of Gog'
            },
            {
                'nation_code': 'MD',
                'name': 'Madai',
                'full_name': 'House of Madai',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🏺',
                'description': 'Son of Japheth, the Medes',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:2'
            },
            {
                'nation_code': 'JV',
                'name': 'Javan',
                'full_name': 'House of Javan',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🌊',
                'description': 'Son of Japheth, father of the Greeks',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:2'
            },
            {
                'nation_code': 'TB',
                'name': 'Tubal',
                'full_name': 'House of Tubal',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '⚒️',
                'description': 'Son of Japheth, the metalworkers',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:2'
            },
            {
                'nation_code': 'ME',
                'name': 'Meshech',
                'full_name': 'House of Meshech',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🏹',
                'description': 'Son of Japheth, the archers',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:2'
            },
            {
                'nation_code': 'TR',
                'name': 'Tiras',
                'full_name': 'House of Tiras',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '⚓',
                'description': 'Son of Japheth, the sea peoples',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:2'
            },
            # Sons of Gomer
            {
                'nation_code': 'AS',
                'name': 'Ashkenaz',
                'full_name': 'House of Ashkenaz',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🔥',
                'description': 'Son of Gomer, the fire spreaders',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:3'
            },
            {
                'nation_code': 'RI',
                'name': 'Riphath',
                'full_name': 'House of Riphath',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🏔️',
                'description': 'Son of Gomer, the mountain dwellers',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:3'
            },
            {
                'nation_code': 'TO',
                'name': 'Togarmah',
                'full_name': 'House of Togarmah',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🐎',
                'description': 'Son of Gomer, the horsemen',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:3'
            },
            # Sons of Javan
            {
                'nation_code': 'EL',
                'name': 'Elishah',
                'full_name': 'House of Elishah',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🏝️',
                'description': 'Son of Javan, the island peoples',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:4'
            },
            {
                'nation_code': 'TA',
                'name': 'Tarshish',
                'full_name': 'House of Tarshish',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🚢',
                'description': 'Son of Javan, the merchants of the sea',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:4'
            },
            {
                'nation_code': 'KI',
                'name': 'Kittim',
                'full_name': 'House of Kittim',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🏛️',
                'description': 'Son of Javan, the bruisers',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:4'
            },
            {
                'nation_code': 'DO',
                'name': 'Dodanim',
                'full_name': 'House of Dodanim',
                'type': 'witness',
                'category': 'japheth',
                'symbol': '🌊',
                'description': 'Son of Javan, the leaders',
                'founding_status': 'genesis_sealed',
                'governance_weight': 0,
                'biblical_reference': 'Genesis 10:4'
            }
        ]
        
        # Add Japhethic nations
        for nation in japhethic_nations:
            existing = db.query_one("SELECT nation_code FROM biblical_nations WHERE nation_code = ?", (nation['nation_code'],))
            if not existing:
                db.execute("""
                    INSERT INTO biblical_nations (
                        nation_code, name, full_name, type, category,
                        symbol, description, founding_status, governance_weight,
                        biblical_reference, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    nation["nation_code"], nation["name"], nation["full_name"],
                    nation["type"], nation["category"], nation["symbol"],
                    nation["description"], nation["founding_status"],
                    nation["governance_weight"], nation["biblical_reference"],
                    current_time
                ))
                print(f"   ✅ Added {nation['name']} ({nation['nation_code']})")
            else:
                print(f"   ⚠️  {nation['name']} already exists")
        
        print("\n📊 UPDATING ANCESTRAL GROUPINGS")
        print("-" * 50)
        
        # Update ancestral groupings for biblical classification
        ancestral_updates = [
            # Edom (12 Dukes)
            ("UPDATE biblical_nations SET ancestral_group = 'Edom', ancestral_description = 'The 12 Dukes of Edom, descendants of Esau' WHERE category = 'edom'", "Edom"),
            # Ishmael (12 Princes) 
            ("UPDATE biblical_nations SET ancestral_group = 'Ishmael', ancestral_description = 'The 12 Princes of Ishmael, sons of Abraham' WHERE category = 'ishmael'", "Ishmael"),
            # Hamitic Nations
            ("UPDATE biblical_nations SET ancestral_group = 'Hamitic Nations', ancestral_description = 'Sons of Ham: Cush, Mizraim, Put, and Canaan' WHERE category = 'hamite'", "Hamitic Nations"),
            # Japhethic Nations
            ("UPDATE biblical_nations SET ancestral_group = 'Japhethic Nations', ancestral_description = 'Sons of Japheth and their descendants' WHERE category = 'japheth'", "Japhethic Nations"),
        ]
        
        for update_query, group_name in ancestral_updates:
            result = db.execute(update_query)
            print(f"   ✅ Updated {group_name} ancestral grouping")
        
        # Verify the updates
        print("\n🎉 VERIFICATION")
        print("-" * 50)
        
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as count, category
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group, category
            ORDER BY ancestral_group
        """)
        
        print("📋 Biblical Ancestral Groups:")
        for group in ancestral_groups:
            print(f"   {group['ancestral_group']:20s}: {group['count']} nations ({group['category']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = update_biblical_nations()
    if success:
        print("\n✅ Biblical nations update completed successfully!")
    else:
        print("\n❌ Biblical nations update failed!")
