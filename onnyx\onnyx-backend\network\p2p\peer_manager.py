"""
ONNYX P2P Peer Manager
Handles peer discovery, connection management, and network communication
"""

import asyncio
import json
import time
import uuid
import websockets
import logging
from typing import Dict, List, Set, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class NodeType(Enum):
    BOOTSTRAP = "bootstrap"
    TRIBAL_ELDER = "tribal_elder"
    COMMUNITY_LIGHT = "community_light"
    FULL_NODE = "full_node"

class PeerStatus(Enum):
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    BANNED = "banned"

@dataclass
class PeerInfo:
    peer_id: str
    address: str
    port: int
    node_type: NodeType
    tribal_code: Optional[str] = None
    voting_weight: int = 0
    reputation: int = 100
    last_seen: int = 0
    status: PeerStatus = PeerStatus.DISCONNECTED
    covenant_tier: int = 0

    def to_dict(self):
        return {
            'peer_id': self.peer_id,
            'address': self.address,
            'port': self.port,
            'node_type': self.node_type.value,
            'tribal_code': self.tribal_code,
            'voting_weight': self.voting_weight,
            'reputation': self.reputation,
            'last_seen': self.last_seen,
            'status': self.status.value,
            'covenant_tier': self.covenant_tier
        }

@dataclass
class NetworkMessage:
    message_type: str
    sender_id: str
    recipient_id: Optional[str]
    data: dict
    timestamp: int
    signature: Optional[str] = None

    def to_json(self):
        return json.dumps({
            'type': self.message_type,
            'sender': self.sender_id,
            'recipient': self.recipient_id,
            'data': self.data,
            'timestamp': self.timestamp,
            'signature': self.signature
        })

class PeerManager:
    """Manages P2P network connections and peer discovery for ONNYX covenant blockchain"""

    def __init__(self, node_id: str, node_type: NodeType, port: int = 8765):
        self.node_id = node_id
        self.node_type = node_type
        self.port = port
        self.peers: Dict[str, PeerInfo] = {}
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.message_handlers: Dict[str, Callable] = {}
        self.server = None
        self.running = False

        # Biblical covenant network parameters
        self.max_peers = 144  # 12 tribes * 12 (biblical significance)
        self.min_elder_nodes = 8  # Minimum 2/3 of 12 tribal elders
        self.heartbeat_interval = 30  # seconds

        # Register default message handlers
        self._register_default_handlers()

    def _register_default_handlers(self):
        """Register default message handlers for network communication"""
        self.message_handlers.update({
            'peer_discovery': self._handle_peer_discovery,
            'peer_announcement': self._handle_peer_announcement,
            'heartbeat': self._handle_heartbeat,
            'block_proposal': self._handle_block_proposal,
            'voice_scroll_vote': self._handle_voice_scroll_vote,
            'covenant_verification': self._handle_covenant_verification
        })

    async def start_server(self):
        """Start the P2P server for incoming connections"""
        try:
            self.server = await websockets.serve(
                self._handle_connection,
                "0.0.0.0",
                self.port
            )
            self.running = True
            logger.info(f"P2P server started on port {self.port} for node {self.node_id}")

            # Start background tasks
            asyncio.create_task(self._heartbeat_loop())
            asyncio.create_task(self._peer_maintenance_loop())

        except Exception as e:
            logger.error(f"Failed to start P2P server: {e}")
            raise

    async def stop_server(self):
        """Stop the P2P server and close all connections"""
        self.running = False
        if self.server:
            self.server.close()
            await self.server.wait_closed()

        # Close all peer connections
        for connection in self.connections.values():
            await connection.close()

        self.connections.clear()
        logger.info(f"P2P server stopped for node {self.node_id}")

    async def connect_to_peer(self, address: str, port: int) -> bool:
        """Connect to a peer node"""
        try:
            uri = f"ws://{address}:{port}"
            websocket = await websockets.connect(uri)

            # Send peer announcement
            announcement = NetworkMessage(
                message_type="peer_announcement",
                sender_id=self.node_id,
                recipient_id=None,
                data={
                    "node_type": self.node_type.value,
                    "port": self.port,
                    "covenant_tier": getattr(self, 'covenant_tier', 0)
                },
                timestamp=int(time.time())
            )

            await websocket.send(announcement.to_json())

            # Handle incoming messages from this peer
            asyncio.create_task(self._handle_peer_messages(websocket))

            logger.info(f"Connected to peer at {address}:{port}")
            return True

        except Exception as e:
            # Only log as warning for connection refused (peer not started yet)
            # This is normal during sequential node deployment
            if "refused" in str(e).lower() or "1225" in str(e):
                logger.debug(f"Peer not available yet at {address}:{port}: {e}")
            else:
                logger.error(f"Failed to connect to peer {address}:{port}: {e}")
            return False

    async def _handle_connection(self, websocket, path=None):
        """Handle incoming peer connections"""
        peer_id = None
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    msg = NetworkMessage(
                        message_type=data['type'],
                        sender_id=data['sender'],
                        recipient_id=data.get('recipient'),
                        data=data['data'],
                        timestamp=data['timestamp'],
                        signature=data.get('signature')
                    )

                    # Store peer connection
                    if not peer_id:
                        peer_id = msg.sender_id
                        self.connections[peer_id] = websocket

                    # Route message to appropriate handler
                    await self._route_message(msg)

                except json.JSONDecodeError:
                    logger.warning(f"Received invalid JSON from peer {peer_id}")
                except Exception as e:
                    logger.error(f"Error handling message from peer {peer_id}: {e}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Peer {peer_id} disconnected")
        except Exception as e:
            logger.error(f"Connection error with peer {peer_id}: {e}")
        finally:
            if peer_id and peer_id in self.connections:
                del self.connections[peer_id]
                if peer_id in self.peers:
                    self.peers[peer_id].status = PeerStatus.DISCONNECTED

    async def _handle_peer_messages(self, websocket):
        """Handle messages from a connected peer"""
        try:
            async for message in websocket:
                data = json.loads(message)
                msg = NetworkMessage(
                    message_type=data['type'],
                    sender_id=data['sender'],
                    recipient_id=data.get('recipient'),
                    data=data['data'],
                    timestamp=data['timestamp'],
                    signature=data.get('signature')
                )
                await self._route_message(msg)
        except Exception as e:
            logger.error(f"Error handling peer messages: {e}")

    async def _route_message(self, message: NetworkMessage):
        """Route incoming messages to appropriate handlers"""
        handler = self.message_handlers.get(message.message_type)
        if handler:
            try:
                await handler(message)
            except Exception as e:
                logger.error(f"Error in message handler for {message.message_type}: {e}")
        else:
            logger.warning(f"No handler for message type: {message.message_type}")

    async def broadcast_message(self, message: NetworkMessage, exclude_peers: Set[str] = None):
        """Broadcast a message to all connected peers"""
        exclude_peers = exclude_peers or set()

        for peer_id, connection in self.connections.items():
            if peer_id not in exclude_peers:
                try:
                    await connection.send(message.to_json())
                except Exception as e:
                    logger.error(f"Failed to send message to peer {peer_id}: {e}")

    async def send_to_peer(self, peer_id: str, message: NetworkMessage) -> bool:
        """Send a message to a specific peer"""
        if peer_id in self.connections:
            try:
                await self.connections[peer_id].send(message.to_json())
                return True
            except Exception as e:
                logger.error(f"Failed to send message to peer {peer_id}: {e}")
        return False

    # Message Handlers
    async def _handle_peer_discovery(self, message: NetworkMessage):
        """Handle peer discovery requests"""
        # Send back our peer list
        peer_list = [peer.to_dict() for peer in self.peers.values()
                    if peer.status == PeerStatus.CONNECTED]

        response = NetworkMessage(
            message_type="peer_list",
            sender_id=self.node_id,
            recipient_id=message.sender_id,
            data={"peers": peer_list},
            timestamp=int(time.time())
        )

        await self.send_to_peer(message.sender_id, response)

    async def _handle_peer_announcement(self, message: NetworkMessage):
        """Handle peer announcements"""
        peer_id = message.sender_id
        data = message.data

        # Create or update peer info
        peer_info = PeerInfo(
            peer_id=peer_id,
            address="",  # Will be filled from connection
            port=data.get('port', 8765),
            node_type=NodeType(data.get('node_type', 'full_node')),
            tribal_code=data.get('tribal_code'),
            voting_weight=data.get('voting_weight', 0),
            covenant_tier=data.get('covenant_tier', 0),
            last_seen=int(time.time()),
            status=PeerStatus.CONNECTED
        )

        self.peers[peer_id] = peer_info
        logger.info(f"Peer {peer_id} announced as {peer_info.node_type.value}")

    async def _handle_heartbeat(self, message: NetworkMessage):
        """Handle heartbeat messages"""
        peer_id = message.sender_id
        if peer_id in self.peers:
            self.peers[peer_id].last_seen = int(time.time())
            self.peers[peer_id].status = PeerStatus.CONNECTED

    async def _handle_block_proposal(self, message: NetworkMessage):
        """Handle block proposals (to be implemented with consensus)"""
        logger.info(f"Received block proposal from {message.sender_id}")
        # Will be implemented in consensus module

    async def _handle_voice_scroll_vote(self, message: NetworkMessage):
        """Handle voice scroll voting messages"""
        logger.info(f"Received voice scroll vote from {message.sender_id}")
        # Will be implemented in governance module

    async def _handle_covenant_verification(self, message: NetworkMessage):
        """Handle covenant compliance verification"""
        logger.info(f"Received covenant verification from {message.sender_id}")
        # Will be implemented in covenant compliance module

    # Background Tasks
    async def _heartbeat_loop(self):
        """Send periodic heartbeats to all connected peers"""
        while self.running:
            heartbeat = NetworkMessage(
                message_type="heartbeat",
                sender_id=self.node_id,
                recipient_id=None,
                data={"timestamp": int(time.time())},
                timestamp=int(time.time())
            )

            await self.broadcast_message(heartbeat)
            await asyncio.sleep(self.heartbeat_interval)

    async def _peer_maintenance_loop(self):
        """Maintain peer connections and remove stale peers"""
        while self.running:
            current_time = int(time.time())
            stale_peers = []

            for peer_id, peer in self.peers.items():
                if current_time - peer.last_seen > self.heartbeat_interval * 3:
                    stale_peers.append(peer_id)

            for peer_id in stale_peers:
                logger.info(f"Removing stale peer {peer_id}")
                if peer_id in self.connections:
                    await self.connections[peer_id].close()
                    del self.connections[peer_id]
                del self.peers[peer_id]

            await asyncio.sleep(self.heartbeat_interval)

    # Utility Methods
    def get_connected_peers(self) -> List[PeerInfo]:
        """Get list of currently connected peers"""
        return [peer for peer in self.peers.values()
                if peer.status == PeerStatus.CONNECTED]

    def get_tribal_elders(self) -> List[PeerInfo]:
        """Get list of connected tribal elder nodes"""
        return [peer for peer in self.peers.values()
                if peer.node_type == NodeType.TRIBAL_ELDER and
                peer.status == PeerStatus.CONNECTED]

    def get_network_stats(self) -> dict:
        """Get network statistics"""
        connected_peers = self.get_connected_peers()
        tribal_elders = self.get_tribal_elders()

        return {
            "total_peers": len(connected_peers),
            "tribal_elders": len(tribal_elders),
            "community_nodes": len([p for p in connected_peers
                                  if p.node_type == NodeType.COMMUNITY_LIGHT]),
            "total_voting_weight": sum(p.voting_weight for p in tribal_elders),
            "network_health": "healthy" if len(tribal_elders) >= self.min_elder_nodes else "degraded"
        }

    def register_message_handler(self, message_type: str, handler: Callable):
        """Register a custom message handler"""
        self.message_handlers[message_type] = handler
