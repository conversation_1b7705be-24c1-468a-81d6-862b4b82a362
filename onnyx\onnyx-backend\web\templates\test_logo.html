<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo Test - ONNYX</title>
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .logo-test {
            margin: 20px;
            padding: 20px;
            border: 2px solid #00d4ff;
            border-radius: 10px;
        }
        .logo-test img {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>ONNYX Logo Loading Test</h1>
        
        <div class="logo-test">
            <h2>Test 1: Direct Image Load</h2>
            <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}" 
                 alt="ONNYX Logo Direct"
                 onload="console.log('Direct logo loaded'); document.getElementById('status1').textContent = 'SUCCESS: Logo loaded';"
                 onerror="console.log('Direct logo failed'); document.getElementById('status1').textContent = 'ERROR: Logo failed to load';">
            <p id="status1">Loading...</p>
        </div>
        
        <div class="logo-test">
            <h2>Test 2: Absolute URL</h2>
            <img src="/static/images/onnyx_logo.png" 
                 alt="ONNYX Logo Absolute"
                 onload="console.log('Absolute logo loaded'); document.getElementById('status2').textContent = 'SUCCESS: Logo loaded';"
                 onerror="console.log('Absolute logo failed'); document.getElementById('status2').textContent = 'ERROR: Logo failed to load';">
            <p id="status2">Loading...</p>
        </div>
        
        <div class="logo-test">
            <h2>Test 3: File Info</h2>
            <p>Expected URL: {{ url_for('static', filename='images/onnyx_logo.png') }}</p>
            <p>File size should be: ~1.5MB</p>
        </div>
        
        <div class="logo-test">
            <h2>Test 4: Network Check</h2>
            <button onclick="checkLogo()">Check Logo URL</button>
            <p id="network-status">Click button to test</p>
        </div>
    </div>
    
    <script>
        function checkLogo() {
            const url = "{{ url_for('static', filename='images/onnyx_logo.png') }}";
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        document.getElementById('network-status').textContent = 
                            `SUCCESS: Logo accessible (${response.status}) - Size: ${response.headers.get('content-length')} bytes`;
                    } else {
                        document.getElementById('network-status').textContent = 
                            `ERROR: HTTP ${response.status} - ${response.statusText}`;
                    }
                })
                .catch(error => {
                    document.getElementById('network-status').textContent = 
                        `ERROR: Network error - ${error.message}`;
                });
        }
        
        // Auto-check on page load
        setTimeout(checkLogo, 1000);
    </script>
</body>
</html>
