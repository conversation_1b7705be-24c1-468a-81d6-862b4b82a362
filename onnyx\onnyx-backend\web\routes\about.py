"""
About Page Routes

Information about the ONNYX platform, its mission, and biblical foundations.
"""

from flask import Blueprint, render_template
import logging

logger = logging.getLogger(__name__)

about_bp = Blueprint('about', __name__, url_prefix='/about')

@about_bp.route('/')
def index():
    """Main about page"""
    try:
        # Platform information
        platform_info = {
            'name': 'ONNYX',
            'version': '1.0.0-beta',
            'description': 'A biblical covenant blockchain platform for the tribes of Israel and witness nations',
            'mission': 'To create a decentralized platform that operates according to biblical principles and serves the covenant community',
            'founded': '2024',
            'creator': 'Jedidiah Israel of the Tribe of Benjamin'
        }
        
        # Biblical foundations
        biblical_foundations = [
            {
                'principle': 'Tribal Governance',
                'description': 'Based on the 12 tribes of Israel with proper tribal representation',
                'scripture': 'Numbers 1:4 - "And with you there shall be a man of every tribe"'
            },
            {
                'principle': 'Sabbath Observance',
                'description': 'Mining and certain operations respect the Sabbath cycle',
                'scripture': 'Exodus 20:8 - "Remember the sabbath day, to keep it holy"'
            },
            {
                'principle': 'Jubilee Economics',
                'description': 'Debt forgiveness and wealth redistribution every 50 years',
                'scripture': 'Leviticus 25:10 - "And ye shall hallow the fiftieth year"'
            },
            {
                'principle': 'Firstfruits Offerings',
                'description': 'Community support through firstfruits contributions',
                'scripture': 'Proverbs 3:9 - "Honour the LORD with thy substance, and with the firstfruits"'
            },
            {
                'principle': 'Righteous Deeds',
                'description': 'Reputation system based on righteous actions',
                'scripture': 'Matthew 5:16 - "Let your light so shine before men"'
            }
        ]
        
        # Technical features
        technical_features = [
            {
                'feature': 'Blockchain Technology',
                'description': 'Secure, decentralized ledger for all transactions and governance'
            },
            {
                'feature': 'Smart Contracts',
                'description': 'Automated biblical law enforcement and covenant compliance'
            },
            {
                'feature': 'Tribal Mining',
                'description': 'Mining operations organized by tribal affiliation and biblical principles'
            },
            {
                'feature': 'Identity Verification',
                'description': 'Multi-tier verification system with Gate Keeper oversight'
            },
            {
                'feature': 'Biblical Tokenomics',
                'description': 'Token economics based on biblical principles of stewardship'
            }
        ]
        
        return render_template('about/index.html',
                             platform_info=platform_info,
                             biblical_foundations=biblical_foundations,
                             technical_features=technical_features)
        
    except Exception as e:
        logger.error(f"About page error: {e}")
        return render_template('about/index.html',
                             platform_info={'name': 'ONNYX', 'description': 'Biblical Covenant Blockchain'},
                             biblical_foundations=[],
                             technical_features=[])

@about_bp.route('/mission')
def mission():
    """Detailed mission statement"""
    return render_template('about/mission.html')

@about_bp.route('/biblical-foundation')
def biblical_foundation():
    """Detailed biblical foundation explanation"""
    return render_template('about/biblical_foundation.html')

@about_bp.route('/technology')
def technology():
    """Technical architecture overview"""
    return render_template('about/technology.html')

@about_bp.route('/team')
def team():
    """Team and leadership information"""
    team_members = [
        {
            'name': 'Jedidiah Israel',
            'title': 'Platform Creator & Chief Architect',
            'tribe': 'Benjamin',
            'description': 'Founder and visionary behind the ONNYX platform',
            'role': 'System Administrator'
        }
    ]
    
    return render_template('about/team.html', team_members=team_members)
