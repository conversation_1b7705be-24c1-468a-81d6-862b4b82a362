{% extends "base.html" %}

{% block title %}Create Your Sela Business | ONNYX Platform{% endblock %}

{% block description %}Create your covenant-compliant business entity on the ONNYX platform with biblical principles and tribal integration.{% endblock %}

{% block head %}
<style>
    .sela-creation-hero {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .business-category-card {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.05) 0%, rgba(154, 0, 255, 0.05) 100%);
        border: 1px solid rgba(0, 255, 247, 0.1);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .business-category-card:hover {
        border-color: rgba(0, 255, 247, 0.3);
        transform: translateY(-2px);
    }
    
    .business-category-card.selected {
        border-color: rgba(0, 255, 247, 0.5);
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.15) 0%, rgba(154, 0, 255, 0.15) 100%);
    }
    
    .covenant-principles {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
        border: 1px solid rgba(255, 215, 0, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .form-section {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="sela-creation-hero p-8 rounded-2xl mb-8 text-center">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                    🏢 Create Your Sela Business
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Establish your covenant-compliant business entity and join the biblical economy
            </p>
            <div class="flex flex-wrap justify-center gap-4 text-sm">
                <span class="px-4 py-2 bg-cyber-cyan bg-opacity-20 rounded-full text-cyber-cyan">
                    Tribal Integration
                </span>
                <span class="px-4 py-2 bg-cyber-purple bg-opacity-20 rounded-full text-cyber-purple">
                    Biblical Principles
                </span>
                <span class="px-4 py-2 bg-cyber-green bg-opacity-20 rounded-full text-cyber-green">
                    Covenant Commerce
                </span>
            </div>
        </div>

        <!-- Covenant Principles -->
        <div class="covenant-principles p-6 rounded-xl mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-yellow-400 mb-4">📖 Biblical Business Principles</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="flex items-start space-x-3">
                    <div class="text-yellow-400">✓</div>
                    <div>
                        <div class="font-semibold text-yellow-300">Sabbath Observance</div>
                        <div class="text-gray-300">Honor the Sabbath in business operations</div>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="text-yellow-400">✓</div>
                    <div>
                        <div class="font-semibold text-yellow-300">Firstfruits Giving</div>
                        <div class="text-gray-300">Contribute to community support pools</div>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="text-yellow-400">✓</div>
                    <div>
                        <div class="font-semibold text-yellow-300">Fair Dealing</div>
                        <div class="text-gray-300">Honest weights and measures</div>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="text-yellow-400">✓</div>
                    <div>
                        <div class="font-semibold text-yellow-300">Jubilee Compliance</div>
                        <div class="text-gray-300">Participate in debt forgiveness cycles</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Creation Form -->
        <form method="POST" action="{{ url_for('sela.create_submit') }}" class="space-y-8">
            <!-- Business Information -->
            <div class="form-section p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">Business Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-semibold text-gray-300 mb-2">Business Name *</label>
                        <input type="text" 
                               name="sela_name" 
                               required
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-cyber-cyan focus:outline-none"
                               placeholder="Enter your business name">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-300 mb-2">Business Address</label>
                        <input type="text" 
                               name="address"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-cyber-cyan focus:outline-none"
                               placeholder="Business address (optional)">
                    </div>
                </div>
                
                <div class="mt-6">
                    <label class="block text-sm font-semibold text-gray-300 mb-2">Business Description *</label>
                    <textarea name="description" 
                              required
                              rows="4"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-cyber-cyan focus:outline-none"
                              placeholder="Describe your business, its mission, and how it aligns with biblical principles"></textarea>
                </div>
            </div>

            <!-- Business Category Selection -->
            <div class="form-section p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-6">Business Category</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for category in business_categories %}
                    <div class="business-category-card p-4 rounded-lg" onclick="selectCategory('{{ category.id }}')">
                        <input type="radio" 
                               name="category" 
                               value="{{ category.id }}" 
                               id="category_{{ category.id }}" 
                               class="hidden" 
                               required>
                        <label for="category_{{ category.id }}" class="cursor-pointer">
                            <div class="font-semibold text-white mb-2">{{ category.name }}</div>
                            <div class="text-sm text-gray-400">{{ category.description }}</div>
                        </label>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Tribal Integration -->
            <div class="form-section p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-6">Tribal Integration</h3>
                <div class="bg-gray-800 p-4 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="text-2xl">🏛️</div>
                        <div>
                            <div class="font-semibold text-white">Your Tribal Affiliation</div>
                            <div class="text-gray-300">
                                Tribe of {{ current_user.tribal_affiliation.title() if current_user.tribal_affiliation else 'Unknown' }}
                            </div>
                            <div class="text-sm text-gray-400 mt-1">
                                Your Sela will be integrated with your tribal governance and mining operations
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Covenant Agreement -->
            <div class="form-section p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-yellow-400 mb-6">Covenant Agreement</h3>
                <div class="space-y-4">
                    <div class="bg-gray-800 p-4 rounded-lg">
                        <div class="text-sm text-gray-300 space-y-2">
                            <p>By creating a Sela business on the ONNYX platform, I agree to:</p>
                            <ul class="list-disc list-inside space-y-1 ml-4">
                                <li>Operate according to biblical business principles</li>
                                <li>Honor the Sabbath in business operations</li>
                                <li>Participate in firstfruits giving and community support</li>
                                <li>Maintain honest and fair business practices</li>
                                <li>Comply with jubilee year debt forgiveness cycles</li>
                                <li>Support tribal governance and community decisions</li>
                                <li>Contribute to the covenant economy through biblical tokenomics</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3">
                        <input type="checkbox" 
                               name="covenant_agreement" 
                               id="covenant_agreement" 
                               required
                               class="mt-1 w-4 h-4 text-cyber-cyan bg-gray-800 border-gray-600 rounded focus:ring-cyber-cyan">
                        <label for="covenant_agreement" class="text-sm text-gray-300">
                            I accept the covenant agreement and commit to operating my Sela business according to biblical principles *
                        </label>
                    </div>
                </div>
            </div>

            <!-- Submit Section -->
            <div class="text-center space-y-4">
                <button type="submit" 
                        class="glass-button-primary px-12 py-4 rounded-xl font-orbitron font-bold text-lg">
                    🏢 Create Sela Business
                </button>
                
                <div class="text-center">
                    <a href="{{ url_for('dashboard.overview') }}" 
                       class="text-gray-400 hover:text-white transition-colors">
                        ← Return to Dashboard
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function selectCategory(categoryId) {
    // Remove selected class from all cards
    document.querySelectorAll('.business-category-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Add selected class to clicked card
    event.currentTarget.classList.add('selected');
    
    // Select the radio button
    document.getElementById('category_' + categoryId).checked = true;
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const requiredFields = ['sela_name', 'description', 'category', 'covenant_agreement'];
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (!field || (field.type === 'checkbox' && !field.checked) || 
                (field.type !== 'checkbox' && !field.value.trim())) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields and accept the covenant agreement.');
        }
    });
});
</script>
{% endblock %}
