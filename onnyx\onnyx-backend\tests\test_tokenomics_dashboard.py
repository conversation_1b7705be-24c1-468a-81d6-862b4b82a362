#!/usr/bin/env python3
"""
Test Biblical Tokenomics Dashboard
"""

import requests
import sys

def test_tokenomics_dashboard():
    """Test the biblical tokenomics dashboard"""
    print("Testing biblical tokenomics dashboard...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/tokenomics/dashboard', timeout=10)
        
        if response.status_code == 200:
            print("✅ Biblical tokenomics dashboard loaded successfully")
            print(f"   Status: {response.status_code}")
            print(f"   Content length: {len(response.content)} bytes")
            
            # Check for error messages in content
            content = response.text.lower()
            if 'error' in content and 'loading' in content:
                print("⚠️ Page loaded but contains error messages")
                return False
            else:
                print("✅ No error messages detected")
                return True
                
        elif response.status_code == 302:
            print("🔄 Dashboard redirected (likely requires authentication)")
            print(f"   Redirect to: {response.headers.get('Location', 'Unknown')}")
            return True
            
        elif response.status_code == 403:
            print("🔒 Access forbidden (authentication required)")
            return True
            
        elif response.status_code == 404:
            print("❌ Dashboard not found (404)")
            return False
            
        elif response.status_code == 500:
            print("❌ Server error (500) - check application logs")
            return False
            
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server")
        print("   Make sure Flask app is running on http://127.0.0.1:5000")
        return False
        
    except Exception as e:
        print(f"❌ Error testing dashboard: {e}")
        return False

if __name__ == "__main__":
    success = test_tokenomics_dashboard()
    sys.exit(0 if success else 1)
