"""
Onnyx Sela Model

This module defines the Sela model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.sela")

class Sela(BaseModel):
    """
    Sela model for the Onnyx blockchain.

    A Sela represents a business or organization in the Onnyx ecosystem.
    """

    TABLE_NAME = "selas"
    PRIMARY_KEY = "sela_id"

    def __init__(
        self,
        sela_id: str,
        name: str,
        founder_id: str,
        sela_type: str,
        token_id: Optional[str] = None,
        services: Optional[List[str]] = None,
        roles: Optional[Dict[str, str]] = None,
        members: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        created_at: Optional[int] = None
    ):
        """
        Initialize a Sela.

        Args:
            sela_id: The Sela ID
            name: The Sela name
            founder_id: The founder's identity ID
            sela_type: The Sela type (e.g., "BUSINESS", "NONPROFIT", "COMMUNITY")
            token_id: The token ID associated with the Sela (optional)
            services: The services offered by the Sela (optional)
            roles: The roles of members in the Sela (optional)
            members: The members of the Sela (optional)
            metadata: Additional metadata for the Sela (optional)
            created_at: The timestamp when the Sela was created (optional)
        """
        self.sela_id = sela_id
        self.name = name
        self.founder_id = founder_id
        self.sela_type = sela_type
        self.token_id = token_id
        self.services = services or []
        self.roles = roles or {}
        self.members = members or []
        self.metadata = metadata or {}
        self.created_at = created_at or int(time.time())

    @classmethod
    def create_table(cls) -> None:
        """Create the Sela table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            founder_id TEXT NOT NULL,
            sela_type TEXT NOT NULL,
            token_id TEXT,
            services TEXT,
            roles TEXT,
            members TEXT,
            metadata TEXT,
            created_at INTEGER
        )
        """)

        conn.commit()
        conn.close()

    @classmethod
    def create(
        cls,
        sela_id: str,
        name: str,
        founder_id: str,
        sela_type: str,
        token_id: Optional[str] = None,
        services: Optional[List[str]] = None,
        roles: Optional[Dict[str, str]] = None,
        members: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        created_at: Optional[int] = None
    ) -> "Sela":
        """
        Create a new Sela.

        Args:
            sela_id: The Sela ID
            name: The Sela name
            founder_id: The founder's identity ID
            sela_type: The Sela type (e.g., "BUSINESS", "NONPROFIT", "COMMUNITY")
            token_id: The token ID associated with the Sela (optional)
            services: The services offered by the Sela (optional)
            roles: The roles of members in the Sela (optional)
            members: The members of the Sela (optional)
            metadata: Additional metadata for the Sela (optional)
            created_at: The timestamp when the Sela was created (optional)

        Returns:
            The created Sela
        """
        # Create the table if it doesn't exist
        cls.create_table()

        # Create the Sela
        sela = cls(
            sela_id=sela_id,
            name=name,
            founder_id=founder_id,
            sela_type=sela_type,
            token_id=token_id,
            services=services,
            roles=roles,
            members=members,
            metadata=metadata,
            created_at=created_at
        )

        # Save the Sela to the database
        sela.save()

        return sela

    def save(self) -> None:
        """Save the Sela to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            name,
            founder_id,
            sela_type,
            token_id,
            services,
            roles,
            members,
            metadata,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.sela_id,
            self.name,
            self.founder_id,
            self.sela_type,
            self.token_id,
            json.dumps(self.services),
            json.dumps(self.roles),
            json.dumps(self.members),
            json.dumps(self.metadata),
            self.created_at
        ))

        conn.commit()
        conn.close()

    @classmethod
    def get_by_id(cls, sela_id: str) -> Optional["Sela"]:
        """
        Get a Sela by ID.

        Args:
            sela_id: The Sela ID

        Returns:
            The Sela, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            name,
            founder_id,
            sela_type,
            token_id,
            services,
            roles,
            members,
            metadata,
            created_at
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (sela_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return cls(
                sela_id=row[0],
                name=row[1],
                founder_id=row[2],
                sela_type=row[3],
                token_id=row[4],
                services=json.loads(row[5]) if row[5] else [],
                roles=json.loads(row[6]) if row[6] else {},
                members=json.loads(row[7]) if row[7] else [],
                metadata=json.loads(row[8]) if row[8] else {},
                created_at=row[9]
            )

        return None

    @classmethod
    def get_all(cls) -> List["Sela"]:
        """
        Get all Selas.

        Returns:
            A list of all Selas
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            name,
            founder_id,
            sela_type,
            token_id,
            services,
            roles,
            members,
            metadata,
            created_at
        FROM {cls.TABLE_NAME}
        """)

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                sela_id=row[0],
                name=row[1],
                founder_id=row[2],
                sela_type=row[3],
                token_id=row[4],
                services=json.loads(row[5]) if row[5] else [],
                roles=json.loads(row[6]) if row[6] else {},
                members=json.loads(row[7]) if row[7] else [],
                metadata=json.loads(row[8]) if row[8] else {},
                created_at=row[9]
            )
            for row in rows
        ]

    @classmethod
    def find_by_founder(cls, founder_id: str) -> List["Sela"]:
        """
        Find Selas by founder ID.

        Args:
            founder_id: The founder's identity ID

        Returns:
            A list of Selas founded by the identity
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            name,
            founder_id,
            sela_type,
            token_id,
            services,
            roles,
            members,
            metadata,
            created_at
        FROM {cls.TABLE_NAME}
        WHERE founder_id = ?
        """, (founder_id,))

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                sela_id=row[0],
                name=row[1],
                founder_id=row[2],
                sela_type=row[3],
                token_id=row[4],
                services=json.loads(row[5]) if row[5] else [],
                roles=json.loads(row[6]) if row[6] else {},
                members=json.loads(row[7]) if row[7] else [],
                metadata=json.loads(row[8]) if row[8] else {},
                created_at=row[9]
            )
            for row in rows
        ]

    @classmethod
    def find_by_member(cls, member_id: str) -> List["Sela"]:
        """
        Find Selas by member ID.

        Args:
            member_id: The member's identity ID

        Returns:
            A list of Selas that the identity is a member of
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            name,
            founder_id,
            sela_type,
            token_id,
            services,
            roles,
            members,
            metadata,
            created_at
        FROM {cls.TABLE_NAME}
        """)

        rows = cursor.fetchall()
        conn.close()

        # Filter Selas where the identity is a member
        result = []
        for row in rows:
            members = json.loads(row[7]) if row[7] else []
            if member_id in members:
                result.append(cls(
                    sela_id=row[0],
                    name=row[1],
                    founder_id=row[2],
                    sela_type=row[3],
                    token_id=row[4],
                    services=json.loads(row[5]) if row[5] else [],
                    roles=json.loads(row[6]) if row[6] else {},
                    members=members,
                    metadata=json.loads(row[8]) if row[8] else {},
                    created_at=row[9]
                ))
        return result

    @classmethod
    def find_by_token(cls, token_id: str) -> List["Sela"]:
        """
        Find Selas by token ID.

        Args:
            token_id: The token ID

        Returns:
            A list of Selas associated with the token
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()

        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            name,
            founder_id,
            sela_type,
            token_id,
            services,
            roles,
            members,
            metadata,
            created_at
        FROM {cls.TABLE_NAME}
        WHERE token_id = ?
        """, (token_id,))

        rows = cursor.fetchall()
        conn.close()

        return [
            cls(
                sela_id=row[0],
                name=row[1],
                founder_id=row[2],
                sela_type=row[3],
                token_id=row[4],
                services=json.loads(row[5]) if row[5] else [],
                roles=json.loads(row[6]) if row[6] else {},
                members=json.loads(row[7]) if row[7] else [],
                metadata=json.loads(row[8]) if row[8] else {},
                created_at=row[9]
            )
            for row in rows
        ]

    def add_service(self, service: str) -> None:
        """
        Add a service to the Sela.

        Args:
            service: The service to add
        """
        if service not in self.services:
            self.services.append(service)
            self.save()

    def remove_service(self, service: str) -> None:
        """
        Remove a service from the Sela.

        Args:
            service: The service to remove
        """
        if service in self.services:
            self.services.remove(service)
            self.save()

    def add_member(self, identity_id: str, role: str = "MEMBER") -> None:
        """
        Add a member to the Sela.

        Args:
            identity_id: The identity ID to add
            role: The role of the identity in the Sela (default: "MEMBER")
        """
        if identity_id not in self.members:
            self.members.append(identity_id)
            self.roles[identity_id] = role
            self.save()

    def remove_member(self, identity_id: str) -> None:
        """
        Remove a member from the Sela.

        Args:
            identity_id: The identity ID to remove
        """
        if identity_id in self.members:
            self.members.remove(identity_id)
            if identity_id in self.roles:
                del self.roles[identity_id]
            self.save()

    def update_role(self, identity_id: str, role: str) -> None:
        """
        Update the role of a member in the Sela.

        Args:
            identity_id: The identity ID
            role: The new role
        """
        if identity_id in self.members:
            self.roles[identity_id] = role
            self.save()

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Sela to a dictionary.

        Returns:
            The Sela as a dictionary
        """
        return {
            "sela_id": self.sela_id,
            "name": self.name,
            "founder_id": self.founder_id,
            "sela_type": self.sela_type,
            "token_id": self.token_id,
            "services": self.services,
            "roles": self.roles,
            "members": self.members,
            "metadata": self.metadata,
            "created_at": self.created_at
        }
