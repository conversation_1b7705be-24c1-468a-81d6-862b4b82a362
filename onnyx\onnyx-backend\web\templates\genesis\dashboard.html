<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genesis Management - ONNYX</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .genesis-dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .status-card {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(16px);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .status-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .status-complete {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
        }
        
        .status-pending {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
        }
        
        .status-incomplete {
            background: rgba(255, 0, 0, 0.2);
            color: #ff0000;
        }
        
        .action-section {
            margin-top: 32px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
        }
        
        .action-card {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(16px);
        }
        
        .btn-genesis {
            background: linear-gradient(135deg, #00d4ff, #8a2be2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 8px 4px;
        }
        
        .btn-genesis:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }
        
        .btn-genesis:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .elder-form {
            display: grid;
            gap: 16px;
            margin-top: 16px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-input, .form-select {
            padding: 12px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            backdrop-filter: blur(8px);
        }
        
        .elder-list {
            margin-top: 16px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .elder-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            margin-bottom: 8px;
            background: rgba(0, 0, 0, 0.2);
        }
        
        .next-step {
            background: linear-gradient(135deg, #00ff00, #00d4ff);
            color: black;
            padding: 16px 24px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}" alt="ONNYX" class="nav-logo">
                <span class="nav-title">GENESIS MANAGEMENT</span>
            </div>
        </div>
    </nav>

    <div class="genesis-dashboard">
        <h1>🌟 ONNYX Genesis Management</h1>
        <p>Monitor and manage the genesis block deployment and tribal council formation.</p>
        
        <div id="next-step" class="next-step" style="display: none;">
            <strong>Next Step:</strong> <span id="next-step-text"></span>
        </div>
        
        <div class="status-grid">
            <!-- Genesis Block Status -->
            <div class="status-card">
                <div class="status-header">
                    <div id="genesis-icon" class="status-icon">📦</div>
                    <h3>Genesis Block</h3>
                </div>
                <div id="genesis-status">Loading...</div>
            </div>
            
            <!-- Tribal Council Status -->
            <div class="status-card">
                <div class="status-header">
                    <div id="council-icon" class="status-icon">👑</div>
                    <h3>Tribal Council</h3>
                </div>
                <div id="council-status">Loading...</div>
            </div>
            
            <!-- Voice Scrolls Status -->
            <div class="status-card">
                <div class="status-header">
                    <div id="scrolls-icon" class="status-icon">📜</div>
                    <h3>Voice Scrolls</h3>
                </div>
                <div id="scrolls-status">Loading...</div>
            </div>
            
            <!-- Nations Status -->
            <div class="status-card">
                <div class="status-header">
                    <div id="nations-icon" class="status-icon">🌍</div>
                    <h3>Biblical Nations</h3>
                </div>
                <div id="nations-status">Loading...</div>
            </div>
        </div>
        
        <div class="action-section">
            <h2>Genesis Actions</h2>
            
            <div class="action-grid">
                <!-- Tribal Elder Management -->
                <div class="action-card">
                    <h3>👑 Tribal Elder Management</h3>
                    <p>Register the twelve tribal elders to form the Council of Twelve Tribes.</p>
                    
                    <div class="elder-form">
                        <div class="form-group">
                            <label>Tribe:</label>
                            <select id="tribe-select" class="form-select">
                                <option value="">Select Tribe</option>
                                <option value="JU">Judah (Royal - 2 votes)</option>
                                <option value="LE">Levi (Priestly - 2 votes)</option>
                                <option value="EP">Ephraim (Fruitful - 2 votes)</option>
                                <option value="BE">Benjamin (1 vote)</option>
                                <option value="SI">Simeon (1 vote)</option>
                                <option value="MA">Manasseh (1 vote)</option>
                                <option value="IS">Issachar (1 vote)</option>
                                <option value="ZE">Zebulun (1 vote)</option>
                                <option value="NA">Naphtali (1 vote)</option>
                                <option value="GA">Gad (1 vote)</option>
                                <option value="AS">Asher (1 vote)</option>
                                <option value="RE">Reuben (1 vote)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Elder Name:</label>
                            <input type="text" id="elder-name" class="form-input" placeholder="Enter elder name">
                        </div>
                        
                        <button onclick="createElder()" class="btn-genesis">Register Elder</button>
                    </div>
                    
                    <div id="elder-list" class="elder-list"></div>
                </div>
                
                <!-- Voice Scrolls Management -->
                <div class="action-card">
                    <h3>📜 Genesis Voice Scrolls</h3>
                    <p>Deploy the three foundational governance scrolls for initial voting.</p>
                    
                    <button onclick="deployScrolls()" class="btn-genesis">Deploy Genesis Scrolls</button>
                    
                    <div id="scroll-list" class="elder-list"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let statusData = {};
        
        async function loadStatus() {
            try {
                const response = await fetch('/genesis/api/status');
                statusData = await response.json();
                updateStatusDisplay();
            } catch (error) {
                console.error('Error loading status:', error);
            }
        }
        
        function updateStatusDisplay() {
            // Genesis Block
            const genesisIcon = document.getElementById('genesis-icon');
            const genesisStatus = document.getElementById('genesis-status');
            
            if (statusData.genesis_block?.exists) {
                genesisIcon.className = 'status-icon status-complete';
                genesisStatus.innerHTML = `
                    <div>✅ <strong>Genesis Block Established</strong></div>
                    <div>Hash: ${statusData.genesis_block.hash}</div>
                    <div>Miner: ${statusData.genesis_block.miner}</div>
                `;
            } else {
                genesisIcon.className = 'status-icon status-incomplete';
                genesisStatus.innerHTML = '<div>❌ Genesis block not found</div>';
            }
            
            // Tribal Council
            const councilIcon = document.getElementById('council-icon');
            const councilStatus = document.getElementById('council-status');
            const elderCount = statusData.tribal_council?.elders_registered || 0;
            
            if (elderCount >= 12) {
                councilIcon.className = 'status-icon status-complete';
                councilStatus.innerHTML = `
                    <div>✅ <strong>Council Complete</strong></div>
                    <div>Elders: ${elderCount}/12</div>
                `;
            } else if (elderCount > 0) {
                councilIcon.className = 'status-icon status-pending';
                councilStatus.innerHTML = `
                    <div>⏳ <strong>Council Forming</strong></div>
                    <div>Elders: ${elderCount}/12</div>
                `;
            } else {
                councilIcon.className = 'status-icon status-incomplete';
                councilStatus.innerHTML = '<div>❌ No elders registered</div>';
            }
            
            // Voice Scrolls
            const scrollsIcon = document.getElementById('scrolls-icon');
            const scrollsStatus = document.getElementById('scrolls-status');
            const scrollCount = statusData.voice_scrolls?.deployed || 0;
            
            if (scrollCount >= 3) {
                scrollsIcon.className = 'status-icon status-complete';
                scrollsStatus.innerHTML = `
                    <div>✅ <strong>Scrolls Deployed</strong></div>
                    <div>Scrolls: ${scrollCount}/3</div>
                `;
            } else if (scrollCount > 0) {
                scrollsIcon.className = 'status-icon status-pending';
                scrollsStatus.innerHTML = `
                    <div>⏳ <strong>Partial Deployment</strong></div>
                    <div>Scrolls: ${scrollCount}/3</div>
                `;
            } else {
                scrollsIcon.className = 'status-icon status-incomplete';
                scrollsStatus.innerHTML = '<div>❌ No scrolls deployed</div>';
            }
            
            // Nations
            const nationsIcon = document.getElementById('nations-icon');
            const nationsStatus = document.getElementById('nations-status');
            const nationCount = statusData.nations?.registered || 0;
            
            if (nationCount >= 47) {
                nationsIcon.className = 'status-icon status-complete';
                nationsStatus.innerHTML = `
                    <div>✅ <strong>All Nations Registered</strong></div>
                    <div>Nations: ${nationCount}/47</div>
                `;
            } else if (nationCount > 0) {
                nationsIcon.className = 'status-icon status-pending';
                nationsStatus.innerHTML = `
                    <div>⏳ <strong>Partial Registration</strong></div>
                    <div>Nations: ${nationCount}/47</div>
                `;
            } else {
                nationsIcon.className = 'status-icon status-incomplete';
                nationsStatus.innerHTML = '<div>❌ No nations registered</div>';
            }
            
            // Next Step
            if (statusData.next_step) {
                document.getElementById('next-step').style.display = 'block';
                document.getElementById('next-step-text').textContent = statusData.next_step;
            }
        }
        
        async function createElder() {
            const tribeCode = document.getElementById('tribe-select').value;
            const elderName = document.getElementById('elder-name').value;
            
            if (!tribeCode || !elderName) {
                alert('Please select a tribe and enter an elder name');
                return;
            }
            
            try {
                const response = await fetch('/genesis/api/create-elder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tribe_code: tribeCode,
                        elder_name: elderName
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`Elder created successfully for ${result.tribe}!`);
                    document.getElementById('elder-name').value = '';
                    document.getElementById('tribe-select').value = '';
                    loadElders();
                    loadStatus();
                } else {
                    alert(`Error: ${result.error}`);
                }
            } catch (error) {
                alert(`Error creating elder: ${error.message}`);
            }
        }
        
        async function deployScrolls() {
            try {
                const response = await fetch('/genesis/api/deploy-scrolls', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`Successfully deployed ${result.deployed_count} scrolls!`);
                    loadScrolls();
                    loadStatus();
                } else {
                    alert(`Error: ${result.error}`);
                }
            } catch (error) {
                alert(`Error deploying scrolls: ${error.message}`);
            }
        }
        
        async function loadElders() {
            try {
                const response = await fetch('/genesis/api/elders');
                const data = await response.json();
                
                const elderList = document.getElementById('elder-list');
                elderList.innerHTML = '';
                
                if (data.elders && data.elders.length > 0) {
                    data.elders.forEach(elder => {
                        const elderDiv = document.createElement('div');
                        elderDiv.className = 'elder-item';
                        elderDiv.innerHTML = `
                            <div>
                                <strong>${elder.name}</strong><br>
                                <small>${elder.tribe_name} (${elder.tribe_code})</small>
                            </div>
                            <div>Tier ${elder.verification_level}</div>
                        `;
                        elderList.appendChild(elderDiv);
                    });
                }
            } catch (error) {
                console.error('Error loading elders:', error);
            }
        }
        
        async function loadScrolls() {
            try {
                const response = await fetch('/genesis/api/scrolls');
                const data = await response.json();
                
                const scrollList = document.getElementById('scroll-list');
                scrollList.innerHTML = '';
                
                if (data.scrolls && data.scrolls.length > 0) {
                    data.scrolls.forEach(scroll => {
                        const scrollDiv = document.createElement('div');
                        scrollDiv.className = 'elder-item';
                        scrollDiv.innerHTML = `
                            <div>
                                <strong>${scroll.title}</strong><br>
                                <small>${scroll.type} - ${scroll.status}</small>
                            </div>
                            <div>${scroll.scroll_id}</div>
                        `;
                        scrollList.appendChild(scrollDiv);
                    });
                }
            } catch (error) {
                console.error('Error loading scrolls:', error);
            }
        }
        
        // Load initial data
        loadStatus();
        loadElders();
        loadScrolls();
        
        // Refresh every 30 seconds
        setInterval(() => {
            loadStatus();
            loadElders();
            loadScrolls();
        }, 30000);
    </script>
</body>
</html>
