# 🔥 ONNYX Enhanced Block Structure

## Overview

The ONNYX blockchain implements a comprehensive enhanced block structure that supports covenant-specific features, biblical compliance, and witness verification. This document outlines the complete block header and body structure as implemented.

## 1️⃣ Block Header

Every ONNYX block contains the following header fields:

### Core Header Fields
- **`block_hash`** — Unique SHA-256 hash for the block
- **`previous_hash`** — Hash of the parent block  
- **`block_height`** — Index in the chain (integer)
- **`timestamp`** — Unix timestamp when block was created
- **`merkle_root`** — Root hash of the block's transactions
- **`proposer_id`** — Identity ID of the validator/elder who proposed the block
- **`signature`** — Cryptographic signature from the proposer
- **`witness_signatures`** — Array of signatures from witness nodes for multi-sig verification

### Additional Header Fields
- **`nonce`** — Proof-of-work nonce (for future PoW implementation)
- **`difficulty`** — Mining difficulty (default: 1)
- **`size`** — Block size in bytes
- **`version`** — Block format version (default: "1.0")

## 2️⃣ Block Body (Transactions)

The block body contains covenant-specific transaction types:

### ✅ Identity Events
- **`identity_registration`** — New identity minted with tribal affiliation, role class, Etzem score
- **`identity_update`** — Updates to status, roles, trust metrics

### ✅ Tokenomics Events  
- **`token_mint`** — Minting of Mikvah, Yovel, or gleaning tokens
- **`token_transfer`** — Sending tokens within covenant economy (usury-free)
- **`gleaning_distribution`** — Special covenant economic actions for the needy
- **`firstfruits`** — First fruits offerings and rewards

### ✅ Governance Actions
- **`voice_scroll_proposal`** — New governance proposal submitted
- **`voice_scroll_vote`** — Votes cast by eligible elders or tribes  
- **`quorum_result`** — Outcome logged if proposal passed/failed

### ✅ Witness Logs
Each transaction includes a `witness_log` with:
- **`witness_id`** — Identity of the witnessing node
- **`signature`** — Cryptographic signature from witness
- **`timestamp`** — When the witness signed
- **`proof_url`** — Optional off-chain reference (IPFS, etc.)

## 3️⃣ Block Metadata

Enhanced metadata supports covenant features:

### Chain Parameters Snapshot
- **`sabbath_cycle`** — Current Sabbath cycle (default: 7)
- **`yovel_year`** — Jubilee year cycle (default: 50)
- **`gleaning_pool_percentage`** — Percentage allocated to gleaning (default: 0.02)
- **`biblical_compliance_required`** — Whether biblical rules are enforced

### Biblical Compliance
- **`biblical_compliance_score`** — Compliance score (0.0-1.0, minimum 0.8)
- **`sabbath_compliant`** — Whether block respects Sabbath rules
- **`gleaning_allocation`** — Actual gleaning pool allocation for this block

### Governance & Trust
- **`trust_score_changes`** — Array of validator/elder trust score updates
- **`disputes`** — Array of challenged transactions flagged by gatekeepers
- **`tribal_signatures`** — Multi-signature verification from tribal elders

### Yovel Cycle Tracking
- **`yovel_cycle`** — Current 7-year jubilee cycle for redistribution

## 🗂️ Sample Enhanced Block JSON

```json
{
  "block_hash": "abc123...",
  "previous_hash": "def456...", 
  "block_height": 1024,
  "timestamp": 1752224525,
  "merkle_root": "merkle789...",
  "proposer_id": "elder_yehudah",
  "signature": "proposer_signature_here",
  "witness_signatures": ["sig1", "sig2"],
  "transactions": [
    {
      "tx_id": "tx001",
      "type": "identity_registration",
      "timestamp": 1752224520,
      "sender": "system",
      "data": {
        "identity_id": "tribe_judah_001",
        "public_key": "pubkey...",
        "tribal_affiliation": "Judah",
        "role": "citizen",
        "etzem_score_init": 100
      },
      "witness_log": {
        "witness_id": "gatekeeper_01",
        "signature": "sig...",
        "timestamp": 1752224520,
        "proof_url": null
      },
      "signature": ""
    },
    {
      "tx_id": "tx002", 
      "type": "token_transfer",
      "timestamp": 1752224523,
      "sender": "identity_A",
      "data": {
        "from": "identity_A",
        "to": "identity_B",
        "amount": 50,
        "token_type": "mikvah"
      },
      "witness_log": {
        "witness_id": "gatekeeper_02", 
        "signature": "sig...",
        "timestamp": 1752224523,
        "proof_url": null
      },
      "signature": ""
    }
  ],
  "metadata": {
    "chain_parameters": {
      "sabbath_cycle": 7,
      "yovel_year": 50,
      "gleaning_pool_percentage": 0.02,
      "biblical_compliance_required": true
    },
    "trust_score_changes": [],
    "disputes": [],
    "biblical_compliance_score": 0.95,
    "yovel_cycle": 7,
    "sabbath_compliant": true,
    "gleaning_allocation": 0.02,
    "tribal_signatures": {
      "judah": "tribal_sig_judah_abc123",
      "benjamin": "tribal_sig_benjamin_def456",
      "levi": "tribal_sig_levi_789abc"
    }
  },
  "nonce": 0,
  "difficulty": 1,
  "size": 1488,
  "version": "1.0"
}
```

## 🛡️ Validation Rules

### Block Header Validation
- All required fields must be present
- Block height must be sequential (previous + 1)
- Previous hash must match the latest block's hash
- Block hash must be correctly calculated
- Merkle root must match calculated root of transactions

### Transaction Validation
- All transactions must have valid structure
- Transaction types must be from approved covenant list
- Usury (interest) is strictly prohibited in token transfers
- Witness logs must have valid signatures
- Biblical compliance score must be ≥ 0.8

### Covenant Compliance
- No interest-bearing transactions allowed
- Gleaning pool allocation must be maintained
- Sabbath compliance checked for timing
- Tribal signatures validated if present

## 🔧 Implementation Files

- **`shared/models/enhanced_block.py`** — Enhanced block data structures
- **`blockchain/core/enhanced_block_builder.py`** — Block creation with covenant features
- **`blockchain/vm/block_validator.py`** — Enhanced validation functions
- **`scripts/test_enhanced_blocks.py`** — Comprehensive test suite

## 🎯 Key Benefits

✅ **Complete Truth Anchor** — Every block preserves covenant identity, transactions, governance, and witness verification

✅ **Biblical Compliance** — Built-in usury prevention and covenant economic rules

✅ **Multi-Signature Support** — Witness logs and tribal elder signatures for enhanced security

✅ **Governance Integration** — Voice Scroll proposals and voting directly on-chain

✅ **Economic Justice** — Gleaning pool and Yovel cycle redistribution mechanisms

✅ **Backward Compatibility** — Legacy block support for smooth migration

This enhanced block structure ensures that ONNYX maintains its covenant principles while providing a robust, scalable blockchain foundation for the biblical economy.
