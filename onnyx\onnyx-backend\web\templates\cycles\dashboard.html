<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biblical Cycles - ONNYX Covenant Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'orbitron': ['Orbitron', 'monospace'],
                        'inter': ['Inter', 'sans-serif']
                    },
                    colors: {
                        'onyx-black': '#0a0a0a',
                        'cyber-cyan': '#00fff7',
                        'cyber-purple': '#9a00ff',
                        'cyber-gold': '#ffd700',
                        'text-primary': '#ffffff',
                        'text-secondary': '#b0b0b0',
                        'text-tertiary': '#808080'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --onyx-black: #0a0a0a;
            --cyber-cyan: #00fff7;
            --cyber-purple: #9a00ff;
            --cyber-gold: #ffd700;
        }
        
        body {
            background: linear-gradient(135deg, var(--onyx-black) 0%, #1a1a2e 50%, var(--onyx-black) 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
        }
        
        .glass-card-premium {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 255, 247, 0.2);
            border-radius: 20px;
        }
        
        .cycle-progress {
            background: linear-gradient(90deg, var(--cyber-cyan) 0%, var(--cyber-purple) 100%);
            height: 8px;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .countdown-number {
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            color: var(--cyber-cyan);
            text-shadow: 0 0 10px var(--cyber-cyan);
        }
        
        .cycle-active {
            border-color: var(--cyber-gold);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }
        
        .pulse-glow {
            animation: pulse-glow 2s infinite;
        }
        
        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 247, 0.3); }
            50% { box-shadow: 0 0 30px rgba(0, 255, 247, 0.6); }
        }
    </style>
</head>
<body class="font-inter text-text-primary">
    <!-- Header -->
    <header class="border-b border-gray-800 bg-black/50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-orbitron font-bold text-cyber-cyan">Biblical Cycles</h1>
                    <div class="text-sm text-text-secondary">
                        Covenant Time Tracking for {{ user_name }}
                    </div>
                </div>
                <div class="text-sm text-text-tertiary">
                    Timezone: {{ user_timezone }}
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Covenant Message -->
        <div class="glass-card-premium p-6 mb-8 text-center">
            <div class="text-lg font-orbitron font-bold text-cyber-gold mb-2">Current Covenant Status</div>
            <div id="covenant-message" class="text-xl text-text-primary">{{ covenant_message }}</div>
            <div class="text-sm text-text-secondary mt-2">
                "To every thing there is a season, and a time to every purpose under the heaven" - Ecclesiastes 3:1
            </div>
        </div>

        <!-- Cycle Status Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Weekly Sabbath -->
            <div class="glass-card p-6 {% if cycle_statuses.weekly_sabbath.is_active %}cycle-active pulse-glow{% endif %}">
                <div class="flex items-center justify-between mb-4">
                    <div class="text-4xl">🕯️</div>
                    <div class="text-sm text-cyber-cyan font-orbitron">
                        {% if cycle_statuses.weekly_sabbath.is_active %}ACTIVE{% else %}PENDING{% endif %}
                    </div>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Weekly Sabbath</h3>
                <div class="text-sm text-text-secondary mb-3">
                    {% if cycle_statuses.weekly_sabbath.current_period %}
                        {{ cycle_statuses.weekly_sabbath.current_period }}
                    {% else %}
                        Next: {{ cycle_statuses.weekly_sabbath.formatted_time_until }}
                    {% endif %}
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                    <div class="cycle-progress" style="width: {{ cycle_statuses.weekly_sabbath.progress_percentage }}%"></div>
                </div>
                <div class="text-xs text-text-tertiary">Exodus 20:8-11</div>
            </div>

            <!-- Sabbatical Year -->
            <div class="glass-card p-6 {% if cycle_statuses.sabbatical_year.is_active %}cycle-active pulse-glow{% endif %}">
                <div class="flex items-center justify-between mb-4">
                    <div class="text-4xl">🌾</div>
                    <div class="text-sm text-cyber-purple font-orbitron">
                        {% if cycle_statuses.sabbatical_year.is_active %}SHMITA{% else %}{{ "%.1f"|format(cycle_statuses.sabbatical_year.progress_percentage) }}%{% endif %}
                    </div>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Sabbatical Year</h3>
                <div class="text-sm text-text-secondary mb-3">
                    {{ cycle_statuses.sabbatical_year.current_period }}
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                    <div class="cycle-progress" style="width: {{ cycle_statuses.sabbatical_year.progress_percentage }}%"></div>
                </div>
                <div class="text-xs text-text-tertiary">Deuteronomy 15:1-2</div>
            </div>

            <!-- Jubilee Cycle -->
            <div class="glass-card p-6 {% if cycle_statuses.jubilee_cycle.is_active %}cycle-active pulse-glow{% endif %}">
                <div class="flex items-center justify-between mb-4">
                    <div class="text-4xl">🎺</div>
                    <div class="text-sm text-cyber-gold font-orbitron">
                        {% if cycle_statuses.jubilee_cycle.is_active %}JUBILEE{% else %}{{ "%.1f"|format(cycle_statuses.jubilee_cycle.progress_percentage) }}%{% endif %}
                    </div>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-gold mb-2">Jubilee Cycle</h3>
                <div class="text-sm text-text-secondary mb-3">
                    {{ cycle_statuses.jubilee_cycle.current_period }}
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                    <div class="cycle-progress" style="width: {{ cycle_statuses.jubilee_cycle.progress_percentage }}%"></div>
                </div>
                <div class="text-xs text-text-tertiary">Leviticus 25:8-13</div>
            </div>

            <!-- New Moon -->
            <div class="glass-card p-6 {% if cycle_statuses.new_moon.is_active %}cycle-active pulse-glow{% endif %}">
                <div class="flex items-center justify-between mb-4">
                    <div class="text-4xl">🌙</div>
                    <div class="text-sm text-text-primary font-orbitron">
                        {% if cycle_statuses.new_moon.is_active %}NEW MOON{% else %}{{ "%.1f"|format(cycle_statuses.new_moon.progress_percentage) }}%{% endif %}
                    </div>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-text-primary mb-2">New Moon</h3>
                <div class="text-sm text-text-secondary mb-3">
                    {{ cycle_statuses.new_moon.current_period }}
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                    <div class="cycle-progress" style="width: {{ cycle_statuses.new_moon.progress_percentage }}%"></div>
                </div>
                <div class="text-xs text-text-tertiary">Numbers 28:11</div>
            </div>

        </div>

        <!-- Detailed Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Upcoming Biblical Holidays -->
            <div class="glass-card p-6">
                <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Upcoming Biblical Holidays</h2>
                <div class="space-y-4">
                    {% for holiday in upcoming_holidays %}
                    <div class="border-l-4 border-cyber-purple pl-4">
                        <div class="font-semibold text-text-primary">{{ holiday.name }}</div>
                        <div class="text-sm text-text-secondary">{{ holiday.date }}</div>
                        <div class="text-xs text-text-tertiary">{{ holiday.description }} - {{ holiday.reference }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Cycle Navigation -->
            <div class="glass-card p-6">
                <h2 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Explore Cycles</h2>
                <div class="grid grid-cols-2 gap-4">
                    <a href="{{ url_for('cycles.sabbath_tracker') }}" 
                       class="glass-card p-4 text-center hover:border-cyber-cyan transition-all duration-300">
                        <div class="text-2xl mb-2">🕯️</div>
                        <div class="font-orbitron font-semibold">Sabbath Tracker</div>
                    </a>
                    <a href="{{ url_for('cycles.jubilee_tracker') }}" 
                       class="glass-card p-4 text-center hover:border-cyber-gold transition-all duration-300">
                        <div class="text-2xl mb-2">🎺</div>
                        <div class="font-orbitron font-semibold">Jubilee Tracker</div>
                    </a>
                    <a href="{{ url_for('cycles.sabbatical_tracker') }}" 
                       class="glass-card p-4 text-center hover:border-cyber-purple transition-all duration-300">
                        <div class="text-2xl mb-2">🌾</div>
                        <div class="font-orbitron font-semibold">Sabbatical Year</div>
                    </a>
                    <a href="{{ url_for('cycles.biblical_calendar') }}" 
                       class="glass-card p-4 text-center hover:border-text-primary transition-all duration-300">
                        <div class="text-2xl mb-2">📅</div>
                        <div class="font-orbitron font-semibold">Full Calendar</div>
                    </a>
                </div>
            </div>

        </div>

    </main>

    <!-- Real-time Updates Script -->
    <script>
        // Update cycle data every minute
        function updateCycleData() {
            fetch('/cycles/api/status?tz={{ user_timezone }}')
                .then(response => response.json())
                .then(data => {
                    // Update covenant message
                    document.getElementById('covenant-message').textContent = data.covenant_message;
                    
                    // Update progress bars and status
                    Object.keys(data.cycles).forEach(cycleKey => {
                        const cycle = data.cycles[cycleKey];
                        const progressBar = document.querySelector(`[data-cycle="${cycleKey}"] .cycle-progress`);
                        if (progressBar) {
                            progressBar.style.width = cycle.progress_percentage + '%';
                        }
                    });
                })
                .catch(error => console.error('Error updating cycle data:', error));
        }

        // Update every minute
        setInterval(updateCycleData, 60000);

        // Update countdown displays
        function updateCountdowns() {
            const countdownElements = document.querySelectorAll('.countdown-number');
            countdownElements.forEach(element => {
                const targetTime = element.dataset.targetTime;
                if (targetTime) {
                    const now = new Date().getTime();
                    const target = new Date(targetTime).getTime();
                    const difference = target - now;
                    
                    if (difference > 0) {
                        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
                        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
                        
                        if (days > 0) {
                            element.textContent = `${days}d ${hours}h`;
                        } else if (hours > 0) {
                            element.textContent = `${hours}h ${minutes}m`;
                        } else {
                            element.textContent = `${minutes}m`;
                        }
                    }
                }
            });
        }

        // Update countdowns every minute
        setInterval(updateCountdowns, 60000);
        updateCountdowns(); // Initial update

        console.log('🕯️ Biblical Cycles Dashboard Initialized');
    </script>
</body>
</html>
