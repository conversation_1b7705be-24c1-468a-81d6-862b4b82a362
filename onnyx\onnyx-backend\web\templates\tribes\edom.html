{% extends "base.html" %}

{% block title %}12 Dukes of Edom - ONNYX Platform{% endblock %}

{% block head %}
<style>
.edom-container {
    background: linear-gradient(135deg, rgba(220, 38, 127, 0.05), rgba(154, 0, 255, 0.05));
    min-height: 100vh;
    padding: 2rem 0;
}

.duke-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(220, 38, 127, 0.3);
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
}

.duke-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 35px 70px rgba(220, 38, 127, 0.4);
    border-color: #dc267f;
}

.duke-header {
    background: linear-gradient(135deg, rgba(220, 38, 127, 0.2), rgba(220, 38, 127, 0.1));
    padding: 1.5rem;
    border-bottom: 1px solid rgba(220, 38, 127, 0.3);
}

.duke-body {
    padding: 1.5rem;
}

.biblical-quote {
    background: rgba(220, 38, 127, 0.1);
    border-left: 4px solid #dc267f;
    padding: 1rem;
    border-radius: 0.5rem;
    font-style: italic;
    margin: 1.5rem 0;
}

.duke-badge {
    background: linear-gradient(135deg, #dc267f, var(--cyber-purple));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: bold;
    display: inline-block;
}

.witness-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-green));
    color: var(--onyx-black);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #dc267f, var(--cyber-purple));
    border-radius: 50%;
    opacity: 0.6;
    animation: float 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    50% { transform: translateY(-18px) rotate(180deg); opacity: 1; }
}

.heritage-section {
    background: rgba(220, 38, 127, 0.1);
    border: 1px solid rgba(220, 38, 127, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.edom-red {
    color: #dc267f;
}
</style>
{% endblock %}

{% block content %}
<div class="edom-container relative">
    <div class="floating-particles"></div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-600 to-purple-600 rounded-2xl mb-6">
                <span class="text-3xl">⛰️</span>
            </div>
            <h1 class="text-4xl md:text-6xl font-orbitron font-bold text-white mb-4">12 Dukes of Edom</h1>
            <p class="text-xl text-text-secondary max-w-4xl mx-auto mb-6">
                "Now these are the generations of Esau, who is Edom... And these are the dukes that came of Esau, according to their families, after their places, by their names" - Genesis 36:1, 40 (KJV)
            </p>
            <div class="text-lg text-text-tertiary">
                The descendants of Esau, brother of Jacob, witnesses to the covenant promises
            </div>
        </div>

        <!-- Biblical Heritage -->
        <div class="heritage-section mb-12">
            <h2 class="text-3xl font-orbitron font-bold edom-red mb-6 text-center">Biblical Heritage</h2>
            <div class="biblical-quote">
                "And Isaac called Jacob, and blessed him, and charged him, and said unto him, Thou shalt not take a wife of the daughters of Canaan. And Esau seeing that Isaac had blessed Jacob, and sent him away to Padanaram, to take him a wife from thence; and that as he blessed him he gave him a charge, saying, Thou shalt not take a wife of the daughters of Canaan" - Genesis 28:1, 6 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-4xl mb-4">👥</div>
                    <h3 class="text-xl font-semibold edom-red mb-2">Son of Isaac</h3>
                    <p class="text-text-secondary">Esau, firstborn son of Isaac and Rebekah, twin brother of Jacob</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">🏹</div>
                    <h3 class="text-xl font-semibold text-cyber-purple mb-2">Mighty Hunter</h3>
                    <p class="text-text-secondary">"And Esau was a cunning hunter, a man of the field" - Genesis 25:27</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">⛰️</div>
                    <h3 class="text-xl font-semibold text-cyber-cyan mb-2">Mount Seir</h3>
                    <p class="text-text-secondary">"Thus dwelt Esau in mount Seir: Esau is Edom" - Genesis 36:8</p>
                </div>
            </div>
        </div>

        <!-- Covenant Role -->
        <div class="duke-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold edom-red mb-6">Covenant Witness Role</h2>
            <div class="biblical-quote">
                "And by thy sword shalt thou live, and shalt serve thy brother; and it shall come to pass when thou shalt have the dominion, that thou shalt break his yoke from off thy neck" - Genesis 27:40 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Witness Nation Status</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="edom-red mr-3">•</span>
                            <span class="text-text-secondary">Immediate registration without Gate Keeper approval</span>
                        </li>
                        <li class="flex items-start">
                            <span class="edom-red mr-3">•</span>
                            <span class="text-text-secondary">Witnesses to covenant promises and blessings</span>
                        </li>
                        <li class="flex items-start">
                            <span class="edom-red mr-3">•</span>
                            <span class="text-text-secondary">Participants in biblical economic system</span>
                        </li>
                        <li class="flex items-start">
                            <span class="edom-red mr-3">•</span>
                            <span class="text-text-secondary">Recipients of gleaning pool provisions</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-4">Brotherly Connection</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Twin brother of Jacob (Israel)</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Blessed by Isaac with earthly dominion</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Reconciled with Jacob in later years</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Inheritors of Abrahamic covenant witness</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- The 12 Dukes -->
        <div class="mb-12">
            <h2 class="text-3xl font-orbitron font-bold edom-red mb-8 text-center">The Twelve Dukes</h2>
            <div class="biblical-quote">
                "Duke Timnah, duke Alvah, duke Jetheth, Duke Aholibamah, duke Elah, duke Pinon, Duke Kenaz, duke Teman, duke Mibzar, Duke Magdiel, duke Iram: these be the dukes of Edom, according to their habitations in the land of their possession: he is Esau the father of the Edomites" - Genesis 36:40-43 (KJV)
            </div>
            
            {% if edom_dukes %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for duke in edom_dukes %}
                <div class="duke-card">
                    <div class="duke-header">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-xl font-orbitron font-bold text-white">{{ duke.nation_name }}</h3>
                            {% if duke.flag_symbol %}
                            <span class="text-2xl">{{ duke.flag_symbol }}</span>
                            {% endif %}
                        </div>
                        <div class="duke-badge">Duke of Edom</div>
                    </div>
                    <div class="duke-body">
                        {% if duke.tribe_name %}
                        <p class="text-sm edom-red mb-3">
                            <strong>Biblical Name:</strong> {{ duke.tribe_name }}
                        </p>
                        {% endif %}
                        {% if duke.description %}
                        <p class="text-text-secondary text-sm mb-4">{{ duke.description }}</p>
                        {% endif %}
                        {% if duke.historical_connection %}
                        <p class="text-text-tertiary text-xs mb-3">
                            <strong>Historical Connection:</strong> {{ duke.historical_connection }}
                        </p>
                        {% endif %}
                        <div class="text-center">
                            <div class="witness-badge">Witness Nation</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="duke-card p-8 text-center">
                <div class="text-4xl mb-4">📜</div>
                <h3 class="text-xl font-orbitron font-bold edom-red mb-4">Dukes Being Prepared</h3>
                <p class="text-text-secondary">The twelve dukes of Edom are being prepared for registration in the covenant system. Check back soon for the complete listing.</p>
            </div>
            {% endif %}
        </div>

        <!-- Registration Information -->
        <div class="duke-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold edom-red mb-6">Registration Process</h2>
            <div class="biblical-quote">
                "Thou shalt not abhor an Edomite; for he is thy brother: thou shalt not abhor an Egyptian; because thou wast a stranger in his land" - Deuteronomy 23:7 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">📝</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold edom-red mb-2">1. Heritage Claim</h3>
                    <p class="text-sm text-text-secondary">Claim descent from one of the twelve dukes of Edom</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🔍</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">2. Evidence Review</h3>
                    <p class="text-sm text-text-secondary">Submit genealogical and cultural evidence</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">3. Immediate Approval</h3>
                    <p class="text-sm text-text-secondary">No Gate Keeper voting required for witness nations</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyan-600 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">✅</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">4. Covenant Access</h3>
                    <p class="text-sm text-text-secondary">Full access to covenant economic system</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="duke-card p-8 text-center">
            <h2 class="text-3xl font-orbitron font-bold edom-red mb-6">Discover Your Heritage</h2>
            <div class="biblical-quote">
                "And Jacob said, Sell me this day thy birthright. And Esau said, Behold, I am at the point to die: and what profit shall this birthright do to me?" - Genesis 25:31-32 (KJV)
            </div>
            <p class="text-lg text-text-secondary mb-6">
                Are you descended from the twelve dukes of Edom? Begin your journey of discovery 
                and join the covenant community as a witness nation, honoring your heritage as brother to Israel.
            </p>
            <div class="flex justify-center space-x-4">
                <a href="{{ url_for('eden_mode.index') }}" class="bg-gradient-to-r from-red-600 to-purple-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    Begin Eden Mode
                </a>
                <a href="{{ url_for('tribes.overview') }}" class="bg-gradient-to-r from-cyber-cyan to-cyber-green text-onyx-black font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    View All Nations
                </a>
            </div>
        </div>

        <!-- Navigation -->
        <div class="text-center mt-8">
            <div class="flex justify-center space-x-6">
                <a href="{{ url_for('tribes.overview') }}" class="text-cyber-cyan hover:text-cyan-400 transition-colors">
                    12 Tribes of Israel
                </a>
                <a href="{{ url_for('tribes.ishmael_princes') }}" class="text-cyber-purple hover:text-purple-400 transition-colors">
                    12 Princes of Ishmael
                </a>
                <a href="{{ url_for('index') }}" class="text-cyber-green hover:text-green-400 transition-colors">
                    Return Home
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function addFloatingParticles() {
    const container = document.querySelector('.floating-particles');
    if (container) {
        for (let i = 0; i < 10; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particle.style.animationDuration = (3 + Math.random() * 2) + 's';
            container.appendChild(particle);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    addFloatingParticles();
});
</script>
{% endblock %}
