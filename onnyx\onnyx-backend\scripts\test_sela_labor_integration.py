#!/usr/bin/env python3
"""
Test Enhanced Sela Business Dashboard Integration
Comprehensive testing of Sela labor management features
"""

import requests
import json
import time
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

BASE_URL = "http://127.0.0.1:5000"

def test_sela_dashboard_page():
    """Test the enhanced Sela dashboard page."""
    print("🏢 Testing Enhanced Sela Dashboard...")
    
    try:
        response = requests.get(f"{BASE_URL}/sela")
        if response.status_code == 200:
            print("✅ Sela Dashboard: Page loads successfully")
            
            # Check for labor management content
            content = response.text.lower()
            if "labor management" in content and "team labor overview" in content:
                print("  ✅ Contains labor management features")
            else:
                print("  ⚠️ May not contain expected labor management content")
        else:
            print(f"❌ Sela Dashboard: Failed to load ({response.status_code})")
            
    except Exception as e:
        print(f"❌ Sela Dashboard: Error loading page - {e}")

def test_sela_members_api():
    """Test the Sela members API."""
    print("👥 Testing Sela Members API...")
    
    try:
        # Test with a sample Sela ID
        test_sela_id = "test-sela-123"
        response = requests.get(f"{BASE_URL}/sela/api/{test_sela_id}/members")
        
        if response.status_code == 401:
            print("✅ Sela Members API: Correctly requires authentication")
        elif response.status_code == 200:
            data = response.json()
            print(f"✅ Sela Members API: Returns {len(data.get('members', []))} members")
        else:
            print(f"❌ Sela Members API: Unexpected status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Sela Members API error: {e}")

def test_sela_labor_statistics_api():
    """Test the Sela labor statistics API."""
    print("📊 Testing Sela Labor Statistics API...")
    
    try:
        test_sela_id = "test-sela-123"
        response = requests.get(f"{BASE_URL}/sela/api/{test_sela_id}/labor-statistics")
        
        if response.status_code == 401:
            print("✅ Sela Labor Statistics API: Correctly requires authentication")
        elif response.status_code == 200:
            data = response.json()
            print("✅ Sela Labor Statistics API: Returns data structure")
            
            # Check data structure
            expected_keys = ['team_statistics', 'recent_labor', 'member_details']
            for key in expected_keys:
                if key in data:
                    print(f"  ✅ Contains {key}")
                else:
                    print(f"  ❌ Missing {key}")
        else:
            print(f"❌ Sela Labor Statistics API: Unexpected status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Sela Labor Statistics API error: {e}")

def test_sela_pending_verifications_api():
    """Test the Sela pending verifications API."""
    print("⏳ Testing Sela Pending Verifications API...")
    
    try:
        test_sela_id = "test-sela-123"
        response = requests.get(f"{BASE_URL}/sela/api/{test_sela_id}/pending-verifications")
        
        if response.status_code == 401:
            print("✅ Sela Pending Verifications API: Correctly requires authentication")
        elif response.status_code == 200:
            data = response.json()
            print(f"✅ Sela Pending Verifications API: Returns {len(data.get('pending_labor', []))} pending items")
        else:
            print(f"❌ Sela Pending Verifications API: Unexpected status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Sela Pending Verifications API error: {e}")

def test_sela_token_distribution_api():
    """Test the Sela token distribution API."""
    print("💰 Testing Sela Token Distribution API...")
    
    try:
        test_sela_id = "test-sela-123"
        
        # Test team distribution
        response = requests.post(f"{BASE_URL}/sela/api/{test_sela_id}/distribute-tokens")
        
        if response.status_code == 401:
            print("✅ Sela Token Distribution API: Correctly requires authentication")
        elif response.status_code == 403:
            print("✅ Sela Token Distribution API: Correctly requires permissions")
        else:
            print(f"❌ Sela Token Distribution API: Unexpected status {response.status_code}")
        
        # Test member distribution
        member_data = {
            "identity_id": "test-identity-123",
            "amount": 50.0
        }
        
        response = requests.post(
            f"{BASE_URL}/sela/api/{test_sela_id}/distribute-member-tokens",
            json=member_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 401:
            print("✅ Sela Member Token Distribution API: Correctly requires authentication")
        elif response.status_code == 403:
            print("✅ Sela Member Token Distribution API: Correctly requires permissions")
        else:
            print(f"❌ Sela Member Token Distribution API: Unexpected status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Sela Token Distribution API error: {e}")

def test_sela_labor_report_api():
    """Test the Sela labor report API."""
    print("📋 Testing Sela Labor Report API...")
    
    try:
        test_sela_id = "test-sela-123"
        response = requests.post(f"{BASE_URL}/sela/api/{test_sela_id}/labor-report")
        
        if response.status_code == 401:
            print("✅ Sela Labor Report API: Correctly requires authentication")
        elif response.status_code == 200:
            data = response.json()
            print("✅ Sela Labor Report API: Returns report data")
            
            # Check report structure
            expected_keys = ['sela_id', 'generated_at', 'summary', 'labor_records']
            for key in expected_keys:
                if key in data:
                    print(f"  ✅ Report contains {key}")
                else:
                    print(f"  ❌ Report missing {key}")
        else:
            print(f"❌ Sela Labor Report API: Unexpected status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Sela Labor Report API error: {e}")

def test_database_integration():
    """Test database integration for Sela labor features."""
    print("🗄️ Testing Database Integration...")
    
    try:
        from shared.db.db import db
        
        # Test that labor records can be linked to Selas
        test_query = """
            SELECT COUNT(*) as count 
            FROM labor_records 
            WHERE sela_id IS NOT NULL
        """
        
        result = db.query_one(test_query)
        sela_labor_count = result['count'] if result else 0
        print(f"✅ Database Integration: {sela_labor_count} labor records linked to Selas")
        
        # Test Sela table exists
        sela_count = db.query_one("SELECT COUNT(*) as count FROM selas")
        print(f"✅ Database Integration: {sela_count['count'] if sela_count else 0} Selas in database")
        
    except Exception as e:
        print(f"❌ Database Integration error: {e}")

def test_tokenomics_integration():
    """Test biblical tokenomics integration with Sela features."""
    print("🪙 Testing Tokenomics Integration...")
    
    try:
        from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        from shared.db.db import db
        
        # Initialize systems
        biblical_tokenomics = BiblicalTokenomics(db)
        mikvah_manager = MikvahTokenManager(db)
        
        print(f"✅ Tokenomics Integration: Biblical system operational")
        print(f"✅ Tokenomics Integration: Mikvah manager operational")
        
        # Test token distribution functionality
        test_identity = "test-sela-member-123"
        test_amount = 10.0
        
        # Check if distribution would work (without actually doing it)
        eligible = biblical_tokenomics.check_yovel_eligibility(test_identity, test_amount)
        print(f"✅ Tokenomics Integration: Yovel eligibility check works: {eligible}")
        
    except Exception as e:
        print(f"❌ Tokenomics Integration error: {e}")

def test_frontend_javascript_features():
    """Test that frontend JavaScript features are properly integrated."""
    print("🌐 Testing Frontend JavaScript Features...")
    
    try:
        response = requests.get(f"{BASE_URL}/sela")
        if response.status_code == 200:
            content = response.text
            
            # Check for JavaScript functions
            js_functions = [
                'loadTeamLaborData',
                'showLaborLogModal',
                'distributeTokens',
                'loadTeamMembers',
                'generateLaborReport'
            ]
            
            for func in js_functions:
                if func in content:
                    print(f"  ✅ JavaScript function found: {func}")
                else:
                    print(f"  ❌ JavaScript function missing: {func}")
            
            # Check for modal HTML
            if 'laborLogModal' in content:
                print("  ✅ Labor log modal HTML present")
            else:
                print("  ❌ Labor log modal HTML missing")
                
        else:
            print("❌ Could not load Sela dashboard for JavaScript testing")
            
    except Exception as e:
        print(f"❌ Frontend JavaScript Features error: {e}")

def test_api_error_handling():
    """Test API error handling for invalid requests."""
    print("🛡️ Testing API Error Handling...")
    
    try:
        # Test with invalid Sela ID
        invalid_sela_id = "invalid-sela-id-999"
        
        response = requests.get(f"{BASE_URL}/sela/api/{invalid_sela_id}/members")
        if response.status_code in [401, 403, 404]:
            print("✅ API Error Handling: Properly handles invalid Sela ID")
        else:
            print(f"❌ API Error Handling: Unexpected response {response.status_code}")
        
        # Test with malformed JSON
        response = requests.post(
            f"{BASE_URL}/sela/api/{invalid_sela_id}/distribute-member-tokens",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code in [400, 401, 403]:
            print("✅ API Error Handling: Properly handles malformed JSON")
        else:
            print(f"❌ API Error Handling: Unexpected response for malformed JSON {response.status_code}")
            
    except Exception as e:
        print(f"❌ API Error Handling error: {e}")

def main():
    """Main test function."""
    print("🏢 Starting Enhanced Sela Business Dashboard Integration Tests...")
    print("=" * 70)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Test dashboard page
    test_sela_dashboard_page()
    print()
    
    # Test API endpoints
    test_sela_members_api()
    print()
    
    test_sela_labor_statistics_api()
    print()
    
    test_sela_pending_verifications_api()
    print()
    
    test_sela_token_distribution_api()
    print()
    
    test_sela_labor_report_api()
    print()
    
    # Test database integration
    test_database_integration()
    print()
    
    # Test tokenomics integration
    test_tokenomics_integration()
    print()
    
    # Test frontend features
    test_frontend_javascript_features()
    print()
    
    # Test error handling
    test_api_error_handling()
    print()
    
    print("=" * 70)
    print("🎉 Enhanced Sela Business Dashboard Integration Testing Complete!")

if __name__ == '__main__':
    main()
