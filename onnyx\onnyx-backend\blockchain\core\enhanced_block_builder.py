"""
Enhanced Block Builder for ONNYX Blockchain

This module provides comprehensive block building functionality with support for
all covenant-specific transaction types and biblical compliance features.
"""

import time
import json
import logging
from typing import Dict, Any, List, Optional

from shared.models.enhanced_block import (
    EnhancedBlock, CovenantTransaction, WitnessLog, 
    BlockMetadata, ChainParameters
)
from shared.db.db import db

logger = logging.getLogger(__name__)


class EnhancedBlockBuilder:
    """
    Enhanced block builder for creating covenant-compliant blocks with all required
    transaction types and biblical compliance features.
    """
    
    def __init__(self):
        self.chain_params = self._load_chain_parameters()
    
    def _load_chain_parameters(self) -> ChainParameters:
        """Load chain parameters from configuration"""
        try:
            with open('data/chain_params.json', 'r') as f:
                params = json.load(f)
            
            return ChainParameters(
                sabbath_cycle=params.get('sabbath_start_day', 7),
                yovel_year=params.get('jubilee_interval_blocks', 50400) // 1008,  # Convert blocks to years
                gleaning_pool_percentage=params.get('gleaning_pool_percentage', 0.02),
                biblical_compliance_required=True
            )
        except Exception as e:
            logger.warning(f"Could not load chain parameters: {e}, using defaults")
            return ChainParameters()
    
    def create_identity_registration_transaction(
        self, 
        identity_id: str, 
        public_key: str, 
        tribal_affiliation: str,
        witness_id: str,
        witness_signature: str
    ) -> CovenantTransaction:
        """Create an identity registration transaction"""
        
        witness_log = WitnessLog(
            witness_id=witness_id,
            signature=witness_signature,
            timestamp=int(time.time())
        )
        
        return CovenantTransaction(
            tx_id=f"identity_reg_{int(time.time())}_{identity_id[:8]}",
            type="identity_registration",
            timestamp=int(time.time()),
            sender="system",
            data={
                "identity_id": identity_id,
                "public_key": public_key,
                "tribal_affiliation": tribal_affiliation,
                "role": "citizen",
                "etzem_score_init": 100
            },
            witness_log=witness_log
        )
    
    def create_token_transfer_transaction(
        self,
        from_identity: str,
        to_identity: str,
        amount: float,
        token_type: str,
        witness_id: str,
        witness_signature: str
    ) -> CovenantTransaction:
        """Create a token transfer transaction"""
        
        witness_log = WitnessLog(
            witness_id=witness_id,
            signature=witness_signature,
            timestamp=int(time.time())
        )
        
        return CovenantTransaction(
            tx_id=f"token_transfer_{int(time.time())}_{from_identity[:8]}",
            type="token_transfer",
            timestamp=int(time.time()),
            sender=from_identity,
            data={
                "from": from_identity,
                "to": to_identity,
                "amount": amount,
                "token_type": token_type
            },
            witness_log=witness_log
        )
    
    def create_voice_scroll_proposal_transaction(
        self,
        proposer_id: str,
        proposal_title: str,
        proposal_description: str,
        voting_period: int,
        witness_id: str,
        witness_signature: str
    ) -> CovenantTransaction:
        """Create a voice scroll proposal transaction"""
        
        witness_log = WitnessLog(
            witness_id=witness_id,
            signature=witness_signature,
            timestamp=int(time.time())
        )
        
        return CovenantTransaction(
            tx_id=f"voice_scroll_{int(time.time())}_{proposer_id[:8]}",
            type="voice_scroll_proposal",
            timestamp=int(time.time()),
            sender=proposer_id,
            data={
                "proposer_id": proposer_id,
                "title": proposal_title,
                "description": proposal_description,
                "voting_period": voting_period,
                "status": "active"
            },
            witness_log=witness_log
        )
    
    def create_voice_scroll_vote_transaction(
        self,
        voter_id: str,
        proposal_id: str,
        vote: str,  # "yes", "no", "abstain"
        witness_id: str,
        witness_signature: str
    ) -> CovenantTransaction:
        """Create a voice scroll vote transaction"""
        
        witness_log = WitnessLog(
            witness_id=witness_id,
            signature=witness_signature,
            timestamp=int(time.time())
        )
        
        return CovenantTransaction(
            tx_id=f"vote_{int(time.time())}_{voter_id[:8]}",
            type="voice_scroll_vote",
            timestamp=int(time.time()),
            sender=voter_id,
            data={
                "voter_id": voter_id,
                "proposal_id": proposal_id,
                "vote": vote,
                "voting_power": 1
            },
            witness_log=witness_log
        )
    
    def create_token_mint_transaction(
        self,
        recipient_id: str,
        amount: float,
        token_type: str,
        reason: str,
        witness_id: str,
        witness_signature: str
    ) -> CovenantTransaction:
        """Create a token mint transaction"""
        
        witness_log = WitnessLog(
            witness_id=witness_id,
            signature=witness_signature,
            timestamp=int(time.time())
        )
        
        return CovenantTransaction(
            tx_id=f"token_mint_{int(time.time())}_{recipient_id[:8]}",
            type="token_mint",
            timestamp=int(time.time()),
            sender="system",
            data={
                "recipient": recipient_id,
                "amount": amount,
                "token_type": token_type,
                "reason": reason
            },
            witness_log=witness_log
        )
    
    def create_gleaning_distribution_transaction(
        self,
        recipients: List[Dict[str, Any]],
        total_amount: float,
        witness_id: str,
        witness_signature: str
    ) -> CovenantTransaction:
        """Create a gleaning pool distribution transaction"""
        
        witness_log = WitnessLog(
            witness_id=witness_id,
            signature=witness_signature,
            timestamp=int(time.time())
        )
        
        return CovenantTransaction(
            tx_id=f"gleaning_{int(time.time())}",
            type="gleaning_distribution",
            timestamp=int(time.time()),
            sender="system",
            data={
                "recipients": recipients,
                "total_amount": total_amount,
                "distribution_type": "gleaning_pool"
            },
            witness_log=witness_log
        )
    
    def create_block(
        self,
        previous_hash: str,
        block_height: int,
        proposer_id: str,
        transactions: List[CovenantTransaction],
        witness_signatures: List[str] = None,
        tribal_signatures: Dict[str, str] = None
    ) -> EnhancedBlock:
        """Create a new enhanced block with all covenant features"""
        
        timestamp = int(time.time())
        
        # Create block metadata
        metadata = BlockMetadata(
            chain_parameters=self.chain_params,
            trust_score_changes=[],
            disputes=[],
            biblical_compliance_score=self._calculate_biblical_compliance(transactions),
            yovel_cycle=self.chain_params.yovel_year,
            sabbath_compliant=self._check_sabbath_compliance(timestamp),
            gleaning_allocation=self.chain_params.gleaning_pool_percentage,
            tribal_signatures=tribal_signatures or {}
        )
        
        # Create the block
        block = EnhancedBlock(
            block_hash="",  # Will be calculated
            previous_hash=previous_hash,
            block_height=block_height,
            timestamp=timestamp,
            merkle_root="",  # Will be calculated
            proposer_id=proposer_id,
            signature="",  # Will be added by signer
            witness_signatures=witness_signatures or [],
            transactions=transactions,
            metadata=metadata
        )
        
        # Calculate merkle root and block hash
        block.merkle_root = block.calculate_merkle_root()
        block.block_hash = block.calculate_block_hash()
        block.size = len(json.dumps(block.to_dict()).encode())
        
        logger.info(f"Created enhanced block {block_height} with {len(transactions)} transactions")
        return block
    
    def _calculate_biblical_compliance(self, transactions: List[CovenantTransaction]) -> float:
        """Calculate biblical compliance score for the block"""
        if not transactions:
            return 1.0
        
        compliant_count = 0
        for tx in transactions:
            # Check for usury (interest-based transactions)
            if tx.type == "token_transfer" and "interest" not in tx.data:
                compliant_count += 1
            elif tx.type in ["identity_registration", "voice_scroll_proposal", "token_mint", "gleaning_distribution"]:
                compliant_count += 1
        
        return compliant_count / len(transactions)
    
    def _check_sabbath_compliance(self, timestamp: int) -> bool:
        """Check if the block timestamp complies with Sabbath rules"""
        # Simple implementation - in production this would check actual Sabbath times
        # For now, assume all blocks are Sabbath compliant
        return True
    
    def get_sample_block_json(self) -> Dict[str, Any]:
        """Get a sample block in the required JSON format"""
        
        # Create sample transactions
        identity_tx = self.create_identity_registration_transaction(
            identity_id="tribe_judah_001",
            public_key="pubkey123...",
            tribal_affiliation="Judah",
            witness_id="gatekeeper_01",
            witness_signature="sig123..."
        )
        
        transfer_tx = self.create_token_transfer_transaction(
            from_identity="identity_A",
            to_identity="identity_B",
            amount=50,
            token_type="mikvah",
            witness_id="gatekeeper_02",
            witness_signature="sig456..."
        )
        
        # Create sample block
        block = self.create_block(
            previous_hash="def456...",
            block_height=1024,
            proposer_id="elder_yehudah",
            transactions=[identity_tx, transfer_tx],
            witness_signatures=["sig1", "sig2"]
        )
        
        return block.to_dict()


# Global instance
enhanced_block_builder = EnhancedBlockBuilder()
