-- Onnyx Database Schema

-- Blocks table
CREATE TABLE IF NOT EXISTS blocks (
    index INTEGER PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    proposer TEXT NOT NULL,
    hash TEXT NOT NULL,
    previous_hash TEXT NOT NULL,
    nonce INTEGER DEFAULT 0,
    signature TEXT,
    signed_by TEXT
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    tx_id TEXT PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT,
    sender TEXT,
    signature TEXT,
    status TEXT DEFAULT 'pending',
    block_hash TEXT,
    created_at INTEGER
);

-- Identities table
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    public_key TEXT NOT NULL,
    nation TEXT,
    metadata TEXT,
    created_at INTEGER NOT NULL
);

-- Identity badges table
CREATE TABLE IF NOT EXISTS identity_badges (
    identity_id TEXT NOT NULL,
    badge TEXT NOT NULL,
    granted_at INTEGER NOT NULL,
    granted_by TEXT,
    PRIMARY KEY (identity_id, badge),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Selas table
CREATE TABLE IF NOT EXISTS selas (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    founder TEXT NOT NULL,
    type TEXT NOT NULL,
    token_type TEXT,
    metadata TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (founder) REFERENCES identities(identity_id)
);

-- Sela members table
CREATE TABLE IF NOT EXISTS sela_members (
    sela_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    role TEXT NOT NULL,
    joined_at INTEGER NOT NULL,
    PRIMARY KEY (sela_id, identity_id),
    FOREIGN KEY (sela_id) REFERENCES selas(id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Tokens table
CREATE TABLE IF NOT EXISTS tokens (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    decimals INTEGER DEFAULT 18,
    supply REAL NOT NULL,
    creator TEXT NOT NULL,
    category TEXT,
    metadata TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (creator) REFERENCES identities(identity_id)
);

-- Balances table
CREATE TABLE IF NOT EXISTS balances (
    identity_id TEXT NOT NULL,
    token_id TEXT NOT NULL,
    amount REAL NOT NULL,
    updated_at INTEGER NOT NULL,
    PRIMARY KEY (identity_id, token_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (token_id) REFERENCES tokens(id)
);

-- Etzem scores table
CREATE TABLE IF NOT EXISTS etzem_scores (
    identity_id TEXT PRIMARY KEY,
    consistency REAL DEFAULT 0,
    tx_score REAL DEFAULT 0,
    trust_weight REAL DEFAULT 0,
    badge_bonus REAL DEFAULT 0,
    sela_participation REAL DEFAULT 0,
    token_impact REAL DEFAULT 0,
    etzem REAL DEFAULT 0,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Zeman credits table
CREATE TABLE IF NOT EXISTS zeman_credits (
    identity_id TEXT NOT NULL,
    service_type TEXT NOT NULL,
    hours REAL NOT NULL,
    description TEXT,
    recipient_id TEXT,
    granted_at INTEGER NOT NULL,
    PRIMARY KEY (identity_id, granted_at),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (recipient_id) REFERENCES identities(identity_id)
);

-- Stakes table
CREATE TABLE IF NOT EXISTS stakes (
    identity_id TEXT NOT NULL,
    token_id TEXT NOT NULL,
    amount REAL NOT NULL,
    locked_until INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    PRIMARY KEY (identity_id, token_id, created_at),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (token_id) REFERENCES tokens(id)
);

-- Voice scrolls table
CREATE TABLE IF NOT EXISTS voice_scrolls (
    id TEXT PRIMARY KEY,
    creator TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    effect TEXT,
    created_at INTEGER NOT NULL,
    expires_at INTEGER NOT NULL,
    status TEXT NOT NULL,
    outcome TEXT NOT NULL,
    resolved_at INTEGER,
    FOREIGN KEY (creator) REFERENCES identities(identity_id)
);

-- Voice scroll votes table
CREATE TABLE IF NOT EXISTS voice_scroll_votes (
    scroll_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    decision TEXT NOT NULL,
    weight REAL NOT NULL,
    is_council_member INTEGER DEFAULT 0,
    voted_at INTEGER NOT NULL,
    PRIMARY KEY (scroll_id, identity_id),
    FOREIGN KEY (scroll_id) REFERENCES voice_scrolls(id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Chain parameters table
CREATE TABLE IF NOT EXISTS chain_parameters (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Event logs table
CREATE TABLE IF NOT EXISTS event_logs (
    block_index INTEGER PRIMARY KEY,
    block_hash TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    timestamp_human TEXT NOT NULL,
    proposer TEXT NOT NULL,
    tx_count INTEGER NOT NULL,
    token_mints INTEGER NOT NULL,
    token_transfers INTEGER NOT NULL,
    token_burns INTEGER NOT NULL,
    proposals INTEGER NOT NULL,
    votes INTEGER NOT NULL,
    identities INTEGER NOT NULL,
    reputation_grants INTEGER NOT NULL,
    stakes INTEGER NOT NULL,
    rewards INTEGER NOT NULL,
    notable_events TEXT NOT NULL,
    FOREIGN KEY (block_index) REFERENCES blocks(index)
);

-- Validator rotation table
CREATE TABLE IF NOT EXISTS validator_rotation (
    height INTEGER PRIMARY KEY,
    current_validator TEXT NOT NULL,
    last_update INTEGER NOT NULL,
    FOREIGN KEY (current_validator) REFERENCES selas(id)
);

-- Validator queue table
CREATE TABLE IF NOT EXISTS validator_queue (
    position INTEGER NOT NULL,
    sela_id TEXT NOT NULL,
    etzem_score REAL NOT NULL,
    updated_at INTEGER NOT NULL,
    PRIMARY KEY (position),
    FOREIGN KEY (sela_id) REFERENCES selas(id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender);
CREATE INDEX IF NOT EXISTS idx_transactions_op ON transactions(op);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_block_hash ON transactions(block_hash);

CREATE INDEX IF NOT EXISTS idx_identity_badges_identity_id ON identity_badges(identity_id);
CREATE INDEX IF NOT EXISTS idx_identity_badges_badge ON identity_badges(badge);

CREATE INDEX IF NOT EXISTS idx_selas_founder ON selas(founder);
CREATE INDEX IF NOT EXISTS idx_selas_type ON selas(type);

CREATE INDEX IF NOT EXISTS idx_sela_members_sela_id ON sela_members(sela_id);
CREATE INDEX IF NOT EXISTS idx_sela_members_identity_id ON sela_members(identity_id);

CREATE INDEX IF NOT EXISTS idx_tokens_creator ON tokens(creator);
CREATE INDEX IF NOT EXISTS idx_tokens_category ON tokens(category);

CREATE INDEX IF NOT EXISTS idx_balances_identity_id ON balances(identity_id);
CREATE INDEX IF NOT EXISTS idx_balances_token_id ON balances(token_id);

CREATE INDEX IF NOT EXISTS idx_etzem_scores_etzem ON etzem_scores(etzem);

CREATE INDEX IF NOT EXISTS idx_zeman_credits_identity_id ON zeman_credits(identity_id);
CREATE INDEX IF NOT EXISTS idx_zeman_credits_service_type ON zeman_credits(service_type);

CREATE INDEX IF NOT EXISTS idx_stakes_identity_id ON stakes(identity_id);
CREATE INDEX IF NOT EXISTS idx_stakes_token_id ON stakes(token_id);
CREATE INDEX IF NOT EXISTS idx_stakes_locked_until ON stakes(locked_until);

CREATE INDEX IF NOT EXISTS idx_voice_scrolls_creator ON voice_scrolls(creator);
CREATE INDEX IF NOT EXISTS idx_voice_scrolls_category ON voice_scrolls(category);
CREATE INDEX IF NOT EXISTS idx_voice_scrolls_status ON voice_scrolls(status);
CREATE INDEX IF NOT EXISTS idx_voice_scrolls_outcome ON voice_scrolls(outcome);

CREATE INDEX IF NOT EXISTS idx_voice_scroll_votes_scroll_id ON voice_scroll_votes(scroll_id);
CREATE INDEX IF NOT EXISTS idx_voice_scroll_votes_identity_id ON voice_scroll_votes(identity_id);
CREATE INDEX IF NOT EXISTS idx_voice_scroll_votes_decision ON voice_scroll_votes(decision);

CREATE INDEX IF NOT EXISTS idx_event_logs_timestamp ON event_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_event_logs_proposer ON event_logs(proposer);
