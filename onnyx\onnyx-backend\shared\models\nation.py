"""
Onnyx Nation Model

This module provides the Nation model for the Onnyx blockchain.
"""

import time
import logging
from typing import Dict, Any, List, Optional, ClassVar

from shared.models.base import BaseModel
from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.nation")

class Nation(BaseModel):
    """
    Nation model for the Onnyx blockchain.
    
    A Nation represents a group of identities that share common characteristics,
    governance, or geographic location.
    """
    
    # Table name
    table_name: ClassVar[str] = "nations"
    
    # Primary key column
    primary_key: ClassVar[str] = "nation_id"
    
    # JSON fields
    json_fields: ClassVar[List[str]] = ["metadata"]
    
    def __init__(
        self,
        nation_id: str,
        name: str,
        description: str,
        founder_id: str,
        metadata: Dict[str, Any] = None,
        status: str = "active",
        created_at: int = None,
        updated_at: int = None,
        **kwargs
    ):
        """
        Initialize the Nation model.
        
        Args:
            nation_id: The nation ID
            name: The nation name
            description: The nation description
            founder_id: The identity ID of the founder
            metadata: The nation metadata
            status: The nation status
            created_at: The creation timestamp
            updated_at: The update timestamp
            **kwargs: Additional attributes
        """
        self.nation_id = nation_id
        self.name = name
        self.description = description
        self.founder_id = founder_id
        self.metadata = metadata or {}
        self.status = status
        self.created_at = created_at or int(time.time())
        self.updated_at = updated_at or int(time.time())
        
        super().__init__(**kwargs)
    
    @classmethod
    def get_by_name(cls, name: str) -> Optional['Nation']:
        """
        Get a nation by name.
        
        Args:
            name: The nation name
        
        Returns:
            The nation or None if not found
        """
        query = f"SELECT * FROM {cls.table_name} WHERE name = ?"
        row = db.query_one(query, (name,))
        
        if row:
            return cls.from_dict(row)
        
        return None
    
    def get_members(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get the identities that are members of this nation.
        
        Args:
            limit: The maximum number of identities to return
            offset: The offset for pagination
        
        Returns:
            A list of identity entries
        """
        query = """
            SELECT *
            FROM identities
            WHERE nation_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """
        return db.query(query, (self.nation_id, limit, offset))
    
    def get_member_count(self) -> int:
        """
        Get the number of identities that are members of this nation.
        
        Returns:
            The number of members
        """
        query = "SELECT COUNT(*) as count FROM identities WHERE nation_id = ?"
        row = db.query_one(query, (self.nation_id,))
        
        return row["count"] if row else 0
    
    def get_total_etzem_score(self) -> int:
        """
        Get the total Etzem score of all identities in this nation.
        
        Returns:
            The total Etzem score
        """
        query = """
            SELECT SUM(score) as total_score
            FROM etzem_scores
            JOIN identities ON etzem_scores.identity_id = identities.identity_id
            WHERE identities.nation_id = ?
        """
        row = db.query_one(query, (self.nation_id,))
        
        return row["total_score"] if row and row["total_score"] else 0
    
    def save(self) -> None:
        """
        Save the nation.
        
        Updates the updated_at timestamp before saving.
        """
        self.updated_at = int(time.time())
        super().save()
    
    @classmethod
    def create(
        cls,
        name: str,
        description: str,
        founder_id: str,
        metadata: Dict[str, Any] = None
    ) -> 'Nation':
        """
        Create a new nation.
        
        Args:
            name: The nation name
            description: The nation description
            founder_id: The identity ID of the founder
            metadata: The nation metadata
        
        Returns:
            The created nation
        """
        # Generate the nation ID from the name
        import hashlib
        nation_id = hashlib.sha256(name.encode()).hexdigest()
        
        # Create the nation
        nation = cls(
            nation_id=nation_id,
            name=name,
            description=description,
            founder_id=founder_id,
            metadata=metadata or {}
        )
        
        # Save the nation
        nation.save()
        
        return nation
    
    @classmethod
    def create_table(cls) -> None:
        """
        Create the nations table if it doesn't exist.
        """
        query = f"""
        CREATE TABLE IF NOT EXISTS {cls.table_name} (
            nation_id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            description TEXT NOT NULL,
            founder_id TEXT NOT NULL,
            metadata TEXT,
            status TEXT NOT NULL,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            FOREIGN KEY (founder_id) REFERENCES identities (identity_id)
        )
        """
        db.execute(query)
