{% extends "base.html" %}

{% block title %}Configure Auto-Mining - {{ validator.name }} - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .config-section {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .config-section:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.3);
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        font-family: 'Orbitron', monospace;
        font-weight: 600;
        color: var(--cyber-cyan);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .form-input, .form-select {
        width: 100%;
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 12px 16px;
        color: white;
        font-family: 'Montserrat', sans-serif;
        transition: all 0.3s ease;
    }
    
    .form-input:focus, .form-select:focus {
        outline: none;
        border-color: var(--cyber-cyan);
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        background: rgba(255, 255, 255, 0.12);
    }
    
    .form-checkbox {
        width: 20px;
        height: 20px;
        background: var(--glass-bg);
        border: 2px solid var(--glass-border);
        border-radius: 4px;
        transition: all 0.3s ease;
        margin-right: 12px;
    }
    
    .form-checkbox:checked {
        background: var(--cyber-cyan);
        border-color: var(--cyber-cyan);
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    }
    
    .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .help-text {
        font-size: 0.85rem;
        color: var(--text-tertiary);
        margin-top: 0.5rem;
        line-height: 1.4;
    }
    
    .validator-info {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .tier-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .tier-basic {
        background: rgba(107, 114, 128, 0.2);
        color: #9ca3af;
        border: 1px solid rgba(107, 114, 128, 0.3);
    }
    
    .tier-optimized {
        background: rgba(0, 212, 255, 0.2);
        color: var(--cyber-cyan);
        border: 1px solid rgba(0, 212, 255, 0.3);
    }
    
    .tier-pro {
        background: rgba(139, 92, 246, 0.2);
        color: var(--cyber-purple);
        border: 1px solid rgba(139, 92, 246, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('auto_mining.dashboard') }}" 
               class="glass-button px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                ← Back to Dashboard
            </a>
        </div>
        
        <h1 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">
            ⚙️ Configure Auto-Mining
        </h1>
        <p class="text-xl text-text-secondary">
            Set up automated mining for {{ validator.name }}
        </p>
    </div>

    <!-- Validator Information -->
    <div class="validator-info">
        <div class="flex items-start justify-between">
            <div>
                <h2 class="text-2xl font-orbitron font-bold text-white mb-2">
                    {{ validator.name }}
                </h2>
                <p class="text-text-tertiary mb-3">
                    {{ validator.category }} • Validator ID: {{ validator.sela_id[:8] }}...
                </p>
                <div class="flex items-center space-x-4">
                    <span class="tier-badge tier-{{ validator.mining_tier or 'basic' }}">
                        {{ (validator.mining_tier or 'basic')|title }} Tier
                    </span>
                    <span class="text-sm text-text-tertiary">
                        Mining Power: <span class="text-cyber-cyan font-mono">{{ validator.mining_power or 1 }}x</span>
                    </span>
                </div>
            </div>
            
            <div class="text-right">
                <div class="text-sm text-text-tertiary">Current Status</div>
                <div class="text-lg font-orbitron font-bold">
                    {% if mining_config.get('mining', False) %}
                    <span class="text-green-400">🟢 Mining</span>
                    {% elif mining_config.get('enabled', False) %}
                    <span class="text-yellow-400">🟡 Enabled</span>
                    {% else %}
                    <span class="text-gray-400">⚪ Disabled</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Form -->
    <form method="POST" class="space-y-6">
        <!-- Basic Settings -->
        <div class="config-section">
            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">
                🔧 Basic Settings
            </h3>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="enabled" class="form-checkbox" 
                           {% if mining_config.get('enabled', False) %}checked{% endif %}>
                    <span>Enable Auto-Mining for this validator</span>
                </label>
                <div class="help-text">
                    When enabled, this validator will automatically participate in mining operations
                    according to the configured schedule and settings.
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Mining Interval (seconds)</label>
                <input type="number" name="custom_interval" class="form-input" 
                       value="{{ mining_config.get('custom_interval', 10) }}" 
                       min="5" max="300" step="1">
                <div class="help-text">
                    Time between mining attempts. Lower values mean more frequent mining but higher resource usage.
                    Recommended: 10-30 seconds for optimal performance.
                </div>
            </div>
        </div>

        <!-- Schedule Settings -->
        <div class="config-section">
            <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                ⏰ Mining Schedule
            </h3>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="schedule_enabled" class="form-checkbox" 
                           {% if mining_config.get('schedule', {}).get('enabled', False) %}checked{% endif %}
                           onchange="toggleScheduleSettings(this.checked)">
                    <span>Enable Scheduled Mining</span>
                </label>
                <div class="help-text">
                    Restrict mining to specific hours of the day. Useful for businesses that want to
                    mine only during operating hours or off-peak times.
                </div>
            </div>
            
            <div id="schedule-settings" style="{% if not mining_config.get('schedule', {}).get('enabled', False) %}display: none;{% endif %}">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-group">
                        <label class="form-label">Start Time</label>
                        <input type="time" name="start_time" class="form-input" 
                               value="{{ mining_config.get('schedule', {}).get('start_time', '09:00') }}">
                        <div class="help-text">
                            Mining will start at this time each day
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">End Time</label>
                        <input type="time" name="end_time" class="form-input" 
                               value="{{ mining_config.get('schedule', {}).get('end_time', '17:00') }}">
                        <div class="help-text">
                            Mining will stop at this time each day
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Mining Days</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {% set days = [
                            ('monday', 'Monday'),
                            ('tuesday', 'Tuesday'),
                            ('wednesday', 'Wednesday'),
                            ('thursday', 'Thursday'),
                            ('friday', 'Friday'),
                            ('saturday', 'Saturday'),
                            ('sunday', 'Sunday')
                        ] %}
                        {% for day_key, day_name in days %}
                        <label class="checkbox-label">
                            <input type="checkbox" name="mining_days" value="{{ day_key }}" class="form-checkbox"
                                   {% if day_key in mining_config.get('schedule', {}).get('days', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']) %}checked{% endif %}>
                            <span class="text-sm">{{ day_name }}</span>
                        </label>
                        {% endfor %}
                    </div>
                    <div class="help-text">
                        Select which days of the week mining should be active
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Settings -->
        <div class="config-section">
            <h3 class="text-xl font-orbitron font-bold text-cyber-blue mb-4">
                🚀 Performance & Monitoring
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-orbitron font-semibold text-white mb-3">Current Performance</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Blocks Mined:</span>
                            <span class="text-green-400 font-mono">{{ validator.blocks_mined or 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Total Earnings:</span>
                            <span class="text-cyber-purple font-mono">{{ "%.2f"|format(validator.mining_rewards_earned or 0) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Mining Tier:</span>
                            <span class="text-cyber-cyan">{{ (validator.mining_tier or 'basic')|title }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Mining Power:</span>
                            <span class="text-cyber-blue font-mono">{{ validator.mining_power or 1 }}x</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-orbitron font-semibold text-white mb-3">Auto-Mining Stats</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Current Uptime:</span>
                            <span class="text-green-400 font-mono">{{ mining_config.get('uptime', '0:00:00') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Restart Attempts:</span>
                            <span class="text-yellow-400 font-mono">{{ mining_config.get('restart_attempts', 0) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Process ID:</span>
                            <span class="text-cyber-cyan font-mono">
                                {% if mining_config.get('process_id') %}
                                {{ mining_config.process_id }}
                                {% else %}
                                Not Running
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-tertiary">Last Restart:</span>
                            <span class="text-text-secondary text-sm">
                                {% if mining_config.get('last_restart') %}
                                {{ mining_config.last_restart[:19] }}
                                {% else %}
                                Never
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-4">
            <button type="submit" 
                    class="glass-button-primary px-8 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                💾 Save Configuration
            </button>
            
            <a href="{{ url_for('auto_mining.dashboard') }}" 
               class="glass-button px-8 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                ❌ Cancel
            </a>
            
            <a href="{{ url_for('sela.profile', sela_id=validator.sela_id) }}" 
               class="glass-button px-8 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                📊 View Validator Details
            </a>
        </div>
    </form>
</div>

<script>
function toggleScheduleSettings(enabled) {
    const scheduleSettings = document.getElementById('schedule-settings');
    if (enabled) {
        scheduleSettings.style.display = 'block';
    } else {
        scheduleSettings.style.display = 'none';
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const interval = document.querySelector('input[name="custom_interval"]').value;
    
    if (interval < 5 || interval > 300) {
        e.preventDefault();
        alert('Mining interval must be between 5 and 300 seconds');
        return false;
    }
    
    const scheduleEnabled = document.querySelector('input[name="schedule_enabled"]').checked;
    if (scheduleEnabled) {
        const startTime = document.querySelector('input[name="start_time"]').value;
        const endTime = document.querySelector('input[name="end_time"]').value;
        
        if (startTime >= endTime) {
            e.preventDefault();
            alert('Start time must be before end time');
            return false;
        }
    }
    
    return true;
});
</script>
{% endblock %}
