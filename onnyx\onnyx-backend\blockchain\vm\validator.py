"""
Onnyx VM Transaction Validator

This module detects the operation type from a transaction and routes it to the
appropriate validation function from opcodes.py.
"""

import sys
import os
import json
import time
import logging
from typing import Dict, Any, Tuple, Optional, List, Union

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.vm.validator")

from opcodes import (
    # Operation codes
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    OP_BURN, OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE, OP_REWARD,

    # Validation functions
    op_mint, op_send, op_identity, op_scroll,
    op_burn, op_grant_reputation, op_stake, op_vote, op_reward,

    # Error types
    OpcodeError
)

class ValidationError(Exception):
    """Exception raised for transaction validation errors."""
    pass

def validate_transaction(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Routes the transaction to the correct validation function based on op type.

    Args:
        tx: The transaction to validate (can be a dict or Transaction object)

    Returns:
        bool: True if the transaction is valid

    Raises:
        ValidationError: If the transaction is invalid
    """
    # Convert Transaction object to dict if needed
    if isinstance(tx, Transaction):
        tx_dict = {
            "tx_id": tx.tx_id,
            "op": tx.op,
            "data": tx.data,
            "sender": tx.sender,
            "signature": tx.signature,
            "timestamp": tx.timestamp
        }
    elif isinstance(tx, dict):
        tx_dict = tx
    else:
        raise ValidationError("Transaction must be a dictionary or Transaction object")

    logger.debug(f"Validating transaction: {tx_dict.get('tx_id', 'unknown')}")

    # Check for op field first, then fall back to type field
    op = tx_dict.get("op")

    if not op:
        # Try to get the type and map it to an opcode
        tx_type = tx_dict.get("type")
        if not tx_type:
            logger.error("Transaction missing 'op' or 'type' field")
            raise ValidationError("Transaction missing 'op' or 'type' field")

        # Map transaction type to opcode
        type_to_op = {
            "mint": OP_MINT,
            "send": OP_SEND,
            "identity": OP_IDENTITY,
            "scroll": OP_SCROLL,
            "burn": OP_BURN,
            "grant_reputation": OP_GRANT_REPUTATION,
            "stake": OP_STAKE,
            "vote": OP_VOTE,
            "reward": OP_REWARD
        }

        op = type_to_op.get(tx_type.lower())
        if not op:
            logger.error(f"Unknown transaction type: {tx_type}")
            raise ValidationError(f"Unknown transaction type: {tx_type}")

    try:
        if op == OP_MINT:
            result = op_mint(tx_dict)
            logger.info(f"Validated MINT transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_SEND:
            result = op_send(tx_dict)
            logger.info(f"Validated SEND transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_IDENTITY:
            result = op_identity(tx_dict)
            logger.info(f"Validated IDENTITY transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_SCROLL:
            result = op_scroll(tx_dict)
            logger.info(f"Validated SCROLL transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_BURN:
            result = op_burn(tx_dict)
            logger.info(f"Validated BURN transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_GRANT_REPUTATION:
            result = op_grant_reputation(tx_dict)
            logger.info(f"Validated GRANT_REPUTATION transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_STAKE:
            result = op_stake(tx_dict)
            logger.info(f"Validated STAKE transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_VOTE:
            result = op_vote(tx_dict)
            logger.info(f"Validated VOTE transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        elif op == OP_REWARD:
            result = op_reward(tx_dict)
            logger.info(f"Validated REWARD transaction: {tx_dict.get('tx_id', 'unknown')}")
            return result
        else:
            logger.error(f"Unknown opcode: {op}")
            raise ValidationError(f"Unknown opcode: {op}")
    except OpcodeError as e:
        # Re-raise opcode errors as validation errors
        logger.error(f"Validation error: {str(e)}")
        raise ValidationError(str(e))

def validate_transaction_safe(tx: Union[Dict[str, Any], Transaction]) -> Tuple[bool, Optional[str]]:
    """
    Safely validates a transaction without raising exceptions.

    Args:
        tx: The transaction to validate (can be a dict or Transaction object)

    Returns:
        A tuple of (is_valid, error_message)
    """
    try:
        validate_transaction(tx)
        return True, None
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Unexpected error during validation: {str(e)}")
        return False, f"Unexpected error: {str(e)}"

def validate_transactions(txs: List[Union[Dict[str, Any], Transaction]]) -> Tuple[List[Union[Dict[str, Any], Transaction]], List[Dict[str, Any]]]:
    """
    Validates a list of transactions.

    Args:
        txs: A list of transactions to validate (can be dicts or Transaction objects)

    Returns:
        A tuple of (valid_transactions, invalid_transactions)
    """
    valid_txs = []
    invalid_txs = []

    logger.info(f"Validating {len(txs)} transactions")

    for tx in txs:
        is_valid, error = validate_transaction_safe(tx)
        if is_valid:
            valid_txs.append(tx)
        else:
            # Get the transaction ID if available
            tx_id = tx.tx_id if isinstance(tx, Transaction) else tx.get("tx_id", "unknown")

            invalid_txs.append({
                "tx": tx,
                "tx_id": tx_id,
                "error": error
            })

    logger.info(f"Validation complete: {len(valid_txs)} valid, {len(invalid_txs)} invalid")
    return valid_txs, invalid_txs

def format_validation_error(error: str) -> Dict[str, Any]:
    """
    Formats a validation error into a structured response.

    Args:
        error: The error message

    Returns:
        A dictionary with error details
    """
    # Parse the error message to extract more details
    error_type = "ValidationError"
    error_code = "UNKNOWN"

    if "missing" in error.lower():
        error_code = "MISSING_FIELD"
    elif "unknown" in error.lower():
        error_code = "UNKNOWN_OPCODE"
    elif "denied" in error.lower():
        error_code = "VALIDATION_FAILED"

        # Extract the specific denial reason
        if ":" in error:
            error_type = error.split(":")[0].strip()
    elif "insufficient" in error.lower():
        error_code = "INSUFFICIENT_FUNDS"
    elif "exists" in error.lower():
        error_code = "ALREADY_EXISTS"
    elif "permission" in error.lower():
        error_code = "PERMISSION_DENIED"

    logger.debug(f"Formatted error: {error_type} - {error_code}")

    return {
        "error": True,
        "error_type": error_type,
        "error_code": error_code,
        "message": error,
        "timestamp": int(time.time())
    }

def get_transaction_type(tx: Union[Dict[str, Any], Transaction]) -> Optional[str]:
    """
    Gets the human-readable transaction type from a transaction.

    Args:
        tx: The transaction (can be a dict or Transaction object)

    Returns:
        The transaction type or None if unknown
    """
    # Get the operation code
    if isinstance(tx, Transaction):
        op = tx.op
    else:
        op = tx.get("op")

    if not op:
        logger.warning("Cannot determine transaction type: missing 'op' field")
        return None

    # Map operation codes to human-readable types
    type_map = {
        OP_MINT: "Token Minting",
        OP_SEND: "Token Transfer",
        OP_IDENTITY: "Identity Creation",
        OP_SCROLL: "Governance Proposal",
        OP_BURN: "Token Burning",
        OP_GRANT_REPUTATION: "Reputation Grant",
        OP_STAKE: "Token Staking",
        OP_VOTE: "Governance Vote",
        OP_REWARD: "Reward Distribution"
    }

    tx_type = type_map.get(op)
    if not tx_type:
        logger.warning(f"Unknown transaction type for op: {op}")

    return tx_type

def summarize_transaction(tx: Union[Dict[str, Any], Transaction]) -> Dict[str, Any]:
    """
    Creates a human-readable summary of a transaction.

    Args:
        tx: The transaction to summarize (can be a dict or Transaction object)

    Returns:
        A dictionary with transaction summary
    """
    # Get the transaction type
    tx_type = get_transaction_type(tx)

    if not tx_type:
        return {
            "type": "Unknown",
            "description": "Unknown transaction type",
            "timestamp": int(time.time())
        }

    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        op = tx.op
        sender = tx.sender
        data = tx.data
        tx_id = tx.tx_id
        timestamp = tx.timestamp
    else:
        op = tx.get("op")
        sender = tx.get("from", tx.get("sender", "N/A"))
        data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")
        timestamp = tx.get("timestamp", int(time.time()))

    # Create the summary
    summary = {
        "tx_id": tx_id,
        "type": tx_type,
        "from": sender,
        "timestamp": timestamp
    }

    # Add operation-specific description
    if op == OP_MINT:
        summary["description"] = f"Mint {data.get('supply', 'unknown')} {data.get('symbol', 'unknown')} tokens"
    elif op == OP_SEND:
        summary["description"] = f"Send {data.get('amount', 'unknown')} {data.get('token_id', 'unknown')} tokens to {data.get('to', 'unknown')}"
    elif op == OP_IDENTITY:
        summary["description"] = f"Create identity {data.get('identity_id', 'unknown')} ({data.get('name', 'unknown')})"
    elif op == OP_SCROLL:
        summary["description"] = f"Propose governance scroll: {data.get('title', 'unknown')}"
    elif op == OP_BURN:
        summary["description"] = f"Burn {data.get('amount', 'unknown')} {data.get('token_id', 'unknown')} tokens"
    elif op == OP_GRANT_REPUTATION:
        summary["description"] = f"Grant {data.get('amount', 'unknown')} reputation to {data.get('to_identity', 'unknown')}"
    elif op == OP_STAKE:
        summary["description"] = f"Stake {data.get('amount', 'unknown')} {data.get('token_id', 'unknown')} tokens for {data.get('duration', 'unknown')} days"
    elif op == OP_VOTE:
        summary["description"] = f"Vote {data.get('vote', 'unknown')} on scroll {data.get('scroll_id', 'unknown')}"
    elif op == OP_REWARD:
        summary["description"] = f"Distribute {data.get('amount', 'unknown')} {data.get('token_id', 'unknown')} tokens as rewards"
    else:
        summary["description"] = "Unknown operation"

    logger.debug(f"Transaction summary: {summary['description']}")
    return summary
