#!/usr/bin/env python3
"""
Fix Tribal Affiliation Column Migration

This script adds the missing 'tribal_affiliation' column to the identities table
and ensures compatibility with Eden Mode identity creation.
"""

import os
import sys
import sqlite3
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table"""
    try:
        result = db.query(f"PRAGMA table_info({table_name})")
        columns = [row['name'] if isinstance(row, dict) else row[1] for row in result]
        return column_name in columns
    except Exception as e:
        logger.error(f"Error checking column existence: {e}")
        return False

def add_tribal_affiliation_column():
    """Add tribal_affiliation column to identities table"""
    try:
        # Check if column already exists
        if check_column_exists('identities', 'tribal_affiliation'):
            logger.info("✅ tribal_affiliation column already exists")
            return True
        
        # Add the column
        logger.info("Adding tribal_affiliation column to identities table...")
        db.execute("ALTER TABLE identities ADD COLUMN tribal_affiliation TEXT DEFAULT ''")
        
        # Copy data from tribe_name to tribal_affiliation if tribe_name exists
        if check_column_exists('identities', 'tribe_name'):
            logger.info("Copying data from tribe_name to tribal_affiliation...")
            db.execute("UPDATE identities SET tribal_affiliation = tribe_name WHERE tribe_name IS NOT NULL")
        
        logger.info("✅ Successfully added tribal_affiliation column")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error adding tribal_affiliation column: {e}")
        return False

def add_missing_eden_mode_columns():
    """Add any other missing columns needed for Eden Mode"""
    try:
        columns_to_add = [
            ('status', 'TEXT DEFAULT "active"'),
            ('role_class', 'TEXT DEFAULT "citizen"'),
            ('verification_level', 'INTEGER DEFAULT 0'),
            ('protection_tier', 'TEXT DEFAULT "Basic"'),
            ('etzem_score', 'INTEGER DEFAULT 100'),
            ('zeman_count', 'INTEGER DEFAULT 0'),
            ('nation_of_origin', 'TEXT DEFAULT ""'),
            ('nation_name', 'TEXT DEFAULT ""'),
            ('vault_status', 'TEXT DEFAULT "active"'),
            ('sabbath_observer', 'BOOLEAN DEFAULT TRUE'),
            ('deeds_score', 'REAL DEFAULT 50.0'),
            ('timezone', 'TEXT DEFAULT "UTC"')
        ]
        
        for column_name, column_def in columns_to_add:
            if not check_column_exists('identities', column_name):
                logger.info(f"Adding {column_name} column...")
                db.execute(f"ALTER TABLE identities ADD COLUMN {column_name} {column_def}")
                logger.info(f"✅ Added {column_name} column")
            else:
                logger.info(f"✅ {column_name} column already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error adding Eden Mode columns: {e}")
        return False

def verify_identities_table():
    """Verify the identities table structure"""
    try:
        logger.info("Verifying identities table structure...")
        
        # Get table info
        result = db.query("PRAGMA table_info(identities)")
        
        logger.info("Current identities table columns:")
        for row in result:
            if isinstance(row, dict):
                logger.info(f"  - {row['name']} ({row['type']})")
            else:
                logger.info(f"  - {row[1]} ({row[2]})")
        
        # Check for required columns
        required_columns = [
            'identity_id', 'name', 'email', 'public_key', 'tribal_affiliation',
            'status', 'role_class', 'verification_level', 'covenant_accepted',
            'eden_mode_completed', 'metadata', 'created_at', 'updated_at'
        ]
        
        existing_columns = [row['name'] if isinstance(row, dict) else row[1] for row in result]
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        
        if missing_columns:
            logger.warning(f"⚠️ Missing columns: {missing_columns}")
            return False
        else:
            logger.info("✅ All required columns present")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error verifying table structure: {e}")
        return False

def test_identity_creation():
    """Test identity creation with the fixed schema"""
    try:
        logger.info("Testing identity creation...")
        
        # Test data
        test_identity = {
            'identity_id': 'TEST_TRIBAL_FIX_123',
            'name': 'Test User',
            'email': '<EMAIL>',
            'public_key': 'test_public_key_123',
            'tribal_affiliation': 'Judah',
            'status': 'active',
            'role_class': 'citizen',
            'verification_level': 1,
            'covenant_accepted': True,
            'eden_mode_completed': True,
            'metadata': '{"test": true}',
            'created_at': int(time.time()),
            'updated_at': int(time.time())
        }
        
        # Try to insert test identity
        columns = ', '.join(test_identity.keys())
        placeholders = ', '.join(['?' for _ in test_identity.keys()])
        values = list(test_identity.values())
        
        db.execute(f"INSERT OR REPLACE INTO identities ({columns}) VALUES ({placeholders})", values)
        
        # Verify insertion
        result = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (test_identity['identity_id'],))
        
        if result:
            logger.info("✅ Test identity creation successful")
            
            # Clean up test data
            db.execute("DELETE FROM identities WHERE identity_id = ?", (test_identity['identity_id'],))
            logger.info("✅ Test data cleaned up")
            return True
        else:
            logger.error("❌ Test identity creation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing identity creation: {e}")
        return False

def main():
    """Main migration function"""
    logger.info("🔧 Starting Tribal Affiliation Column Fix...")
    logger.info("=" * 60)
    
    try:
        import time
        
        # Step 1: Add tribal_affiliation column
        if not add_tribal_affiliation_column():
            logger.error("❌ Failed to add tribal_affiliation column")
            return 1
        
        # Step 2: Add other missing Eden Mode columns
        if not add_missing_eden_mode_columns():
            logger.error("❌ Failed to add Eden Mode columns")
            return 1
        
        # Step 3: Verify table structure
        if not verify_identities_table():
            logger.error("❌ Table verification failed")
            return 1
        
        # Step 4: Test identity creation
        if not test_identity_creation():
            logger.error("❌ Identity creation test failed")
            return 1
        
        logger.info("=" * 60)
        logger.info("🎉 Tribal Affiliation Column Fix Complete!")
        logger.info("✅ Eden Mode identity creation should now work")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
