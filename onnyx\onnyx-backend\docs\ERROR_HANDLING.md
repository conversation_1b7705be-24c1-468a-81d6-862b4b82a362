# Biblical Tokenomics Error Handling & Edge Cases

This document covers error handling strategies and edge cases for the biblical tokenomics system.

## Error Handling Strategy

### 1. Graceful Degradation
The system is designed to gracefully degrade when components fail:

- **Tokenomics Module Failure**: Falls back to standard mining rewards
- **Database Errors**: Logs errors and continues with default values
- **API Failures**: Returns appropriate HTTP status codes with error messages

### 2. Logging and Monitoring
All errors are logged with appropriate severity levels:

```python
# Example error logging
logger.error(f"Error calculating tiered mining reward: {e}")
logger.warning(f"Concentration limit exceeded by {identity_id}")
logger.info(f"Sabbath period detected - mining rewards blocked")
```

## Edge Cases and Solutions

### 1. Sabbath Period Transitions

**Edge Case**: Mining operation starts before Sabbath but completes during Sabbath

**Solution**:
```python
def create_coinbase_tx(self, proposer_id: str, block_height: int = None) -> List[Dict[str, Any]]:
    # Check Sabbath status at the start of mining
    if biblical_tokenomics.is_sabbath_period():
        logger.info("Sabbath period detected - mining rewards blocked")
        biblical_tokenomics.record_sabbath_observance(proposer_id, block_height or 0)
        return []  # No mining during Sabbath
```

**Edge Case**: Timezone differences causing incorrect Sabbath detection

**Solution**: Use UTC consistently and allow configuration of timezone offsets:
```python
# In chain_parameters.py
"sabbath_timezone_offset": 0,  # UTC offset in hours
```

### 2. Loan Forgiveness Automation

**Edge Case**: Borrower makes payment exactly at grace period expiration

**Solution**: Use block height for deterministic timing:
```python
def check_loan_forgiveness(self, current_block: int) -> List[str]:
    for loan in loans:
        blocks_elapsed = current_block - loan["created_block"]
        payment_ratio = loan["amount_paid"] / loan["amount"] if loan["amount"] > 0 else 0
        
        # Use >= for inclusive grace period
        if (blocks_elapsed >= loan["grace_blocks"] and 
            payment_ratio >= loan["forgiveness_threshold"]):
            self.forgive_loan(loan["loan_id"], "SYSTEM_AUTO")
```

**Edge Case**: Loan amount is zero or negative

**Solution**: Validate loan amounts during creation:
```python
def create_loan(self, lender_id: str, borrower_id: str, amount: float, ...):
    if amount <= 0:
        logger.error(f"Invalid loan amount: {amount}")
        return ""
```

### 3. Concentration Penalty Enforcement

**Edge Case**: Balance calculation includes staked or locked tokens

**Solution**: Only count liquid balances for concentration limits:
```python
def _get_total_balance(self, identity_id: str) -> float:
    # Only count liquid balances, exclude staked tokens
    result = db.query_one("""
        SELECT SUM(balance) as total_balance 
        FROM token_balances 
        WHERE identity_id = ? AND token_id NOT LIKE '%_STAKED'
    """, (identity_id,))
    return result["total_balance"] if result and result["total_balance"] else 0.0
```

**Edge Case**: Rapid balance changes during mining

**Solution**: Use snapshot-based calculations:
```python
def enforce_concentration_limits(self, identity_id: str, current_block: int) -> bool:
    # Take balance snapshot at specific block height
    total_balance = self._get_balance_at_block(identity_id, current_block)
    # ... rest of logic
```

### 4. Token Classification Edge Cases

**Edge Case**: Token already has a different classification

**Solution**: Allow reclassification with audit trail:
```python
def assign_token_class(self, token_id: str, class_type: str, metadata: str = "{}") -> bool:
    # Check for existing classification
    existing = self.get_token_class(token_id)
    if existing and existing != class_type:
        logger.info(f"Reclassifying token {token_id} from {existing} to {class_type}")
        
        # Record the change in metadata
        metadata_dict = json.loads(metadata)
        metadata_dict["previous_class"] = existing
        metadata_dict["reclassified_at"] = int(time.time())
        metadata = json.dumps(metadata_dict)
    
    # Proceed with classification
    db.execute("""
        INSERT OR REPLACE INTO token_classes (token_id, class_type, class_metadata, assigned_at)
        VALUES (?, ?, ?, ?)
    """, (token_id, class_type, metadata, int(time.time())))
```

### 5. Deed Score Manipulation

**Edge Case**: Artificial deed score inflation

**Solution**: Implement deed validation and decay:
```python
def record_deed(self, identity_id: str, deed_type: str, deed_value: float, ...):
    # Validate deed value based on type
    max_values = {
        "MUTUAL_AID": 10.0,
        "DONATION": 5.0,
        "FIRSTFRUITS": float('inf'),  # No limit on offerings
        "SABBATH_OBSERVANCE": 0.2
    }
    
    max_value = max_values.get(deed_type, 1.0)
    if deed_value > max_value:
        logger.warning(f"Deed value {deed_value} exceeds maximum {max_value} for {deed_type}")
        deed_value = max_value
    
    # Apply deed score decay over time
    self._apply_deed_decay(identity_id)
    
    # Record the deed
    # ... rest of logic
```

### 6. Gleaning Pool Edge Cases

**Edge Case**: Pool balance becomes negative due to concurrent claims

**Solution**: Use database transactions for atomic operations:
```python
def claim_from_gleaning_pool(self, claimant_id: str, amount: float, justification: str) -> bool:
    try:
        # Start transaction
        db.begin_transaction()
        
        # Check current balance with row lock
        pool_result = db.query_one("""
            SELECT total_amount FROM jubilee_pools 
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
            FOR UPDATE
        """)
        
        pool_balance = pool_result['total_amount'] if pool_result else 0.0
        
        if amount > pool_balance:
            db.rollback_transaction()
            return False
        
        # Deduct from pool and credit to claimant
        # ... update operations
        
        db.commit_transaction()
        return True
        
    except Exception as e:
        db.rollback_transaction()
        logger.error(f"Error in gleaning pool claim: {e}")
        return False
```

### 7. Mining Reward Calculation Edge Cases

**Edge Case**: Deed score becomes negative

**Solution**: Use absolute values and bounds checking:
```python
def calculate_tiered_mining_reward(self, proposer_id: str, base_reward: float) -> Tuple[float, float]:
    deed_score = max(0, self._get_deed_score(proposer_id))  # Ensure non-negative
    
    # Calculate deed bonus with bounds
    deed_multiplier = chain_parameters.get("deed_score_multiplier", 0.1)
    deed_bonus = min(deed_score * deed_multiplier, deed_multiplier)  # Cap at multiplier
    
    # ... rest of calculation
```

**Edge Case**: Base reward is zero or negative

**Solution**: Validate inputs and use minimum bounds:
```python
def calculate_tiered_mining_reward(self, proposer_id: str, base_reward: float) -> Tuple[float, float]:
    if base_reward <= 0:
        logger.warning(f"Invalid base reward: {base_reward}, using minimum")
        base_reward = chain_parameters.get("min_block_reward", 2)
    
    # ... rest of calculation
```

## Database Error Handling

### 1. Connection Failures
```python
def safe_db_operation(operation_func, *args, **kwargs):
    """Safely execute database operations with retry logic."""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            return operation_func(*args, **kwargs)
        except Exception as e:
            logger.warning(f"Database operation failed (attempt {attempt + 1}): {e}")
            if attempt == max_retries - 1:
                logger.error(f"Database operation failed after {max_retries} attempts")
                raise
            time.sleep(0.1 * (2 ** attempt))  # Exponential backoff
```

### 2. Data Integrity Issues
```python
def validate_data_integrity():
    """Validate biblical tokenomics data integrity."""
    issues = []
    
    # Check for orphaned deeds
    orphaned_deeds = db.query("""
        SELECT COUNT(*) as count FROM deeds_ledger d 
        LEFT JOIN identities i ON d.identity_id = i.identity_id 
        WHERE i.identity_id IS NULL
    """)
    if orphaned_deeds[0]['count'] > 0:
        issues.append(f"Found {orphaned_deeds[0]['count']} orphaned deeds")
    
    # Check for negative pool balances
    negative_pools = db.query("""
        SELECT pool_id, total_amount FROM jubilee_pools 
        WHERE total_amount < 0
    """)
    if negative_pools:
        issues.append(f"Found {len(negative_pools)} pools with negative balances")
    
    return issues
```

## API Error Handling

### 1. Input Validation
```python
def validate_api_input(data, required_fields, field_types):
    """Validate API input data."""
    errors = []
    
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")
    
    for field, expected_type in field_types.items():
        if field in data and not isinstance(data[field], expected_type):
            errors.append(f"Field {field} must be of type {expected_type.__name__}")
    
    return errors
```

### 2. Rate Limiting
```python
from functools import wraps
import time

def rate_limit(max_calls=10, window=60):
    """Rate limiting decorator for API endpoints."""
    calls = {}
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            client_id = request.remote_addr  # Use IP as client identifier
            
            if client_id not in calls:
                calls[client_id] = []
            
            # Remove old calls outside the window
            calls[client_id] = [call_time for call_time in calls[client_id] 
                               if now - call_time < window]
            
            if len(calls[client_id]) >= max_calls:
                return jsonify({'error': 'Rate limit exceeded'}), 429
            
            calls[client_id].append(now)
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
```

## Performance Optimization

### 1. Caching Strategy
```python
from functools import lru_cache
import time

class CachedTokenomics:
    def __init__(self):
        self._deed_score_cache = {}
        self._cache_ttl = 300  # 5 minutes
    
    def get_deed_score_cached(self, identity_id: str) -> float:
        now = time.time()
        cache_key = identity_id
        
        if (cache_key in self._deed_score_cache and 
            now - self._deed_score_cache[cache_key]['timestamp'] < self._cache_ttl):
            return self._deed_score_cache[cache_key]['value']
        
        # Cache miss - fetch from database
        score = self._get_deed_score(identity_id)
        self._deed_score_cache[cache_key] = {
            'value': score,
            'timestamp': now
        }
        
        return score
```

### 2. Batch Operations
```python
def batch_process_loan_forgiveness(self, current_block: int, batch_size: int = 100) -> List[str]:
    """Process loan forgiveness in batches to avoid performance issues."""
    forgiven_loans = []
    offset = 0
    
    while True:
        # Process loans in batches
        loans_batch = db.query("""
            SELECT * FROM loans 
            WHERE status = 'ACTIVE' 
            LIMIT ? OFFSET ?
        """, (batch_size, offset))
        
        if not loans_batch:
            break
        
        for loan in loans_batch:
            # Check forgiveness criteria
            if self._should_forgive_loan(loan, current_block):
                self.forgive_loan(loan["loan_id"], "SYSTEM_AUTO")
                forgiven_loans.append(loan["loan_id"])
        
        offset += batch_size
    
    return forgiven_loans
```

## Monitoring and Alerting

### 1. Health Checks
```python
def health_check() -> Dict[str, Any]:
    """Comprehensive health check for biblical tokenomics system."""
    health = {
        'status': 'healthy',
        'checks': {},
        'timestamp': int(time.time())
    }
    
    try:
        # Database connectivity
        db.query("SELECT 1")
        health['checks']['database'] = 'ok'
    except Exception as e:
        health['checks']['database'] = f'error: {e}'
        health['status'] = 'unhealthy'
    
    try:
        # Tokenomics module
        from shared.models.tokenomics import biblical_tokenomics
        biblical_tokenomics.is_sabbath_period()
        health['checks']['tokenomics'] = 'ok'
    except Exception as e:
        health['checks']['tokenomics'] = f'error: {e}'
        health['status'] = 'unhealthy'
    
    return health
```

### 2. Metrics Collection
```python
def collect_metrics() -> Dict[str, Any]:
    """Collect biblical tokenomics metrics."""
    metrics = {}
    
    try:
        # Pool balances
        pools = db.query("SELECT pool_id, total_amount FROM jubilee_pools")
        metrics['pool_balances'] = {pool['pool_id']: pool['total_amount'] for pool in pools}
        
        # Active loans
        loan_count = db.query_one("SELECT COUNT(*) as count FROM loans WHERE status = 'ACTIVE'")
        metrics['active_loans'] = loan_count['count'] if loan_count else 0
        
        # Recent deeds
        recent_deeds = db.query_one("""
            SELECT COUNT(*) as count FROM deeds_ledger 
            WHERE timestamp > ?
        """, (int(time.time()) - 86400,))
        metrics['deeds_24h'] = recent_deeds['count'] if recent_deeds else 0
        
    except Exception as e:
        logger.error(f"Error collecting metrics: {e}")
        metrics['error'] = str(e)
    
    return metrics
```

This comprehensive error handling and edge case documentation ensures the biblical tokenomics system is robust and production-ready.
