"""
Biblical Cycle Tracker - Sabbath & Jubilee Cycle Management
Provides comprehensive tracking and visualization of biblical cycles.

This module handles:
1. Sabbath cycle tracking (weekly, monthly, annual)
2. Jubilee cycle calculation and countdown
3. Sabbatical year (<PERSON><PERSON><PERSON>) tracking
4. Biblical calendar integration
5. Cycle enforcement and notifications

Author: ONNYX Development Team
Date: 2025-07-17
"""

import sqlite3
import time
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import pytz

from shared.config.onnyx_config import onnyx_config

logger = logging.getLogger(__name__)

class CycleType(Enum):
    WEEKLY_SABBATH = "WEEKLY_SABBATH"
    NEW_MOON = "NEW_MOON"
    BIBLICAL_FEAST = "BIBLICAL_FEAST"
    SABBATICAL_YEAR = "SABBATICAL_YEAR"
    JUBILEE_YEAR = "JUBILEE_YEAR"

@dataclass
class CycleStatus:
    cycle_type: CycleType
    is_active: bool
    current_period: Optional[str]
    next_start: Optional[datetime]
    next_end: Optional[datetime]
    time_until_next: Optional[timedelta]
    progress_percentage: float
    description: str
    biblical_reference: str

class BiblicalCycleTracker:
    """
    Comprehensive biblical cycle tracking system for ONNYX platform.
    Handles all biblical time cycles including Sabbath, Jubilee, and feast days.
    """
    
    # Biblical epoch - Creation date according to traditional Jewish calendar
    BIBLICAL_EPOCH = datetime(1970, 1, 1, tzinfo=timezone.utc)  # Simplified for now
    SABBATICAL_CYCLE_YEARS = 7
    JUBILEE_CYCLE_YEARS = 50
    
    def __init__(self):
        self.db_path = onnyx_config.db_path
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_current_sabbath_status(self, user_timezone: str = "UTC") -> CycleStatus:
        """
        Get current weekly Sabbath status.
        
        Args:
            user_timezone: User's timezone for accurate Sabbath calculation
            
        Returns:
            CycleStatus for weekly Sabbath
        """
        try:
            tz = pytz.timezone(user_timezone)
            now = datetime.now(tz)
            
            # Calculate next Friday 6 PM (Sabbath start)
            days_until_friday = (4 - now.weekday()) % 7  # Friday is weekday 4
            if days_until_friday == 0 and now.hour >= 18:  # Already past Friday 6 PM
                days_until_friday = 7
            
            next_sabbath_start = now.replace(hour=18, minute=0, second=0, microsecond=0)
            next_sabbath_start += timedelta(days=days_until_friday)
            
            next_sabbath_end = next_sabbath_start + timedelta(hours=25)  # 25 hours (sunset to sunset)
            
            # Check if currently in Sabbath
            is_sabbath = False
            current_period = None
            
            if now.weekday() == 4 and now.hour >= 18:  # Friday 6 PM or later
                is_sabbath = True
                current_period = "Friday Evening - Sabbath Beginning"
            elif now.weekday() == 5:  # Saturday
                is_sabbath = True
                if now.hour < 19:
                    current_period = "Saturday - Sabbath Day"
                else:
                    current_period = "Saturday Evening - Sabbath Ending"
            
            # Calculate progress through the week
            week_start = now - timedelta(days=now.weekday())
            week_progress = (now - week_start).total_seconds() / (7 * 24 * 3600) * 100
            
            time_until_next = next_sabbath_start - now if not is_sabbath else next_sabbath_end - now
            
            return CycleStatus(
                cycle_type=CycleType.WEEKLY_SABBATH,
                is_active=is_sabbath,
                current_period=current_period,
                next_start=next_sabbath_start if not is_sabbath else None,
                next_end=next_sabbath_end if is_sabbath else None,
                time_until_next=time_until_next,
                progress_percentage=week_progress,
                description="Weekly Sabbath observance from Friday evening to Saturday evening",
                biblical_reference="Exodus 20:8-11 - Remember the sabbath day, to keep it holy"
            )
            
        except Exception as e:
            logger.error(f"Error getting Sabbath status: {e}")
            return self._create_error_status(CycleType.WEEKLY_SABBATH, str(e))
    
    def get_jubilee_cycle_status(self) -> CycleStatus:
        """
        Get current Jubilee cycle status.
        
        Returns:
            CycleStatus for Jubilee cycle
        """
        try:
            now = datetime.now(timezone.utc)
            years_since_epoch = (now - self.BIBLICAL_EPOCH).days / 365.25
            
            current_jubilee_cycle = int(years_since_epoch // self.JUBILEE_CYCLE_YEARS)
            years_into_cycle = years_since_epoch % self.JUBILEE_CYCLE_YEARS
            years_until_next = self.JUBILEE_CYCLE_YEARS - years_into_cycle
            
            # Calculate next Jubilee year
            next_jubilee_year = self.BIBLICAL_EPOCH.year + ((current_jubilee_cycle + 1) * self.JUBILEE_CYCLE_YEARS)
            next_jubilee_date = datetime(next_jubilee_year, 1, 1, tzinfo=timezone.utc)
            
            # Check if currently in Jubilee year
            is_jubilee_year = abs(years_into_cycle - self.JUBILEE_CYCLE_YEARS) < 1.0
            
            progress_percentage = (years_into_cycle / self.JUBILEE_CYCLE_YEARS) * 100
            
            return CycleStatus(
                cycle_type=CycleType.JUBILEE_YEAR,
                is_active=is_jubilee_year,
                current_period=f"Year {int(years_into_cycle) + 1} of Jubilee Cycle {current_jubilee_cycle + 1}",
                next_start=next_jubilee_date,
                next_end=None,
                time_until_next=next_jubilee_date - now,
                progress_percentage=progress_percentage,
                description=f"50-year Jubilee cycle for land restoration and debt forgiveness",
                biblical_reference="Leviticus 25:8-13 - And ye shall hallow the fiftieth year"
            )
            
        except Exception as e:
            logger.error(f"Error getting Jubilee status: {e}")
            return self._create_error_status(CycleType.JUBILEE_YEAR, str(e))
    
    def get_sabbatical_year_status(self) -> CycleStatus:
        """
        Get current Sabbatical year (Shmita) status.
        
        Returns:
            CycleStatus for Sabbatical year
        """
        try:
            now = datetime.now(timezone.utc)
            years_since_epoch = (now - self.BIBLICAL_EPOCH).days / 365.25
            
            current_sabbatical_cycle = int(years_since_epoch // self.SABBATICAL_CYCLE_YEARS)
            years_into_cycle = years_since_epoch % self.SABBATICAL_CYCLE_YEARS
            years_until_next = self.SABBATICAL_CYCLE_YEARS - years_into_cycle
            
            # Calculate next Sabbatical year
            next_sabbatical_year = self.BIBLICAL_EPOCH.year + ((current_sabbatical_cycle + 1) * self.SABBATICAL_CYCLE_YEARS)
            next_sabbatical_date = datetime(next_sabbatical_year, 1, 1, tzinfo=timezone.utc)
            
            # Check if currently in Sabbatical year
            is_sabbatical_year = abs(years_into_cycle - (self.SABBATICAL_CYCLE_YEARS - 1)) < 1.0
            
            progress_percentage = (years_into_cycle / self.SABBATICAL_CYCLE_YEARS) * 100
            
            return CycleStatus(
                cycle_type=CycleType.SABBATICAL_YEAR,
                is_active=is_sabbatical_year,
                current_period=f"Year {int(years_into_cycle) + 1} of 7-year cycle",
                next_start=next_sabbatical_date,
                next_end=None,
                time_until_next=next_sabbatical_date - now,
                progress_percentage=progress_percentage,
                description="7-year Sabbatical cycle for land rest and debt forgiveness",
                biblical_reference="Deuteronomy 15:1-2 - At the end of every seven years thou shalt make a release"
            )
            
        except Exception as e:
            logger.error(f"Error getting Sabbatical status: {e}")
            return self._create_error_status(CycleType.SABBATICAL_YEAR, str(e))
    
    def get_new_moon_status(self, user_timezone: str = "UTC") -> CycleStatus:
        """
        Get current New Moon status.
        
        Args:
            user_timezone: User's timezone
            
        Returns:
            CycleStatus for New Moon cycle
        """
        try:
            # Simplified new moon calculation - in production, use astronomical library
            tz = pytz.timezone(user_timezone)
            now = datetime.now(tz)
            
            # Approximate lunar cycle (29.53 days)
            lunar_cycle_days = 29.53
            
            # Calculate approximate next new moon (simplified)
            days_since_epoch = (now - self.BIBLICAL_EPOCH.replace(tzinfo=tz)).days
            days_into_cycle = days_since_epoch % lunar_cycle_days
            days_until_next = lunar_cycle_days - days_into_cycle
            
            next_new_moon = now + timedelta(days=days_until_next)
            
            # Check if currently in new moon period (within 1 day)
            is_new_moon = days_into_cycle < 1.0 or days_into_cycle > (lunar_cycle_days - 1.0)
            
            progress_percentage = (days_into_cycle / lunar_cycle_days) * 100
            
            return CycleStatus(
                cycle_type=CycleType.NEW_MOON,
                is_active=is_new_moon,
                current_period="New Moon Period" if is_new_moon else f"Day {int(days_into_cycle) + 1} of lunar month",
                next_start=next_new_moon,
                next_end=None,
                time_until_next=next_new_moon - now,
                progress_percentage=progress_percentage,
                description="Monthly New Moon observance according to biblical calendar",
                biblical_reference="Numbers 28:11 - And in the beginnings of your months"
            )
            
        except Exception as e:
            logger.error(f"Error getting New Moon status: {e}")
            return self._create_error_status(CycleType.NEW_MOON, str(e))
    
    def get_all_cycle_statuses(self, user_timezone: str = "UTC") -> Dict[str, CycleStatus]:
        """
        Get status of all biblical cycles.
        
        Args:
            user_timezone: User's timezone
            
        Returns:
            Dictionary of all cycle statuses
        """
        return {
            "weekly_sabbath": self.get_current_sabbath_status(user_timezone),
            "jubilee_cycle": self.get_jubilee_cycle_status(),
            "sabbatical_year": self.get_sabbatical_year_status(),
            "new_moon": self.get_new_moon_status(user_timezone)
        }
    
    def get_cycle_message(self, user_timezone: str = "UTC") -> str:
        """
        Get dynamic covenant cycle message for display.
        
        Args:
            user_timezone: User's timezone
            
        Returns:
            Formatted message about current cycle status
        """
        try:
            statuses = self.get_all_cycle_statuses(user_timezone)
            
            sabbath = statuses["weekly_sabbath"]
            jubilee = statuses["jubilee_cycle"]
            sabbatical = statuses["sabbatical_year"]
            
            # Priority messages
            if sabbath.is_active:
                return f"🕯️ Currently observing Sabbath - {sabbath.current_period}"
            
            if sabbatical.is_active:
                return f"🌾 Sabbatical Year (Shmita) - Land rest and debt forgiveness period"
            
            if jubilee.is_active:
                return f"🎺 JUBILEE YEAR - Land returns to original owners, liberty proclaimed!"
            
            # Countdown messages
            sabbath_hours = int(sabbath.time_until_next.total_seconds() / 3600) if sabbath.time_until_next else 0
            if sabbath_hours < 48:
                return f"⏰ Next Sabbath begins in {sabbath_hours} hours"
            
            sabbatical_years = int(sabbatical.time_until_next.days / 365.25) if sabbatical.time_until_next else 0
            if sabbatical_years <= 2:
                return f"📅 Next Shmita (Sabbatical Year) in {sabbatical_years} years"
            
            jubilee_years = int(jubilee.time_until_next.days / 365.25) if jubilee.time_until_next else 0
            return f"🏛️ You are in {jubilee.current_period} - {jubilee_years} years until next Jubilee"
            
        except Exception as e:
            logger.error(f"Error generating cycle message: {e}")
            return "📖 Walking in covenant time according to biblical principles"
    
    def _create_error_status(self, cycle_type: CycleType, error_msg: str) -> CycleStatus:
        """Create error status for failed cycle calculations."""
        return CycleStatus(
            cycle_type=cycle_type,
            is_active=False,
            current_period=None,
            next_start=None,
            next_end=None,
            time_until_next=None,
            progress_percentage=0.0,
            description=f"Error calculating {cycle_type.value}: {error_msg}",
            biblical_reference=""
        )
    
    def format_time_until(self, time_delta: timedelta) -> str:
        """Format time delta into human-readable string."""
        if not time_delta:
            return "Unknown"
        
        total_seconds = int(time_delta.total_seconds())
        
        if total_seconds < 0:
            return "Past"
        
        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        minutes = (total_seconds % 3600) // 60
        
        if days > 365:
            years = days // 365
            remaining_days = days % 365
            return f"{years} year{'s' if years != 1 else ''}, {remaining_days} day{'s' if remaining_days != 1 else ''}"
        elif days > 0:
            return f"{days} day{'s' if days != 1 else ''}, {hours} hour{'s' if hours != 1 else ''}"
        elif hours > 0:
            return f"{hours} hour{'s' if hours != 1 else ''}, {minutes} minute{'s' if minutes != 1 else ''}"
        else:
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
