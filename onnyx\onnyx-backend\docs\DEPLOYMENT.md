# ONNYX Platform Deployment Guide

## 🎯 Single Deployment Strategy

The ONNYX platform uses a **unified deployment approach** with all functionality served from one Render instance.

## Architecture Overview

```
Production Deployment: https://onnyx-backend.onrender.com
├── Frontend: Integrated Flask templates + static assets
├── Backend: Flask application with REST APIs
├── Database: SQLite (onnyx.db) with production data
├── Auto-Mining: Background daemon processes
└── Real-time Data: Live blockchain and validator information
```

## Deployment Components

### Core Application
- **Location**: `onnyx-backend/web/app.py`
- **Type**: Flask application
- **Port**: 5000 (configurable via environment)
- **Database**: SQLite at `data/onnyx.db`

### Frontend Integration
- **Templates**: `onnyx-backend/web/templates/`
- **Static Assets**: `onnyx-backend/web/static/`
- **Theme**: Onyx Stone with cyber-cyan accents
- **Responsive**: Mobile-first design

### API Endpoints
- **Identity**: `/api/identity/`
- **Validators**: `/api/sela/`
- **Blockchain**: `/api/explorer/`
- **Mining**: `/api/mining/`
- **Auto-Mining**: `/auto-mining/api/`

## Local Development

### Prerequisites
```bash
# Required software
Python 3.9+
SQLite3
Git
```

### Setup
```bash
# Clone repository
git clone <repository-url>
cd onnyx

# Install dependencies
pip install -r requirements.txt

# Initialize database
python scripts/init_db.py

# Start development server
cd onnyx-backend
python web/app.py
```

### Development URLs
- **Platform**: http://localhost:5000
- **API Docs**: http://localhost:5000/api/docs
- **Auto-Mining**: http://localhost:5000/auto-mining/

## Production Deployment (Render)

### Environment Variables
```bash
# Required
DATABASE_URL=sqlite:///data/onnyx.db
FLASK_ENV=production
PORT=5000

# Optional
SECRET_KEY=<secure-random-key>
DEBUG=False
```

### Build Configuration
```bash
# Build command
pip install -r requirements.txt

# Start command
cd onnyx-backend && python web/app.py
```

### Database Setup
```bash
# Production database initialization
python scripts/phase1_production_setup.py

# Verify setup
python scripts/health_check.py
```

## Auto-Mining Configuration

### Start Auto-Mining System
```bash
# Start daemon
python scripts/auto_mining_manager.py start --daemon

# Check status
python scripts/auto_mining_manager.py status

# Monitor operations
python scripts/monitor_mining.py
```

### Validator Configuration
```bash
# Configure specific validator
python scripts/auto_mining_manager.py configure \
  --sela-id <validator-id> \
  --interval 300 \
  --schedule true \
  --start-time "09:00" \
  --end-time "17:00"

# Enable auto-mining
python scripts/auto_mining_manager.py enable --sela-id <validator-id>
```

## Database Management

### Production Database
- **Location**: `data/onnyx.db`
- **Backup**: Automatic daily backups
- **Schema**: Latest production schema

### Key Tables
- `identities` - User identity records
- `selas` - Business validator registry
- `blocks` - Blockchain data
- `transactions` - Transaction history
- `mining_rewards` - Mining earnings

### Backup & Restore
```bash
# Create backup
cp data/onnyx.db data/onnyx_backup_$(date +%Y%m%d_%H%M%S).db

# Restore from backup
cp data/onnyx_backup_YYYYMMDD_HHMMSS.db data/onnyx.db
```

## Monitoring & Health Checks

### System Health
```bash
# Check platform health
python scripts/health_check.py

# Monitor mining status
python scripts/mining_status.py

# View production metrics
python scripts/generate_weekly_summary.py
```

### Log Files
- **Application**: `logs/api.log`
- **Auto-Mining**: `auto_mining.log`
- **Sela Mining**: `sela_miner.log`

## Security Considerations

### Key Management
- **Private Keys**: Stored in `credentials/` directory
- **Session Security**: Flask session management
- **Database**: SQLite with file-level security

### Access Control
- **Authentication**: Email-based login system
- **Authorization**: Role-based access (Platform Founder, Validators)
- **API Security**: CORS and request validation

## Troubleshooting

### Common Issues

#### Database Connection
```bash
# Check database integrity
python check_db.py

# Recreate tables if needed
python create_missing_tables.py
```

#### Auto-Mining Problems
```bash
# Restart auto-mining
python scripts/auto_mining_manager.py stop
python scripts/auto_mining_manager.py start --daemon

# Check validator status
python scripts/auto_mining_manager.py status
```

#### Frontend Issues
```bash
# Force refresh frontend assets
python force_refresh_frontend.py

# Verify template rendering
python scripts/test_frontend_improvements.py
```

### Support Resources
- **Platform**: https://onnyx-backend.onrender.com
- **Documentation**: `/docs` directory
- **Health Check**: `/health` endpoint
- **API Status**: `/api/status` endpoint

## Deployment Checklist

### Pre-Deployment
- [ ] Database initialized with production data
- [ ] Environment variables configured
- [ ] Auto-mining system tested
- [ ] Frontend assets compiled
- [ ] Health checks passing

### Post-Deployment
- [ ] Platform accessible at production URL
- [ ] User registration/login working
- [ ] Validator operations functional
- [ ] Auto-mining system active
- [ ] Real-time data displaying correctly

### Monitoring
- [ ] Application logs clean
- [ ] Database performance optimal
- [ ] Auto-mining processes running
- [ ] API endpoints responding
- [ ] Frontend loading correctly

---

**ONNYX Platform** - Single deployment, maximum reliability.
