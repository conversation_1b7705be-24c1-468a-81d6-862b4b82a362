{% extends "base.html" %}

{% block title %}Profile Dashboard - ONNYX{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Enhanced Header Section -->
    <section class="pt-24 pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="flex justify-center mb-8">
                    <div class="glass-card-premium w-24 h-24 rounded-3xl flex items-center justify-center">
                        <svg class="w-12 h-12 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <h1 class="text-5xl md:text-6xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Profile Dashboard
                </h1>
                <p class="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                    Comprehensive overview of your covenant journey and biblical tokenomics participation
                </p>

                <!-- Quick Stats Overview -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-5xl mx-auto">
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">{{ stats.total_onx_earned|round(2) }}</div>
                        <div class="text-sm text-text-tertiary">Total ONX Earned</div>
                    </div>
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">{{ stats.covenant_compliance }}%</div>
                        <div class="text-sm text-text-tertiary">Covenant Compliance</div>
                    </div>
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="text-3xl font-orbitron font-bold text-cyber-green mb-2">{{ stats.blocks_contributed }}</div>
                        <div class="text-sm text-text-tertiary">Blocks Contributed</div>
                    </div>
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="text-3xl font-orbitron font-bold text-cyber-yellow mb-2">{{ stats.community_rank }}</div>
                        <div class="text-sm text-text-tertiary">Community Rank</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Dashboard Content -->
    <section class="pb-32">
        <div class="container-xl mx-auto px-6">
            <div class="max-w-7xl mx-auto space-y-16">

                <!-- Covenant Journey Progress -->
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">🛤️ Covenant Journey</h2>
                            <p class="text-text-secondary">Your progress through the biblical covenant system</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ journey.current_level }}</div>
                            <div class="text-sm text-text-tertiary">Current Level</div>
                        </div>
                    </div>

                    <!-- Journey Timeline -->
                    <div class="relative">
                        <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-cyber-cyan via-cyber-purple to-cyber-green"></div>

                        <div class="space-y-8">
                            <!-- Genesis Registration -->
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-green to-green-400 rounded-2xl flex items-center justify-center relative z-10">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-2">Genesis Registration</h3>
                                    <p class="text-text-secondary mb-2">Joined the ONNYX covenant blockchain</p>
                                    <div class="text-sm text-text-tertiary">{{ journey.genesis_date }}</div>
                                </div>
                            </div>

                            <!-- CIPP Enrollment -->
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center relative z-10">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-2">CIPP Enrollment</h3>
                                    <p class="text-text-secondary mb-2">Achieved {{ identity.metadata_parsed.get('cipp_tier', 'Tier 0') }} protection level</p>
                                    <div class="text-sm text-text-tertiary">{{ journey.cipp_date }}</div>
                                </div>
                            </div>

                            <!-- First Mining Contribution -->
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center relative z-10">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-2">First Mining Contribution</h3>
                                    <p class="text-text-secondary mb-2">Started contributing to network security</p>
                                    <div class="text-sm text-text-tertiary">{{ journey.first_mining_date }}</div>
                                </div>
                            </div>

                            <!-- Biblical Tokenomics Participation -->
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-2xl flex items-center justify-center relative z-10">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-orbitron font-bold text-cyber-yellow mb-2">Biblical Tokenomics</h3>
                                    <p class="text-text-secondary mb-2">Active participant in covenant economics</p>
                                    <div class="text-sm text-text-tertiary">{{ journey.tokenomics_date }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Biblical Tokenomics Participation History -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Yovel Cycle Participation -->
                    <div class="glass-card-enhanced p-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan">📅 Yovel Participation</h3>
                            <div class="text-right">
                                <div class="text-lg font-orbitron font-bold text-cyber-cyan">{{ biblical_stats.yovel_cycles_participated }}</div>
                                <div class="text-sm text-text-tertiary">Cycles</div>
                            </div>
                        </div>

                        <!-- Yovel Progress Chart -->
                        <div class="space-y-4">
                            <div class="flex justify-between text-sm text-text-secondary">
                                <span>Current Cycle Progress</span>
                                <span>{{ biblical_stats.current_cycle_progress }}%</span>
                            </div>
                            <div class="w-full bg-onyx-light rounded-full h-3">
                                <div class="bg-gradient-to-r from-cyber-cyan to-cyber-blue h-3 rounded-full transition-all duration-500"
                                     style="width: {{ biblical_stats.current_cycle_progress }}%"></div>
                            </div>

                            <div class="grid grid-cols-2 gap-4 mt-6">
                                <div class="text-center">
                                    <div class="text-xl font-orbitron font-bold text-cyber-cyan">{{ biblical_stats.total_yovel_tokens }}</div>
                                    <div class="text-sm text-text-tertiary">Total Tokens</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-xl font-orbitron font-bold text-cyber-green">{{ biblical_stats.next_reset_years }}</div>
                                    <div class="text-sm text-text-tertiary">Years to Reset</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gleaning Pool Contributions -->
                    <div class="glass-card-enhanced p-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-orbitron font-bold text-cyber-green">🌾 Gleaning Contributions</h3>
                            <div class="text-right">
                                <div class="text-lg font-orbitron font-bold text-cyber-green">{{ biblical_stats.total_gleaning_contributions }}</div>
                                <div class="text-sm text-text-tertiary">ONX</div>
                            </div>
                        </div>

                        <!-- Contribution History -->
                        <div class="space-y-3">
                            {% for contribution in biblical_stats.recent_gleaning %}
                            <div class="flex justify-between items-center p-3 glass-card rounded-lg">
                                <div>
                                    <div class="text-sm font-orbitron text-white">{{ contribution.amount }} ONX</div>
                                    <div class="text-xs text-text-tertiary">{{ contribution.date }}</div>
                                </div>
                                <div class="text-xs text-cyber-green">{{ contribution.season }}</div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="mt-6 text-center">
                            <a href="{{ url_for('tokenomics.gleaning') }}"
                               class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                View All Contributions
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Labor Records & Community Contributions -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Labor Records -->
                    <div class="lg:col-span-2 glass-card-enhanced p-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-orbitron font-bold text-cyber-purple">💼 Labor Records</h3>
                            <a href="{{ url_for('labor.records') }}"
                               class="glass-button px-4 py-2 rounded-lg font-orbitron text-sm transition-all duration-300 hover:scale-105">
                                View All →
                            </a>
                        </div>

                        <!-- Recent Labor Activities -->
                        <div class="space-y-4">
                            {% for record in labor_records[:5] %}
                            <div class="glass-card p-4 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <div class="w-8 h-8 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2"></path>
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="font-orbitron font-semibold text-white">{{ record.task_type }}</div>
                                                <div class="text-sm text-text-tertiary">{{ record.date }}</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-text-secondary">{{ record.description }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-orbitron font-bold text-cyber-purple">{{ record.mikvah_tokens }}</div>
                                        <div class="text-xs text-text-tertiary">Mikvah Tokens</div>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <div class="text-center py-8">
                                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center opacity-50">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2"></path>
                                    </svg>
                                </div>
                                <p class="text-text-secondary">No labor records yet</p>
                                <a href="{{ url_for('labor.submit') }}"
                                   class="inline-block mt-4 glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                    Submit First Record
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Community Contributions -->
                    <div class="glass-card-enhanced p-8">
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-yellow mb-6">🤝 Community Impact</h3>

                        <div class="space-y-6">
                            <!-- Contribution Score -->
                            <div class="text-center">
                                <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-2xl flex items-center justify-center">
                                    <div class="text-2xl font-orbitron font-bold text-onyx-black">{{ community_stats.contribution_score }}</div>
                                </div>
                                <div class="text-lg font-orbitron font-bold text-cyber-yellow">Contribution Score</div>
                                <div class="text-sm text-text-tertiary">Community Rank: #{{ community_stats.rank }}</div>
                            </div>

                            <!-- Contribution Breakdown -->
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-text-secondary">Validator Operations</span>
                                    <span class="text-sm font-orbitron text-cyber-cyan">{{ community_stats.validator_score }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-text-secondary">Network Security</span>
                                    <span class="text-sm font-orbitron text-cyber-purple">{{ community_stats.security_score }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-text-secondary">Biblical Compliance</span>
                                    <span class="text-sm font-orbitron text-cyber-green">{{ community_stats.compliance_score }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-text-secondary">Community Support</span>
                                    <span class="text-sm font-orbitron text-cyber-yellow">{{ community_stats.support_score }}</span>
                                </div>
                            </div>

                            <!-- Recent Achievements -->
                            <div class="pt-4 border-t border-glass-border">
                                <h4 class="text-sm font-orbitron font-bold text-white mb-3">Recent Achievements</h4>
                                <div class="space-y-2">
                                    {% for achievement in community_stats.recent_achievements %}
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-cyber-yellow rounded-full"></div>
                                        <span class="text-xs text-text-secondary">{{ achievement.title }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sela Validator Performance (if applicable) -->
                {% if user_sela %}
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">🏢 Validator Performance</h2>
                            <p class="text-text-secondary">{{ user_sela.name }} - Business validator metrics</p>
                        </div>
                        <a href="{{ url_for('sela.dashboard') }}"
                           class="glass-button-enhanced px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            Detailed View →
                        </a>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Trust Score -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-4L21 8.5l-7 7-3-3"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ user_sela.trust_score|round(1) }}</div>
                            <div class="text-sm text-text-secondary">Trust Score</div>
                        </div>

                        <!-- Mining Performance -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2">{{ user_sela.blocks_mined }}</div>
                            <div class="text-sm text-text-secondary">Blocks Mined</div>
                        </div>

                        <!-- ONX Balance -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ user_sela.onx_balance|round(2) }}</div>
                            <div class="text-sm text-text-secondary">ONX Balance</div>
                        </div>

                        <!-- Mining Tier -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                            <div class="text-lg font-orbitron font-bold text-cyber-yellow mb-2">{{ user_sela.mining_tier|title }}</div>
                            <div class="text-sm text-text-secondary">Mining Tier</div>
                        </div>
                    </div>

                    <!-- Performance Chart Placeholder -->
                    <div class="glass-card p-6 rounded-xl">
                        <h4 class="text-lg font-orbitron font-bold text-white mb-4">30-Day Performance Trend</h4>
                        <div class="h-32 bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10 rounded-lg flex items-center justify-center">
                            <div class="text-center">
                                <svg class="w-12 h-12 mx-auto mb-2 text-cyber-cyan opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <p class="text-text-secondary text-sm">Performance chart coming soon</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

            </div>
        </div>
    </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize profile dashboard
    console.log('🚀 Profile Dashboard Initialized');

    // Add smooth scroll animations for timeline
    const timelineItems = document.querySelectorAll('.flex.items-center.space-x-6');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, { threshold: 0.1 });

    timelineItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });
});
</script>
{% endblock %}
