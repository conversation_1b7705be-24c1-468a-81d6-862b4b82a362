/**
 * Consistent Hover Effects System
 * Ensures all cards have the same sensitivity and behavior throughout the ONNYX platform
 */

(function() {
    'use strict';

    // Configuration for standardized hover effects
    const HOVER_CONFIG = {
        // Standard transform for all cards
        hoverTransform: 'translateY(-2px)',
        // Transition timing
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        // Hover detection sensitivity
        hoverDelay: 50, // ms
        // Form element protection
        protectForms: true
    };

    // Card selectors to standardize
    const CARD_SELECTORS = [
        '.glass-card',
        '.glass-card-enhanced', 
        '.glass-card-premium',
        '.neuro-card',
        '.performance-card',
        '.stat-card',
        '[class*="card"]:not(.no-hover)'
    ];

    // Form element selectors to protect
    const FORM_SELECTORS = [
        'input',
        'select', 
        'textarea',
        'button[type="submit"]',
        'button[type="button"]',
        '.form-control'
    ];

    class ConsistentHoverEffects {
        constructor() {
            this.init();
        }

        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setupHoverEffects());
            } else {
                this.setupHoverEffects();
            }

            // Re-initialize when new content is added
            this.observeNewContent();
        }

        setupHoverEffects() {
            this.standardizeCardHovers();
            this.protectFormElements();
            this.removeConflictingEffects();
            this.addConsistentTransitions();
        }

        standardizeCardHovers() {
            const cards = document.querySelectorAll(CARD_SELECTORS.join(', '));
            
            cards.forEach(card => {
                // Remove any existing hover event listeners
                this.cleanupExistingHovers(card);
                
                // Add standardized hover behavior
                this.addStandardizedHover(card);
            });
        }

        cleanupExistingHovers(element) {
            // Clone element to remove all event listeners
            const newElement = element.cloneNode(true);
            element.parentNode.replaceChild(newElement, element);
            return newElement;
        }

        addStandardizedHover(card) {
            let hoverTimeout;

            // Store original transform
            const originalTransform = getComputedStyle(card).transform;

            card.addEventListener('mouseenter', () => {
                clearTimeout(hoverTimeout);
                hoverTimeout = setTimeout(() => {
                    if (!this.isFormInteraction(card)) {
                        card.style.transform = HOVER_CONFIG.hoverTransform;
                        card.style.transition = HOVER_CONFIG.transition;
                    }
                }, HOVER_CONFIG.hoverDelay);
            });

            card.addEventListener('mouseleave', () => {
                clearTimeout(hoverTimeout);
                card.style.transform = originalTransform === 'none' ? '' : originalTransform;
            });

            // Prevent hover effects during form interactions
            if (HOVER_CONFIG.protectForms) {
                this.protectFormInteractions(card);
            }
        }

        protectFormElements() {
            const forms = document.querySelectorAll(FORM_SELECTORS.join(', '));
            
            forms.forEach(formElement => {
                formElement.addEventListener('focus', () => {
                    const parentCard = formElement.closest(CARD_SELECTORS.join(', '));
                    if (parentCard) {
                        parentCard.classList.add('form-active');
                        parentCard.style.transform = '';
                    }
                });

                formElement.addEventListener('blur', () => {
                    const parentCard = formElement.closest(CARD_SELECTORS.join(', '));
                    if (parentCard) {
                        parentCard.classList.remove('form-active');
                    }
                });
            });
        }

        protectFormInteractions(card) {
            const formElements = card.querySelectorAll(FORM_SELECTORS.join(', '));
            
            formElements.forEach(element => {
                element.addEventListener('mouseenter', (e) => {
                    e.stopPropagation();
                    card.style.transform = '';
                });

                element.addEventListener('focus', () => {
                    card.classList.add('form-active');
                    card.style.transform = '';
                });

                element.addEventListener('blur', () => {
                    card.classList.remove('form-active');
                });
            });
        }

        isFormInteraction(card) {
            return card.classList.contains('form-active') || 
                   card.querySelector(':focus') !== null;
        }

        removeConflictingEffects() {
            // Remove conflicting CSS classes
            const conflictingClasses = [
                'hover:scale-105',
                'hover:scale-110', 
                'hover:scale-95'
            ];

            conflictingClasses.forEach(className => {
                const elements = document.querySelectorAll(`.${className}`);
                elements.forEach(el => el.classList.remove(className));
            });

            // Override inline styles that might conflict
            const elementsWithInlineHover = document.querySelectorAll('[style*="transform"]');
            elementsWithInlineHover.forEach(el => {
                if (el.matches(CARD_SELECTORS.join(', '))) {
                    el.style.transition = HOVER_CONFIG.transition;
                }
            });
        }

        addConsistentTransitions() {
            const style = document.createElement('style');
            style.textContent = `
                /* Consistent hover effects override */
                ${CARD_SELECTORS.join(', ')} {
                    transition: ${HOVER_CONFIG.transition} !important;
                    cursor: pointer !important;
                    will-change: transform, box-shadow, background !important;
                }

                /* Prevent scaling transforms */
                ${CARD_SELECTORS.join(', ')}:hover {
                    transform: ${HOVER_CONFIG.hoverTransform} !important;
                }

                /* Form protection */
                ${CARD_SELECTORS.join(', ')}.form-active {
                    transform: none !important;
                }

                /* Form elements within cards */
                ${CARD_SELECTORS.join(', ')} ${FORM_SELECTORS.join(', ')} {
                    pointer-events: auto !important;
                    z-index: 10 !important;
                    position: relative !important;
                }
            `;
            
            document.head.appendChild(style);
        }

        observeNewContent() {
            // Watch for dynamically added content
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if the added node or its children contain cards
                            const cards = node.matches && node.matches(CARD_SELECTORS.join(', ')) 
                                ? [node] 
                                : node.querySelectorAll ? Array.from(node.querySelectorAll(CARD_SELECTORS.join(', '))) 
                                : [];
                            
                            cards.forEach(card => this.addStandardizedHover(card));
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // Initialize the consistent hover effects system
    new ConsistentHoverEffects();

    // Export for manual initialization if needed
    window.ConsistentHoverEffects = ConsistentHoverEffects;

})();
