{% extends "base.html" %}

{% block title %}Covenant Inscription - ONNYX Eden Mode{% endblock %}
{% block meta_description %}Complete your verified Israelite covenant registration with blockchain identity inscription and tribal membership activation.{% endblock %}

{% block head %}
<style>
    .inscription-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .covenant-scroll {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border: 2px solid rgba(212, 175, 55, 0.5);
        border-radius: 16px;
        padding: 2rem;
        margin: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    .covenant-scroll::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #D4AF37, #F7D794);
    }
    
    .covenant-seal {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: linear-gradient(135deg, #D4AF37, #F7D794);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        margin: 0 auto 1rem;
        position: relative;
        animation: float 3s ease-in-out infinite;
        box-shadow: 0 20px 40px rgba(212, 175, 55, 0.3);
    }
    
    .tribal-badge {
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(139, 92, 246, 0.2));
        border: 1px solid rgba(79, 70, 229, 0.4);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: center;
    }
    
    .identity-preview {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .identity-field {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .identity-field:last-child {
        border-bottom: none;
    }
    
    .wallet-address {
        font-family: 'Courier New', monospace;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
        word-break: break-all;
    }
    
    .inscription-form {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .form-input {
        width: 100%;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        transition: all 0.3s ease;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #4F46E5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
    
    .covenant-checkbox {
        display: flex;
        align-items: center;
        margin: 1.5rem 0;
        padding: 1rem;
        background: rgba(212, 175, 55, 0.1);
        border: 1px solid rgba(212, 175, 55, 0.3);
        border-radius: 8px;
    }
    
    .progress-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
    }
    
    .progress-step {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 0.5rem;
        font-weight: bold;
        color: white;
    }
    
    .progress-step.completed {
        background: linear-gradient(135deg, #10B981, #34D399);
    }
    
    .progress-step.current {
        background: linear-gradient(135deg, #D4AF37, #F7D794);
        animation: pulse 2s infinite;
        color: #1F2937;
    }
    
    .progress-connector {
        width: 3rem;
        height: 3px;
        margin: 0 0.5rem;
    }
    
    .progress-connector.completed {
        background: linear-gradient(90deg, #10B981, #34D399);
    }
    
    .inscription-animation {
        text-align: center;
        padding: 3rem;
        display: none;
    }
    
    .blockchain-visualization {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
    }
    
    .block {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        margin: 0 0.5rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        animation: blockAnimation 2s ease-in-out infinite;
    }
    
    .block:nth-child(2) { animation-delay: 0.3s; }
    .block:nth-child(3) { animation-delay: 0.6s; }
    .block:nth-child(4) { animation-delay: 0.9s; }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
    
    @keyframes blockAnimation {
        0%, 100% { transform: scale(1) rotateY(0deg); }
        50% { transform: scale(1.1) rotateY(180deg); }
    }
    
    .success-message {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(34, 197, 94, 0.2));
        border: 2px solid rgba(16, 185, 129, 0.4);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        margin: 2rem 0;
        display: none;
    }

    .verification-message {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.2), rgba(154, 0, 255, 0.2));
        border: 2px solid rgba(0, 255, 247, 0.4);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        margin: 2rem 0;
        display: none;
    }

    .glass-button-primary {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.3), rgba(0, 128, 255, 0.3));
        border: 2px solid rgba(0, 255, 247, 0.5);
        color: white;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }

    .glass-button {
        background: rgba(30, 41, 59, 0.5);
        border: 2px solid rgba(154, 0, 255, 0.5);
        color: white;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-48 h-48 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float-delayed"></div>
        <div class="absolute bottom-20 left-1/3 w-40 h-40 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
    </div>

    <div class="container mx-auto px-4 py-8 relative z-10">
        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step completed">1</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">2</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">3</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">4</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step current">5</div>
        </div>

        <div class="inscription-container">
            <!-- Header -->
            <div class="text-center mb-8">
                <!-- Navigation - Back to Tribal Selection -->
                <div class="mb-6">
                    <a href="/auth/eden-mode/israelite-step2" 
                       class="inline-flex items-center px-4 py-2 text-sm text-gray-400 hover:text-cyber-cyan transition-colors duration-300 border border-gray-600 hover:border-cyber-cyan rounded-lg">
                        ← Back to Tribal Selection
                    </a>
                    
                    <!-- Developer Bypass Option (if not already bypassed) -->
                    <div class="mt-4">
                        <button onclick="toggleDeveloperBypass()" 
                                class="text-sm text-gray-400 hover:text-cyber-cyan transition-colors duration-300 px-4 py-2 rounded border border-gray-600 hover:border-cyber-cyan"
                                style="opacity: 0.7;">
                            🔑 Developer Bypass (Skip Verification)
                        </button>
                        <div id="developerBypass" class="hidden mt-4 max-w-md mx-auto">
                            <div class="bg-cyber-purple/10 border border-cyber-purple/20 rounded-lg p-6">
                                <h4 class="text-lg font-orbitron font-semibold text-cyber-purple mb-4">
                                    🔑 Developer Bypass
                                </h4>
                                <p class="text-sm text-text-secondary mb-4">
                                    Enter the covenant phrase to proceed as developer-verified
                                </p>
                                <input type="password" 
                                       id="bypassPassword" 
                                       placeholder="Enter covenant phrase..."
                                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-purple focus:border-cyber-purple transition-all duration-300 mb-4">
                                <button onclick="validateBypass()" 
                                        class="w-full px-6 py-3 bg-gradient-to-r from-cyber-purple to-cyber-blue text-white rounded-lg font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                    🚀 Activate Developer Mode
                                </button>
                                <p class="text-xs text-gray-400 mt-2 text-center">
                                    This will mark your identity as developer-verified
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h1 class="text-4xl font-bold text-white mb-4">
                    <span class="bg-gradient-to-r from-yellow-400 via-amber-400 to-orange-400 bg-clip-text text-transparent">
                        Covenant Inscription
                    </span>
                </h1>
                <p class="text-gray-300 max-w-2xl mx-auto">
                    Your heritage has been verified by the Gate Keeper council. 
                    Complete your blockchain identity inscription to join the covenant.
                </p>
            </div>

            <!-- Covenant Scroll -->
            <div class="covenant-scroll">
                <div class="text-center">
                    <div class="covenant-seal">📜</div>
                    <h2 class="text-2xl font-bold text-white mb-4">Verified Heritage Confirmation</h2>
                    <div class="tribal-badge">
                        <h3 class="text-xl font-bold text-purple-200 mb-2">
                            Tribe of {{ session.get('selected_tribe', 'Unknown') }}
                        </h3>
                        <p class="text-purple-300">
                            <strong>✅ Gate Keeper Approved</strong> - Your heritage documentation has been verified 
                            by our tribal council and you are confirmed as a legitimate descendant.
                        </p>
                    </div>
                </div>
                
                <div class="mt-6 p-4 bg-amber-900 bg-opacity-30 rounded-lg border border-amber-600">
                    <h4 class="font-bold text-amber-200 mb-2">Covenant Declaration</h4>
                    <p class="text-amber-100 text-sm leading-relaxed">
                        "I, having been verified as a descendant of the tribe of {{ session.get('selected_tribe', 'Unknown') }}, 
                        do solemnly covenant to uphold the biblical principles of righteousness, justice, and mercy. 
                        I commit to honor my ancestral heritage and contribute to the restoration of the covenant community 
                        according to the laws and statutes given to our forefathers."
                    </p>
                </div>
            </div>

            <!-- Identity Preview -->
            <div class="identity-preview">
                <h3 class="text-xl font-bold text-white mb-4">Your Blockchain Identity</h3>
                <div class="identity-field">
                    <span class="text-gray-300">Identity Type:</span>
                    <span class="text-white font-semibold">Verified Israelite</span>
                </div>
                <div class="identity-field">
                    <span class="text-gray-300">Tribal Lineage:</span>
                    <span class="text-white font-semibold">{{ session.get('selected_tribe', 'Unknown') }}</span>
                </div>
                <div class="identity-field">
                    <span class="text-gray-300">Verification Status:</span>
                    <span class="text-green-400 font-semibold">✅ Gate Keeper Approved</span>
                </div>
                <div class="identity-field">
                    <span class="text-gray-300">Covenant Rights:</span>
                    <span class="text-white font-semibold">Full Member Privileges</span>
                </div>
                <div class="identity-field">
                    <span class="text-gray-300">Wallet Address:</span>
                    <span class="wallet-address text-blue-300" id="walletAddress">Generating...</span>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="inscription-form" id="registrationForm">
                <h3 class="text-xl font-bold text-white mb-4">Complete Your Registration</h3>
                
                <div class="form-group">
                    <label class="form-label" for="fullName">Full Name *</label>
                    <input type="text" id="fullName" class="form-input" placeholder="Your full legal name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="email">Email Address *</label>
                    <input type="email" id="email" class="form-input" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="hebrewName">Hebrew Name (Optional)</label>
                    <input type="text" id="hebrewName" class="form-input" placeholder="Your Hebrew or biblical name">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="laborCategory">Primary Labor Category *</label>
                    <select id="laborCategory" class="form-input" required>
                        <option value="">Select your primary calling...</option>
                        <option value="agriculture">Agriculture & Food Production</option>
                        <option value="craftsmanship">Craftsmanship & Manufacturing</option>
                        <option value="commerce">Commerce & Trade</option>
                        <option value="education">Education & Teaching</option>
                        <option value="healthcare">Healthcare & Healing</option>
                        <option value="governance">Governance & Administration</option>
                        <option value="technology">Technology & Innovation</option>
                        <option value="arts">Arts & Creative Expression</option>
                        <option value="ministry">Ministry & Spiritual Leadership</option>
                        <option value="security">Security & Protection</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="selaChoice">Business Association</label>
                    <select id="selaChoice" class="form-input">
                        <option value="community">Community Member (No business)</option>
                        <option value="create">Create New Sela Business</option>
                        <option value="join">Join Existing Sela</option>
                    </select>
                </div>

                <!-- Covenant Acceptance -->
                <div class="covenant-checkbox">
                    <input type="checkbox" id="covenantAccept" class="mr-3 transform scale-125">
                    <label for="covenantAccept" class="text-amber-200">
                        I solemnly accept the covenant terms and commit to upholding biblical principles 
                        as a verified member of the tribe of {{ session.get('selected_tribe', 'Unknown') }}.
                    </label>
                </div>

                <!-- Submit Button -->
                <div class="text-center mt-6">
                    <button id="inscribeIdentity" disabled
                            class="bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 text-lg">
                        🔏 Inscribe Identity on Blockchain
                    </button>
                </div>
            </div>

            <!-- Inscription Animation -->
            <div class="inscription-animation" id="inscriptionAnimation">
                <h3 class="text-2xl font-bold text-white mb-4">Inscribing Your Identity...</h3>
                <div class="blockchain-visualization">
                    <div class="block">📜</div>
                    <div class="block">🔗</div>
                    <div class="block">⛓️</div>
                    <div class="block">✅</div>
                </div>
                <p class="text-gray-300">Your covenant identity is being permanently recorded on the blockchain...</p>
            </div>

            <!-- Success Message -->
            <div class="success-message" id="successMessage">
                <div class="text-6xl mb-4">🎉</div>
                <h3 class="text-2xl font-bold text-emerald-200 mb-4">Covenant Inscription Complete!</h3>
                <p class="text-emerald-300 mb-6">
                    Welcome to the covenant community as a verified member of the tribe of {{ session.get('selected_tribe', 'Unknown') }}. 
                    Your identity has been permanently inscribed on the blockchain.
                </p>
                <a href="{{ url_for('dashboard.overview') }}"
                   class="inline-block bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                    Enter Your Dashboard →
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registrationForm');
    const covenantCheckbox = document.getElementById('covenantAccept');
    const inscribeButton = document.getElementById('inscribeIdentity');
    const inscriptionAnimation = document.getElementById('inscriptionAnimation');
    const successMessage = document.getElementById('successMessage');
    const walletAddressElement = document.getElementById('walletAddress');
    
    // Generate wallet address
    const walletAddress = generateWalletAddress();
    walletAddressElement.textContent = walletAddress;
    
    // Form validation
    function validateForm() {
        const fullName = document.getElementById('fullName').value.trim();
        const email = document.getElementById('email').value.trim();
        const laborCategory = document.getElementById('laborCategory').value;
        const covenantAccepted = covenantCheckbox.checked;

        const isValid = fullName && email && laborCategory && covenantAccepted;
        inscribeButton.disabled = !isValid;

        // Debug logging
        console.log('Form validation:', {
            fullName: fullName,
            email: email,
            laborCategory: laborCategory,
            covenantAccepted: covenantAccepted,
            isValid: isValid,
            buttonDisabled: inscribeButton.disabled
        });
    }
    
    // Add event listeners for form validation
    ['fullName', 'email', 'laborCategory'].forEach(id => {
        document.getElementById(id).addEventListener('input', validateForm);
    });
    covenantCheckbox.addEventListener('change', validateForm);
    
    // Handle inscription
    inscribeButton.addEventListener('click', function() {
        if (this.disabled) return;

        const formData = {
            fullName: document.getElementById('fullName').value,
            email: document.getElementById('email').value,
            hebrewName: document.getElementById('hebrewName').value,
            laborCategory: document.getElementById('laborCategory').value,
            selaChoice: document.getElementById('selaChoice').value,
            selectedTribe: sessionStorage.getItem('selectedTribe') || "{{ session.get('selected_tribe', 'Unknown') }}",
            selectedNation: 'ISR', // Israel nation code
            selectedNationName: 'Israel', // Israel nation name
            covenantPath: 'israelite',
            gatekeeperVerified: !sessionStorage.getItem('developerBypass'),
            walletAddress: walletAddress,
            // Include developer bypass information
            developerBypass: sessionStorage.getItem('developerBypass') === 'true',
            bypassType: sessionStorage.getItem('bypassType') || '',
            skipGateKeeper: sessionStorage.getItem('skipGateKeeper') === 'true'
        };

        // Debug logging
        console.log('Submitting form data:', formData);
        console.log('Session storage data:', {
            selectedTribe: sessionStorage.getItem('selectedTribe'),
            developerBypass: sessionStorage.getItem('developerBypass'),
            bypassType: sessionStorage.getItem('bypassType'),
            skipGateKeeper: sessionStorage.getItem('skipGateKeeper')
        });
        
        // Hide form and show animation
        form.style.display = 'none';
        inscriptionAnimation.style.display = 'block';
        
        // Simulate blockchain inscription
        setTimeout(() => {
            createIdentity(formData);
        }, 3000);
    });
    
    function generateWalletAddress() {
        // Generate a realistic-looking wallet address
        const prefix = 'ONX';
        const chars = '0123456789ABCDEF';
        let address = prefix;
        for (let i = 0; i < 34; i++) {
            address += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return address;
    }
    
    async function createIdentity(formData) {
        try {
            const response = await fetch('/auth/eden-mode/create-identity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();

            // Debug logging
            console.log('API response:', result);

            if (result.success) {
                // Store success data
                sessionStorage.setItem('edenMode_completed', 'true');
                sessionStorage.setItem('edenMode_identityId', result.identity_id);
                sessionStorage.setItem('edenMode_walletAddress', result.wallet_address);

                // Debug logging
                console.log('Verification status:', {
                    requires_gate_keeper: result.requires_gate_keeper_verification,
                    status: result.status,
                    message: result.message
                });

                // Handle different verification statuses
                if (result.requires_gate_keeper_verification) {
                    // Gate Keeper verification required
                    console.log('Showing Gate Keeper verification message');
                    showGateKeeperVerificationMessage(result);
                } else {
                    // Immediate verification (developer bypass or witness nation)
                    console.log('Showing success message');
                    showSuccessMessage(result);
                }
            } else {
                console.error('API error:', result.error || 'Registration failed');
                throw new Error(result.error || 'Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);

            // More detailed error handling
            let errorMessage = 'Registration failed: ';
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage += 'Network connection error. Please check your internet connection and try again.';
            } else if (error.message.includes('404')) {
                errorMessage += 'Registration service not found. Please contact support.';
            } else if (error.message.includes('500')) {
                errorMessage += 'Server error. Please try again in a few minutes.';
            } else {
                errorMessage += error.message;
            }

            alert(errorMessage);

            // Show form again
            inscriptionAnimation.style.display = 'none';
            form.style.display = 'block';
        }
    }

    function showGateKeeperVerificationMessage(result) {
        // Hide inscription animation
        inscriptionAnimation.style.display = 'none';

        // Create Gate Keeper verification message
        const verificationMessage = document.createElement('div');
        verificationMessage.className = 'verification-message';
        verificationMessage.innerHTML = `
            <div class="text-6xl mb-4">⚖️</div>
            <h3 class="text-2xl font-bold text-cyber-cyan mb-4">Gate Keeper Verification Required</h3>
            <p class="text-cyber-blue mb-6">
                ${result.message}
            </p>
            <div class="bg-gradient-to-r from-cyber-purple/20 to-cyber-blue/20 rounded-lg p-6 mb-6">
                <h4 class="font-bold text-cyber-purple mb-3">Next Steps:</h4>
                <ul class="text-left space-y-2">
                    ${result.next_steps.map(step => `<li class="flex items-start"><span class="text-cyber-cyan mr-2">•</span><span>${step}</span></li>`).join('')}
                </ul>
            </div>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="${result.redirect_url}"
                   class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    📊 Track Verification Progress
                </a>
                <a href="/dashboard/"
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    📱 Go to Dashboard
                </a>
            </div>
        `;

        // Replace success message with verification message
        const container = successMessage.parentNode;
        container.insertBefore(verificationMessage, successMessage);
        successMessage.style.display = 'none';
        verificationMessage.style.display = 'block';
    }

    function showSuccessMessage(result) {
        // Show standard success message
        inscriptionAnimation.style.display = 'none';
        successMessage.style.display = 'block';
    }

    // Pre-fill form if data exists in session
    const existingData = sessionStorage.getItem('edenMode_formData');
    if (existingData) {
        const data = JSON.parse(existingData);
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = data[key];
            }
        });
        validateForm();
    }
    
    // Save form data on input
    form.addEventListener('input', function() {
        const formData = {};
        ['fullName', 'email', 'hebrewName', 'laborCategory', 'selaChoice'].forEach(id => {
            formData[id] = document.getElementById(id).value;
        });
        sessionStorage.setItem('edenMode_formData', JSON.stringify(formData));
    });
});

// Developer Bypass Functions
function toggleDeveloperBypass() {
    const bypassSection = document.getElementById('developerBypass');
    bypassSection.classList.toggle('hidden');
    
    // Focus on password input if showing
    if (!bypassSection.classList.contains('hidden')) {
        setTimeout(() => {
            document.getElementById('bypassPassword').focus();
        }, 100);
    }
}

function validateBypass() {
    const password = document.getElementById('bypassPassword').value.trim();
    const correctPassword = "Israel United In Christ";
    
    if (password === correctPassword) {
        // Store bypass flag
        sessionStorage.setItem('developerBypass', 'true');
        sessionStorage.setItem('bypassType', 'developer');
        sessionStorage.setItem('bypassTimestamp', Date.now().toString());
        sessionStorage.setItem('skipGateKeeper', 'true');
        sessionStorage.setItem('verificationStatus', 'developer-bypassed');
        
        // Show success message
        const button = document.querySelector('button[onclick="validateBypass()"]');
        button.innerHTML = '✅ Developer Mode Activated';
        button.disabled = true;
        button.classList.remove('bg-gradient-to-r', 'from-cyber-purple', 'to-cyber-blue');
        button.classList.add('bg-green-600');
        
        // Update the page to show developer mode
        updateForDeveloperMode();
        
    } else {
        // Show error
        const input = document.getElementById('bypassPassword');
        input.style.borderColor = '#ef4444';
        input.style.boxShadow = '0 0 10px rgba(239, 68, 68, 0.3)';
        input.value = '';
        input.placeholder = 'Incorrect phrase - try again';
        
        setTimeout(() => {
            input.style.borderColor = '';
            input.style.boxShadow = '';
            input.placeholder = 'Enter covenant phrase...';
        }, 2000);
    }
}

function updateForDeveloperMode() {
    // Update the verification message
    const verificationSection = document.querySelector('.tribal-badge');
    if (verificationSection) {
        verificationSection.innerHTML = `
            <h3 class="text-xl font-bold text-green-400 mb-2">
                Developer Mode - All Tribes Access
            </h3>
            <p class="text-green-300">
                <strong>🚀 Developer Verified</strong> - You have developer access to all tribal registrations.
                This bypasses the normal Gate Keeper verification process.
            </p>
        `;
    }
    
    // Hide the developer bypass section
    document.getElementById('developerBypass').classList.add('hidden');
    
    // Show success notification
    setTimeout(() => {
        alert('✅ Developer mode activated! You can now proceed with any tribal registration.');
    }, 500);
}
</script>
{% endblock %}
