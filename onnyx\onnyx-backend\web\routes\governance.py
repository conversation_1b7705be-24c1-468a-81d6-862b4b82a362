"""
Governance Routes - Complete Governance Interface
Handles comprehensive governance functionality including:
- Public Council of 12 overview
- Active Voice Scrolls display and voting
- Gate Keeper verification votes
- Dispute resolution system
- Governance process management
- Smart contract execution
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from shared.db.db import db
from shared.models.identity_verification import IdentityVerificationProposal
from shared.models.voice_scroll import VoiceScroll
from shared.models.wisdom_engine import WisdomEngine, DisputeType
import json
import logging
import time
import uuid

logger = logging.getLogger(__name__)

governance_bp = Blueprint('governance', __name__, url_prefix='/governance')

@governance_bp.route('/')
def dashboard():
    """Main governance dashboard."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))

        identity_id = session['identity_id']

        # Get user info
        user = db.query_one("""
            SELECT name, verification_level, etzem_score, nation_code, nation_name, role_class
            FROM identities WHERE identity_id = ?
        """, (identity_id,))

        if not user:
            return redirect(url_for('auth.login'))

        # Check if user has governance access (Tier 1+ required)
        if user['verification_level'] < 1:
            return render_template('governance/access_denied.html',
                                 required_tier=1,
                                 current_tier=user['verification_level'])

        # Get active Voice Scrolls
        active_scrolls = db.query("""
            SELECT vs.*, i.name as creator_name, i.nation_code,
                   COUNT(v.vote_id) as total_votes,
                   SUM(CASE WHEN v.vote = 'yes' THEN 1 ELSE 0 END) as yes_votes,
                   SUM(CASE WHEN v.vote = 'no' THEN 1 ELSE 0 END) as no_votes
            FROM voice_scrolls vs
            LEFT JOIN identities i ON vs.creator_id = i.identity_id
            LEFT JOIN votes v ON vs.scroll_id = v.scroll_id
            WHERE vs.status IN ('PROPOSED', 'VOTING')
            GROUP BY vs.scroll_id
            ORDER BY vs.created_at DESC
            LIMIT 10
        """)

        # Get pending disputes
        pending_disputes = db.query("""
            SELECT case_id, case_title, dispute_type, priority_level,
                   submitted_at, assigned_elder_id
            FROM dispute_cases
            WHERE case_status IN ('SUBMITTED', 'UNDER_REVIEW')
            ORDER BY priority_level DESC, submitted_at ASC
            LIMIT 5
        """)

        # Get user's voting participation
        user_votes = db.query_one("""
            SELECT COUNT(*) as votes_cast,
                   COUNT(DISTINCT vs.scroll_id) as scrolls_participated
            FROM votes v
            LEFT JOIN voice_scrolls vs ON v.scroll_id = vs.scroll_id
            WHERE v.identity_id = ?
            AND vs.created_at > ?
        """, (identity_id, time.time() - (30 * 86400)))  # Last 30 days

        participation_data = user_votes if user_votes else {'votes_cast': 0, 'scrolls_participated': 0}

        # Calculate governance statistics
        governance_stats = {
            'active_scrolls': len(active_scrolls),
            'pending_disputes': len(pending_disputes),
            'user_participation': participation_data
        }

        return render_template('governance/dashboard.html',
                             user=user,
                             active_scrolls=active_scrolls,
                             pending_disputes=pending_disputes,
                             governance_stats=governance_stats)

    except Exception as e:
        logger.error(f"Error in governance dashboard: {e}")
        return render_template('error.html', error="Failed to load governance dashboard"), 500

@governance_bp.route('/public')
def public_governance():
    """Public governance transparency page."""
    try:
        # Get Council of 12 Gate Keepers
        gate_keepers = db.query("""
            SELECT identity_id, name, metadata
            FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
            AND JSON_EXTRACT(metadata, '$.council_member') = 1
            ORDER BY name
        """)
        
        # Parse metadata for each Gate Keeper
        for gk in gate_keepers:
            if gk['metadata']:
                gk['metadata'] = json.loads(gk['metadata'])
        
        # Get active Voice Scrolls
        active_scrolls = VoiceScroll.find_by_status("active")
        
        # Get recent identity verification proposals
        pending_verifications = IdentityVerificationProposal.get_pending_proposals()
        
        # Get governance statistics
        stats = {
            'total_gate_keepers': len(gate_keepers),
            'active_proposals': len(active_scrolls),
            'pending_verifications': len(pending_verifications),
            'total_voice_scrolls': len(VoiceScroll.get_all())
        }
        
        return render_template('governance/public.html',
                             gate_keepers=gate_keepers,
                             active_scrolls=active_scrolls,
                             pending_verifications=pending_verifications,
                             stats=stats)
                             
    except Exception as e:
        logger.error(f"Error loading public governance: {e}")
        return render_template('error.html', 
                             error_message="Unable to load governance information"), 500

@governance_bp.route('/council')
def council_overview():
    """Detailed Council of 12 overview."""
    try:
        # Get all tribal representatives and Gate Keepers
        council_members = db.query("""
            SELECT identity_id, name, metadata
            FROM identities 
            WHERE (JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
                   OR JSON_EXTRACT(metadata, '$.tribal_role') = 'Elder')
            AND JSON_EXTRACT(metadata, '$.council_member') = 1
            ORDER BY JSON_EXTRACT(metadata, '$.tribe_name'), name
        """)
        
        # Parse metadata and group by tribe
        tribes = {}
        for member in council_members:
            if member['metadata']:
                metadata = json.loads(member['metadata'])
                tribe_name = metadata.get('tribe_name', 'Unknown')
                if tribe_name not in tribes:
                    tribes[tribe_name] = []
                member['metadata'] = metadata
                tribes[tribe_name].append(member)
        
        return render_template('governance/council.html',
                             council_members=council_members,
                             tribes=tribes)
                             
    except Exception as e:
        logger.error(f"Error loading council overview: {e}")
        return render_template('error.html', 
                             error_message="Unable to load council information"), 500

@governance_bp.route('/voice-scrolls')
def voice_scrolls():
    """Active Voice Scrolls display."""
    try:
        # Get all Voice Scrolls grouped by status
        all_scrolls = VoiceScroll.get_all()
        
        scrolls_by_status = {
            'active': [],
            'approved': [],
            'rejected': [],
            'expired': []
        }
        
        for scroll in all_scrolls:
            status = scroll.status.lower()
            if status in scrolls_by_status:
                scrolls_by_status[status].append(scroll)
        
        # Get identity verification scrolls specifically
        identity_scrolls = VoiceScroll.find_by_category("IDENTITY_VERIFICATION")
        
        return render_template('governance/voice_scrolls.html',
                             scrolls_by_status=scrolls_by_status,
                             identity_scrolls=identity_scrolls)
                             
    except Exception as e:
        logger.error(f"Error loading voice scrolls: {e}")
        return render_template('error.html', 
                             error_message="Unable to load voice scrolls"), 500

@governance_bp.route('/verification/<proposal_id>')
def verification_details(proposal_id):
    """Detailed view of an identity verification proposal."""
    try:
        # Get the Voice Scroll
        voice_scroll = VoiceScroll.find_by_id(proposal_id)
        if not voice_scroll:
            return render_template('error.html', 
                                 error_message="Verification proposal not found"), 404
        
        # Get the verification proposal details
        verification = db.query_one("""
            SELECT proposal_id, identity_id, tribe_code, applicant_data,
                   votes, status, created_at
            FROM identity_verification_proposals
            WHERE proposal_id = ?
        """, (proposal_id,))
        
        if verification:
            verification['applicant_data'] = json.loads(verification['applicant_data'])
            verification['votes'] = json.loads(verification['votes'])
        
        # Get Gate Keeper information for votes
        gate_keepers = db.query("""
            SELECT identity_id, name, metadata
            FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
            ORDER BY name
        """)
        
        for gk in gate_keepers:
            if gk['metadata']:
                gk['metadata'] = json.loads(gk['metadata'])
        
        return render_template('governance/verification_details.html',
                             voice_scroll=voice_scroll,
                             verification=verification,
                             gate_keepers=gate_keepers)
                             
    except Exception as e:
        logger.error(f"Error loading verification details: {e}")
        return render_template('error.html', 
                             error_message="Unable to load verification details"), 500

@governance_bp.route('/process')
def governance_process():
    """Governance process explanation."""
    return render_template('governance/process.html')

@governance_bp.route('/faq')
def governance_faq():
    """FAQ about biblical governance."""
    return render_template('governance/faq.html')

@governance_bp.route('/api/stats')
def api_governance_stats():
    """API endpoint for governance statistics."""
    try:
        # Get various governance statistics
        stats = {}
        
        # Gate Keeper stats
        gate_keepers = db.query("""
            SELECT COUNT(*) as count FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
        """)
        stats['gate_keepers'] = gate_keepers[0]['count'] if gate_keepers else 0
        
        # Voice Scrolls stats
        voice_scrolls = db.query("""
            SELECT status, COUNT(*) as count FROM voice_scrolls 
            GROUP BY status
        """)
        stats['voice_scrolls'] = {vs['status']: vs['count'] for vs in voice_scrolls}
        
        # Identity verification stats
        verifications = db.query("""
            SELECT status, COUNT(*) as count FROM identity_verification_proposals 
            GROUP BY status
        """)
        stats['verifications'] = {v['status']: v['count'] for v in verifications}
        
        # Tribal representation stats
        tribes = db.query("""
            SELECT JSON_EXTRACT(metadata, '$.tribe_name') as tribe, COUNT(*) as count
            FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') IN ('Gate_Keeper', 'Elder')
            GROUP BY JSON_EXTRACT(metadata, '$.tribe_name')
        """)
        stats['tribal_representation'] = {t['tribe']: t['count'] for t in tribes if t['tribe']}
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting governance stats: {e}")
        return jsonify({'error': 'Failed to load governance statistics'}), 500

@governance_bp.route('/api/recent-activity')
def api_recent_activity():
    """API endpoint for recent governance activity."""
    try:
        # Get recent Voice Scrolls
        recent_scrolls = db.query("""
            SELECT scroll_id, title, category, status, created_at
            FROM voice_scrolls 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        # Get recent verification proposals
        recent_verifications = db.query("""
            SELECT proposal_id, identity_id, tribe_code, status, created_at
            FROM identity_verification_proposals 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        return jsonify({
            'success': True,
            'recent_scrolls': recent_scrolls,
            'recent_verifications': recent_verifications
        })
        
    except Exception as e:
        logger.error(f"Error getting recent activity: {e}")
        return jsonify({'error': 'Failed to load recent activity'}), 500

@governance_bp.route('/vote/<scroll_id>', methods=['GET', 'POST'])
def vote_on_scroll(scroll_id):
    """Vote on a Voice Scroll."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))

        identity_id = session['identity_id']

        # Get scroll details
        scroll = db.query_one("""
            SELECT vs.*, i.name as creator_name, i.nation_code
            FROM voice_scrolls vs
            LEFT JOIN identities i ON vs.creator_id = i.identity_id
            WHERE vs.scroll_id = ?
        """, (scroll_id,))

        if not scroll:
            return render_template('error.html', error="Voice Scroll not found"), 404

        # Check if scroll is still open for voting
        if scroll['status'] not in ['PROPOSED', 'VOTING']:
            return render_template('governance/voting_closed.html', scroll=scroll)

        # Check if user has already voted
        existing_vote = db.query_one("""
            SELECT vote FROM votes WHERE scroll_id = ? AND identity_id = ?
        """, (scroll_id, identity_id))

        if request.method == 'POST':
            if existing_vote:
                return jsonify({'error': 'You have already voted on this scroll'}), 400

            data = request.get_json()
            vote_decision = data.get('vote', '').lower()
            vote_reason = data.get('reason', '').strip()

            if vote_decision not in ['yes', 'no', 'abstain']:
                return jsonify({'error': 'Invalid vote decision'}), 400

            # Record the vote
            vote_id = f"VOTE_{int(time.time())}_{identity_id[:8]}"

            db.execute("""
                INSERT INTO votes
                (vote_id, scroll_id, identity_id, vote, reason, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (vote_id, scroll_id, identity_id, vote_decision, vote_reason, int(time.time())))

            logger.info(f"Vote recorded: {vote_decision} on {scroll_id} by {identity_id}")
            return jsonify({'success': True, 'vote_id': vote_id})

        # GET request - show voting interface
        # Get current vote tally
        vote_tally = db.query_one("""
            SELECT
                COUNT(CASE WHEN vote = 'yes' THEN 1 END) as yes_votes,
                COUNT(CASE WHEN vote = 'no' THEN 1 END) as no_votes,
                COUNT(CASE WHEN vote = 'abstain' THEN 1 END) as abstain_votes,
                COUNT(*) as total_votes
            FROM votes WHERE scroll_id = ?
        """, (scroll_id,))

        return render_template('governance/vote.html',
                             scroll=scroll,
                             existing_vote=existing_vote,
                             vote_tally=vote_tally)

    except Exception as e:
        logger.error(f"Error in vote interface: {e}")
        return render_template('error.html', error="Failed to load voting interface"), 500

@governance_bp.route('/disputes')
def dispute_resolution():
    """Dispute resolution interface."""
    try:
        if 'identity_id' not in session:
            return redirect(url_for('auth.login'))

        user = db.query_one("""
            SELECT verification_level, name FROM identities WHERE identity_id = ?
        """, (session['identity_id'],))

        if not user or user['verification_level'] < 1:
            return render_template('governance/access_denied.html', required_tier=1)

        # Get all dispute cases
        disputes = db.query("""
            SELECT dc.*,
                   p.name as plaintiff_name, p.nation_code as plaintiff_tribe,
                   d.name as defendant_name, d.nation_code as defendant_tribe,
                   e.name as elder_name, e.nation_code as elder_tribe
            FROM dispute_cases dc
            LEFT JOIN identities p ON dc.plaintiff_id = p.identity_id
            LEFT JOIN identities d ON dc.defendant_id = d.identity_id
            LEFT JOIN identities e ON dc.assigned_elder_id = e.identity_id
            ORDER BY dc.priority_level DESC, dc.submitted_at ASC
        """)

        return render_template('governance/disputes.html',
                             disputes=disputes,
                             user=user)

    except Exception as e:
        logger.error(f"Error in dispute resolution: {e}")
        return render_template('error.html', error="Failed to load dispute resolution"), 500
