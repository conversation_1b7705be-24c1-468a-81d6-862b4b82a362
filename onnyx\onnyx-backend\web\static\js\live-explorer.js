/**
 * ONNYX Live Blockchain Explorer
 * Real-time blockchain data visualization with cyberpunk aesthetic
 */

class LiveExplorer {
    constructor() {
        this.refreshInterval = 30000; // 30 seconds
        this.intervals = [];
        this.isVisible = true;
        this.init();
    }

    init() {
        console.log('🔗 ONNYX Live Explorer Initialized');
        this.setupVisibilityHandling();
        this.startLiveUpdates();
        this.setupEventListeners();
        this.addLoadingStates();
    }

    setupVisibilityHandling() {
        // Pause updates when page is not visible
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                this.startLiveUpdates();
            } else {
                this.stopLiveUpdates();
            }
        });
    }

    startLiveUpdates() {
        if (!this.isVisible) return;

        // Initial load
        this.updateAllData();

        // Set up intervals
        this.intervals.push(
            setInterval(() => this.updateBlocks(), this.refreshInterval),
            setInterval(() => this.updateTransactions(), this.refreshInterval),
            setInterval(() => this.updateMiningStats(), this.refreshInterval * 2), // Less frequent
            setInterval(() => this.updateNetworkStats(), this.refreshInterval * 3) // Even less frequent
        );

        console.log('🔄 Live updates started');
    }

    stopLiveUpdates() {
        this.intervals.forEach(interval => clearInterval(interval));
        this.intervals = [];
        console.log('⏸️ Live updates paused');
    }

    setupEventListeners() {
        // Refresh buttons
        document.querySelectorAll('[data-refresh]').forEach(button => {
            button.addEventListener('click', (e) => {
                const section = e.target.dataset.refresh;
                this.refreshSection(section);
            });
        });

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.startLiveUpdates();
                } else {
                    this.stopLiveUpdates();
                }
            });
        }
    }

    addLoadingStates() {
        // Add loading states to all data containers
        const containers = [
            '#recent-blocks-container',
            '#recent-transactions-container',
            '#mining-stats-container',
            '#network-stats-container'
        ];

        containers.forEach(selector => {
            const container = document.querySelector(selector);
            if (container) {
                container.classList.add('loading');
            }
        });
    }

    async updateAllData() {
        try {
            await Promise.all([
                this.updateBlocks(),
                this.updateTransactions(),
                this.updateMiningStats(),
                this.updateNetworkStats()
            ]);
        } catch (error) {
            console.error('Error updating all data:', error);
        }
    }

    async updateBlocks() {
        try {
            const response = await fetch('/explorer/api/live/blocks');
            const data = await response.json();

            if (data.success) {
                this.renderBlocks(data.blocks);
                this.updateMetric('#total-blocks', data.total_blocks);
            }
        } catch (error) {
            console.error('Error updating blocks:', error);
            this.showError('#recent-blocks-container', 'Failed to load blocks');
        }
    }

    async updateTransactions() {
        try {
            const response = await fetch('/explorer/api/live/transactions');
            const data = await response.json();

            if (data.success) {
                this.renderTransactions(data.transactions);
                this.updateMetric('#total-transactions', data.total_transactions);
            }
        } catch (error) {
            console.error('Error updating transactions:', error);
            this.showError('#recent-transactions-container', 'Failed to load transactions');
        }
    }

    async updateMiningStats() {
        try {
            const response = await fetch('/explorer/api/live/mining');
            const data = await response.json();

            if (data.success) {
                this.renderMiningStats(data.mining_stats);
            }
        } catch (error) {
            console.error('Error updating mining stats:', error);
            this.showError('#mining-stats-container', 'Failed to load mining stats');
        }
    }

    async updateNetworkStats() {
        try {
            const response = await fetch('/explorer/api/live/network');
            const data = await response.json();

            if (data.success) {
                this.renderNetworkStats(data.network_stats);
            }
        } catch (error) {
            console.error('Error updating network stats:', error);
            this.showError('#network-stats-container', 'Failed to load network stats');
        }
    }

    renderBlocks(blocks) {
        const container = document.querySelector('#recent-blocks-container');
        if (!container) return;

        container.classList.remove('loading');

        const blocksHtml = blocks.map(block => `
            <div class="block-item glass-card" data-block="${block.block_height}">
                <div class="block-header">
                    <span class="block-height">#${block.block_height}</span>
                    <span class="block-age" data-format="timestamp" data-value="${block.timestamp * 1000}">
                        ${this.formatTimestamp(block.timestamp)}
                    </span>
                </div>
                <div class="block-details">
                    <div class="block-hash" data-format="hash" title="${block.block_hash}">
                        ${this.formatHash(block.block_hash)}
                    </div>
                    <div class="block-miner">
                        <span class="miner-label">Miner:</span>
                        <span class="miner-id">${this.formatHash(block.miner, 6)}</span>
                        ${block.miner_tribal_affiliation ?
                            `<span class="tribal-badge tribal-${block.miner_tribal_affiliation.toLowerCase()}">${block.miner_tribal_affiliation}</span>`
                            : ''
                        }
                    </div>
                    <div class="block-stats">
                        <span class="tx-count">${block.transaction_count || 0} txs</span>
                        <span class="block-size">${this.formatBytes(block.block_size || 0)}</span>
                        <span class="difficulty">Diff: ${block.difficulty || 'N/A'}</span>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = blocksHtml;
        this.addBlockAnimations();
    }

    renderTransactions(transactions) {
        const container = document.querySelector('#recent-transactions-container');
        if (!container) return;

        container.classList.remove('loading');

        const txHtml = transactions.map(tx => `
            <div class="transaction-item glass-card" data-tx="${tx.tx_hash}">
                <div class="tx-header">
                    <span class="tx-hash" data-format="hash" title="${tx.tx_hash}">
                        ${this.formatHash(tx.tx_hash)}
                    </span>
                    <span class="tx-age" data-format="timestamp" data-value="${tx.timestamp * 1000}">
                        ${this.formatTimestamp(tx.timestamp)}
                    </span>
                </div>
                <div class="tx-details">
                    <div class="tx-flow">
                        <div class="tx-sender">
                            <span class="address">${this.formatHash(tx.sender, 8)}</span>
                            ${tx.sender_tribal_affiliation ?
                                `<span class="tribal-badge tribal-${tx.sender_tribal_affiliation.toLowerCase()}">${tx.sender_tribal_affiliation}</span>`
                                : ''
                            }
                        </div>
                        <div class="tx-arrow">→</div>
                        <div class="tx-receiver">
                            <span class="address">${this.formatHash(tx.receiver, 8)}</span>
                            ${tx.receiver_tribal_affiliation ?
                                `<span class="tribal-badge tribal-${tx.receiver_tribal_affiliation.toLowerCase()}">${tx.receiver_tribal_affiliation}</span>`
                                : ''
                            }
                        </div>
                    </div>
                    <div class="tx-stats">
                        <span class="tx-amount">${this.formatAmount(tx.amount)} ONX</span>
                        <span class="tx-type">${tx.tx_type || 'Transfer'}</span>
                        <span class="tx-confirmations">${tx.confirmation_count || 0} conf</span>
                        <span class="tx-status status-${tx.status || 'pending'}">${tx.status || 'pending'}</span>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = txHtml;
        this.addTransactionAnimations();
    }

    renderMiningStats(stats) {
        const container = document.querySelector('#mining-stats-container');
        if (!container) return;

        container.classList.remove('loading');

        // Update individual metrics
        this.updateMetric('#current-difficulty', stats.current_difficulty);
        this.updateMetric('#network-hashrate', this.formatHashrate(stats.network_hashrate));
        this.updateMetric('#active-miners', stats.active_miners);
        this.updateMetric('#blocks-today', stats.blocks_today);
        this.updateMetric('#avg-block-time', this.formatDuration(stats.average_block_time));
        this.updateMetric('#mining-rewards-today', `${stats.mining_rewards_distributed} ONX`);

        // Update Sabbath compliance
        const sabbathStatus = document.querySelector('#sabbath-status');
        if (sabbathStatus && stats.sabbath_compliance) {
            const compliance = stats.sabbath_compliance;
            sabbathStatus.innerHTML = `
                <span class="sabbath-indicator ${compliance.is_sabbath ? 'sabbath-active' : 'sabbath-inactive'}">
                    ${compliance.is_sabbath ? '🕯️ Sabbath' : '⚡ Active'}
                </span>
                <span class="compliance-status ${compliance.compliance ? 'compliant' : 'non-compliant'}">
                    ${compliance.compliance ? 'Compliant' : 'Non-Compliant'}
                </span>
            `;
        }

        // Update tribal mining distribution
        this.renderTribalDistribution('#tribal-mining-chart', stats.tribal_mining_distribution);
    }

    renderNetworkStats(stats) {
        const container = document.querySelector('#network-stats-container');
        if (!container) return;

        container.classList.remove('loading');

        // Update network metrics
        this.updateMetric('#node-count', stats.node_count);
        this.updateMetric('#network-latency', `${stats.network_latency.average}ms`);
        this.updateMetric('#covenant-compliance', `${stats.covenant_compliance_score}%`);

        // Update consensus status
        const consensusStatus = document.querySelector('#consensus-status');
        if (consensusStatus && stats.consensus_status) {
            const status = stats.consensus_status;
            consensusStatus.innerHTML = `
                <div class="consensus-indicator status-${status.status}">
                    <span class="status-dot"></span>
                    <span class="status-text">${status.status.toUpperCase()}</span>
                </div>
                <div class="health-bar">
                    <div class="health-fill" style="width: ${status.health * 100}%"></div>
                </div>
            `;
        }

        // Update sync status
        const syncStatus = document.querySelector('#sync-status');
        if (syncStatus && stats.sync_status) {
            const sync = stats.sync_status;
            syncStatus.innerHTML = `
                <div class="sync-indicator status-${sync.status}">
                    <span class="sync-text">${sync.status.toUpperCase()}</span>
                    <span class="sync-progress">${sync.progress}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${sync.progress}%"></div>
                </div>
            `;
        }

        // Update tribal node distribution
        this.renderTribalDistribution('#tribal-nodes-chart', stats.tribal_node_distribution);

        // Update governance activity
        const govActivity = document.querySelector('#governance-activity');
        if (govActivity && stats.governance_activity) {
            const activity = stats.governance_activity;
            govActivity.innerHTML = `
                <div class="gov-metric">
                    <span class="metric-label">Elder Activities</span>
                    <span class="metric-value">${activity.elder_activities}</span>
                </div>
                <div class="gov-metric">
                    <span class="metric-label">Consensus</span>
                    <span class="metric-value ${activity.consensus_reached ? 'positive' : 'negative'}">
                        ${activity.consensus_reached ? 'Reached' : 'Pending'}
                    </span>
                </div>
            `;
        }
    }

    renderTribalDistribution(selector, distribution) {
        const container = document.querySelector(selector);
        if (!container || !distribution) return;

        const distributionHtml = Object.entries(distribution)
            .sort((a, b) => b[1].percentage - a[1].percentage)
            .map(([tribe, data]) => `
                <div class="tribal-distribution-item">
                    <div class="tribe-info">
                        <span class="tribe-name">${tribe}</span>
                        <span class="tribe-percentage">${data.percentage}%</span>
                    </div>
                    <div class="tribe-bar">
                        <div class="tribe-fill tribal-${tribe.toLowerCase()}"
                             style="width: ${data.percentage}%"></div>
                    </div>
                    <div class="tribe-count">${data.blocks || data.nodes || 0}</div>
                </div>
            `).join('');

        container.innerHTML = distributionHtml;
    }

    // Utility functions
    formatTimestamp(timestamp) {
        const date = new Date(timestamp * 1000);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;

        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatHash(hash, length = 8) {
        if (!hash) return '—';
        if (hash.length <= length * 2) return hash;
        return `${hash.substring(0, length)}...${hash.substring(hash.length - length)}`;
    }

    formatAmount(amount) {
        if (!amount) return '0.00';
        return parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 6
        });
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatHashrate(hashrate) {
        if (hashrate < 1000) return `${hashrate} H/s`;
        if (hashrate < 1000000) return `${(hashrate / 1000).toFixed(1)} KH/s`;
        if (hashrate < 1000000000) return `${(hashrate / 1000000).toFixed(1)} MH/s`;
        return `${(hashrate / 1000000000).toFixed(1)} GH/s`;
    }

    formatDuration(seconds) {
        if (seconds < 60) return `${seconds}s`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
        return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
    }

    updateMetric(selector, value) {
        const element = document.querySelector(selector);
        if (!element) return;

        // Add update animation
        element.style.transform = 'scale(1.05)';
        element.style.transition = 'transform 0.2s ease';

        setTimeout(() => {
            element.textContent = value;
            element.style.transform = 'scale(1)';
        }, 100);
    }

    addBlockAnimations() {
        const blocks = document.querySelectorAll('.block-item');
        blocks.forEach((block, index) => {
            block.style.animationDelay = `${index * 0.1}s`;
            block.classList.add('fade-in');
        });
    }

    addTransactionAnimations() {
        const transactions = document.querySelectorAll('.transaction-item');
        transactions.forEach((tx, index) => {
            tx.style.animationDelay = `${index * 0.05}s`;
            tx.classList.add('slide-in');
        });
    }

    showError(selector, message) {
        const container = document.querySelector(selector);
        if (!container) return;

        container.classList.remove('loading');
        container.innerHTML = `
            <div class="error-message">
                <span class="error-icon">⚠️</span>
                <span class="error-text">${message}</span>
                <button class="retry-button" onclick="liveExplorer.updateAllData()">Retry</button>
            </div>
        `;
    }

    refreshSection(section) {
        switch (section) {
            case 'blocks':
                this.updateBlocks();
                break;
            case 'transactions':
                this.updateTransactions();
                break;
            case 'mining':
                this.updateMiningStats();
                break;
            case 'network':
                this.updateNetworkStats();
                break;
            default:
                this.updateAllData();
        }
    }

    destroy() {
        this.stopLiveUpdates();
        console.log('🔗 Live Explorer destroyed');
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.liveExplorer = new LiveExplorer();
    });
} else {
    window.liveExplorer = new LiveExplorer();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.liveExplorer) {
        window.liveExplorer.destroy();
    }
});
