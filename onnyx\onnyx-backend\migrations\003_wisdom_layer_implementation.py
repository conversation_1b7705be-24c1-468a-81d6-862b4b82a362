#!/usr/bin/env python3
"""
Wisdom Layer Implementation Migration
Creates the Solomonic judgment system with storage for rulings, parables, and scrolls.
Implements on-chain wisdom as logic templates for divine governance.

This migration adds:
1. Wisdom scrolls storage (parables, teachings, prophecies)
2. Judgment records and precedents
3. Dispute resolution tracking
4. Biblical reference library
5. Wisdom engine logic templates

Author: ONNYX Development Team
Date: 2025-07-17
Priority: HIGH - Required for divine governance system
"""

import sqlite3
import sys
import os
import logging
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_connection():
    """Get database connection."""
    try:
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        if backend_dir.endswith('migrations'):
            backend_dir = os.path.dirname(backend_dir)
        
        db_path = os.path.join(backend_dir, 'data', 'onnyx.db')
        logger.info(f"Connecting to database: {db_path}")
        
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
        
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return None

def create_wisdom_scrolls_table(conn):
    """Create wisdom scrolls storage table for parables, teachings, and prophecies."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wisdom_scrolls (
                scroll_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                scroll_type TEXT NOT NULL, -- 'PARABLE', 'TEACHING', 'PROPHECY', 'JUDGMENT_TEMPLATE', 'PROVERB'
                author_identity_id TEXT,
                biblical_references TEXT, -- JSON array of scripture references
                wisdom_category TEXT, -- 'JUSTICE', 'MERCY', 'GOVERNANCE', 'ECONOMICS', 'RELATIONSHIPS'
                application_context TEXT, -- When/how this wisdom should be applied
                precedent_weight INTEGER DEFAULT 1, -- How much weight this carries in decisions
                usage_count INTEGER DEFAULT 0, -- How often this wisdom has been applied
                effectiveness_score REAL DEFAULT 0.0, -- How effective this wisdom has proven
                created_at INTEGER NOT NULL,
                last_applied INTEGER,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (author_identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_wisdom_scrolls_type ON wisdom_scrolls(scroll_type)",
            "CREATE INDEX IF NOT EXISTS idx_wisdom_scrolls_category ON wisdom_scrolls(wisdom_category)",
            "CREATE INDEX IF NOT EXISTS idx_wisdom_scrolls_author ON wisdom_scrolls(author_identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_wisdom_scrolls_weight ON wisdom_scrolls(precedent_weight)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created wisdom_scrolls table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create wisdom_scrolls table: {e}")
        return False

def create_judgment_records_table(conn):
    """Create judgment records table for storing elder decisions and precedents."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS judgment_records (
                judgment_id TEXT PRIMARY KEY,
                case_title TEXT NOT NULL,
                case_description TEXT NOT NULL,
                dispute_type TEXT NOT NULL, -- 'PROPERTY', 'DEBT', 'LABOR', 'COVENANT_VIOLATION', 'INHERITANCE'
                plaintiff_id TEXT NOT NULL,
                defendant_id TEXT NOT NULL,
                presiding_elder_id TEXT NOT NULL,
                additional_judges TEXT, -- JSON array of other elder IDs involved
                judgment_text TEXT NOT NULL,
                biblical_basis TEXT NOT NULL, -- Scripture references supporting the judgment
                wisdom_scrolls_cited TEXT, -- JSON array of wisdom scroll IDs referenced
                judgment_type TEXT NOT NULL, -- 'BINDING', 'ADVISORY', 'PRECEDENT_SETTING'
                enforcement_method TEXT, -- How the judgment should be enforced
                appeal_status TEXT DEFAULT 'NONE', -- 'NONE', 'PENDING', 'UPHELD', 'OVERTURNED'
                created_at INTEGER NOT NULL,
                executed_at INTEGER,
                appealed_at INTEGER,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (plaintiff_id) REFERENCES identities(identity_id),
                FOREIGN KEY (defendant_id) REFERENCES identities(identity_id),
                FOREIGN KEY (presiding_elder_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_judgment_records_type ON judgment_records(dispute_type)",
            "CREATE INDEX IF NOT EXISTS idx_judgment_records_elder ON judgment_records(presiding_elder_id)",
            "CREATE INDEX IF NOT EXISTS idx_judgment_records_plaintiff ON judgment_records(plaintiff_id)",
            "CREATE INDEX IF NOT EXISTS idx_judgment_records_defendant ON judgment_records(defendant_id)",
            "CREATE INDEX IF NOT EXISTS idx_judgment_records_created ON judgment_records(created_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created judgment_records table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create judgment_records table: {e}")
        return False

def create_dispute_cases_table(conn):
    """Create dispute cases table for tracking ongoing disputes."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dispute_cases (
                case_id TEXT PRIMARY KEY,
                case_title TEXT NOT NULL,
                case_description TEXT NOT NULL,
                dispute_type TEXT NOT NULL,
                plaintiff_id TEXT NOT NULL,
                defendant_id TEXT NOT NULL,
                assigned_elder_id TEXT,
                case_status TEXT DEFAULT 'SUBMITTED', -- 'SUBMITTED', 'UNDER_REVIEW', 'HEARING_SCHEDULED', 'JUDGED', 'APPEALED', 'CLOSED'
                priority_level INTEGER DEFAULT 1, -- 1-5, with 5 being highest priority
                evidence_submitted TEXT, -- JSON array of evidence documents/links
                witness_statements TEXT, -- JSON array of witness testimony
                biblical_principles_involved TEXT, -- JSON array of relevant biblical principles
                wisdom_consultation_requested BOOLEAN DEFAULT FALSE,
                public_hearing BOOLEAN DEFAULT FALSE,
                submitted_at INTEGER NOT NULL,
                hearing_scheduled_at INTEGER,
                judgment_deadline INTEGER,
                resolved_at INTEGER,
                final_judgment_id TEXT, -- Links to judgment_records table
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (plaintiff_id) REFERENCES identities(identity_id),
                FOREIGN KEY (defendant_id) REFERENCES identities(identity_id),
                FOREIGN KEY (assigned_elder_id) REFERENCES identities(identity_id),
                FOREIGN KEY (final_judgment_id) REFERENCES judgment_records(judgment_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_dispute_cases_status ON dispute_cases(case_status)",
            "CREATE INDEX IF NOT EXISTS idx_dispute_cases_type ON dispute_cases(dispute_type)",
            "CREATE INDEX IF NOT EXISTS idx_dispute_cases_elder ON dispute_cases(assigned_elder_id)",
            "CREATE INDEX IF NOT EXISTS idx_dispute_cases_priority ON dispute_cases(priority_level)",
            "CREATE INDEX IF NOT EXISTS idx_dispute_cases_submitted ON dispute_cases(submitted_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created dispute_cases table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create dispute_cases table: {e}")
        return False

def create_biblical_references_table(conn):
    """Create biblical references library table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biblical_references (
                reference_id TEXT PRIMARY KEY,
                book TEXT NOT NULL,
                chapter INTEGER NOT NULL,
                verse_start INTEGER NOT NULL,
                verse_end INTEGER,
                text_kjv TEXT NOT NULL,
                principle_category TEXT NOT NULL, -- 'JUSTICE', 'MERCY', 'GOVERNANCE', 'ECONOMICS', 'RELATIONSHIPS'
                application_context TEXT, -- When this scripture applies
                related_topics TEXT, -- JSON array of related topics
                cross_references TEXT, -- JSON array of related scripture references
                usage_count INTEGER DEFAULT 0,
                created_at INTEGER NOT NULL,
                metadata TEXT DEFAULT '{}'
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_biblical_references_book ON biblical_references(book)",
            "CREATE INDEX IF NOT EXISTS idx_biblical_references_category ON biblical_references(principle_category)",
            "CREATE INDEX IF NOT EXISTS idx_biblical_references_chapter ON biblical_references(book, chapter)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created biblical_references table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create biblical_references table: {e}")
        return False

def create_wisdom_applications_table(conn):
    """Create wisdom applications tracking table."""
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS wisdom_applications (
                application_id TEXT PRIMARY KEY,
                wisdom_scroll_id TEXT NOT NULL,
                applied_to_case_id TEXT, -- Links to dispute_cases or judgment_records
                applied_by_elder_id TEXT NOT NULL,
                application_context TEXT NOT NULL,
                outcome_description TEXT,
                effectiveness_rating INTEGER, -- 1-5 rating of how effective the wisdom was
                lessons_learned TEXT,
                applied_at INTEGER NOT NULL,
                outcome_recorded_at INTEGER,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (wisdom_scroll_id) REFERENCES wisdom_scrolls(scroll_id),
                FOREIGN KEY (applied_by_elder_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_wisdom_applications_scroll ON wisdom_applications(wisdom_scroll_id)",
            "CREATE INDEX IF NOT EXISTS idx_wisdom_applications_elder ON wisdom_applications(applied_by_elder_id)",
            "CREATE INDEX IF NOT EXISTS idx_wisdom_applications_applied ON wisdom_applications(applied_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        logger.info("✅ Created wisdom_applications table with indexes")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create wisdom_applications table: {e}")
        return False

def seed_initial_wisdom_data(conn):
    """Seed the database with initial biblical wisdom and references."""
    try:
        cursor = conn.cursor()
        
        # Add some foundational biblical references
        biblical_refs = [
            {
                "reference_id": "DEUT_16_18_20",
                "book": "Deuteronomy",
                "chapter": 16,
                "verse_start": 18,
                "verse_end": 20,
                "text_kjv": "Judges and officers shalt thou make thee in all thy gates, which the LORD thy God giveth thee, throughout thy tribes: and they shall judge the people with just judgment. Thou shalt not wrest judgment; thou shalt not respect persons, neither take a gift: for a gift doth blind the eyes of the wise, and pervert the words of the righteous. That which is altogether just shalt thou follow, that thou mayest live, and inherit the land which the LORD thy God giveth thee.",
                "principle_category": "JUSTICE",
                "application_context": "Establishing fair judgment and avoiding corruption in governance",
                "related_topics": '["governance", "corruption", "fairness", "judgment"]'
            },
            {
                "reference_id": "PROV_27_5_6",
                "book": "Proverbs",
                "chapter": 27,
                "verse_start": 5,
                "verse_end": 6,
                "text_kjv": "Open rebuke is better than secret love. Faithful are the wounds of a friend; but the kisses of an enemy are deceitful.",
                "principle_category": "RELATIONSHIPS",
                "application_context": "Handling disputes and correction within the community",
                "related_topics": '["correction", "friendship", "honesty", "rebuke"]'
            }
        ]
        
        for ref in biblical_refs:
            cursor.execute("""
                INSERT OR IGNORE INTO biblical_references 
                (reference_id, book, chapter, verse_start, verse_end, text_kjv, 
                 principle_category, application_context, related_topics, 
                 cross_references, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                ref["reference_id"], ref["book"], ref["chapter"], 
                ref["verse_start"], ref["verse_end"], ref["text_kjv"],
                ref["principle_category"], ref["application_context"], 
                ref["related_topics"], '[]', int(datetime.now().timestamp())
            ))
        
        # Add some foundational wisdom scrolls
        wisdom_scrolls = [
            {
                "scroll_id": "SOLOMON_JUDGMENT_TEMPLATE_001",
                "title": "The Wisdom of Two Mothers",
                "content": "When two parties claim the same thing, propose a solution that reveals the true heart of each claimant. The one who truly cares will choose preservation over possession.",
                "scroll_type": "JUDGMENT_TEMPLATE",
                "wisdom_category": "JUSTICE",
                "application_context": "Disputes over ownership or custody where both parties have seemingly equal claims",
                "precedent_weight": 5
            },
            {
                "scroll_id": "ECONOMIC_JUSTICE_001",
                "title": "The Principle of Gleaning",
                "content": "Leave the corners of your field unharvested, and do not gather every grape from your vineyard. These belong to the poor and the stranger. True prosperity includes provision for those in need.",
                "scroll_type": "TEACHING",
                "wisdom_category": "ECONOMICS",
                "application_context": "Economic policies and wealth distribution decisions",
                "precedent_weight": 4
            }
        ]
        
        for scroll in wisdom_scrolls:
            cursor.execute("""
                INSERT OR IGNORE INTO wisdom_scrolls 
                (scroll_id, title, content, scroll_type, wisdom_category, 
                 application_context, precedent_weight, biblical_references, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                scroll["scroll_id"], scroll["title"], scroll["content"],
                scroll["scroll_type"], scroll["wisdom_category"],
                scroll["application_context"], scroll["precedent_weight"],
                '[]', int(datetime.now().timestamp())
            ))
        
        conn.commit()
        logger.info("✅ Seeded initial wisdom data")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to seed initial wisdom data: {e}")
        return False

def run_wisdom_layer_migration():
    """Run the complete wisdom layer implementation migration."""
    logger.info("🧠 Starting Wisdom Layer Implementation Migration")
    logger.info("=" * 70)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        migration_steps = [
            ("Wisdom Scrolls", create_wisdom_scrolls_table),
            ("Judgment Records", create_judgment_records_table),
            ("Dispute Cases", create_dispute_cases_table),
            ("Biblical References", create_biblical_references_table),
            ("Wisdom Applications", create_wisdom_applications_table),
            ("Initial Wisdom Data", seed_initial_wisdom_data)
        ]
        
        success_count = 0
        
        for step_name, step_function in migration_steps:
            logger.info(f"📋 Creating {step_name}...")
            if step_function(conn):
                success_count += 1
        
        logger.info("\n📊 Wisdom Layer Migration Results:")
        logger.info("=" * 45)
        logger.info(f"✅ Components created successfully: {success_count}/{len(migration_steps)}")
        
        overall_success = success_count == len(migration_steps)
        
        if overall_success:
            logger.info("\n🎉 SUCCESS: Wisdom Layer implementation completed!")
            logger.info("   - Solomonic judgment system established")
            logger.info("   - Parable and teaching storage implemented")
            logger.info("   - Dispute resolution tracking added")
            logger.info("   - Biblical reference library created")
            logger.info("   - Wisdom application tracking enabled")
            logger.info("   - Divine governance foundation laid")
        else:
            logger.error("\n⚠️ Migration completed with issues - manual review required")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Wisdom layer migration failed: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = run_wisdom_layer_migration()
    sys.exit(0 if success else 1)
