{% extends "base.html" %}

{% block title %}System Management - ONNYX Admin{% endblock %}

{% block head %}
<style>
    .admin-warning {
        background: linear-gradient(135deg, rgba(255, 165, 0, 0.1), rgba(255, 69, 0, 0.1));
        border: 1px solid rgba(255, 165, 0, 0.3);
    }
    
    .admin-card {
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid rgba(255, 0, 0, 0.3);
        backdrop-filter: blur(10px);
    }
    
    .system-metric {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        backdrop-filter: blur(12px);
        transition: all 0.3s ease;
    }
    
    .system-metric:hover {
        border-color: var(--cyber-cyan);
        box-shadow: 0 0 20px rgba(0, 255, 247, 0.2);
    }
    
    .danger-zone {
        background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(153, 27, 27, 0.1));
        border: 1px solid rgba(220, 38, 38, 0.3);
    }
    
    .action-button {
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 247, 0.3);
    }
    
    .log-entry {
        background: rgba(0, 0, 0, 0.5);
        border-left: 3px solid var(--cyber-cyan);
        transition: all 0.3s ease;
    }
    
    .log-entry:hover {
        background: rgba(0, 255, 247, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Admin Header -->
        <div class="admin-warning p-4 rounded-xl mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl">⚙️</div>
                    <div>
                        <h1 class="text-2xl font-orbitron font-bold text-orange-400">System Management</h1>
                        <p class="text-orange-300">Monitor and control ONNYX platform systems</p>
                    </div>
                </div>
                <a href="{{ url_for('admin.dashboard') }}" class="glass-button-secondary px-4 py-2 rounded-lg">
                    ← Back to Admin Dashboard
                </a>
            </div>
        </div>

        <!-- System Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="system-metric p-6 rounded-xl text-center">
                <div class="text-3xl mb-2">🗄️</div>
                <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ system_info.database_tables }}</div>
                <div class="text-gray-400">Database Tables</div>
            </div>
            <div class="system-metric p-6 rounded-xl text-center">
                <div class="text-3xl mb-2">📊</div>
                <div class="text-2xl font-orbitron font-bold text-green-400">{{ system_info.total_records }}</div>
                <div class="text-gray-400">Total Records</div>
            </div>
            <div class="system-metric p-6 rounded-xl text-center">
                <div class="text-3xl mb-2">🔄</div>
                <div class="text-2xl font-orbitron font-bold text-yellow-400" id="uptime">{{ system_info.uptime }}</div>
                <div class="text-gray-400">System Uptime</div>
            </div>
            <div class="system-metric p-6 rounded-xl text-center">
                <div class="text-3xl mb-2">📦</div>
                <div class="text-2xl font-orbitron font-bold text-cyber-purple">{{ system_info.system_version }}</div>
                <div class="text-gray-400">Version</div>
            </div>
        </div>

        <!-- System Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Database Management -->
            <div class="admin-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">🗄️ Database Management</h3>
                <div class="space-y-4">
                    <button onclick="backupDatabase()" class="action-button w-full glass-button-primary p-4 rounded-lg text-left">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold">💾 Create Database Backup</div>
                                <div class="text-sm text-gray-400">Create a full backup of the database</div>
                            </div>
                            <div class="text-2xl">→</div>
                        </div>
                    </button>
                    
                    <button onclick="optimizeDatabase()" class="action-button w-full glass-button-secondary p-4 rounded-lg text-left">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold">⚡ Optimize Database</div>
                                <div class="text-sm text-gray-400">Run VACUUM and optimization</div>
                            </div>
                            <div class="text-2xl">→</div>
                        </div>
                    </button>
                    
                    <button onclick="viewDatabaseStats()" class="action-button w-full glass-button-secondary p-4 rounded-lg text-left">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold">📊 Database Statistics</div>
                                <div class="text-sm text-gray-400">View detailed database metrics</div>
                            </div>
                            <div class="text-2xl">→</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Mining System Control -->
            <div class="admin-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-6">⛏️ Mining System Control</h3>
                <div class="space-y-4">
                    <button onclick="checkMiningStatus()" class="action-button w-full glass-button-primary p-4 rounded-lg text-left">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold">📊 Mining Status</div>
                                <div class="text-sm text-gray-400">Check current mining operations</div>
                            </div>
                            <div class="text-2xl">→</div>
                        </div>
                    </button>
                    
                    <button onclick="resetMiningSystem()" class="action-button w-full glass-button-secondary p-4 rounded-lg text-left">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold">🔄 Reset Mining System</div>
                                <div class="text-sm text-gray-400">Restart mining processes</div>
                            </div>
                            <div class="text-2xl">→</div>
                        </div>
                    </button>
                    
                    <a href="{{ url_for('auto_mining.dashboard') }}" class="action-button block w-full glass-button-secondary p-4 rounded-lg text-left">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold">⚙️ Mining Dashboard</div>
                                <div class="text-sm text-gray-400">Access full mining controls</div>
                            </div>
                            <div class="text-2xl">→</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- System Logs -->
        <div class="admin-card p-6 rounded-xl mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">📋 System Activity Log</h3>
                <button onclick="refreshLogs()" class="glass-button-secondary px-4 py-2 rounded-lg">
                    🔄 Refresh
                </button>
            </div>
            <div id="system-logs" class="space-y-3 max-h-96 overflow-y-auto">
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyber-cyan mx-auto"></div>
                    <p class="text-gray-400 mt-4">Loading system logs...</p>
                </div>
            </div>
        </div>

        <!-- Danger Zone -->
        <div class="danger-zone p-6 rounded-xl">
            <h3 class="text-xl font-orbitron font-bold text-red-400 mb-6">⚠️ Danger Zone</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="confirmSystemReset()" class="w-full bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg transition-colors">
                    <div class="font-semibold">🔄 Reset System</div>
                    <div class="text-sm opacity-90">Reset all system data (DANGEROUS)</div>
                </button>
                
                <button onclick="confirmDatabaseWipe()" class="w-full bg-red-800 hover:bg-red-900 text-white p-4 rounded-lg transition-colors">
                    <div class="font-semibold">💥 Wipe Database</div>
                    <div class="text-sm opacity-90">Delete all data (IRREVERSIBLE)</div>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Load system logs on page load
document.addEventListener('DOMContentLoaded', function() {
    loadSystemLogs();
    updateUptime();
    setInterval(updateUptime, 60000); // Update every minute
});

async function loadSystemLogs() {
    try {
        // Simulate loading recent admin actions from deeds_ledger
        const response = await fetch('/admin/api/system/stats');
        const stats = await response.json();
        
        // For now, show some sample logs - in production this would fetch real logs
        const sampleLogs = [
            { timestamp: Date.now() - 300000, type: 'INFO', message: 'System startup completed successfully', user: 'SYSTEM' },
            { timestamp: Date.now() - 600000, type: 'ADMIN', message: 'User management accessed', user: '<EMAIL>' },
            { timestamp: Date.now() - 900000, type: 'INFO', message: 'Database optimization completed', user: 'SYSTEM' },
            { timestamp: Date.now() - 1200000, type: 'MINING', message: 'Mining system initialized', user: 'SYSTEM' },
            { timestamp: Date.now() - 1800000, type: 'ADMIN', message: 'Admin dashboard accessed', user: '<EMAIL>' }
        ];
        
        displayLogs(sampleLogs);
        
    } catch (error) {
        console.error('Error loading system logs:', error);
        document.getElementById('system-logs').innerHTML = `
            <div class="text-center py-8">
                <div class="text-red-400 text-xl mb-4">❌</div>
                <p class="text-red-300">Error loading system logs</p>
            </div>
        `;
    }
}

function displayLogs(logs) {
    const logsContainer = document.getElementById('system-logs');
    
    if (logs.length === 0) {
        logsContainer.innerHTML = '<p class="text-gray-400 text-center py-8">No recent system activity</p>';
        return;
    }
    
    logsContainer.innerHTML = logs.map(log => {
        const timeAgo = formatTimeAgo(log.timestamp);
        const typeColor = {
            'INFO': 'text-blue-400',
            'ADMIN': 'text-orange-400', 
            'MINING': 'text-green-400',
            'ERROR': 'text-red-400',
            'WARNING': 'text-yellow-400'
        }[log.type] || 'text-gray-400';
        
        return `
            <div class="log-entry p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 text-xs font-semibold rounded ${typeColor} bg-opacity-20">${log.type}</span>
                        <span class="text-white">${log.message}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-400">${log.user}</div>
                        <div class="text-xs text-gray-500">${timeAgo}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
}

function updateUptime() {
    // Simple uptime calculation - in production this would be more sophisticated
    const startTime = new Date('2025-01-01').getTime();
    const now = Date.now();
    const uptime = now - startTime;
    
    const days = Math.floor(uptime / 86400000);
    const hours = Math.floor((uptime % 86400000) / 3600000);
    
    document.getElementById('uptime').textContent = `${days}d ${hours}h`;
}

async function backupDatabase() {
    if (!confirm('Create a database backup? This may take a few moments.')) return;
    
    try {
        showNotification('Creating database backup...', 'info');
        
        // In a real implementation, this would call a backup API
        setTimeout(() => {
            showNotification('Database backup created successfully!', 'success');
            loadSystemLogs(); // Refresh logs
        }, 2000);
        
    } catch (error) {
        showNotification('Error creating backup: ' + error.message, 'error');
    }
}

async function optimizeDatabase() {
    if (!confirm('Optimize the database? This may temporarily slow down the system.')) return;
    
    try {
        showNotification('Optimizing database...', 'info');
        
        // In a real implementation, this would call an optimization API
        setTimeout(() => {
            showNotification('Database optimization completed!', 'success');
            loadSystemLogs(); // Refresh logs
        }, 3000);
        
    } catch (error) {
        showNotification('Error optimizing database: ' + error.message, 'error');
    }
}

function viewDatabaseStats() {
    // In a real implementation, this would show detailed database statistics
    alert('Database Statistics:\n\nTables: {{ system_info.database_tables }}\nTotal Records: {{ system_info.total_records }}\nSize: ~50MB\nLast Backup: {{ system_info.last_backup }}');
}

async function checkMiningStatus() {
    try {
        showNotification('Checking mining status...', 'info');
        
        // In a real implementation, this would check actual mining status
        setTimeout(() => {
            showNotification('Mining system is operational', 'success');
        }, 1000);
        
    } catch (error) {
        showNotification('Error checking mining status: ' + error.message, 'error');
    }
}

function resetMiningSystem() {
    if (!confirm('Reset the mining system? This will restart all mining processes.')) return;
    
    showNotification('Resetting mining system...', 'info');
    
    // In a real implementation, this would reset mining processes
    setTimeout(() => {
        showNotification('Mining system reset completed!', 'success');
        loadSystemLogs(); // Refresh logs
    }, 2000);
}

function confirmSystemReset() {
    if (!confirm('⚠️ WARNING: This will reset all system data!\n\nThis action cannot be undone. Are you absolutely sure?')) return;
    if (!confirm('Type "RESET" to confirm this dangerous action:') || prompt('Type "RESET":') !== 'RESET') return;
    
    showNotification('System reset initiated... This is a dangerous operation!', 'error');
    
    // In a real implementation, this would perform a system reset
    setTimeout(() => {
        alert('System reset would be performed here. This is a demo.');
    }, 2000);
}

function confirmDatabaseWipe() {
    if (!confirm('💥 EXTREME WARNING: This will permanently delete ALL data!\n\nThis action is IRREVERSIBLE. Are you absolutely sure?')) return;
    if (!confirm('Type "WIPE DATABASE" to confirm:') || prompt('Type "WIPE DATABASE":') !== 'WIPE DATABASE') return;
    
    showNotification('Database wipe would be performed here. This is a demo - no data was actually deleted.', 'error');
}

function refreshLogs() {
    loadSystemLogs();
    showNotification('System logs refreshed', 'info');
}

function showNotification(message, type) {
    if (typeof Onnyx !== 'undefined' && Onnyx.utils && Onnyx.utils.showNotification) {
        Onnyx.utils.showNotification(message, type);
    } else {
        alert(message);
    }
}
</script>
{% endblock %}
