"""
Onnyx Database-Backed Chain Parameters Module

This module provides a SQLite-backed chain parameters implementation.
"""

import os
import json
import time
import logging
from typing import Dict, Any, Optional

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.config.db_chain_parameters")

# Default chain parameters
DEFAULTS = {
    "block_reward": 10,
    "reward_token": "ONX",
    "quorum_percent": 50,
    "vote_pass_ratio": 0.6,
    "mint_cap_factor": 1.0,
    "min_etzem_score": 30,
    "validator_badges": ["VALIDATOR_ELIGIBLE_BADGE"],
    "council_badges": ["COUNCIL_ELIGIBLE_BADGE"],
    "guardian_badges": ["GUARDIAN_ELIGIBLE_BADGE"],
    "proposal_badges": ["PROPOSAL_ELIGIBLE_BADGE"],
    "validator_rotation_interval": 3600,  # 1 hour in seconds
    "scroll_voting_period": 604800,  # 7 days in seconds
    "scroll_implementation_delay": 86400,  # 1 day in seconds
    "max_token_supply": 1000000000,
    "min_stake_amount": 100,
    "stake_lock_period": 2592000,  # 30 days in seconds
    "max_mempool_size": 1000,
    "max_block_size": 1000000,
    "target_block_time": 60,  # seconds
    "difficulty_adjustment_period": 100,  # blocks
    "max_transaction_size": 100000,
    "max_transactions_per_block": 1000
}

class DBChainParameters:
    """
    DBChainParameters manages the chain parameters for the Onnyx blockchain using a SQLite database.
    """
    
    def __init__(self):
        """Initialize the DBChainParameters."""
        # Check if the chain_parameters table exists
        if not db.table_exists("chain_parameters"):
            logger.warning("Chain parameters table does not exist.")
            self._initialize_defaults()
    
    def _initialize_defaults(self):
        """Initialize the default chain parameters."""
        try:
            # Insert the default parameters
            for key, value in DEFAULTS.items():
                self.set(key, value)
            
            logger.info("Initialized default chain parameters")
        except Exception as e:
            logger.error(f"Error initializing default chain parameters: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a chain parameter.
        
        Args:
            key: The parameter key
            default: The default value to return if the key is not found
        
        Returns:
            The parameter value
        """
        try:
            # Query the database for the parameter
            param = db.query_one("SELECT value FROM chain_parameters WHERE key = ?", (key,))
            
            if param:
                # Parse the value
                return json.loads(param["value"])
            
            # If the parameter doesn't exist, return the default
            if default is not None:
                return default
            
            # If no default is provided, return the default from DEFAULTS
            return DEFAULTS.get(key)
        except Exception as e:
            logger.error(f"Error getting chain parameter: {str(e)}")
            
            # Return the default
            if default is not None:
                return default
            
            # If no default is provided, return the default from DEFAULTS
            return DEFAULTS.get(key)
    
    def set(self, key: str, value: Any):
        """
        Set a chain parameter.
        
        Args:
            key: The parameter key
            value: The parameter value
        """
        try:
            # Check if the parameter exists
            param = db.query_one("SELECT * FROM chain_parameters WHERE key = ?", (key,))
            
            if param:
                # Update the parameter
                db.update(
                    "chain_parameters",
                    {
                        "value": json.dumps(value),
                        "updated_at": int(time.time())
                    },
                    "key = ?",
                    (key,)
                )
            else:
                # Insert the parameter
                db.insert("chain_parameters", {
                    "key": key,
                    "value": json.dumps(value),
                    "updated_at": int(time.time())
                })
            
            logger.info(f"Set chain parameter {key} to {value}")
        except Exception as e:
            logger.error(f"Error setting chain parameter: {str(e)}")
            raise
    
    def update(self, params: Dict[str, Any]):
        """
        Update multiple chain parameters.
        
        Args:
            params: The parameters to update
        """
        try:
            # Update each parameter
            for key, value in params.items():
                self.set(key, value)
            
            logger.info(f"Updated chain parameters: {list(params.keys())}")
        except Exception as e:
            logger.error(f"Error updating chain parameters: {str(e)}")
            raise
    
    def reset(self, key: str):
        """
        Reset a chain parameter to its default value.
        
        Args:
            key: The parameter key
        """
        try:
            if key in DEFAULTS:
                self.set(key, DEFAULTS[key])
                logger.info(f"Reset chain parameter {key} to default value {DEFAULTS[key]}")
            else:
                logger.warning(f"No default value for chain parameter {key}")
        except Exception as e:
            logger.error(f"Error resetting chain parameter: {str(e)}")
            raise
    
    def reset_all(self):
        """Reset all chain parameters to their default values."""
        try:
            # Delete all parameters
            db.execute("DELETE FROM chain_parameters")
            
            # Insert the default parameters
            self._initialize_defaults()
            
            logger.info("Reset all chain parameters to default values")
        except Exception as e:
            logger.error(f"Error resetting all chain parameters: {str(e)}")
            raise
    
    def all(self) -> Dict[str, Any]:
        """
        Get all chain parameters.
        
        Returns:
            All chain parameters
        """
        try:
            # Query the database for all parameters
            params = db.query("SELECT key, value FROM chain_parameters")
            
            # Parse the values
            return {param["key"]: json.loads(param["value"]) for param in params}
        except Exception as e:
            logger.error(f"Error getting all chain parameters: {str(e)}")
            return DEFAULTS.copy()

# Create a global instance of the DBChainParameters
db_chain_parameters = DBChainParameters()
