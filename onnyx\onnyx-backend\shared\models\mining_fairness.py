"""
Mining Fairness & Balance Audit System
Implements biblical "last shall be first" principles and anti-gaming mechanisms.

This module provides:
1. Fairness checks for performance-based multipliers
2. "Last shall be first" balance logic
3. Humility weights and anti-gaming mechanisms
4. Mining reward distribution auditing
5. Community mining equality measures

Author: ONNYX Development Team
Date: 2025-07-17
"""

import sqlite3
import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta, timezone

from shared.config.onnyx_config import onnyx_config
from shared.config.chain_parameters import chain_parameters

logger = logging.getLogger(__name__)

class FairnessViolationType(Enum):
    EXCESSIVE_CONCENTRATION = "EXCESSIVE_CONCENTRATION"
    GAMING_DETECTED = "GAMING_DETECTED"
    HUMILITY_VIOLATION = "HUMILITY_VIOLATION"
    TRIBAL_IMBALANCE = "TRIBAL_IMBALANCE"
    COMMUNITY_NEGLECT = "COMMUNITY_NEGLECT"

@dataclass
class FairnessMetrics:
    identity_id: str
    mining_power_score: float
    humility_weight: float
    community_contribution: float
    tribal_balance_factor: float
    last_shall_be_first_bonus: float
    gaming_penalty: float
    overall_fairness_score: float
    violations: List[FairnessViolationType]

class MiningFairnessAuditor:
    """
    Comprehensive mining fairness auditor implementing biblical principles.
    Ensures "the last shall be first" and prevents gaming of the system.
    """
    
    def __init__(self):
        self.db_path = onnyx_config.db_path
        
        # Biblical fairness parameters
        self.MAX_CONCENTRATION_RATIO = 0.1  # No single miner should have >10% of network power
        self.HUMILITY_BONUS_CAP = 0.5  # Max 50% bonus for humility
        self.COMMUNITY_CONTRIBUTION_WEIGHT = 0.3  # 30% weight for community contributions
        self.TRIBAL_BALANCE_WEIGHT = 0.2  # 20% weight for tribal balance
        self.LAST_FIRST_MULTIPLIER = 2.0  # 2x bonus for "last shall be first"
        
        # Gaming detection thresholds
        self.GAMING_DETECTION_WINDOW = 86400 * 7  # 7 days
        self.SUSPICIOUS_PATTERN_THRESHOLD = 0.8  # 80% correlation indicates gaming
        
    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def audit_mining_fairness(self, identity_id: str) -> FairnessMetrics:
        """
        Comprehensive fairness audit for a miner.
        
        Args:
            identity_id: Identity ID of the miner
            
        Returns:
            FairnessMetrics with complete fairness assessment
        """
        try:
            conn = self._get_connection()
            
            # Calculate base mining power
            mining_power = self._calculate_mining_power(conn, identity_id)
            
            # Calculate humility weight
            humility_weight = self._calculate_humility_weight(conn, identity_id)
            
            # Calculate community contribution score
            community_contribution = self._calculate_community_contribution(conn, identity_id)
            
            # Calculate tribal balance factor
            tribal_balance = self._calculate_tribal_balance_factor(conn, identity_id)
            
            # Calculate "last shall be first" bonus
            last_first_bonus = self._calculate_last_first_bonus(conn, identity_id)
            
            # Detect gaming patterns
            gaming_penalty = self._detect_gaming_patterns(conn, identity_id)
            
            # Check for violations
            violations = self._check_fairness_violations(conn, identity_id, mining_power)
            
            # Calculate overall fairness score
            overall_score = self._calculate_overall_fairness_score(
                mining_power, humility_weight, community_contribution,
                tribal_balance, last_first_bonus, gaming_penalty
            )
            
            conn.close()
            
            return FairnessMetrics(
                identity_id=identity_id,
                mining_power_score=mining_power,
                humility_weight=humility_weight,
                community_contribution=community_contribution,
                tribal_balance_factor=tribal_balance,
                last_shall_be_first_bonus=last_first_bonus,
                gaming_penalty=gaming_penalty,
                overall_fairness_score=overall_score,
                violations=violations
            )
            
        except Exception as e:
            logger.error(f"Error auditing mining fairness for {identity_id}: {e}")
            return self._create_default_metrics(identity_id)
    
    def _calculate_mining_power(self, conn: sqlite3.Connection, identity_id: str) -> float:
        """Calculate normalized mining power score (0.0 to 1.0)."""
        try:
            # Get recent mining activity (last 30 days)
            thirty_days_ago = int(time.time()) - (30 * 86400)
            
            miner_blocks = conn.execute("""
                SELECT COUNT(*) as block_count,
                       SUM(CAST(JSON_EXTRACT(metadata, '$.reward_amount') AS REAL)) as total_rewards
                FROM blocks 
                WHERE proposer_id = ? AND timestamp > ?
            """, (identity_id, thirty_days_ago)).fetchone()
            
            # Get network totals for normalization
            network_totals = conn.execute("""
                SELECT COUNT(*) as total_blocks,
                       COUNT(DISTINCT proposer_id) as unique_miners
                FROM blocks 
                WHERE timestamp > ?
            """, (thirty_days_ago,)).fetchone()
            
            if not network_totals or network_totals['total_blocks'] == 0:
                return 0.0
            
            # Calculate relative mining power
            miner_share = miner_blocks['block_count'] / network_totals['total_blocks']
            
            # Normalize to 0-1 scale
            return min(1.0, miner_share * network_totals['unique_miners'])
            
        except Exception as e:
            logger.error(f"Error calculating mining power: {e}")
            return 0.0
    
    def _calculate_humility_weight(self, conn: sqlite3.Connection, identity_id: str) -> float:
        """Calculate humility weight based on biblical principles."""
        try:
            # Check for humility indicators
            humility_score = 0.0
            
            # 1. Gleaning pool contributions (giving to the poor)
            gleaning_contributions = conn.execute("""
                SELECT SUM(deed_value) as total_gleaning
                FROM deeds_ledger 
                WHERE identity_id = ? 
                AND deed_type = 'GLEANING_CONTRIBUTION'
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if gleaning_contributions and gleaning_contributions['total_gleaning']:
                humility_score += min(0.3, gleaning_contributions['total_gleaning'] / 1000)
            
            # 2. Sabbath observance (spiritual discipline)
            sabbath_observance = conn.execute("""
                SELECT COUNT(*) as observance_count
                FROM deeds_ledger 
                WHERE identity_id = ? 
                AND deed_type = 'SABBATH_OBSERVANCE'
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if sabbath_observance:
                # Bonus for consistent Sabbath observance
                humility_score += min(0.2, sabbath_observance['observance_count'] / 20)
            
            # 3. Community service deeds
            community_service = conn.execute("""
                SELECT COUNT(*) as service_count
                FROM deeds_ledger 
                WHERE identity_id = ? 
                AND deed_type IN ('COMMUNITY_SERVICE', 'HELPING_NEIGHBOR', 'TEACHING')
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if community_service:
                humility_score += min(0.3, community_service['service_count'] / 10)
            
            # 4. Penalty for boastful behavior (negative deeds)
            boastful_behavior = conn.execute("""
                SELECT COUNT(*) as violation_count
                FROM deeds_ledger 
                WHERE identity_id = ? 
                AND deed_type IN ('BOASTING', 'PRIDE', 'SELFISHNESS')
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if boastful_behavior and boastful_behavior['violation_count'] > 0:
                humility_score -= boastful_behavior['violation_count'] * 0.1
            
            # Ensure score is between 0 and max cap
            return max(0.0, min(self.HUMILITY_BONUS_CAP, humility_score))
            
        except Exception as e:
            logger.error(f"Error calculating humility weight: {e}")
            return 0.0
    
    def _calculate_community_contribution(self, conn: sqlite3.Connection, identity_id: str) -> float:
        """Calculate community contribution score."""
        try:
            contribution_score = 0.0
            
            # 1. Labor system participation
            labor_participation = conn.execute("""
                SELECT COUNT(*) as labor_count,
                       AVG(CAST(JSON_EXTRACT(metadata, '$.quality_score') AS REAL)) as avg_quality
                FROM labor_records 
                WHERE worker_id = ? 
                AND verification_status = 'verified'
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if labor_participation and labor_participation['labor_count']:
                contribution_score += min(0.3, labor_participation['labor_count'] / 20)
                if labor_participation['avg_quality']:
                    contribution_score += min(0.2, labor_participation['avg_quality'] / 5)
            
            # 2. Sela business operations (job creation)
            sela_operations = conn.execute("""
                SELECT COUNT(*) as sela_count
                FROM selas 
                WHERE owner_id = ? 
                AND status = 'active'
            """, (identity_id,)).fetchone()
            
            if sela_operations and sela_operations['sela_count']:
                contribution_score += min(0.3, sela_operations['sela_count'] / 3)
            
            # 3. Voice Scroll participation (governance)
            governance_participation = conn.execute("""
                SELECT COUNT(*) as vote_count
                FROM votes 
                WHERE identity_id = ? 
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if governance_participation and governance_participation['vote_count']:
                contribution_score += min(0.2, governance_participation['vote_count'] / 10)
            
            return min(1.0, contribution_score)
            
        except Exception as e:
            logger.error(f"Error calculating community contribution: {e}")
            return 0.0
    
    def _calculate_tribal_balance_factor(self, conn: sqlite3.Connection, identity_id: str) -> float:
        """Calculate tribal balance factor to encourage diversity."""
        try:
            # Get miner's tribe
            miner_tribe = conn.execute("""
                SELECT nation_code FROM identities WHERE identity_id = ?
            """, (identity_id,)).fetchone()
            
            if not miner_tribe:
                return 0.0
            
            tribe_code = miner_tribe['nation_code']
            
            # Get recent mining distribution by tribe (last 100 blocks)
            tribal_distribution = conn.execute("""
                SELECT i.nation_code, COUNT(*) as block_count
                FROM blocks b
                JOIN identities i ON b.proposer_id = i.identity_id
                WHERE b.timestamp > ?
                GROUP BY i.nation_code
                ORDER BY block_count DESC
            """, (time.time() - (7 * 86400),)).fetchall()  # Last 7 days
            
            if not tribal_distribution:
                return 1.0  # No recent blocks, give full bonus
            
            total_blocks = sum([t['block_count'] for t in tribal_distribution])
            tribe_blocks = next((t['block_count'] for t in tribal_distribution if t['nation_code'] == tribe_code), 0)
            
            # Calculate tribal representation ratio
            tribe_ratio = tribe_blocks / total_blocks if total_blocks > 0 else 0
            ideal_ratio = 1.0 / 12  # Ideal is 1/12 for each tribe
            
            # Bonus for underrepresented tribes (last shall be first)
            if tribe_ratio < ideal_ratio:
                return 1.0 + (ideal_ratio - tribe_ratio) * 2  # Up to 2x bonus
            else:
                return max(0.5, 1.0 - (tribe_ratio - ideal_ratio))  # Penalty for overrepresentation
            
        except Exception as e:
            logger.error(f"Error calculating tribal balance factor: {e}")
            return 1.0
    
    def _calculate_last_first_bonus(self, conn: sqlite3.Connection, identity_id: str) -> float:
        """Calculate 'last shall be first' bonus for disadvantaged miners."""
        try:
            bonus = 0.0
            
            # 1. Low total balance bonus (help the poor)
            total_balance = conn.execute("""
                SELECT COALESCE(SUM(balance), 0) as total
                FROM token_balances 
                WHERE identity_id = ?
            """, (identity_id,)).fetchone()
            
            balance = total_balance['total'] if total_balance else 0
            
            # Inverse relationship: lower balance = higher bonus
            if balance < 1000:  # Very low balance
                bonus += 0.5
            elif balance < 10000:  # Low balance
                bonus += 0.3
            elif balance < 100000:  # Medium balance
                bonus += 0.1
            
            # 2. New miner bonus (help newcomers)
            first_block = conn.execute("""
                SELECT MIN(timestamp) as first_mining
                FROM blocks 
                WHERE proposer_id = ?
            """, (identity_id,)).fetchone()
            
            if first_block and first_block['first_mining']:
                days_mining = (time.time() - first_block['first_mining']) / 86400
                if days_mining < 30:  # New miner (less than 30 days)
                    bonus += 0.3 * (30 - days_mining) / 30
            
            # 3. Low Etzem score bonus (help those with low reputation)
            etzem_score = conn.execute("""
                SELECT etzem_score FROM identities WHERE identity_id = ?
            """, (identity_id,)).fetchone()
            
            if etzem_score and etzem_score['etzem_score'] < 100:
                bonus += 0.2 * (100 - etzem_score['etzem_score']) / 100
            
            return min(1.0, bonus)  # Cap at 100% bonus
            
        except Exception as e:
            logger.error(f"Error calculating last first bonus: {e}")
            return 0.0
    
    def _detect_gaming_patterns(self, conn: sqlite3.Connection, identity_id: str) -> float:
        """Detect gaming patterns and apply penalties."""
        try:
            penalty = 0.0
            
            # 1. Detect coordinated mining (suspicious timing patterns)
            recent_blocks = conn.execute("""
                SELECT timestamp, block_height
                FROM blocks 
                WHERE proposer_id = ? 
                AND timestamp > ?
                ORDER BY timestamp
            """, (identity_id, time.time() - self.GAMING_DETECTION_WINDOW)).fetchall()
            
            if len(recent_blocks) > 10:
                # Check for suspiciously regular intervals
                intervals = []
                for i in range(1, len(recent_blocks)):
                    intervals.append(recent_blocks[i]['timestamp'] - recent_blocks[i-1]['timestamp'])
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    variance = sum([(x - avg_interval) ** 2 for x in intervals]) / len(intervals)
                    
                    # Low variance indicates artificial regularity
                    if variance < 100:  # Very regular timing
                        penalty += 0.3
            
            # 2. Detect Sybil attacks (multiple identities from same source)
            # This would require more sophisticated analysis in production
            
            # 3. Detect wash trading or artificial deed inflation
            deed_pattern = conn.execute("""
                SELECT deed_type, COUNT(*) as count, 
                       AVG(deed_value) as avg_value,
                       MAX(deed_value) as max_value
                FROM deeds_ledger 
                WHERE identity_id = ? 
                AND timestamp > ?
                GROUP BY deed_type
            """, (identity_id, time.time() - self.GAMING_DETECTION_WINDOW)).fetchall()
            
            for deed in deed_pattern:
                # Suspicious if too many identical deeds with same value
                if deed['count'] > 50 and deed['avg_value'] == deed['max_value']:
                    penalty += 0.2
            
            return min(1.0, penalty)  # Cap penalty at 100%
            
        except Exception as e:
            logger.error(f"Error detecting gaming patterns: {e}")
            return 0.0
    
    def _check_fairness_violations(self, conn: sqlite3.Connection, identity_id: str, mining_power: float) -> List[FairnessViolationType]:
        """Check for various fairness violations."""
        violations = []
        
        try:
            # 1. Excessive concentration
            if mining_power > self.MAX_CONCENTRATION_RATIO:
                violations.append(FairnessViolationType.EXCESSIVE_CONCENTRATION)
            
            # 2. Gaming detection
            gaming_penalty = self._detect_gaming_patterns(conn, identity_id)
            if gaming_penalty > 0.5:
                violations.append(FairnessViolationType.GAMING_DETECTED)
            
            # 3. Humility violations (excessive pride/boasting)
            pride_deeds = conn.execute("""
                SELECT COUNT(*) as count
                FROM deeds_ledger 
                WHERE identity_id = ? 
                AND deed_type IN ('BOASTING', 'PRIDE', 'SELFISHNESS')
                AND timestamp > ?
            """, (identity_id, time.time() - (30 * 86400))).fetchone()
            
            if pride_deeds and pride_deeds['count'] > 5:
                violations.append(FairnessViolationType.HUMILITY_VIOLATION)
            
            # 4. Community neglect
            community_score = self._calculate_community_contribution(conn, identity_id)
            if community_score < 0.1 and mining_power > 0.05:  # High mining power but low community contribution
                violations.append(FairnessViolationType.COMMUNITY_NEGLECT)
            
            return violations
            
        except Exception as e:
            logger.error(f"Error checking fairness violations: {e}")
            return violations
    
    def _calculate_overall_fairness_score(self, mining_power: float, humility_weight: float,
                                        community_contribution: float, tribal_balance: float,
                                        last_first_bonus: float, gaming_penalty: float) -> float:
        """Calculate overall fairness score."""
        try:
            # Base score from mining power (normalized)
            base_score = mining_power
            
            # Apply positive modifiers
            base_score *= (1.0 + humility_weight)
            base_score *= (1.0 + community_contribution * self.COMMUNITY_CONTRIBUTION_WEIGHT)
            base_score *= tribal_balance
            base_score *= (1.0 + last_first_bonus)
            
            # Apply penalties
            base_score *= (1.0 - gaming_penalty)
            
            # Ensure score is between 0 and reasonable maximum
            return max(0.0, min(5.0, base_score))
            
        except Exception as e:
            logger.error(f"Error calculating overall fairness score: {e}")
            return 0.0
    
    def _create_default_metrics(self, identity_id: str) -> FairnessMetrics:
        """Create default metrics for error cases."""
        return FairnessMetrics(
            identity_id=identity_id,
            mining_power_score=0.0,
            humility_weight=0.0,
            community_contribution=0.0,
            tribal_balance_factor=1.0,
            last_shall_be_first_bonus=0.0,
            gaming_penalty=0.0,
            overall_fairness_score=0.0,
            violations=[]
        )
    
    def get_network_fairness_report(self) -> Dict[str, Any]:
        """Generate comprehensive network fairness report."""
        try:
            conn = self._get_connection()
            
            # Get all active miners
            active_miners = conn.execute("""
                SELECT DISTINCT proposer_id
                FROM blocks 
                WHERE timestamp > ?
            """, (time.time() - (30 * 86400),)).fetchall()
            
            fairness_metrics = []
            total_violations = 0
            
            for miner in active_miners:
                metrics = self.audit_mining_fairness(miner['proposer_id'])
                fairness_metrics.append(metrics)
                total_violations += len(metrics.violations)
            
            # Calculate network-wide statistics
            if fairness_metrics:
                avg_fairness = sum([m.overall_fairness_score for m in fairness_metrics]) / len(fairness_metrics)
                max_concentration = max([m.mining_power_score for m in fairness_metrics])
                avg_humility = sum([m.humility_weight for m in fairness_metrics]) / len(fairness_metrics)
            else:
                avg_fairness = max_concentration = avg_humility = 0.0
            
            conn.close()
            
            return {
                'network_fairness_score': avg_fairness,
                'max_concentration_ratio': max_concentration,
                'average_humility_weight': avg_humility,
                'total_violations': total_violations,
                'active_miners': len(fairness_metrics),
                'fairness_metrics': [
                    {
                        'identity_id': m.identity_id,
                        'overall_score': m.overall_fairness_score,
                        'violations': [v.value for v in m.violations]
                    } for m in fairness_metrics
                ],
                'recommendations': self._generate_fairness_recommendations(fairness_metrics)
            }
            
        except Exception as e:
            logger.error(f"Error generating network fairness report: {e}")
            return {'error': str(e)}
    
    def _generate_fairness_recommendations(self, metrics: List[FairnessMetrics]) -> List[str]:
        """Generate recommendations for improving network fairness."""
        recommendations = []
        
        # Check for concentration issues
        high_concentration = [m for m in metrics if m.mining_power_score > self.MAX_CONCENTRATION_RATIO]
        if high_concentration:
            recommendations.append(f"⚠️ {len(high_concentration)} miners have excessive concentration. Consider implementing stronger concentration penalties.")
        
        # Check for low community participation
        low_community = [m for m in metrics if m.community_contribution < 0.2]
        if len(low_community) > len(metrics) * 0.5:
            recommendations.append("📈 Over 50% of miners have low community contribution. Consider incentivizing community participation.")
        
        # Check for gaming patterns
        gaming_detected = [m for m in metrics if FairnessViolationType.GAMING_DETECTED in m.violations]
        if gaming_detected:
            recommendations.append(f"🚨 Gaming patterns detected in {len(gaming_detected)} miners. Implement stronger anti-gaming measures.")
        
        # Check for tribal imbalance
        tribal_imbalance = [m for m in metrics if m.tribal_balance_factor < 0.8]
        if len(tribal_imbalance) > 3:
            recommendations.append("⚖️ Tribal imbalance detected. Consider stronger tribal rotation incentives.")
        
        if not recommendations:
            recommendations.append("✅ Network fairness appears healthy. Continue monitoring.")
        
        return recommendations

# Global instance
mining_fairness_auditor = MiningFairnessAuditor()
