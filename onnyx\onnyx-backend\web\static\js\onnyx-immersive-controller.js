/**
 * ONNYX Immersive Eden Mode Controller
 * Manages the complete spiritual awakening journey through identity onboarding
 */

class OnnyxImmersiveController {
    constructor() {
        this.currentStep = 1;
        this.maxSteps = 5;
        this.sessionData = {};
        this.audioEnabled = false;
        this.animationQueue = [];

        this.init();
    }

    init() {
        console.log('🌟 ONNYX Immersive Controller Initialized');

        this.detectCurrentStep();
        this.loadSessionData();
        this.initializeAudio();
        this.setupGlobalEventListeners();
        this.initializeAnimationSystem();

        // Initialize step-specific functionality
        this.initializeCurrentStep();
    }

    // ===== STEP DETECTION AND ROUTING =====

    detectCurrentStep() {
        const path = window.location.pathname;

        if (path.includes('step1')) this.currentStep = 1;
        else if (path.includes('step2')) this.currentStep = 2;
        else if (path.includes('step3')) this.currentStep = 3;
        else if (path.includes('step4')) this.currentStep = 4;
        else if (path.includes('step5')) this.currentStep = 5;

        console.log(`📍 Current Step: ${this.currentStep}`);
    }

    initializeCurrentStep() {
        switch(this.currentStep) {
            case 1:
                this.initializeStep1();
                break;
            case 2:
                this.initializeStep2();
                break;
            case 3:
                this.initializeStep3();
                break;
            case 4:
                this.initializeStep4();
                break;
            case 5:
                this.initializeStep5();
                break;
        }
    }

    // ===== SESSION DATA MANAGEMENT =====

    loadSessionData() {
        const keys = [
            'edenMode_selectedNation',
            'edenMode_selectedTribe',
            'edenMode_covenantAccepted',
            'edenMode_fullName',
            'edenMode_email',
            'edenMode_laborCategory',
            'edenMode_selaChoice',
            'edenMode_businessType'
        ];

        keys.forEach(key => {
            const value = sessionStorage.getItem(key);
            if (value) {
                this.sessionData[key.replace('edenMode_', '')] = value;
            }
        });

        console.log('📊 Session Data Loaded:', this.sessionData);
    }

    saveSessionData(key, value) {
        this.sessionData[key] = value;
        sessionStorage.setItem(`edenMode_${key}`, value);
        console.log(`💾 Saved: ${key} = ${value}`);
    }

    // ===== AUDIO SYSTEM =====

    initializeAudio() {
        // Ambient audio setup (optional)
        this.audioContext = null;
        this.ambientSounds = {
            step1: null, // Mystical awakening sounds
            step2: null, // Tribal/ancient sounds
            step3: null, // Covenant/sacred sounds
            step4: null, // Business/prosperity sounds
            step5: null  // Blockchain/digital sounds
        };

        this.setupAudioControls();
    }

    setupAudioControls() {
        const audioToggle = document.getElementById('audioToggle');
        if (audioToggle) {
            audioToggle.addEventListener('click', () => {
                this.toggleAudio();
            });
        }
    }

    toggleAudio() {
        this.audioEnabled = !this.audioEnabled;
        console.log(`🔊 Audio ${this.audioEnabled ? 'enabled' : 'disabled'}`);

        if (this.audioEnabled) {
            this.playAmbientSound();
        } else {
            this.stopAmbientSound();
        }
    }

    playAmbientSound() {
        // Implementation for ambient audio
        // This would load and play appropriate sounds for each step
        console.log(`🎵 Playing ambient sound for step ${this.currentStep}`);
    }

    stopAmbientSound() {
        // Stop all ambient sounds
        console.log('🔇 Stopping ambient sounds');
    }

    // ===== ANIMATION SYSTEM =====

    initializeAnimationSystem() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupParticleEffects();
    }

    setupIntersectionObserver() {
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerElementAnimation(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        // Observe all animatable elements
        document.querySelectorAll('.fade-in-sequence, .fade-in-up, .slide-in-left, .slide-in-right')
                .forEach(el => this.observer.observe(el));
    }

    triggerElementAnimation(element) {
        // Skip roadmap elements to prevent positioning conflicts
        if (element.closest('[class*="roadmap"]') || element.classList.toString().includes('roadmap')) {
            return;
        }

        const delay = parseInt(element.dataset.delay) || 0;

        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0) translateX(0) scale(1)';
            element.classList.add('animated');

            // Trigger any custom animations
            this.triggerCustomAnimation(element);
        }, delay);
    }

    triggerCustomAnimation(element) {
        // Custom animation triggers based on element classes
        if (element.classList.contains('glow-on-reveal')) {
            element.style.boxShadow = '0 0 30px rgba(0, 255, 247, 0.4)';
        }

        if (element.classList.contains('count-up')) {
            this.animateCountUp(element);
        }
    }

    animateCountUp(element) {
        const target = parseInt(element.dataset.target) || 100;
        const duration = parseInt(element.dataset.duration) || 2000;
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            element.textContent = Math.floor(current);

            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    }

    setupScrollAnimations() {
        // Parallax and scroll-based effects
        let ticking = false;

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateScrollAnimations();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }

    updateScrollAnimations() {
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;

        // Update particle positions based on scroll
        document.querySelectorAll('.floating-particles, .tribal-patterns, .covenant-energy')
                .forEach(element => {
                    const speed = parseFloat(element.dataset.speed) || 0.5;
                    element.style.transform = `translateY(${scrollY * speed}px)`;
                });
    }

    setupParticleEffects() {
        // Create dynamic particle systems for each step
        this.createParticleSystem();
    }

    createParticleSystem() {
        const particleContainer = document.createElement('div');
        particleContainer.className = 'particle-system';
        particleContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        `;

        document.body.appendChild(particleContainer);

        // Create particles based on current step
        this.generateParticles(particleContainer);
    }

    generateParticles(container) {
        const particleCount = 20;
        const colors = ['#00fff7', '#9a00ff', '#00ff88'];

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'floating-particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: ${colors[Math.floor(Math.random() * colors.length)]};
                border-radius: 50%;
                opacity: 0.6;
                animation: floatParticle ${5 + Math.random() * 10}s linear infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 5}s;
            `;

            container.appendChild(particle);
        }
    }

    // ===== STEP-SPECIFIC INITIALIZERS =====

    initializeStep1() {
        console.log('🌅 Initializing Step 1: The Forgotten Legacy');

        // Setup step 1 specific functionality
        this.setupStep1Interactions();
        this.playStepTransition(1);
    }

    initializeStep2() {
        console.log('🏛️ Initializing Step 2: Nations Hidden in Plain Sight');

        this.setupStep2Interactions();
        this.loadNationData();
        this.playStepTransition(2);
    }

    initializeStep3() {
        console.log('⚖️ Initializing Step 3: The Covenant Reawakens');

        this.setupStep3Interactions();
        this.displayPreviousSelections();
        this.playStepTransition(3);
    }

    initializeStep4() {
        console.log('🏢 Initializing Step 4: Eden Mode Activation');

        this.setupStep4Interactions();
        this.playStepTransition(4);
    }

    initializeStep5() {
        console.log('⛓️ Initializing Step 5: Blockchain Inscription');

        this.setupStep5Interactions();
        this.prepareBlockchainInscription();
        this.playStepTransition(5);
    }

    // ===== STEP INTERACTIONS =====

    setupStep1Interactions() {
        // Step 1 specific interactions
        const beginButton = document.getElementById('beginJourney');
        if (beginButton) {
            beginButton.addEventListener('click', () => {
                this.transitionToStep(2);
            });
        }
    }

    setupStep2Interactions() {
        // Nation selection logic handled by step-specific scripts
        // This provides additional coordination
    }

    setupStep3Interactions() {
        // Labor selection and form validation
        // Coordinated with step-specific scripts
    }

    setupStep4Interactions() {
        // Sela creation or joining logic
    }

    setupStep5Interactions() {
        // Final blockchain inscription
    }

    // ===== NAVIGATION AND TRANSITIONS =====

    transitionToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > this.maxSteps) {
            console.error('Invalid step number:', stepNumber);
            return;
        }

        console.log(`🚀 Transitioning to step ${stepNumber}`);

        // Add transition effects
        this.addTransitionEffects();

        // Navigate after transition
        setTimeout(() => {
            window.location.href = `/auth/eden-mode/step${stepNumber}`;
        }, 1000);
    }

    addTransitionEffects() {
        // Create transition overlay
        const overlay = document.createElement('div');
        overlay.className = 'step-transition-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(0, 255, 247, 0.1), rgba(154, 0, 255, 0.1));
            backdrop-filter: blur(10px);
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.5s ease;
        `;

        document.body.appendChild(overlay);

        // Fade in overlay
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 50);

        // Remove overlay after transition
        setTimeout(() => {
            overlay.remove();
        }, 1500);
    }

    playStepTransition(stepNumber) {
        // Play step-specific transition animations
        console.log(`🎬 Playing transition for step ${stepNumber}`);

        // Add step-specific visual effects
        this.addStepSpecificEffects(stepNumber);
    }

    addStepSpecificEffects(stepNumber) {
        const effects = {
            1: () => this.addAwakeningEffects(),
            2: () => this.addTribalEffects(),
            3: () => this.addCovenantEffects(),
            4: () => this.addActivationEffects(),
            5: () => this.addBlockchainEffects()
        };

        if (effects[stepNumber]) {
            effects[stepNumber]();
        }
    }

    addAwakeningEffects() {
        // Mystical awakening visual effects
        console.log('✨ Adding awakening effects');
    }

    addTribalEffects() {
        // Tribal/ancient visual effects
        console.log('🏛️ Adding tribal effects');
    }

    addCovenantEffects() {
        // Sacred covenant visual effects
        console.log('📿 Adding covenant effects');
    }

    addActivationEffects() {
        // Eden mode activation effects
        console.log('🌟 Adding activation effects');
    }

    addBlockchainEffects() {
        // Blockchain inscription effects
        console.log('⛓️ Adding blockchain effects');
    }

    // ===== UTILITY METHODS =====

    loadNationData() {
        // Load biblical nations data if needed
        console.log('📊 Loading nation data');
    }

    displayPreviousSelections() {
        // Display user's previous selections
        console.log('📋 Displaying previous selections:', this.sessionData);
    }

    prepareBlockchainInscription() {
        // Prepare final blockchain inscription
        console.log('⛓️ Preparing blockchain inscription');
    }

    // ===== GLOBAL EVENT LISTENERS =====

    setupGlobalEventListeners() {
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.handleEscape();
            }
        });

        // Window resize handling
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    handleEscape() {
        // Handle escape key press
        console.log('⌨️ Escape pressed');
    }

    handleResize() {
        // Handle window resize
        console.log('📐 Window resized');
    }

    handleVisibilityChange() {
        // Handle tab visibility change
        if (document.hidden && this.audioEnabled) {
            this.stopAmbientSound();
        } else if (!document.hidden && this.audioEnabled) {
            this.playAmbientSound();
        }
    }

    // ===== CLEANUP =====

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }

        this.stopAmbientSound();

        // Remove particle systems
        document.querySelectorAll('.particle-system').forEach(el => el.remove());

        console.log('🧹 ONNYX Immersive Controller destroyed');
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.onnyxImmersive = new OnnyxImmersiveController();
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes floatParticle {
        0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
        10% { opacity: 0.6; }
        90% { opacity: 0.6; }
        100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
    }

    .animated {
        animation-fill-mode: both;
    }

    .step-transition-overlay {
        pointer-events: none;
    }
`;
document.head.appendChild(style);
