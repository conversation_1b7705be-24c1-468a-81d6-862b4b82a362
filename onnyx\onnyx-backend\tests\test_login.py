#!/usr/bin/env python3
"""
Test login script to simulate logging in as test user
"""

import requests
import json

def test_login():
    """Test login with test user"""
    try:
        # Test the login endpoint
        login_url = "http://127.0.0.1:5000/auth/login"
        
        # First, get the login page to see if it works
        response = requests.get(login_url)
        print(f"Login page status: {response.status_code}")
        
        # Try to login with test credentials
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpassword'  # Any password should work for test
        }
        
        session = requests.Session()
        response = session.post(login_url, data=login_data)
        print(f"Login attempt status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            
            # Try to access dashboard
            dashboard_url = "http://127.0.0.1:5000/dashboard/"
            dashboard_response = session.get(dashboard_url)
            print(f"Dashboard access status: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                print("✅ Dashboard accessible!")
            else:
                print(f"❌ Dashboard not accessible: {dashboard_response.status_code}")
                print(dashboard_response.text[:500])
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(response.text[:500])
            
    except Exception as e:
        print(f"❌ Error testing login: {e}")

if __name__ == "__main__":
    test_login()
