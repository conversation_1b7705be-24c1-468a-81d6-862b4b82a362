#!/usr/bin/env python3
"""
Fix missing Biblical Tokenomics columns and tables
"""

import os
import sys
import sqlite3

def fix_database():
    """Add missing columns and tables for Biblical Tokenomics."""
    
    db_path = 'shared/db/db/onnyx.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Adding missing Biblical Tokenomics columns...")
        
        # Get current columns
        cursor.execute("PRAGMA table_info(identities)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        
        # Define missing tokenomics columns
        tokenomics_columns = [
            ("sabbath_observer", "BOOLEAN DEFAULT 0"),
            ("last_active_timestamp", "INTEGER DEFAULT 0"),
            ("last_transaction_height", "INTEGER DEFAULT 0"),
            ("deeds_score", "REAL DEFAULT 0.0")
        ]
        
        added_count = 0
        for col_name, col_def in tokenomics_columns:
            if col_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE identities ADD COLUMN {col_name} {col_def}")
                    print(f"✅ Added column: {col_name}")
                    added_count += 1
                except sqlite3.OperationalError as e:
                    if 'duplicate column name' not in str(e):
                        print(f"⚠️ Could not add {col_name}: {e}")
                    else:
                        print(f"ℹ️ Column {col_name} already exists")
            else:
                print(f"ℹ️ Column {col_name} already exists")
        
        print("\n🔧 Creating missing Biblical Tokenomics tables...")
        
        # Create etzem_history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS etzem_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                old_score INTEGER NOT NULL,
                new_score INTEGER NOT NULL,
                change_reason TEXT NOT NULL,
                change_amount INTEGER NOT NULL,
                timestamp INTEGER NOT NULL
            )
        ''')
        print("✅ Created/verified etzem_history table")
        
        # Create mining_rewards table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mining_rewards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                block_height INTEGER NOT NULL,
                base_reward REAL NOT NULL,
                tier_multiplier REAL NOT NULL,
                final_reward REAL NOT NULL,
                sabbath_compliant BOOLEAN NOT NULL,
                timestamp INTEGER NOT NULL
            )
        ''')
        print("✅ Created/verified mining_rewards table")
        
        # Create gleaning_pool table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS gleaning_pool (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contributor_id TEXT NOT NULL,
                amount REAL NOT NULL,
                contribution_type TEXT NOT NULL,
                timestamp INTEGER NOT NULL,
                block_height INTEGER
            )
        ''')
        print("✅ Created/verified gleaning_pool table")
        
        # Create sabbath_enforcement table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sabbath_enforcement (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                sabbath_start INTEGER NOT NULL,
                sabbath_end INTEGER NOT NULL,
                violations INTEGER DEFAULT 0,
                compliance_score REAL DEFAULT 1.0
            )
        ''')
        print("✅ Created/verified sabbath_enforcement table")
        
        # Create mining_tier_history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mining_tier_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                old_tier INTEGER NOT NULL,
                new_tier INTEGER NOT NULL,
                change_reason TEXT NOT NULL,
                timestamp INTEGER NOT NULL
            )
        ''')
        print("✅ Created/verified mining_tier_history table")
        
        conn.commit()
        
        if added_count > 0:
            print(f"\n🎉 Successfully added {added_count} columns")
        else:
            print("\nℹ️ All required columns already exist")
        
        # Verify final state
        cursor.execute("PRAGMA table_info(identities)")
        final_columns = [col[1] for col in cursor.fetchall()]
        
        required_columns = ['sabbath_observer', 'last_active_timestamp', 'last_transaction_height', 'deeds_score']
        missing_columns = [col for col in required_columns if col not in final_columns]
        
        if missing_columns:
            print(f"⚠️ Still missing columns: {missing_columns}")
        else:
            print("✅ All required tokenomics columns present")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 FIXING BIBLICAL TOKENOMICS DATABASE")
    print("=" * 50)
    
    if fix_database():
        print("✅ Database fix completed successfully")
        print("🎉 Eden Mode should now work without errors!")
    else:
        print("❌ Database fix failed")
        sys.exit(1)
