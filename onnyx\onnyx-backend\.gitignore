# ONNYX Covenant Blockchain Platform - Git Ignore
# Professional repository structure for production deployment

# ============================================================================
# PYTHON PATTERNS
# ============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
.venv/
.env
.env.local
.env.development
.env.test
.env.production

# ============================================================================
# BLOCKCHAIN-SPECIFIC PATTERNS
# ============================================================================

# Database files (keep schema, ignore data)
*.db
*.db-journal
*.db-wal
*.db-shm
shared/data/onnyx.db*
shared/db/onnyx*.db
web/data/*.db

# Blockchain data
blockchain/data/*.json
data/backups/*.db
data/backups/*.json

# Mining and node data
mining_data/
node_data/
peer_data/
*.mining
*.node

# Wallet and key files
wallets/
keys/
private_keys/
*.wallet
*.keystore
mnemonic.txt

# Temporary blockchain files
*.chain
*.mempool
*.pending

# ============================================================================
# DEVELOPMENT TEMPORARY FILES
# ============================================================================

# Development scripts (temporary fixes)
fix_*.py
quick_*.py
setup_*.py
update_*.py
test_*.py
verify_*.py
temp_*.py
debug_*.py
demo_*.py

# Migration scripts (keep in scripts/ folder only)
/migrate_*.py
/migration_*.py

# ============================================================================
# IDE AND EDITOR FILES
# ============================================================================

# Visual Studio Code
.vscode/
.history/

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# ============================================================================
# LOGS AND MONITORING
# ============================================================================

# Log files
*.log
logs/
log/
auto_mining.log
sela_miner.log
blockchain.log
consensus.log
p2p.log
mining.log
validator.log

# Monitoring data
monitoring/
metrics/
*.metrics

# ============================================================================
# WEB APPLICATION FILES
# ============================================================================

# Flask
instance/
.webassets-cache
.flask_session

# Static file builds
web/static/build/
web/static/dist/

# Uploaded files
uploads/
media/

# ============================================================================
# SECURITY AND CREDENTIALS
# ============================================================================

# Environment files
.env*
!.env.example

# Credentials
credentials/
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# API keys and tokens
api_keys.txt
tokens.txt
auth_tokens.json

# ============================================================================
# BACKUP AND ARCHIVE FILES
# ============================================================================

# Backup files
*.backup
*.bak
*.old
*.orig
*.save
*~

# Archive files
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z

# ============================================================================
# DOCUMENTATION BUILD FILES
# ============================================================================

# Sphinx documentation
docs/_build/
docs/build/

# MkDocs
site/

# ============================================================================
# PACKAGE MANAGER FILES
# ============================================================================

# npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Composer
vendor/
composer.lock

# ============================================================================
# SYSTEM AND RUNTIME FILES
# ============================================================================

# Process IDs
*.pid
*.seed
*.pid.lock

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# ============================================================================
# KEEP THESE IMPORTANT FILES
# ============================================================================

# Always track these essential files
!requirements.txt
!README.md
!onnyx_whitepaper.md
!shared/schemas/schema.sql
!shared/schemas/production_schema.sql
!shared/config/genesis.json
!docs/
!scripts/
!web/templates/
!web/static/css/
!web/static/js/
!web/static/images/
