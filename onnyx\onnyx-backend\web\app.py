#!/usr/bin/env python3
"""
Onnyx Web Application

Main Flask application for the Onnyx platform frontend.
"""

import os
import sys
import logging
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.models.identity import Identity
from shared.db.db import db
from blockchain.wallet.wallet import Wallet

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.web")

def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)

    # Enable CORS for all routes
    CORS(app, origins=['*'])  # Allow all origins for now, restrict in production

    # Add moment function to Jinja2 globals for date/time formatting
    class MomentWrapper:
        def __init__(self, dt):
            self.dt = dt

        def format(self, fmt):
            return self.dt.strftime(fmt)

        def fromNow(self):
            return "just now"  # Simple implementation

    @app.template_global()
    def moment(timestamp=None):
        """Simple moment function for date/time formatting"""
        if timestamp is None:
            return MomentWrapper(datetime.now())
        if isinstance(timestamp, (int, float)):
            return MomentWrapper(datetime.fromtimestamp(timestamp))
        return MomentWrapper(timestamp if isinstance(timestamp, datetime) else datetime.now())

    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['DATABASE_URL'] = os.environ.get('DATABASE_URL', 'sqlite:///shared/db/db/onnyx.db')

    # Initialize database if needed
    try:
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
        from init_db import check_tables, init_database
        if not check_tables():
            logger.info("Database tables missing, initializing...")
            init_database()
    except Exception as e:
        logger.warning(f"Database initialization check failed: {e}")

    # Register blueprints
    from web.routes.auth import auth_bp
    from web.routes.dashboard import dashboard_bp
    from web.routes.sela import sela_bp
    from web.routes.explorer import explorer_bp
    from web.routes.api import api_bp
    from web.routes.auto_mining import auto_mining_bp
    from web.routes.tokenomics import tokenomics_bp
    from web.routes.labor import labor_bp
    from web.routes.genesis import genesis_bp
    from web.routes.public_onboarding import public_bp
    from web.routes.network_status import network_bp
    from web.routes.eden_mode import eden_mode_bp
    from web.routes.onboarding import onboarding_bp
    from web.routes.tribes import tribes_bp
    from web.routes.governance import governance_bp
    from web.routes.faq import faq_bp
    from web.routes.admin import admin_bp
    from web.routes.about import about_bp
    from web.routes.community_mining import community_mining_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(sela_bp, url_prefix='/sela')
    app.register_blueprint(explorer_bp, url_prefix='/explorer')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(auto_mining_bp, url_prefix='/auto-mining')
    app.register_blueprint(tokenomics_bp, url_prefix='/tokenomics')
    app.register_blueprint(labor_bp)
    app.register_blueprint(genesis_bp)
    app.register_blueprint(public_bp)
    app.register_blueprint(network_bp)
    app.register_blueprint(eden_mode_bp)
    app.register_blueprint(onboarding_bp)
    app.register_blueprint(tribes_bp)
    app.register_blueprint(governance_bp)
    app.register_blueprint(faq_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(about_bp)
    app.register_blueprint(community_mining_bp)

    # Template filters
    @app.template_filter('truncate_hash')
    def truncate_hash(hash_string, length=8):
        """Truncate a hash for display."""
        if not hash_string:
            return ""
        return f"{hash_string[:length]}...{hash_string[-4:]}"

    @app.template_filter('format_timestamp')
    def format_timestamp(timestamp):
        """Format a timestamp for display."""
        import datetime
        if not timestamp:
            return ""

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                # Try to parse ISO 8601 datetime string
                if 'T' in timestamp:
                    # Remove microseconds if present and parse
                    timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                    dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                else:
                    # Try to parse as string representation of Unix timestamp
                    dt = datetime.datetime.fromtimestamp(float(timestamp))
            elif isinstance(timestamp, (int, float)):
                # Handle numeric Unix timestamp
                dt = datetime.datetime.fromtimestamp(timestamp)
            else:
                # Fallback for other types
                return str(timestamp)

            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError, OSError) as e:
            # Return the original value if parsing fails
            return str(timestamp)

    @app.template_filter('timestamp_to_time')
    def timestamp_to_time(timestamp):
        """Convert timestamp to time string."""
        import datetime
        if not timestamp:
            return "Unknown"

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                # Try to parse ISO 8601 datetime string
                if 'T' in timestamp:
                    # Remove microseconds if present and parse
                    timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                    dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                else:
                    # Try to parse as string representation of Unix timestamp
                    dt = datetime.datetime.fromtimestamp(float(timestamp))
            elif isinstance(timestamp, (int, float)):
                # Handle numeric Unix timestamp
                dt = datetime.datetime.fromtimestamp(timestamp)
            else:
                # Fallback for other types
                return "Unknown"

            return dt.strftime('%H:%M:%S')
        except (ValueError, TypeError, OSError) as e:
            # Return "Unknown" if parsing fails
            return "Unknown"

    @app.template_filter('timestamp_to_date')
    def timestamp_to_date(timestamp):
        """Convert timestamp to date string."""
        import datetime
        if not timestamp:
            return ""

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                # Try to parse ISO 8601 datetime string
                if 'T' in timestamp:
                    # Remove microseconds if present and parse
                    timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                    dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                else:
                    # Try to parse as string representation of Unix timestamp
                    dt = datetime.datetime.fromtimestamp(float(timestamp))
            elif isinstance(timestamp, (int, float)):
                # Handle numeric Unix timestamp
                dt = datetime.datetime.fromtimestamp(timestamp)
            else:
                # Fallback for other types
                return str(timestamp)

            return dt.strftime('%Y-%m-%d')
        except (ValueError, TypeError, OSError) as e:
            # Return the original value if parsing fails
            return str(timestamp)

    # Context processors
    @app.context_processor
    def inject_global_data():
        """Inject global data into all templates."""
        # Get platform statistics with proper error handling
        try:
            identity_result = db.query_one("SELECT COUNT(*) as count FROM identities")
            identity_count = identity_result['count'] if isinstance(identity_result, dict) else identity_result[0] if identity_result else 0

            sela_result = db.query_one("SELECT COUNT(*) as count FROM selas")
            sela_count = sela_result['count'] if isinstance(sela_result, dict) else sela_result[0] if sela_result else 0

            transaction_result = db.query_one("SELECT COUNT(*) as count FROM transactions")
            transaction_count = transaction_result['count'] if isinstance(transaction_result, dict) else transaction_result[0] if transaction_result else 0

            block_count = 0
            if db.table_exists('blocks'):
                block_result = db.query_one("SELECT COUNT(*) as count FROM blocks")
                block_count = block_result['count'] if isinstance(block_result, dict) else block_result[0] if block_result else 0
        except Exception as e:
            logger.warning(f"Error getting platform stats: {e}")
            identity_count = sela_count = transaction_count = block_count = 0

        # Check blockchain network status
        blockchain_status = {
            'network_running': getattr(app, 'blockchain_running', False),
            'mining_enabled': getattr(app, 'mining_running', False),
            'last_block_time': 'Unknown',
            'peer_count': 0
        }

        return {
            'platform_stats': {
                'identities': identity_count,
                'selas': sela_count,
                'transactions': transaction_count,
                'blocks': block_count
            },
            'blockchain_status': blockchain_status,
            'current_user': get_current_user()
        }

    # Routes
    @app.route('/')
    def index():
        """Landing page."""
        # Get recent Selas with error handling
        recent_selas = []
        recent_transactions = []

        try:
            recent_selas = db.query("""
                SELECT s.*, i.name as owner_name
                FROM selas s
                JOIN identities i ON s.identity_id = i.identity_id
                WHERE s.status = 'active'
                ORDER BY s.created_at DESC
                LIMIT 6
            """)
        except Exception as e:
            logger.warning(f"Could not fetch recent selas: {e}")

        try:
            # Get recent transactions
            recent_transactions = db.query("""
                SELECT * FROM transactions
                ORDER BY created_at DESC
                LIMIT 5
            """)
        except Exception as e:
            logger.warning(f"Could not fetch recent transactions: {e}")

        return render_template('index.html',
                             recent_selas=recent_selas,
                             recent_transactions=recent_transactions)

    @app.route('/register')
    def register_choice():
        """Registration choice page - redirects to Eden Mode immersive experience."""
        # Redirect all registration requests to Eden Mode
        return redirect(url_for('eden_mode.step1'))

    @app.route('/register-legacy')
    def register_choice_legacy():
        """Legacy registration choice page (fallback)."""
        # Check if Genesis Identity already exists
        genesis_exists = False
        try:
            result = db.query_one("SELECT identity_id FROM identities WHERE metadata LIKE '%Platform Founder%'")
            genesis_exists = bool(result)
        except Exception as e:
            logger.warning(f"Could not check for Genesis Identity: {e}")

        return render_template('auth/register_choice.html', genesis_exists=genesis_exists)

    @app.route('/validators')
    def validators():
        """Validator Network page - redirect to sela directory."""
        from flask import redirect, url_for
        return redirect(url_for('sela.directory'))

    @app.route('/test-footer')
    def test_footer():
        """Footer positioning test page."""
        return render_template('test_footer.html')

    @app.route('/test-logo')
    def test_logo():
        """Test page for logo loading issues."""
        return render_template('test_logo.html')

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500

    return app

def get_current_user():
    """Get the current user from session."""
    if 'identity_id' in session:
        return Identity.get_by_id(session['identity_id'])
    return None

def require_auth(f):
    """Decorator to require authentication."""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'identity_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def create_sample_validators():
    """Create sample validators if none exist."""
    try:
        import time
        import json

        # Check if validators already exist
        existing = db.query("SELECT COUNT(*) as count FROM selas")
        if existing and existing[0]['count'] > 0:
            return

        print("Creating sample validators...")

        # Create sample identities
        identities = [
            ('validator_001', 'TechCorp Solutions', '<EMAIL>', 'us', 'United States'),
            ('validator_002', 'Covenant Agriculture Co-op', '<EMAIL>', 'ca', 'Canada')
        ]

        for identity_id, name, email, nation_code, nation_name in identities:
            db.execute('''
                INSERT OR REPLACE INTO identities
                (identity_id, name, email, nation_code, nation_name, role_class, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', [identity_id, name, email, nation_code, nation_name, 'business', 'active', int(time.time())])

        # Create sample validators
        validators = [
            {
                'sela_id': 'sela_techcorp_001',
                'identity_id': 'validator_001',
                'name': 'TechCorp Solutions',
                'category': 'Technology',
                'description': 'Leading blockchain technology solutions provider',
                'metadata': json.dumps({'business_type': 'Technology Services', 'employees': 50})
            },
            {
                'sela_id': 'sela_agriculture_002',
                'identity_id': 'validator_002',
                'name': 'Covenant Agriculture Co-op',
                'category': 'Agriculture',
                'description': 'Sustainable farming cooperative following biblical principles',
                'metadata': json.dumps({'business_type': 'Agricultural Cooperative', 'members': 25})
            }
        ]

        for validator in validators:
            db.execute('''
                INSERT OR REPLACE INTO selas
                (sela_id, identity_id, name, category, description, stake_amount,
                 stake_token_id, status, created_at, updated_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', [
                validator['sela_id'], validator['identity_id'], validator['name'],
                validator['category'], validator['description'], 1000,
                'ONX', 'active', int(time.time()), int(time.time()), validator['metadata']
            ])

        print("Sample validators created successfully!")

    except Exception as e:
        print(f"Error creating sample validators: {e}")

def start_blockchain_backend():
    """Start the blockchain backend services."""
    import threading
    import asyncio
    import subprocess
    import sys
    import time
    
    logger.info("🔥 Starting ONNYX Blockchain Backend...")
    
    def run_blockchain_network():
        """Run the blockchain network in a separate thread."""
        try:
            # First start the basic P2P network
            from scripts.deploy_p2p_network import NetworkDeployment
            
            async def start_network():
                # Phase 1: Start basic network
                logger.info("📡 Starting P2P Network...")
                deployment = NetworkDeployment()
                success = await deployment.deploy_network()
                
                if success:
                    app.blockchain_running = True
                    logger.info("✅ P2P Network started successfully")
                    
                    # Phase 2: Start mining network
                    logger.info("⛏️ Starting Mining Network...")
                    try:
                        from scripts.deploy_p2p_mining import P2PMiningDeployment
                        mining_deployment = P2PMiningDeployment()
                        mining_success = await mining_deployment.deploy_mining_network()
                        
                        if mining_success:
                            app.mining_running = True
                            logger.info("✅ Mining network started successfully")
                        else:
                            logger.warning("⚠️ Mining network failed to start, continuing with basic network")
                    except Exception as e:
                        logger.warning(f"Mining network startup failed: {e}")
                    
                    # Keep the network running
                    logger.info("🌐 Blockchain network is now running...")
                    while True:
                        await asyncio.sleep(60)  # Health check every minute
                        
                else:
                    logger.error("❌ Failed to start blockchain network")
            
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(start_network())
            
        except Exception as e:
            logger.error(f"Blockchain network startup error: {e}")
            logger.info("🔄 Falling back to database-only mode")
    
    # Initialize status flags
    app.blockchain_running = False
    app.mining_running = False
    
    # Start blockchain network in background thread
    blockchain_thread = threading.Thread(target=run_blockchain_network, daemon=True)
    blockchain_thread.start()
    
    # Give the blockchain a moment to start
    time.sleep(5)
    logger.info("🚀 Blockchain backend startup initiated")

if __name__ == '__main__':
    app = create_app()

    # Create sample validators on startup
    create_sample_validators()

    # Start blockchain backend
    start_blockchain_backend()

    app.run(debug=True, host='0.0.0.0', port=5000)
