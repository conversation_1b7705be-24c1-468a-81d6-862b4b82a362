"""
Onnyx Event Logger Module

This module provides the EventLogger class for logging blockchain events.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

# Set up logging
logger = logging.getLogger("onnyx.analytics.event_logger")

class EventLogger:
    """
    EventLogger logs blockchain events for analytics purposes.
    """
    
    def __init__(self, path: str = "data/event_log.json"):
        """
        Initialize the EventLogger.
        
        Args:
            path: Path to the event log JSON file
        """
        self.path = path
        
        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.path), exist_ok=True)
        
        # Create the file if it doesn't exist
        if not os.path.exists(self.path):
            with open(self.path, "w") as f:
                json.dump([], f)
                logger.info(f"Created empty event log at {self.path}")
    
    def log_block(self, block: Dict[str, Any], txs: List[Dict[str, Any]], proposer: str):
        """
        Log a block and its transactions.
        
        Args:
            block: The block to log
            txs: The transactions in the block
            proposer: The identity ID of the block proposer
        """
        # Create the log entry
        log_entry = {
            "block_index": block["index"],
            "block_hash": block["hash"],
            "timestamp": block["timestamp"],
            "proposer": proposer,
            "tx_count": len(txs),
            "token_mints": sum(1 for t in txs if t.get("op") == "OP_MINT"),
            "token_transfers": sum(1 for t in txs if t.get("op") == "OP_SEND"),
            "token_burns": sum(1 for t in txs if t.get("op") == "OP_BURN"),
            "proposals": sum(1 for t in txs if t.get("op") == "OP_SCROLL"),
            "votes": sum(1 for t in txs if t.get("op") == "OP_VOTE"),
            "identities": sum(1 for t in txs if t.get("op") == "OP_IDENTITY"),
            "reputation_grants": sum(1 for t in txs if t.get("op") == "OP_GRANT_REPUTATION"),
            "stakes": sum(1 for t in txs if t.get("op") == "OP_STAKE"),
            "rewards": sum(1 for t in txs if t.get("op") == "OP_REWARD"),
            "notable_events": self._extract_notable_events(txs)
        }
        
        # Add human-readable timestamp
        log_entry["timestamp_human"] = datetime.fromtimestamp(
            log_entry["timestamp"]
        ).strftime("%Y-%m-%d %H:%M:%S")
        
        # Load existing logs
        try:
            with open(self.path, "r") as f:
                logs = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            logs = []
        
        # Add the new log entry
        logs.append(log_entry)
        
        # Save the logs
        with open(self.path, "w") as f:
            json.dump(logs, f, indent=2)
        
        logger.info(f"Logged block {block['index']} with {len(txs)} transactions")
        
        return log_entry
    
    def _extract_notable_events(self, txs: List[Dict[str, Any]]) -> List[str]:
        """
        Extract notable events from transactions.
        
        Args:
            txs: The transactions to extract events from
        
        Returns:
            A list of notable events
        """
        notable_events = []
        
        for tx in txs:
            op = tx.get("op")
            
            if not op:
                continue
            
            if op == "OP_MINT":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                to = tx.get("to") or tx.get("data", {}).get("to")
                
                if token_id and amount and to:
                    notable_events.append(f"MINT: {amount} {token_id} to {to}")
            
            elif op == "OP_SEND":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                to = tx.get("to") or tx.get("data", {}).get("to")
                
                if token_id and amount and from_id and to:
                    notable_events.append(f"TRANSFER: {amount} {token_id} from {from_id} to {to}")
            
            elif op == "OP_BURN":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if token_id and amount and from_id:
                    notable_events.append(f"BURN: {amount} {token_id} by {from_id}")
            
            elif op == "OP_SCROLL":
                title = tx.get("data", {}).get("title")
                category = tx.get("data", {}).get("category")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if title and from_id:
                    notable_events.append(f"PROPOSAL: '{title}' ({category}) by {from_id}")
            
            elif op == "OP_VOTE":
                scroll_id = tx.get("data", {}).get("scroll_id")
                vote = tx.get("data", {}).get("vote")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if scroll_id and vote and from_id:
                    notable_events.append(f"VOTE: {from_id} voted {vote} on {scroll_id}")
            
            elif op == "OP_IDENTITY":
                identity_id = tx.get("data", {}).get("identity_id")
                name = tx.get("data", {}).get("name")
                
                if identity_id and name:
                    notable_events.append(f"IDENTITY: {name} ({identity_id}) registered")
            
            elif op == "OP_GRANT_REPUTATION":
                to = tx.get("to") or tx.get("data", {}).get("to")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if to and amount and from_id:
                    notable_events.append(f"REPUTATION: {amount} granted to {to} by {from_id}")
            
            elif op == "OP_STAKE":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if token_id and amount and from_id:
                    notable_events.append(f"STAKE: {amount} {token_id} by {from_id}")
            
            elif op == "OP_REWARD":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                to = tx.get("to") or tx.get("data", {}).get("to")
                
                if token_id and amount and to:
                    notable_events.append(f"REWARD: {amount} {token_id} to {to}")
        
        return notable_events
    
    def get_logs(self, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get event logs.
        
        Args:
            limit: Maximum number of logs to return (optional)
            offset: Offset for pagination
        
        Returns:
            A list of event logs
        """
        try:
            with open(self.path, "r") as f:
                logs = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            logs = []
        
        # Sort by block index (newest first)
        logs = sorted(logs, key=lambda x: x["block_index"], reverse=True)
        
        # Apply offset and limit
        if offset:
            logs = logs[offset:]
        
        if limit:
            logs = logs[:limit]
        
        return logs
    
    def generate_summary(self, days: int = 7) -> Dict[str, Any]:
        """
        Generate a summary of event logs for a given number of days.
        
        Args:
            days: Number of days to include in the summary
        
        Returns:
            A summary of event logs
        """
        # Initialize summary
        summary = {
            "blocks": 0,
            "transactions": 0,
            "token_mints": 0,
            "token_transfers": 0,
            "token_burns": 0,
            "proposals": 0,
            "votes": 0,
            "identities": 0,
            "reputation_grants": 0,
            "stakes": 0,
            "rewards": 0,
            "unique_proposers": set(),
            "start_timestamp": 0,
            "end_timestamp": 0
        }
        
        # Calculate the cutoff timestamp
        now = int(time.time())
        cutoff = now - (days * 86400)  # Convert days to seconds
        
        # Get logs
        logs = self.get_logs()
        
        # Filter logs by timestamp
        recent_logs = [log for log in logs if log["timestamp"] >= cutoff]
        
        # Update summary
        for log in recent_logs:
            summary["blocks"] += 1
            summary["transactions"] += log["tx_count"]
            summary["token_mints"] += log["token_mints"]
            summary["token_transfers"] += log["token_transfers"]
            summary["token_burns"] += log["token_burns"]
            summary["proposals"] += log["proposals"]
            summary["votes"] += log["votes"]
            summary["identities"] += log["identities"]
            summary["reputation_grants"] += log["reputation_grants"]
            summary["stakes"] += log["stakes"]
            summary["rewards"] += log["rewards"]
            summary["unique_proposers"].add(log["proposer"])
        
        # Convert set to list for JSON serialization
        summary["unique_proposers"] = list(summary["unique_proposers"])
        summary["unique_proposer_count"] = len(summary["unique_proposers"])
        
        # Set start and end timestamps
        if recent_logs:
            summary["start_timestamp"] = min(log["timestamp"] for log in recent_logs)
            summary["end_timestamp"] = max(log["timestamp"] for log in recent_logs)
            
            # Add human-readable timestamps
            summary["start_timestamp_human"] = datetime.fromtimestamp(
                summary["start_timestamp"]
            ).strftime("%Y-%m-%d %H:%M:%S")
            
            summary["end_timestamp_human"] = datetime.fromtimestamp(
                summary["end_timestamp"]
            ).strftime("%Y-%m-%d %H:%M:%S")
        
        return summary

# Create a global instance of the EventLogger
event_logger = EventLogger()
