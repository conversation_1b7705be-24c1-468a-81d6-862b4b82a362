#!/usr/bin/env python3
"""
Initialize CIPP Tables Script
Creates the Covenant Identity Protection Protocol tables
"""

import os
import sys
import time
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.cipp_init")

def create_cipp_tables():
    """Create CIPP tables."""
    try:
        logger.info("🛡️ Creating CIPP tables...")
        
        # Biblical nations registry
        db.execute("""
            CREATE TABLE IF NOT EXISTS biblical_nations (
                nation_code TEXT PRIMARY KEY,
                nation_name TEXT NOT NULL,
                tribe_name TEXT NOT NULL,
                description TEXT,
                flag_symbol TEXT,
                covenant_status TEXT DEFAULT 'Active',
                created_at INTEGER NOT NULL
            )
        """)
        
        # Verification progress tracking
        db.execute("""
            CREATE TABLE IF NOT EXISTS verification_progress (
                identity_id TEXT PRIMARY KEY,
                tier_0_completed BOOLEAN DEFAULT 1,
                tier_1_completed BOOLEAN DEFAULT 0,
                tier_2_completed BOOLEAN DEFAULT 0,
                tier_3_completed BOOLEAN DEFAULT 0,
                tier_1_method TEXT,
                tier_2_contributions INTEGER DEFAULT 0,
                tier_3_governance_count INTEGER DEFAULT 0,
                last_updated INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Protection requests and incidents
        db.execute("""
            CREATE TABLE IF NOT EXISTS protection_requests (
                request_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                request_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'PENDING',
                requested_at INTEGER NOT NULL,
                processed_at INTEGER,
                processed_by TEXT,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Covenant scroll acceptance tracking
        db.execute("""
            CREATE TABLE IF NOT EXISTS covenant_acceptances (
                identity_id TEXT PRIMARY KEY,
                scroll_version TEXT NOT NULL DEFAULT 'v1.0',
                accepted_at INTEGER NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                signature_hash TEXT,
                witness_count INTEGER DEFAULT 0,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Etzem score history for reputation tracking
        db.execute("""
            CREATE TABLE IF NOT EXISTS etzem_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                old_score INTEGER NOT NULL,
                new_score INTEGER NOT NULL,
                change_reason TEXT NOT NULL,
                change_amount INTEGER NOT NULL,
                timestamp INTEGER NOT NULL,
                block_height INTEGER,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Zeman (seasonal activity) tracking
        db.execute("""
            CREATE TABLE IF NOT EXISTS zeman_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                season_start INTEGER NOT NULL,
                season_end INTEGER NOT NULL,
                activity_count INTEGER DEFAULT 0,
                contribution_value REAL DEFAULT 0.0,
                season_score INTEGER DEFAULT 0,
                completed BOOLEAN DEFAULT 0,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Vault security events
        db.execute("""
            CREATE TABLE IF NOT EXISTS vault_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                triggered_by TEXT,
                reason TEXT,
                timestamp INTEGER NOT NULL,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        logger.info("✅ CIPP tables created successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to create CIPP tables: {e}")
        raise

def populate_biblical_nations():
    """Populate biblical nations table."""
    try:
        logger.info("🏛️ Populating biblical nations...")
        
        nations_data = [
            ('JU', 'Judah', 'Tribe of Judah', 'The royal tribe, keepers of the scepter', '🦁'),
            ('BE', 'Benjamin', 'Tribe of Benjamin', 'The beloved tribe, warriors and protectors', '🐺'),
            ('EP', 'Ephraim', 'Tribe of Ephraim', 'The fruitful tribe, leaders of the northern kingdom', '🌾'),
            ('MA', 'Manasseh', 'Tribe of Manasseh', 'The forgetful tribe, blessed with abundance', '🌳'),
            ('LE', 'Levi', 'Tribe of Levi', 'The priestly tribe, servants of the sanctuary', '⚖️'),
            ('IS', 'Issachar', 'Tribe of Issachar', 'The wise tribe, discerners of times and seasons', '📚'),
            ('ZE', 'Zebulun', 'Tribe of Zebulun', 'The merchant tribe, dwellers by the sea', '⛵'),
            ('RU', 'Reuben', 'Tribe of Reuben', 'The firstborn tribe, unstable as water', '💧'),
            ('GA', 'Gad', 'Tribe of Gad', 'The warrior tribe, overcomers of adversity', '⚔️'),
            ('AS', 'Asher', 'Tribe of Asher', 'The blessed tribe, providers of royal dainties', '🍯'),
            ('NA', 'Naphtali', 'Tribe of Naphtali', 'The swift tribe, messengers of good tidings', '🦌'),
            ('DA', 'Dan', 'Tribe of Dan', 'The judging tribe, serpents by the way', '🐍'),
            ('SI', 'Simeon', 'Tribe of Simeon', 'The hearing tribe, scattered among brothers', '👂')
        ]
        
        current_time = int(time.time())
        for nation_data in nations_data:
            db.execute("""
                INSERT OR IGNORE INTO biblical_nations 
                (nation_code, nation_name, tribe_name, description, flag_symbol, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (*nation_data, current_time))
        
        logger.info(f"✅ Populated {len(nations_data)} biblical nations")
        
    except Exception as e:
        logger.error(f"❌ Failed to populate biblical nations: {e}")
        raise

def create_indexes():
    """Create indexes for CIPP tables."""
    try:
        logger.info("📊 Creating CIPP indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_verification_progress_identity ON verification_progress(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_protection_requests_identity ON protection_requests(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_protection_requests_status ON protection_requests(status)",
            "CREATE INDEX IF NOT EXISTS idx_covenant_acceptances_identity ON covenant_acceptances(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_etzem_history_identity ON etzem_history(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_etzem_history_timestamp ON etzem_history(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_zeman_activities_identity ON zeman_activities(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_zeman_activities_season ON zeman_activities(season_start, season_end)",
            "CREATE INDEX IF NOT EXISTS idx_vault_events_identity ON vault_events(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_vault_events_type ON vault_events(event_type)"
        ]
        
        for index_sql in indexes:
            db.execute(index_sql)
        
        logger.info(f"✅ Created {len(indexes)} CIPP indexes")
        
    except Exception as e:
        logger.error(f"❌ Failed to create CIPP indexes: {e}")
        raise

def main():
    """Main initialization function."""
    try:
        logger.info("🛡️ Starting CIPP Tables Initialization...")
        
        # Check if database is accessible
        try:
            db.query_one("SELECT COUNT(*) as count FROM identities")
        except Exception as e:
            logger.error(f"❌ Cannot access database: {e}")
            return False
        
        # Create CIPP tables
        create_cipp_tables()
        
        # Populate biblical nations
        populate_biblical_nations()
        
        # Create indexes
        create_indexes()
        
        logger.info("🎉 CIPP Tables Initialization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ CIPP Tables Initialization failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
