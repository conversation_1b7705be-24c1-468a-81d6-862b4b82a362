#!/usr/bin/env python3
"""
Biblical Tokenomics Performance Optimization Script

This script optimizes the biblical tokenomics system for production performance
by creating additional indexes, analyzing query patterns, and implementing
performance improvements.
"""

import os
import sys
import time
import logging
from typing import List, Dict, Any

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

logger = logging.getLogger("onnyx.optimization")

def create_performance_indexes():
    """Create additional indexes for optimal query performance."""
    
    indexes = [
        # Deeds ledger optimizations
        "CREATE INDEX IF NOT EXISTS idx_deeds_ledger_timestamp ON deeds_ledger(timestamp DESC)",
        "CREATE INDEX IF NOT EXISTS idx_deeds_ledger_identity_timestamp ON deeds_ledger(identity_id, timestamp DESC)",
        "CREATE INDEX IF NOT EXISTS idx_deeds_ledger_type_timestamp ON deeds_ledger(deed_type, timestamp DESC)",
        
        # Loans optimizations
        "CREATE INDEX IF NOT EXISTS idx_loans_status_created ON loans(status, created_block)",
        "CREATE INDEX IF NOT EXISTS idx_loans_borrower_status ON loans(borrower_id, status)",
        "CREATE INDEX IF NOT EXISTS idx_loans_lender_status ON loans(lender_id, status)",
        "CREATE INDEX IF NOT EXISTS idx_loans_grace_blocks ON loans(grace_blocks, created_block)",
        
        # Jubilee pools optimizations
        "CREATE INDEX IF NOT EXISTS idx_jubilee_pools_type_amount ON jubilee_pools(pool_type, total_amount)",
        "CREATE INDEX IF NOT EXISTS idx_jubilee_pools_last_distribution ON jubilee_pools(last_distribution)",
        
        # Token balances optimizations
        "CREATE INDEX IF NOT EXISTS idx_token_balances_identity_updated ON token_balances(identity_id, updated_at DESC)",
        "CREATE INDEX IF NOT EXISTS idx_token_balances_token_balance ON token_balances(token_id, balance DESC)",
        
        # Identities optimizations for tokenomics
        "CREATE INDEX IF NOT EXISTS idx_identities_last_active ON identities(last_active_timestamp DESC)",
        "CREATE INDEX IF NOT EXISTS idx_identities_last_transaction ON identities(last_transaction_height DESC)",
        "CREATE INDEX IF NOT EXISTS idx_identities_deeds_score ON identities(deeds_score DESC)",
        "CREATE INDEX IF NOT EXISTS idx_identities_sabbath_observer ON identities(sabbath_observer)",
        
        # Dormant accounts optimizations
        "CREATE INDEX IF NOT EXISTS idx_dormant_accounts_last_activity ON dormant_accounts(last_activity)",
        "CREATE INDEX IF NOT EXISTS idx_dormant_accounts_status_since ON dormant_accounts(status, dormant_since)",
        
        # Token classes optimizations
        "CREATE INDEX IF NOT EXISTS idx_token_classes_type_assigned ON token_classes(class_type, assigned_at DESC)",
        
        # Sabbath periods optimizations
        "CREATE INDEX IF NOT EXISTS idx_sabbath_periods_active ON sabbath_periods(start_timestamp, end_timestamp)"
    ]
    
    logger.info("Creating performance indexes...")
    
    for index_sql in indexes:
        try:
            db.execute(index_sql)
            index_name = index_sql.split("IF NOT EXISTS ")[1].split(" ON")[0]
            logger.info(f"✅ Created index: {index_name}")
        except Exception as e:
            logger.error(f"❌ Failed to create index: {e}")
    
    logger.info("Performance indexes creation completed")

def analyze_query_performance():
    """Analyze query performance for common tokenomics operations."""
    
    logger.info("Analyzing query performance...")
    
    # Test queries with timing
    test_queries = [
        {
            "name": "Get deed score for identity",
            "query": "SELECT deeds_score FROM identities WHERE identity_id = ?",
            "params": ("test_identity_123",)
        },
        {
            "name": "Get recent deeds for identity",
            "query": """
                SELECT * FROM deeds_ledger 
                WHERE identity_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 10
            """,
            "params": ("test_identity_123",)
        },
        {
            "name": "Get active loans for borrower",
            "query": """
                SELECT * FROM loans 
                WHERE borrower_id = ? AND status = 'ACTIVE'
                ORDER BY created_at DESC
            """,
            "params": ("test_borrower_123",)
        },
        {
            "name": "Get gleaning pool balance",
            "query": """
                SELECT total_amount FROM jubilee_pools 
                WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
            """,
            "params": ()
        },
        {
            "name": "Get dormant accounts",
            "query": """
                SELECT identity_id FROM identities 
                WHERE last_transaction_height < ? AND last_transaction_height > 0
                LIMIT 100
            """,
            "params": (1000,)
        },
        {
            "name": "Get total balance for identity",
            "query": """
                SELECT SUM(balance) as total_balance 
                FROM token_balances 
                WHERE identity_id = ?
            """,
            "params": ("test_identity_123",)
        }
    ]
    
    performance_results = []
    
    for test in test_queries:
        start_time = time.time()
        
        try:
            if test["params"]:
                result = db.query(test["query"], test["params"])
            else:
                result = db.query(test["query"])
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            performance_results.append({
                "query": test["name"],
                "execution_time_ms": execution_time,
                "result_count": len(result) if result else 0,
                "status": "success"
            })
            
            logger.info(f"✅ {test['name']}: {execution_time:.2f}ms ({len(result) if result else 0} results)")
            
        except Exception as e:
            performance_results.append({
                "query": test["name"],
                "execution_time_ms": 0,
                "result_count": 0,
                "status": f"error: {e}"
            })
            
            logger.error(f"❌ {test['name']}: {e}")
    
    return performance_results

def optimize_database_settings():
    """Optimize database settings for tokenomics operations."""
    
    logger.info("Optimizing database settings...")
    
    # SQLite optimization pragmas
    optimizations = [
        "PRAGMA journal_mode = WAL",  # Write-Ahead Logging for better concurrency
        "PRAGMA synchronous = NORMAL",  # Balance between safety and performance
        "PRAGMA cache_size = 10000",  # Increase cache size
        "PRAGMA temp_store = MEMORY",  # Store temporary tables in memory
        "PRAGMA mmap_size = 268435456",  # 256MB memory-mapped I/O
        "PRAGMA optimize"  # Analyze and optimize the database
    ]
    
    for pragma in optimizations:
        try:
            db.execute(pragma)
            logger.info(f"✅ Applied: {pragma}")
        except Exception as e:
            logger.error(f"❌ Failed to apply {pragma}: {e}")

def create_materialized_views():
    """Create materialized views for frequently accessed data."""
    
    logger.info("Creating materialized views...")
    
    # Create a view for identity statistics
    try:
        db.execute("DROP VIEW IF EXISTS identity_stats")
        db.execute("""
            CREATE VIEW identity_stats AS
            SELECT 
                i.identity_id,
                i.name,
                i.deeds_score,
                i.last_active_timestamp,
                i.sabbath_observer,
                COALESCE(SUM(tb.balance), 0) as total_balance,
                COUNT(d.id) as total_deeds,
                COUNT(l1.loan_id) as loans_as_lender,
                COUNT(l2.loan_id) as loans_as_borrower
            FROM identities i
            LEFT JOIN token_balances tb ON i.identity_id = tb.identity_id
            LEFT JOIN deeds_ledger d ON i.identity_id = d.identity_id
            LEFT JOIN loans l1 ON i.identity_id = l1.lender_id
            LEFT JOIN loans l2 ON i.identity_id = l2.borrower_id
            GROUP BY i.identity_id, i.name, i.deeds_score, i.last_active_timestamp, i.sabbath_observer
        """)
        logger.info("✅ Created identity_stats view")
    except Exception as e:
        logger.error(f"❌ Failed to create identity_stats view: {e}")
    
    # Create a view for pool statistics
    try:
        db.execute("DROP VIEW IF EXISTS pool_stats")
        db.execute("""
            CREATE VIEW pool_stats AS
            SELECT 
                pool_type,
                COUNT(*) as pool_count,
                SUM(total_amount) as total_amount,
                AVG(total_amount) as avg_amount,
                MAX(last_distribution) as last_distribution
            FROM jubilee_pools
            GROUP BY pool_type
        """)
        logger.info("✅ Created pool_stats view")
    except Exception as e:
        logger.error(f"❌ Failed to create pool_stats view: {e}")

def cleanup_old_data():
    """Clean up old data to improve performance."""
    
    logger.info("Cleaning up old data...")
    
    # Clean up old deed records (keep last 6 months)
    six_months_ago = int(time.time()) - (6 * 30 * 24 * 3600)
    
    try:
        result = db.execute("""
            DELETE FROM deeds_ledger 
            WHERE timestamp < ? AND deed_type NOT IN ('FIRSTFRUITS', 'SABBATH_OBSERVANCE')
        """, (six_months_ago,))
        logger.info(f"✅ Cleaned up old deed records")
    except Exception as e:
        logger.error(f"❌ Failed to clean up deed records: {e}")
    
    # Clean up completed loans older than 1 year
    one_year_ago = int(time.time()) - (365 * 24 * 3600)
    
    try:
        result = db.execute("""
            DELETE FROM loans 
            WHERE status IN ('REPAID', 'FORGIVEN') AND created_at < ?
        """, (one_year_ago,))
        logger.info(f"✅ Cleaned up old loan records")
    except Exception as e:
        logger.error(f"❌ Failed to clean up loan records: {e}")
    
    # Clean up old sabbath periods (keep last year)
    try:
        result = db.execute("""
            DELETE FROM sabbath_periods 
            WHERE end_timestamp < ?
        """, (one_year_ago,))
        logger.info(f"✅ Cleaned up old sabbath periods")
    except Exception as e:
        logger.error(f"❌ Failed to clean up sabbath periods: {e}")

def generate_performance_report():
    """Generate a performance report for the tokenomics system."""
    
    logger.info("Generating performance report...")
    
    report = {
        "timestamp": int(time.time()),
        "database_stats": {},
        "table_stats": {},
        "index_stats": {}
    }
    
    try:
        # Get database size
        db_size = db.query_one("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
        report["database_stats"]["size_bytes"] = db_size["size"] if db_size else 0
        
        # Get table statistics
        tables = ["identities", "deeds_ledger", "loans", "jubilee_pools", "token_classes", "sabbath_periods"]
        
        for table in tables:
            try:
                count_result = db.query_one(f"SELECT COUNT(*) as count FROM {table}")
                report["table_stats"][table] = count_result["count"] if count_result else 0
            except Exception as e:
                report["table_stats"][table] = f"error: {e}"
        
        # Get index usage statistics
        indexes = db.query("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
        report["index_stats"]["total_indexes"] = len(indexes)
        report["index_stats"]["index_names"] = [idx["name"] for idx in indexes]
        
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        report["error"] = str(e)
    
    return report

def main():
    """Run the complete optimization process."""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting biblical tokenomics performance optimization...")
    
    try:
        # Step 1: Create performance indexes
        create_performance_indexes()
        
        # Step 2: Optimize database settings
        optimize_database_settings()
        
        # Step 3: Create materialized views
        create_materialized_views()
        
        # Step 4: Analyze query performance
        performance_results = analyze_query_performance()
        
        # Step 5: Clean up old data
        cleanup_old_data()
        
        # Step 6: Generate performance report
        report = generate_performance_report()
        
        # Display summary
        logger.info("Optimization completed successfully!")
        logger.info(f"Database size: {report['database_stats'].get('size_bytes', 0):,} bytes")
        logger.info(f"Total indexes: {report['index_stats'].get('total_indexes', 0)}")
        
        # Show performance results
        logger.info("Query Performance Summary:")
        for result in performance_results:
            if result["status"] == "success":
                logger.info(f"  {result['query']}: {result['execution_time_ms']:.2f}ms")
        
        print("\n🎉 Biblical Tokenomics Performance Optimization Complete!")
        print("✅ Performance indexes created")
        print("✅ Database settings optimized")
        print("✅ Materialized views created")
        print("✅ Query performance analyzed")
        print("✅ Old data cleaned up")
        print("✅ Performance report generated")
        
    except Exception as e:
        logger.error(f"Optimization failed: {e}")
        print(f"\n❌ Optimization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
