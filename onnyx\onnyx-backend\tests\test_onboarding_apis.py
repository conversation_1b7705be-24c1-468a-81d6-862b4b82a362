#!/usr/bin/env python3
"""
Test Onboarding API Endpoints
"""

import requests
import sys

def test_onboarding_apis():
    """Test the new onboarding API endpoints"""
    print("Testing onboarding API endpoints...")
    
    endpoints = [
        '/onboarding/api/identities',
        '/onboarding/api/tribal-elders', 
        '/onboarding/api/validators',
        '/onboarding/api/citizens'
    ]
    
    success_count = 0
    
    for endpoint in endpoints:
        try:
            print(f"\nTesting {endpoint}...")
            response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=10)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print("SUCCESS: Endpoint accessible")
                try:
                    data = response.json()
                    if data.get('success'):
                        count = data.get('count', 0)
                        print(f"SUCCESS: Found {count} records")
                        success_count += 1
                    else:
                        print(f"WARNING: API returned success=false: {data.get('error', 'Unknown error')}")
                except:
                    print("WARNING: Response not valid JSON")
                    
            elif response.status_code == 302:
                print("REDIRECT: Authentication required (expected)")
                success_count += 1
                
            elif response.status_code == 403:
                print("FORBIDDEN: Permission required (expected)")
                success_count += 1
                
            elif response.status_code == 404:
                print("ERROR: Endpoint not found")
                
            elif response.status_code == 500:
                print("ERROR: Server error")
                
            else:
                print(f"UNEXPECTED: Status {response.status_code}")
                
        except Exception as e:
            print(f"ERROR: {e}")
    
    return success_count > 0

def test_onboarding_dashboard():
    """Test the main onboarding dashboard"""
    print("\nTesting onboarding dashboard...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/onboarding/", timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: Dashboard loaded")
            content = response.text.lower()
            
            checks = [
                ('statistics', 'total identities' in content),
                ('clickable', 'click to view list' in content),
                ('javascript', 'showidentitieslist' in content),
                ('modal', 'listmodal' in content)
            ]
            
            for check_name, check_result in checks:
                if check_result:
                    print(f"SUCCESS: {check_name} found")
                else:
                    print(f"WARNING: {check_name} not found")
                    
            return True
            
        elif response.status_code in [302, 403]:
            print("REDIRECT/FORBIDDEN: Authentication required (expected)")
            return True
            
        else:
            print(f"ERROR: Unexpected status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    print("Testing Onboarding Dashboard Enhancements")
    print("=" * 50)
    
    api_success = test_onboarding_apis()
    dashboard_success = test_onboarding_dashboard()
    
    print("\nResults:")
    if api_success:
        print("✅ API endpoints working")
    else:
        print("❌ API endpoints have issues")
        
    if dashboard_success:
        print("✅ Dashboard loading correctly")
    else:
        print("❌ Dashboard has issues")
    
    overall_success = api_success and dashboard_success
    print(f"\n{'SUCCESS' if overall_success else 'FAILURE'}: Onboarding dashboard enhancements")
    
    sys.exit(0 if overall_success else 1)
