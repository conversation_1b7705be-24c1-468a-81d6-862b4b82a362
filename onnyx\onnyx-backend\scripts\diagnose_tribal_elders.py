#!/usr/bin/env python3
"""
Diagnose Tribal Elders Database Issue
Analyzes the current state of tribal elders in the database and provides cleanup recommendations
"""

import sys
import os
import json
import time
import sqlite3

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Simple database connection for diagnostics
def get_db_connection():
    """Get database connection."""
    # Try multiple possible database locations
    possible_paths = [
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'db', 'db', 'onnyx.db'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'db', 'onnyx.db'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'onnyx.db'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'data', 'onnyx.db')
    ]

    for db_path in possible_paths:
        if os.path.exists(db_path):
            print(f"Using database: {db_path}")
            return sqlite3.connect(db_path)

    # If no database found, use the first path
    print(f"No existing database found, using: {possible_paths[0]}")
    return sqlite3.connect(possible_paths[0])

def query_db(query, params=None):
    """Execute a query and return results."""
    conn = get_db_connection()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    if params:
        cursor.execute(query, params)
    else:
        cursor.execute(query)

    results = cursor.fetchall()
    conn.close()
    return [dict(row) for row in results]

def query_db_one(query, params=None):
    """Execute a query and return single result."""
    results = query_db(query, params)
    return results[0] if results else None

def analyze_tribal_elders():
    """Analyze the current state of tribal elders in the database."""
    print("🔍 TRIBAL ELDERS DATABASE ANALYSIS")
    print("=" * 50)

    try:
        # First, check the database schema
        print("📋 DATABASE SCHEMA ANALYSIS:")

        # Check identities table structure
        columns_info = query_db("PRAGMA table_info(identities)")
        columns = [col['name'] for col in columns_info]
        print(f"   Identities table columns: {', '.join(columns)}")

        # Check total identities
        total_identities = query_db("SELECT COUNT(*) as count FROM identities")
        total_count = total_identities[0]['count'] if total_identities else 0
        print(f"   Total identities: {total_count}")

        # Check if tribal elder columns exist
        has_role_class = 'role_class' in columns
        has_tribal_affiliation = 'tribal_affiliation' in columns
        has_nation_code = 'nation_code' in columns

        print(f"   Has role_class column: {has_role_class}")
        print(f"   Has tribal_affiliation column: {has_tribal_affiliation}")
        print(f"   Has nation_code column: {has_nation_code}")

        # Initialize counts
        role_class_count = 0
        tribal_affiliation_count = 0
        covenant_count = 0

        # Check tribal elders if columns exist
        if has_role_class:
            role_class_elders = query_db("""
                SELECT COUNT(*) as count
                FROM identities
                WHERE role_class = 'Tribal_Elder'
            """)
            role_class_count = role_class_elders[0]['count'] if role_class_elders else 0

        if has_tribal_affiliation:
            tribal_affiliation_elders = query_db("""
                SELECT COUNT(*) as count
                FROM identities
                WHERE tribal_affiliation IS NOT NULL
            """)
            tribal_affiliation_count = tribal_affiliation_elders[0]['count'] if tribal_affiliation_elders else 0

        if has_nation_code:
            covenant_tribes = ['JU', 'LE', 'EP', 'BE', 'SI', 'MA', 'GA', 'AS', 'NA', 'ZE', 'IS', 'DA']
            covenant_elders = query_db(f"""
                SELECT COUNT(*) as count
                FROM identities
                WHERE nation_code IN ({','.join(['?' for _ in covenant_tribes])})
            """, covenant_tribes)
            covenant_count = covenant_elders[0]['count'] if covenant_elders else 0

        print(f"\n📊 TRIBAL ELDERS COUNT ANALYSIS:")
        print(f"   By role_class='Tribal_Elder': {role_class_count}")
        print(f"   By tribal_affiliation: {tribal_affiliation_count}")
        print(f"   By covenant nation_codes: {covenant_count}")

        # Show current identities
        print(f"\n📋 CURRENT IDENTITIES:")
        all_identities = query_db("SELECT identity_id, name, metadata FROM identities")
        for identity in all_identities:
            print(f"   {identity['identity_id']}: {identity['name']}")
            try:
                metadata = json.loads(identity['metadata'])
                if 'type' in metadata:
                    print(f"      Type: {metadata['type']}")
            except:
                pass

        # Detailed breakdown by role_class (if column exists)
        duplicates = []
        if has_role_class and role_class_count > 0:
            print(f"\n📋 ROLE_CLASS='Tribal_Elder' BREAKDOWN:")
            detailed_elders = query_db("""
                SELECT identity_id, name, created_at
                FROM identities
                WHERE role_class = 'Tribal_Elder'
                ORDER BY created_at
            """)

            for elder in detailed_elders:
                created_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(elder['created_at']))
                print(f"   {elder['identity_id']}: {elder['name']} (created: {created_date})")

            if role_class_count > 12:
                print(f"\n⚠️ ISSUE DETECTED: Found {role_class_count} tribal elders, expected 12")
                print("   This suggests duplicate or excessive elder creation")

        elif not has_role_class:
            print(f"\n⚠️ SCHEMA ISSUE: Database missing tribal elder columns")
            print("   The identities table doesn't have the expected columns for tribal elders")
            print("   Expected columns: role_class, tribal_affiliation, nation_code")
            print("   This explains why the blockchain explorer shows hardcoded data")

        # Check tribal council configuration
        print(f"\n🏛️ TRIBAL COUNCIL CONFIGURATION:")
        council_config = query_db_one("""
            SELECT value FROM system_config WHERE key = 'tribal_council'
        """)

        if council_config:
            try:
                council_data = json.loads(council_config['value'])
                print(f"   Council members: {council_data.get('total_members', 'Unknown')}")
                print(f"   Total voting weight: {council_data.get('total_voting_weight', 'Unknown')}")
                print(f"   Status: {council_data.get('status', 'Unknown')}")
            except:
                print("   Council configuration exists but is malformed")
        else:
            print("   No tribal council configuration found")

        return {
            'role_class_count': role_class_count,
            'tribal_affiliation_count': tribal_affiliation_count,
            'covenant_count': covenant_count,
            'has_duplicates': len(duplicates) > 0 if role_class_count > 0 else False,
            'has_council_config': council_config is not None,
            'has_role_class': has_role_class,
            'has_tribal_affiliation': has_tribal_affiliation,
            'has_nation_code': has_nation_code,
            'total_identities': total_count
        }

    except Exception as e:
        print(f"❌ Error analyzing tribal elders: {e}")
        return None

def recommend_cleanup(analysis):
    """Provide cleanup recommendations based on analysis."""
    if not analysis:
        return

    print(f"\n💡 CLEANUP RECOMMENDATIONS:")
    print("=" * 50)

    # Check for schema issues first
    if not analysis['has_role_class']:
        print("🚨 CRITICAL SCHEMA ISSUE DETECTED:")
        print("   The database is missing the required columns for tribal elders")
        print("   Current identities table is using an old/different schema")
        print("")
        print("   SOLUTIONS:")
        print("   1. Run database migration to add missing columns:")
        print("      ALTER TABLE identities ADD COLUMN role_class TEXT DEFAULT 'Community_Member';")
        print("      ALTER TABLE identities ADD COLUMN tribal_affiliation TEXT;")
        print("      ALTER TABLE identities ADD COLUMN nation_code TEXT;")
        print("      ALTER TABLE identities ADD COLUMN nation_name TEXT;")
        print("      ALTER TABLE identities ADD COLUMN verification_level INTEGER DEFAULT 0;")
        print("      ALTER TABLE identities ADD COLUMN covenant_accepted BOOLEAN DEFAULT FALSE;")
        print("      ALTER TABLE identities ADD COLUMN vault_status TEXT DEFAULT 'Active';")
        print("      ALTER TABLE identities ADD COLUMN etzem_score INTEGER DEFAULT 0;")
        print("")
        print("   2. OR run a complete database reset and re-initialize with proper schema")
        print("   3. Then run tribal elder registration script")
        print("")
        print("   This explains why the blockchain explorer shows hardcoded data!")
        return

    if analysis['role_class_count'] == 0:
        print("✅ No tribal elders found - database schema is correct but empty")
        print("   Recommendation: Run tribal elder registration script")

    elif analysis['role_class_count'] == 12 and not analysis['has_duplicates']:
        print("✅ Exactly 12 tribal elders found with no duplicates")
        print("   Database appears to be in correct state")

    elif analysis['role_class_count'] > 12:
        print(f"⚠️ Excessive tribal elders found ({analysis['role_class_count']})")
        print("   Recommendations:")
        print("   1. Delete all tribal elders: DELETE FROM identities WHERE role_class = 'Tribal_Elder'")
        print("   2. Clear council config: DELETE FROM system_config WHERE key = 'tribal_council'")
        print("   3. Re-run tribal elder registration script")

    elif analysis['has_duplicates']:
        print("⚠️ Duplicate tribal elders detected")
        print("   Recommendations:")
        print("   1. Keep only the first elder per tribe (by created_at)")
        print("   2. Delete duplicates")
        print("   3. Verify council configuration")

    if not analysis['has_council_config']:
        print("⚠️ No tribal council configuration found")
        print("   Recommendation: Run council formation script")

def main():
    """Main diagnostic function."""
    print("🌟 ONNYX TRIBAL ELDERS DIAGNOSTIC TOOL")
    print("=" * 60)

    analysis = analyze_tribal_elders()
    recommend_cleanup(analysis)

    print(f"\n📋 NEXT STEPS:")
    print("1. Review the analysis above")
    print("2. If cleanup is needed, run the recommended SQL commands")
    print("3. Re-run tribal elder registration if necessary")
    print("4. Verify the blockchain explorer shows correct data")

if __name__ == '__main__':
    main()
