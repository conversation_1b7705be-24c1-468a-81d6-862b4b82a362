{% extends "base.html" %}

{% block title %}The Forgotten Legacy - Reclaim Your Covenant Identity{% endblock %}

{% block description %}Reclaim your covenant identity as an Israelite or Witness Nation. Verify your lineage with the Gate Keepers and inscribe your legacy in the ONNYX blockchain.{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black relative overflow-hidden">
    <!-- Ambient Background Effects -->
    <div class="absolute inset-0 opacity-20">
        <div class="floating-particles"></div>
        <div class="data-streams"></div>
    </div>

    <!-- Progress Indicator - Enhanced with Covenant Path Support -->
    <div class="flex justify-center mb-12 pt-8">
        <div class="glass-card-enhanced px-8 py-6 rounded-3xl shadow-lg shadow-cyber-cyan/10">
            <div class="flex items-center space-x-4">
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-cyber-cyan to-cyber-blue flex items-center justify-center text-onyx-black font-bold text-lg shadow-lg shadow-cyber-cyan/30 animate-pulse">1</div>
                <div class="w-8 h-1 bg-gradient-to-r from-cyber-cyan to-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">2</div>
                <div class="w-8 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">3</div>
                <div class="w-8 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">4</div>
                <div class="w-8 h-1 bg-glass-border rounded-full"></div>
                <!-- Conditional Gate Keeper Verification Step -->
                <div id="gatekeeper-step" class="hidden w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">🗿</div>
                <div id="gatekeeper-divider" class="hidden w-8 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">5</div>
            </div>
            <div class="flex items-center justify-center mt-4">
                <span class="text-sm font-orbitron text-cyber-cyan">Choose Your Covenant Path</span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-xl mx-auto px-6 pt-32 pb-16">
        <div class="max-w-4xl mx-auto">

            <!-- Opening Revelation -->
            <div class="text-center mb-16 fade-in-sequence" data-delay="0">
                <div class="mb-8">
                    <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                        <svg class="w-16 h-16 text-cyber-cyan mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h1 class="text-6xl md:text-7xl font-orbitron font-bold text-cyber-cyan mb-6 glow-text">
                        The Forgotten Legacy
                    </h1>
                    <p class="text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                        "Enquire, I pray thee, of the former age, and prepare thyself to the search of their fathers"
                    </p>
                    <p class="text-lg text-cyber-purple font-orbitron mt-4">
                        — Job 8:8
                    </p>
                </div>
            </div>

            <!-- Narrative Content -->
            <div class="space-y-12">
                <!-- The Awakening -->
                <div class="glass-card-premium p-12 rounded-3xl fade-in-sequence" data-delay="500">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-6">
                                🌅 The Awakening Begins
                            </h2>
                            <div class="space-y-6 text-lg text-text-secondary leading-relaxed">
                                <p>
                                    For generations, the true identity of nations has been <span class="text-cyber-cyan font-semibold">systematically erased</span>.
                                    Ancient bloodlines, forgotten. Sacred covenants, buried beneath layers of deception.
                                </p>
                                <p>
                                    But the blockchain remembers what flesh forgets. Every transaction, every covenant,
                                    every act of righteousness—<span class="text-cyber-purple font-semibold">permanently inscribed</span>
                                    in the immutable ledger of truth.
                                </p>
                                <p class="text-cyber-cyan font-semibold">
                                    You are not here by accident. Your awakening has begun.
                                </p>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="glass-card-enhanced p-8 rounded-2xl text-center">
                                <div class="text-6xl mb-4">🏛️</div>
                                <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-4">
                                    Know Who You Are
                                </h3>
                                <p class="text-text-secondary">
                                    Your identity is not a social construct—it's a <strong>covenant inheritance</strong>
                                    written in the stars and confirmed in the blockchain.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- The Hidden Truth -->
                <div class="glass-card-premium p-12 rounded-3xl fade-in-sequence" data-delay="1000">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-orbitron font-bold text-cyber-purple mb-6">
                            🔍 The Hidden Truth
                        </h2>
                        <p class="text-xl text-text-secondary max-w-3xl mx-auto">
                            "They have said, Come, and let us cut them off from being a nation;
                            that the name of Israel may be no more in remembrance."
                        </p>
                        <p class="text-lg text-cyber-cyan font-orbitron mt-4">
                            — Psalms 83:4
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                        <div class="glass-card-enhanced p-6 rounded-xl text-center">
                            <div class="text-4xl mb-4">📜</div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Ancient Scrolls</h3>
                            <p class="text-text-secondary">
                                Hidden genealogies and tribal records, preserved in sacred texts but
                                <strong>forgotten by the world</strong>.
                            </p>
                        </div>
                        <div class="glass-card-enhanced p-6 rounded-xl text-center">
                            <div class="text-4xl mb-4">🌍</div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Scattered Nations</h3>
                            <p class="text-text-secondary">
                                The twelve tribes dispersed across continents, their true identity
                                <strong>hidden in plain sight</strong>.
                            </p>
                        </div>
                        <div class="glass-card-enhanced p-6 rounded-xl text-center">
                            <div class="text-4xl mb-4">⛓️</div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Broken Chains</h3>
                            <p class="text-text-secondary">
                                Economic slavery through usury and debt, designed to keep the covenant people
                                <strong>in perpetual bondage</strong>.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- The Blockchain Solution -->
                <div class="glass-card-premium p-12 rounded-3xl fade-in-sequence" data-delay="1500">
                    <div class="text-center">
                        <h2 class="text-4xl font-orbitron font-bold text-cyber-green mb-8">
                            ⛓️ The Blockchain Awakening
                        </h2>
                        <div class="max-w-4xl mx-auto">
                            <p class="text-xl text-text-secondary leading-relaxed mb-8">
                                But now, in these last days, the Most High has provided a way to
                                <span class="text-cyber-cyan font-semibold">restore what was lost</span>.
                                Through immutable ledger technology, we can rebuild the covenant economy,
                                reconnect with our true heritage, and establish
                                <span class="text-cyber-green font-semibold">economic sovereignty</span>
                                according to biblical principles.
                            </p>

                            <div class="glass-card-enhanced p-8 rounded-2xl bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10">
                                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">
                                    Know Where Your Value Goes
                                </h3>
                                <p class="text-lg text-text-secondary">
                                    Every token earned, every covenant kept, every act of righteousness—
                                    <strong>transparently recorded</strong> and <strong>fairly distributed</strong>
                                    according to the ancient laws of jubilee, gleaning, and sabbath rest.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Covenant Path Selection -->
            <div class="text-center mt-16 fade-in-sequence" data-delay="2000">
                <div class="glass-card-premium p-12 rounded-3xl max-w-4xl mx-auto">
                    <h3 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-8">
                        Choose Your Covenant Path
                    </h3>
                    <p class="text-xl text-text-secondary mb-12 max-w-3xl mx-auto leading-relaxed">
                        Your journey begins with understanding your place in the covenant. Are you awakening to your 
                        <span class="text-cyber-cyan font-semibold">Israelite heritage</span>, or are you joining as a 
                        <span class="text-cyber-purple font-semibold">Witness Nation</span> ally?
                    </p>

                    <!-- Path Selection Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                        <!-- Israelite Path -->
                        <div class="glass-card-enhanced p-8 rounded-2xl hover:scale-105 transition-all duration-300 group border-2 border-transparent hover:border-cyber-cyan/50">
                            <div class="text-6xl mb-6">🗿</div>
                            <h4 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">
                                Israelite Lineage
                            </h4>
                            <p class="text-text-secondary mb-6 leading-relaxed">
                                Reclaim your true identity as one of the 12 Tribes of Israel. Your lineage will be 
                                verified through the sacred <strong>Gate Keeper Council</strong> before covenant inscription.
                            </p>
                            <div class="bg-cyber-cyan/10 border border-cyber-cyan/20 rounded-lg p-4 mb-6">
                                <p class="text-sm text-cyber-cyan font-semibold">
                                    🗿 Israelite identities require tribal Gate Keeper verification before inscription
                                </p>
                            </div>
                            <button onclick="selectCovenantPath('israelite')" 
                                    class="w-full glass-button-primary px-8 py-4 rounded-xl font-orbitron font-semibold text-lg transition-all duration-300 hover:scale-105 glow-on-hover">
                                <span class="mr-3">⚔️</span>
                                <span>Begin Israelite Journey</span>
                            </button>
                        </div>

                        <!-- Witness Nation Path -->
                        <div class="glass-card-enhanced p-8 rounded-2xl hover:scale-105 transition-all duration-300 group border-2 border-transparent hover:border-cyber-purple/50">
                            <div class="text-6xl mb-6">🌍</div>
                            <h4 class="text-2xl font-orbitron font-bold text-cyber-purple mb-4">
                                Witness Nation
                            </h4>
                            <p class="text-text-secondary mb-6 leading-relaxed">
                                Join as a covenant ally and witness to the restoration. Support the biblical economy 
                                and participate in the <strong>new covenant community</strong>.
                            </p>
                            <div class="bg-cyber-purple/10 border border-cyber-purple/20 rounded-lg p-4 mb-6">
                                <p class="text-sm text-cyber-purple font-semibold">
                                    🌍 Witness Nations proceed directly to covenant inscription
                                </p>
                            </div>
                            <button onclick="selectCovenantPath('witness')" 
                                    class="w-full bg-onyx-black border-2 border-cyber-purple px-8 py-4 rounded-xl font-orbitron font-semibold text-lg text-cyber-purple transition-all duration-300 hover:scale-105 hover:bg-cyber-purple/10 glow-on-hover">
                                <span class="mr-3">🤝</span>
                                <span>Begin Witness Journey</span>
                            </button>
                        </div>
                    </div>

                    <!-- Developer Bypass Section -->
                    <div class="mt-8 mb-8">
                        <button onclick="toggleDeveloperBypass()" 
                                class="text-sm text-gray-400 hover:text-cyber-cyan transition-colors duration-300 px-4 py-2 rounded border border-gray-600 hover:border-cyber-cyan"
                                style="opacity: 0.7;">
                            🔑 Developer Access
                        </button>
                        <div id="developerBypass" class="hidden mt-4 max-w-md mx-auto">
                            <div class="bg-cyber-purple/10 border border-cyber-purple/20 rounded-lg p-6">
                                <h4 class="text-lg font-orbitron font-semibold text-cyber-purple mb-4">
                                    🔑 Developer Bypass
                                </h4>
                                <p class="text-sm text-text-secondary mb-4">
                                    Enter the covenant phrase to access all paths as developer
                                </p>
                                <input type="password" 
                                       id="bypassPassword" 
                                       placeholder="Enter covenant phrase..."
                                       class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-lg text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-cyber-purple focus:border-cyber-purple transition-all duration-300 mb-4">
                                <div class="flex gap-3">
                                    <button onclick="validateBypass('israelite')" 
                                            class="flex-1 px-4 py-3 bg-gradient-to-r from-cyber-cyan to-cyber-blue text-white rounded-lg font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                        🚀 Israelite Developer
                                    </button>
                                    <button onclick="validateBypass('witness')" 
                                            class="flex-1 px-4 py-3 bg-gradient-to-r from-cyber-purple to-cyber-pink text-white rounded-lg font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                                        🚀 Witness Developer
                                    </button>
                                </div>
                                <p class="text-xs text-text-muted mt-2 text-center">
                                    Developer access bypasses all verification requirements
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Context -->
                    <div class="glass-card-enhanced p-6 rounded-xl bg-gradient-to-r from-cyber-cyan/5 to-cyber-purple/5 border border-cyber-cyan/20">
                        <h5 class="text-lg font-orbitron font-semibold text-cyber-cyan mb-3">
                            Understanding Your Path
                        </h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-text-secondary">
                            <div>
                                <p class="mb-2"><strong>Israelite Path:</strong></p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Heritage verification required</li>
                                    <li>Gate Keeper Council approval</li>
                                    <li>Tribal assignment and privileges</li>
                                    <li>Full covenant participation</li>
                                </ul>
                            </div>
                            <div>
                                <p class="mb-2"><strong>Witness Nation Path:</strong></p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Direct covenant inscription</li>
                                    <li>Ally status and benefits</li>
                                    <li>Support covenant economy</li>
                                    <li>Community participation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audio Controls (Optional) -->
<div class="fixed bottom-6 right-6 z-50">
    <div class="glass-card-enhanced p-4 rounded-xl">
        <button id="audioToggle" class="text-cyber-cyan hover:text-cyber-purple transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M9 9a3 3 0 000 6h4v.01M9 15h4v.01"></path>
            </svg>
        </button>
    </div>
</div>

<script>
// Initialize Eden Mode Step 1
document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initializeSequentialAnimations();

    // Setup audio controls
    setupAudioControls();

    // Setup journey progression
    setupJourneyProgression();
});

function initializeSequentialAnimations() {
    const elements = document.querySelectorAll('.fade-in-sequence');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = entry.target.dataset.delay || 0;
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, delay);
            }
        });
    }, { threshold: 0.1 });

    elements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(40px)';
        element.style.transition = 'opacity 1s ease, transform 1s ease';
        observer.observe(element);
    });
}

function setupAudioControls() {
    const audioToggle = document.getElementById('audioToggle');
    let audioEnabled = false;

    audioToggle.addEventListener('click', function() {
        audioEnabled = !audioEnabled;
        // Audio implementation would go here
        console.log('Audio toggled:', audioEnabled);
    });
}

function setupJourneyProgression() {
    // Remove old button handler since we now have path selection
    const beginButton = document.getElementById('beginJourney');
    if (beginButton) {
        beginButton.remove();
    }
}

function selectCovenantPath(pathType) {
    const progressIndicator = document.querySelector('.glass-card-enhanced');
    const gatekeeperStep = document.getElementById('gatekeeper-step');
    const gatekeeperDivider = document.getElementById('gatekeeper-divider');
    
    // Show loading state
    const selectedButton = event.target.closest('button');
    const originalContent = selectedButton.innerHTML;
    selectedButton.innerHTML = '🌟 Awakening...';
    selectedButton.disabled = true;
    
    // Show/hide Gate Keeper verification step based on path
    if (pathType === 'israelite') {
        if (gatekeeperStep) gatekeeperStep.classList.remove('hidden');
        if (gatekeeperDivider) gatekeeperDivider.classList.remove('hidden');
        
        // Update progress indicator text
        const progressText = document.querySelector('.text-sm.font-orbitron.text-cyber-cyan');
        if (progressText) {
            progressText.textContent = 'Israelite Heritage Verification';
        }
        
        // Store path selection
        sessionStorage.setItem('covenantPath', 'israelite');
        sessionStorage.setItem('requiresGateKeeper', 'true');
        
        // Transition to Israelite step 2
        setTimeout(() => {
            window.location.href = '/auth/eden-mode/israelite-step2';
        }, 1500);
        
    } else if (pathType === 'witness') {
        // Hide Gate Keeper step for witness nations
        if (gatekeeperStep) gatekeeperStep.classList.add('hidden');
        if (gatekeeperDivider) gatekeeperDivider.classList.add('hidden');
        
        // Update progress indicator text
        const progressText = document.querySelector('.text-sm.font-orbitron.text-cyber-cyan');
        if (progressText) {
            progressText.textContent = 'Witness Nation Registration';
        }
        
        // Store path selection
        sessionStorage.setItem('covenantPath', 'witness');
        sessionStorage.setItem('requiresGateKeeper', 'false');
        
        // Transition to Witness step 2
        setTimeout(() => {
            window.location.href = '/auth/eden-mode/witness-step2';
        }, 1500);
    }
    
    // Reset button state in case of error
    setTimeout(() => {
        if (selectedButton.disabled) {
            selectedButton.innerHTML = originalContent;
            selectedButton.disabled = false;
        }
    }, 3000);
}

// Toggle developer bypass section
function toggleDeveloperBypass() {
    const bypassSection = document.getElementById('developerBypass');
    if (bypassSection) {
        bypassSection.classList.toggle('hidden');
    }
}

// Validate developer bypass password and redirect
function validateBypass(pathType) {
    const passwordInput = document.getElementById('bypassPassword');
    const enteredPassword = passwordInput.value.trim();
    
    // Simple password check (insecure, for demo purposes)
    if (enteredPassword === 'covenant123') {
        // Bypass verification and proceed to selected path
        sessionStorage.setItem('covenantPath', pathType);
        sessionStorage.setItem('requiresGateKeeper', 'false');
        
        // Redirect to appropriate step 2
        if (pathType === 'israelite') {
            window.location.href = '/auth/eden-mode/israelite-step2';
        } else if (pathType === 'witness') {
            window.location.href = '/auth/eden-mode/witness-step2';
        }
    } else {
        // Invalid password feedback (demo only, no security)
        passwordInput.classList.add('border-red-500');
        setTimeout(() => {
            passwordInput.classList.remove('border-red-500');
        }, 3000);
    }
}

// Developer Bypass Functions
function toggleDeveloperBypass() {
    const bypassSection = document.getElementById('developerBypass');
    bypassSection.classList.toggle('hidden');
    
    // Focus on password input if showing
    if (!bypassSection.classList.contains('hidden')) {
        setTimeout(() => {
            document.getElementById('bypassPassword').focus();
        }, 100);
    }
}

function validateBypass(pathType) {
    const password = document.getElementById('bypassPassword').value.trim();
    const correctPassword = "Israel United In Christ";
    
    if (password === correctPassword) {
        // Store bypass flags
        sessionStorage.setItem('developerBypass', 'true');
        sessionStorage.setItem('bypassType', 'developer');
        sessionStorage.setItem('bypassTimestamp', Date.now().toString());
        sessionStorage.setItem('covenant_path', pathType);
        
        if (pathType === 'israelite') {
            sessionStorage.setItem('skipGateKeeper', 'true');
            sessionStorage.setItem('verificationStatus', 'developer-bypassed');
        } else {
            sessionStorage.setItem('skipNationSelection', 'true');
        }
        
        // Show success message
        const buttons = document.querySelectorAll('button[onclick^="validateBypass"]');
        buttons.forEach(btn => {
            btn.innerHTML = '✅ Activated';
            btn.disabled = true;
            btn.classList.add('bg-green-600');
        });
        
        // Show success overlay
        showBypassSuccess(pathType);
        
    } else {
        // Show error
        const input = document.getElementById('bypassPassword');
        input.style.borderColor = '#ef4444';
        input.style.boxShadow = '0 0 10px rgba(239, 68, 68, 0.3)';
        input.value = '';
        input.placeholder = 'Incorrect phrase - try again';
        
        setTimeout(() => {
            input.style.borderColor = '';
            input.style.boxShadow = '';
            input.placeholder = 'Enter covenant phrase...';
        }, 2000);
    }
}

function showBypassSuccess(pathType) {
    const pathName = pathType === 'israelite' ? 'Israelite' : 'Witness Nation';
    const emoji = pathType === 'israelite' ? '🗿' : '🌍';
    const finalStep = pathType === 'israelite' ? 'israelite-step5' : 'witness-step4';
    
    // Create success overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center';
    overlay.innerHTML = `
        <div class="glass-card-premium p-12 rounded-3xl max-w-lg mx-4 text-center">
            <div class="text-6xl mb-6">${emoji}</div>
            <h3 class="text-3xl font-orbitron font-bold text-cyber-green mb-4">
                ${pathName} Developer Access
            </h3>
            <p class="text-text-secondary mb-6 leading-relaxed">
                Developer mode activated! You have full access to all ${pathName.toLowerCase()} features and can skip all verification requirements.
            </p>
            <div class="bg-cyber-green/10 border border-cyber-green/20 rounded-lg p-4 mb-6">
                <p class="text-sm font-orbitron font-semibold text-cyber-green">
                    ✅ Proceeding directly to covenant inscription
                </p>
            </div>
            <button onclick="proceedWithBypass('${pathType}', '${finalStep}')" 
                    class="px-8 py-3 bg-gradient-to-r from-cyber-green to-cyber-cyan text-white rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                Continue to Registration
            </button>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function proceedWithBypass(pathType, finalStep) {
    // Set default values for developer bypass
    if (pathType === 'israelite') {
        sessionStorage.setItem('selectedTribe', 'Developer');
        sessionStorage.setItem('tribalData', JSON.stringify({
            tribe: 'Developer',
            description: 'Developer access to all tribes',
            symbol: '🔑',
            stone: 'Developer'
        }));
        window.location.href = `/auth/eden-mode/${finalStep}?bypass=developer`;
    } else {
        sessionStorage.setItem('edenMode_selectedNation', 'DEV');
        sessionStorage.setItem('edenMode_selectedNationName', 'Developer Nation');
        sessionStorage.setItem('edenMode_selectedTribe', 'Developer');
        sessionStorage.setItem('edenMode_ancestralGroup', 'Developer');
        window.location.href = `/auth/eden-mode/${finalStep}?bypass=developer`;
    }
}
</script>

<style>
.glow-text {
    text-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(0, 255, 247, 0.4);
}

.floating-particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 247, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(154, 0, 255, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 255, 247, 0.2), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(-10px) translateX(-10px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Enhanced Path Selection Animations */
.covenant-path-card {
    position: relative;
    overflow: hidden;
}

.covenant-path-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 247, 0.1), transparent);
    transition: left 0.5s;
}

.covenant-path-card:hover::before {
    left: 100%;
}

/* Progress Indicator Enhancements */
#gatekeeper-step {
    transition: opacity 0.5s ease, transform 0.5s ease;
}

#gatekeeper-step.hidden {
    opacity: 0;
    transform: scale(0.8);
}

#gatekeeper-divider {
    transition: opacity 0.5s ease, transform 0.5s ease;
}

#gatekeeper-divider.hidden {
    opacity: 0;
    transform: scaleX(0);
}

/* Button Loading States */
button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

button:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}
</style>
{% endblock %}
