#!/usr/bin/env python3
"""
Migrate Database Schema for Tribal Elders
Adds missing columns to the identities table to support tribal elder functionality
"""

import sys
import os
import sqlite3
import time

def migrate_database():
    """Add missing columns to identities table."""
    print("🔧 MIGRATING DATABASE SCHEMA FOR TRIBAL ELDERS")
    print("=" * 60)
    
    # Connect to the main database
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'db', 'db', 'onnyx.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    print(f"📂 Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current schema
        cursor.execute("PRAGMA table_info(identities)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 Current columns: {', '.join(columns)}")
        
        # Define columns to add
        new_columns = [
            ("role_class", "TEXT DEFAULT 'Community_Member'"),
            ("tribal_affiliation", "TEXT"),
            ("nation_code", "TEXT"),
            ("nation_name", "TEXT"),
            ("verification_level", "INTEGER DEFAULT 0"),
            ("covenant_accepted", "BOOLEAN DEFAULT FALSE"),
            ("vault_status", "TEXT DEFAULT 'Active'"),
            ("etzem_score", "INTEGER DEFAULT 0"),
            ("nation_of_origin", "TEXT"),
            ("status", "TEXT DEFAULT 'active'"),
            ("email", "TEXT"),
            ("protection_tier", "TEXT DEFAULT 'Basic'"),
            ("zeman_count", "INTEGER DEFAULT 0")
        ]
        
        # Add missing columns
        added_columns = []
        for col_name, col_def in new_columns:
            if col_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE identities ADD COLUMN {col_name} {col_def}")
                    added_columns.append(col_name)
                    print(f"✅ Added column: {col_name}")
                except Exception as e:
                    print(f"❌ Failed to add {col_name}: {e}")
        
        if added_columns:
            print(f"\n🎉 Successfully added {len(added_columns)} columns")
            
            # Create system_config table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at INTEGER NOT NULL
                )
            """)
            print("✅ Created system_config table")
            
            conn.commit()
            print("✅ Database migration completed")
            
            # Verify the migration
            cursor.execute("PRAGMA table_info(identities)")
            new_columns_list = [col[1] for col in cursor.fetchall()]
            print(f"📋 Updated columns: {', '.join(new_columns_list)}")
            
            return True
        else:
            print("ℹ️ No columns needed to be added")
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    finally:
        conn.close()

def main():
    """Main migration function."""
    print("🌟 ONNYX DATABASE SCHEMA MIGRATION")
    print("=" * 60)
    
    print("This will add missing columns to the identities table to support tribal elders.")
    response = input("Continue with migration? (y/N): ")
    
    if response.lower() != 'y':
        print("Migration cancelled")
        return
    
    if migrate_database():
        print(f"\n📋 NEXT STEPS:")
        print("1. Run tribal elder registration script")
        print("2. Verify the blockchain explorer shows real data")
        print("3. Test the tribal governance system")
    else:
        print(f"\n❌ Migration failed - check errors above")

if __name__ == '__main__':
    main()
