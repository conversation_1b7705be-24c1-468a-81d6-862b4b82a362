/**
 * ONNYX Platform Service Worker
 * Caching strategy for optimal performance
 */

const CACHE_NAME = 'onnyx-v1.0.0';
const STATIC_CACHE = 'onnyx-static-v1.0.0';
const DYNAMIC_CACHE = 'onnyx-dynamic-v1.0.0';

// Resources to cache immediately
const STATIC_ASSETS = [
    '/',
    '/static/css/main.css',
    '/static/js/main.js',
    '/static/js/enhanced-navigation.js',
    '/static/js/performance-optimizer.js',
    '/static/images/onnyx_logo.png',
    '/static/fonts/orbitron-v29-latin-regular.woff2',
    '/static/fonts/orbitron-v29-latin-700.woff2',
    '/static/fonts/orbitron-v29-latin-900.woff2'
];

// Network-first resources (always try network first)
const NETWORK_FIRST = [
    '/api/',
    '/auth/',
    '/dashboard/',
    '/explorer/stats'
];

// Cache-first resources (serve from cache if available)
const CACHE_FIRST = [
    '/static/',
    '/images/',
    '/fonts/'
];

/**
 * Install event - cache static assets
 */
self.addEventListener('install', event => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('📦 Caching static assets...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Failed to cache static assets:', error);
            })
    );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker activated');
                return self.clients.claim();
            })
    );
});

/**
 * Fetch event - implement caching strategies
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip cross-origin requests (except for fonts and images)
    if (url.origin !== location.origin && !isCacheableResource(url)) {
        return;
    }
    
    // Determine caching strategy
    if (isNetworkFirst(request.url)) {
        event.respondWith(networkFirst(request));
    } else if (isCacheFirst(request.url)) {
        event.respondWith(cacheFirst(request));
    } else {
        event.respondWith(staleWhileRevalidate(request));
    }
});

/**
 * Network-first strategy
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('🌐 Network failed, trying cache:', request.url);
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page for navigation requests
        if (request.mode === 'navigate') {
            return caches.match('/offline.html');
        }
        
        throw error;
    }
}

/**
 * Cache-first strategy
 */
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('❌ Failed to fetch resource:', request.url, error);
        throw error;
    }
}

/**
 * Stale-while-revalidate strategy
 */
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(error => {
        console.log('🌐 Network failed for:', request.url);
        return null;
    });
    
    return cachedResponse || fetchPromise;
}

/**
 * Check if URL should use network-first strategy
 */
function isNetworkFirst(url) {
    return NETWORK_FIRST.some(pattern => url.includes(pattern));
}

/**
 * Check if URL should use cache-first strategy
 */
function isCacheFirst(url) {
    return CACHE_FIRST.some(pattern => url.includes(pattern));
}

/**
 * Check if resource is cacheable (fonts, images from CDNs)
 */
function isCacheableResource(url) {
    return url.pathname.match(/\.(woff2?|ttf|eot|png|jpg|jpeg|gif|svg|webp)$/);
}

/**
 * Background sync for offline actions
 */
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        console.log('🔄 Background sync triggered');
        event.waitUntil(doBackgroundSync());
    }
});

/**
 * Perform background sync
 */
async function doBackgroundSync() {
    try {
        // Sync any pending actions when back online
        const pendingActions = await getStoredActions();
        
        for (const action of pendingActions) {
            try {
                await fetch(action.url, action.options);
                await removeStoredAction(action.id);
            } catch (error) {
                console.log('⏳ Action still pending:', action.url);
            }
        }
    } catch (error) {
        console.error('❌ Background sync failed:', error);
    }
}

/**
 * Get stored actions from IndexedDB
 */
async function getStoredActions() {
    // Simplified implementation - in production, use IndexedDB
    return JSON.parse(localStorage.getItem('pendingActions') || '[]');
}

/**
 * Remove stored action
 */
async function removeStoredAction(actionId) {
    const actions = await getStoredActions();
    const filteredActions = actions.filter(action => action.id !== actionId);
    localStorage.setItem('pendingActions', JSON.stringify(filteredActions));
}

/**
 * Push notification handling
 */
self.addEventListener('push', event => {
    if (!event.data) return;
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/static/images/onnyx_logo.png',
        badge: '/static/images/onnyx_badge.png',
        vibrate: [200, 100, 200],
        data: data.data,
        actions: data.actions || []
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

/**
 * Notification click handling
 */
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'open_app') {
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.notification.data && event.notification.data.url) {
        event.waitUntil(
            clients.openWindow(event.notification.data.url)
        );
    }
});

/**
 * Message handling from main thread
 */
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

console.log('🔧 ONNYX Service Worker loaded');
