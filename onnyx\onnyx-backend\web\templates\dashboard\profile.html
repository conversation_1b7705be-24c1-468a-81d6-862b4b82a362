{% extends "base.html" %}

{% block title %}Profile Settings - ONNYX Dashboard{% endblock %}

{% block content %}
<!-- Profile Header Section -->
<section class="section-spacing">
    <div class="container container-lg">
        <div class="text-center mb-16 slide-up">
            <div class="flex justify-center mb-8">
                <div class="glass-card-premium w-16 h-16 rounded-2xl flex flex-center">
                    <svg class="w-8 h-8 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            </div>
            <h1 class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-cyan mb-6">
                Covenant Identity Profile
            </h1>
            <p class="text-xl text-secondary max-w-3xl mx-auto leading-relaxed">
                "But ye are a chosen generation, a royal priesthood, an holy nation, a peculiar people" - 1 Peter 2:9 (KJ<PERSON>)
            </p>
            <div class="mt-4 text-sm text-text-tertiary italic">
                Manage your covenant identity and walk in biblical righteousness
            </div>

            <!-- Profile Status Indicators -->
            <div class="grid grid-4 grid-equal-height mt-12">
                <div class="stat-card">
                    <div class="stat-icon bg-gradient-to-br from-cyber-green to-green-400">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="stat-value text-cyber-green">Verified</div>
                    <div class="stat-label">Identity Status</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon bg-gradient-to-br from-cyber-purple to-cyber-blue">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="stat-value text-cyber-purple">{{ identity.metadata_parsed.get('cipp_tier', 'Tier 0') }}</div>
                    <div class="stat-label">CIPP Level</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon bg-gradient-to-br from-cyber-cyan to-cyber-blue">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-cyan mb-1">{{ identity.metadata_parsed.get('etzem_score', '0') }}</div>
                        <div class="text-sm text-text-tertiary">Etzem Score</div>
                    </div>

                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-yellow mb-1">{{ identity.metadata_parsed.get('biblical_nation', 'Unassigned') }}</div>
                        <div class="text-sm text-text-tertiary">Biblical Nation</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class="pb-32">
        <div class="container-xl mx-auto px-6">
            <div class="max-w-6xl mx-auto space-y-16">

                <!-- Biblical Covenant Economics Section -->
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">📜 Covenant Economics</h2>
                            <p class="text-text-secondary">"And ye shall hallow the fiftieth year, and proclaim liberty throughout all the land" - Leviticus 25:10 (KJV)</p>
                            <div class="mt-2 text-sm text-text-tertiary">Your participation in biblical economic principles</div>
                        </div>
                        <a href="{{ url_for('tokenomics.overview') }}"
                           class="glass-button-enhanced px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            View Covenant Details →
                        </a>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Yovel (Jubilee) Status -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ biblical_data.user_yovel_tokens|round(2) if biblical_data else '0.00' }}</div>
                            <div class="text-sm text-text-secondary mb-3">Yovel (Jubilee) Tokens</div>
                            <div class="text-xs text-text-tertiary mb-2">
                                Cycle {{ biblical_data.current_yovel_cycle if biblical_data else '0' }} •
                                {{ biblical_data.years_until_reset if biblical_data else '0' }} years until liberty
                            </div>
                            <div class="text-xs text-cyber-cyan italic">
                                "Proclaim liberty throughout all the land"
                            </div>
                        </div>

                        <!-- Covenant Faithfulness -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2">{{ identity.metadata_parsed.get('covenant_compliance', '85') }}%</div>
                            <div class="text-sm text-text-secondary mb-3">Covenant Faithfulness</div>
                            <div class="text-xs {{ 'text-cyber-green' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 80 else 'text-cyber-yellow' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 60 else 'text-cyber-red' }} mb-2">
                                {{ 'Faithful Servant' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 80 else 'Growing in Faith' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 60 else 'Seek Righteousness' }}
                            </div>
                            <div class="text-xs text-cyber-purple italic">
                                "Be ye therefore perfect" - Matthew 5:48
                            </div>
                        </div>

                        <!-- Gleaning for the Poor -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ identity.metadata_parsed.get('gleaning_contributions', '12.5') }}</div>
                            <div class="text-sm text-text-secondary mb-3">Gleaning Contributions</div>
                            <div class="text-xs text-text-tertiary mb-2">
                                {{ identity.metadata_parsed.get('gleaning_count', '3') }} acts of charity this season
                            </div>
                            <div class="text-xs text-cyber-green italic">
                                "Leave them for the poor" - Leviticus 19:10
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Biblical Walk & Observance -->
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">✡️ Biblical Walk</h2>
                            <p class="text-text-secondary">"Remember the sabbath day, to keep it holy" - Exodus 20:8 (KJV)</p>
                            <div class="mt-2 text-sm text-text-tertiary">Your observance of biblical commandments and righteous living</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Sabbath Observance -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-purple-600 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2">{{ identity.metadata_parsed.get('sabbath_observance', '92') }}%</div>
                            <div class="text-sm text-text-secondary mb-3">Sabbath Observance</div>
                            <div class="text-xs text-cyber-purple italic">
                                "Six days shalt thou labour"
                            </div>
                        </div>

                        <!-- Feast Day Participation -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-yellow to-yellow-500 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-yellow mb-2">{{ identity.metadata_parsed.get('feast_participation', '7') }}/7</div>
                            <div class="text-sm text-text-secondary mb-3">Feast Days Observed</div>
                            <div class="text-xs text-cyber-yellow italic">
                                "These are the feasts of the LORD"
                            </div>
                        </div>

                        <!-- Tithing & Offerings -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-500 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ identity.metadata_parsed.get('tithe_percentage', '10') }}%</div>
                            <div class="text-sm text-text-secondary mb-3">Faithful Tithing</div>
                            <div class="text-xs text-cyber-green italic">
                                "Bring ye all the tithes"
                            </div>
                        </div>

                        <!-- Righteous Deeds -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyan-500 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ identity.metadata_parsed.get('righteous_deeds', '24') }}</div>
                            <div class="text-sm text-text-secondary mb-3">Righteous Deeds</div>
                            <div class="text-xs text-cyber-cyan italic">
                                "Let your light so shine"
                            </div>
                        </div>
                    </div>

                    <!-- Biblical Principles Progress -->
                    <div class="mt-8 p-6 bg-gradient-to-r from-cyber-purple/10 to-cyber-cyan/10 rounded-xl border border-cyber-purple/30">
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Walk in Righteousness</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-text-secondary">Love thy neighbor</span>
                                    <span class="text-sm text-cyber-cyan">{{ identity.metadata_parsed.get('love_neighbor_score', '88') }}%</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-cyber-cyan to-cyber-purple h-2 rounded-full" style="width: {{ identity.metadata_parsed.get('love_neighbor_score', '88') }}%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-text-secondary">Care for the poor</span>
                                    <span class="text-sm text-cyber-green">{{ identity.metadata_parsed.get('care_poor_score', '76') }}%</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-cyber-green to-green-500 h-2 rounded-full" style="width: {{ identity.metadata_parsed.get('care_poor_score', '76') }}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 text-center">
                            <p class="text-sm text-text-tertiary italic">"But be ye doers of the word, and not hearers only" - James 1:22 (KJV)</p>
                        </div>
                    </div>
                </div>

                <!-- Covenant Identity Information -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">🔐 Covenant Identity</h2>
                            <p class="text-text-secondary">"And I will write upon him the name of my God" - Revelation 3:12 (KJV)</p>
                            <div class="mt-2 text-sm text-text-tertiary">Update your covenant identity information</div>
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('dashboard.update_profile') }}" class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Name Field -->
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-text-secondary mb-3">
                                    Full Name *
                                </label>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ identity.name }}"
                                       required
                                       maxlength="100"
                                       class="w-full h-12 px-6 glass-card border border-glass-border rounded-xl text-white placeholder-text-tertiary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                            </div>

                            <!-- Email Field -->
                            <div class="space-y-2">
                                <label for="email" class="block text-sm font-medium text-text-secondary mb-3">
                                    Email Address *
                                </label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ identity.email }}"
                                       required
                                       maxlength="255"
                                       class="w-full h-12 px-6 glass-card border border-glass-border rounded-xl text-white placeholder-text-tertiary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                            </div>
                        </div>

                            <!-- Read-only Identity Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-700">
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">
                                        Identity ID
                                    </label>
                                    <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300 font-mono text-sm">
                                        {{ identity.identity_id[:16] }}...
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">
                                        Status
                                    </label>
                                    <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ identity.status.title() }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Role Information -->
                            {% if identity.metadata_parsed.get('role') %}
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">
                                    Role
                                </label>
                                <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300">
                                    {{ identity.metadata_parsed.role }}
                                    {% if identity.metadata_parsed.get('genesis_identity') %}
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            Genesis Identity
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-8">
                            <button type="submit"
                                    class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                                Update Identity
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Tribal Heritage & Biblical Lineage -->
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">🏛️ Tribal Heritage</h2>
                            <p class="text-text-secondary">"And the children of Israel shall pitch their tents, every man by his own camp" - Numbers 1:52 (KJV)</p>
                            <div class="mt-2 text-sm text-text-tertiary">Your biblical lineage and tribal identity</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Covenant Tribe -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-blue-600 rounded-2xl flex items-center justify-center">
                                <span class="text-2xl">✡️</span>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ identity.covenant_tribe or 'Unassigned' }}</div>
                            <div class="text-sm text-text-secondary mb-3">Covenant Tribe</div>
                            <div class="text-xs text-cyber-cyan italic">
                                {% if identity.covenant_tribe %}
                                "The tribe of {{ identity.covenant_tribe }}"
                                {% else %}
                                "Seek your inheritance"
                                {% endif %}
                            </div>
                        </div>

                        <!-- Witness Nation -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-purple-600 rounded-2xl flex items-center justify-center">
                                <span class="text-2xl">🌍</span>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2">{{ identity.witness_nation or 'Unassigned' }}</div>
                            <div class="text-sm text-text-secondary mb-3">Witness Nation</div>
                            <div class="text-xs text-cyber-purple italic">
                                "Ye shall be witnesses unto me"
                            </div>
                        </div>

                        <!-- Biblical Role -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-600 rounded-2xl flex items-center justify-center">
                                <span class="text-2xl">👑</span>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ identity.role_class.title() if identity.role_class else 'Citizen' }}</div>
                            <div class="text-sm text-text-secondary mb-3">Covenant Role</div>
                            <div class="text-xs text-cyber-green italic">
                                {% if identity.role_class == 'system_admin' %}
                                "Rulers over thousands"
                                {% elif identity.role_class == 'gate_keeper' %}
                                "Keepers of the gates"
                                {% elif identity.role_class == 'tribal_elder' %}
                                "Elders of the people"
                                {% else %}
                                "A royal priesthood"
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Tribal Blessings -->
                    {% if identity.covenant_tribe %}
                    <div class="mt-8 p-6 bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10 rounded-xl border border-cyber-cyan/30">
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Tribal Blessing</h3>
                        <div class="text-center">
                            <p class="text-text-secondary italic mb-4">
                                {% if identity.covenant_tribe == 'Judah' %}
                                "Judah is a lion's whelp: from the prey, my son, thou art gone up" - Genesis 49:9
                                {% elif identity.covenant_tribe == 'Benjamin' %}
                                "Benjamin shall ravin as a wolf: in the morning he shall devour the prey" - Genesis 49:27
                                {% elif identity.covenant_tribe == 'Levi' %}
                                "Let thy Thummim and thy Urim be with thy holy one" - Deuteronomy 33:8
                                {% elif identity.covenant_tribe == 'Joseph' %}
                                "Joseph is a fruitful bough, even a fruitful bough by a well" - Genesis 49:22
                                {% elif identity.covenant_tribe == 'Reuben' %}
                                "Reuben, thou art my firstborn, my might, and the beginning of my strength" - Genesis 49:3
                                {% elif identity.covenant_tribe == 'Simeon' %}
                                "I will divide them in Jacob, and scatter them in Israel" - Genesis 49:7
                                {% elif identity.covenant_tribe == 'Gad' %}
                                "Gad, a troop shall overcome him: but he shall overcome at the last" - Genesis 49:19
                                {% elif identity.covenant_tribe == 'Asher' %}
                                "Out of Asher his bread shall be fat, and he shall yield royal dainties" - Genesis 49:20
                                {% elif identity.covenant_tribe == 'Naphtali' %}
                                "Naphtali is a hind let loose: he giveth goodly words" - Genesis 49:21
                                {% elif identity.covenant_tribe == 'Zebulun' %}
                                "Zebulun shall dwell at the haven of the sea" - Genesis 49:13
                                {% elif identity.covenant_tribe == 'Issachar' %}
                                "Issachar is a strong ass couching down between two burdens" - Genesis 49:14
                                {% elif identity.covenant_tribe == 'Dan' %}
                                "Dan shall judge his people, as one of the tribes of Israel" - Genesis 49:16
                                {% else %}
                                "Blessed be he that enlargeth Gad" - Deuteronomy 33:20
                                {% endif %}
                            </p>
                            <div class="text-sm text-cyber-cyan">Walk in the blessing of your fathers</div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- CIPP (Covenant Identity Protection Protocol) Settings -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">🛡️ Covenant Protection</h2>
                            <p class="text-text-secondary">"The name of the LORD is a strong tower: the righteous runneth into it, and is safe" - Proverbs 18:10 (KJV)</p>
                            <div class="mt-2 text-sm text-text-tertiary">Covenant Identity Protection Protocol (CIPP) configuration</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full bg-cyber-green animate-pulse"></div>
                            <span class="text-sm font-orbitron text-cyber-green">PROTECTED</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Current CIPP Tier -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-4L21 8.5l-7 7-3-3"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-1">{{ identity.metadata_parsed.get('cipp_tier', 'Tier 0') }}</div>
                            <div class="text-sm text-text-tertiary">Current Tier</div>
                        </div>

                        <!-- Etzem Score -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-1">{{ identity.metadata_parsed.get('etzem_score', '0') }}</div>
                            <div class="text-sm text-text-tertiary">Etzem Score</div>
                        </div>

                        <!-- Biblical Nation -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-lg font-orbitron font-bold text-cyber-yellow mb-1">{{ identity.metadata_parsed.get('biblical_nation', 'Unassigned') }}</div>
                            <div class="text-sm text-text-tertiary">Biblical Nation</div>
                        </div>

                        <!-- Protection Status -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <div class="text-lg font-orbitron font-bold text-cyber-green mb-1">Protected</div>
                            <div class="text-sm text-text-tertiary">Vault Status</div>
                        </div>
                    </div>

                    <!-- CIPP Actions -->
                    <div class="flex flex-wrap gap-4 justify-center">
                        <button class="glass-button-enhanced px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                                onclick="alert('CIPP Tier upgrade coming soon!')">
                            🔺 Upgrade Tier
                        </button>
                        <button class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                                onclick="alert('Covenant Scroll viewer coming soon!')">
                            📜 View Covenant Scroll
                        </button>
                        <button class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                                onclick="alert('Protection Settings coming soon!')">
                            🛡️ Protection Settings
                        </button>
                    </div>
                </div>

                <!-- Mining Preferences -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">⛏️ Mining Preferences</h2>
                            <p class="text-text-secondary">Configure your mining behavior and biblical compliance</p>
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('dashboard.update_mining_preferences') }}" class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Default Mining Intensity -->
                            <div class="space-y-3">
                                <label for="default_intensity" class="block text-sm font-medium text-text-secondary">
                                    Default Mining Intensity
                                </label>
                                <select id="default_intensity" name="default_intensity"
                                        class="w-full h-12 px-6 glass-card border border-glass-border rounded-xl text-white focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                                    <option value="low" {{ 'selected' if identity.metadata_parsed.get('default_mining_intensity') == 'low' else '' }}>Low (Energy Efficient)</option>
                                    <option value="medium" {{ 'selected' if identity.metadata_parsed.get('default_mining_intensity', 'medium') == 'medium' else '' }}>Medium (Balanced)</option>
                                    <option value="high" {{ 'selected' if identity.metadata_parsed.get('default_mining_intensity') == 'high' else '' }}>High (Maximum Performance)</option>
                                </select>
                            </div>

                            <!-- Sabbath Compliance -->
                            <div class="space-y-3">
                                <label class="block text-sm font-medium text-text-secondary">
                                    Sabbath Compliance
                                </label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center space-x-3 cursor-pointer">
                                        <input type="radio" name="sabbath_compliance" value="strict"
                                               {{ 'checked' if identity.metadata_parsed.get('sabbath_compliance', 'strict') == 'strict' else '' }}
                                               class="w-4 h-4 text-cyber-purple bg-transparent border-glass-border focus:ring-cyber-purple focus:ring-2">
                                        <span class="text-white font-orbitron">Strict (Auto-stop)</span>
                                    </label>
                                    <label class="flex items-center space-x-3 cursor-pointer">
                                        <input type="radio" name="sabbath_compliance" value="flexible"
                                               {{ 'checked' if identity.metadata_parsed.get('sabbath_compliance') == 'flexible' else '' }}
                                               class="w-4 h-4 text-cyber-purple bg-transparent border-glass-border focus:ring-cyber-purple focus:ring-2">
                                        <span class="text-white font-orbitron">Flexible</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-Mining Schedule -->
                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-text-secondary">
                                Auto-Mining Schedule
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="weekdays"
                                               {{ 'checked' if 'weekdays' in identity.metadata_parsed.get('mining_schedule', []) else '' }}
                                               class="w-4 h-4 text-cyber-cyan bg-transparent border-glass-border rounded focus:ring-cyber-cyan focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Weekdays</span>
                                    </label>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="weekends"
                                               {{ 'checked' if 'weekends' in identity.metadata_parsed.get('mining_schedule', []) else '' }}
                                               class="w-4 h-4 text-cyber-cyan bg-transparent border-glass-border rounded focus:ring-cyber-cyan focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Weekends</span>
                                    </label>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="sabbath_observer"
                                               {{ 'checked' if identity.metadata_parsed.get('sabbath_observer', False) else '' }}
                                               class="w-4 h-4 text-cyber-purple bg-transparent border-glass-border rounded focus:ring-cyber-purple focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Sabbath Observer</span>
                                    </label>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="yovel_aware"
                                               {{ 'checked' if identity.metadata_parsed.get('yovel_aware', True) else '' }}
                                               class="w-4 h-4 text-cyber-yellow bg-transparent border-glass-border rounded focus:ring-cyber-yellow focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Yovel Aware</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-8">
                            <button type="submit"
                                    class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                                Update Mining Preferences
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Business Profile Card (if user has a Sela) -->
                {% if user_sela %}
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Business Validator Profile
                        </h2>
                        <p class="text-gray-300 mt-2">Manage your business information and services</p>
                    </div>

                    <div class="card-body">
                        <form method="POST" action="{{ url_for('dashboard.update_business_profile') }}" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Business Name -->
                                <div>
                                    <label for="business_name" class="block text-sm font-medium text-gray-300 mb-2">
                                        Business Name *
                                    </label>
                                    <input type="text"
                                           id="business_name"
                                           name="business_name"
                                           value="{{ user_sela.name }}"
                                           required
                                           maxlength="100"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-300 mb-2">
                                        Business Category *
                                    </label>
                                    <select id="category"
                                            name="category"
                                            required
                                            class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                        <option value="">Select Category</option>
                                        <option value="services" {% if user_sela.category == 'services' %}selected{% endif %}>Services</option>
                                        <option value="healthcare" {% if user_sela.category == 'healthcare' %}selected{% endif %}>Healthcare</option>
                                        <option value="technology" {% if user_sela.category == 'technology' %}selected{% endif %}>Technology</option>
                                        <option value="retail" {% if user_sela.category == 'retail' %}selected{% endif %}>Retail</option>
                                        <option value="food" {% if user_sela.category == 'food' %}selected{% endif %}>Food & Beverage</option>
                                        <option value="education" {% if user_sela.category == 'education' %}selected{% endif %}>Education</option>
                                        <option value="finance" {% if user_sela.category == 'finance' %}selected{% endif %}>Finance</option>
                                        <option value="other" {% if user_sela.category == 'other' %}selected{% endif %}>Other</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                                    Business Description
                                </label>
                                <textarea id="description"
                                          name="description"
                                          rows="3"
                                          maxlength="500"
                                          placeholder="Describe your business and what you offer..."
                                          class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">{{ user_sela.description or '' }}</textarea>
                            </div>

                            <!-- Contact Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Phone -->
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">
                                        Phone Number
                                    </label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           value="{{ user_sela.phone or '' }}"
                                           placeholder="(*************"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>

                                <!-- Website -->
                                <div>
                                    <label for="website" class="block text-sm font-medium text-gray-300 mb-2">
                                        Website
                                    </label>
                                    <input type="url"
                                           id="website"
                                           name="website"
                                           value="{{ user_sela.website or '' }}"
                                           placeholder="https://yourbusiness.com"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>
                            </div>

                            <!-- Address -->
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-300 mb-2">
                                    Business Address
                                </label>
                                <input type="text"
                                       id="address"
                                       name="address"
                                       value="{{ user_sela.address or '' }}"
                                       placeholder="123 Business St, City, State 12345"
                                       maxlength="255"
                                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                            </div>

                            <!-- Services -->
                            <div>
                                <label for="services" class="block text-sm font-medium text-gray-300 mb-2">
                                    Services Offered
                                </label>
                                <textarea id="services"
                                          name="services"
                                          rows="3"
                                          maxlength="500"
                                          placeholder="List the services you provide..."
                                          class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">{{ user_sela.services or '' }}</textarea>
                            </div>

                            <!-- Business Stats (Read-only) -->
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t border-gray-700">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyan-400">{{ user_sela.trust_score or 0 }}</div>
                                    <div class="text-sm text-gray-400">Trust Score</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-400">{{ user_sela.mining_tier or 'Basic' }}</div>
                                    <div class="text-sm text-gray-400">Mining Tier</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-400">{{ "%.2f"|format(user_sela.onx_balance or 0) }}</div>
                                    <div class="text-sm text-gray-400">ONX Balance</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-400">{{ user_sela.blocks_mined or 0 }}</div>
                                    <div class="text-sm text-gray-400">Blocks Mined</div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end pt-6">
                                <button type="submit"
                                        class="px-8 py-3 bg-gradient-to-r from-purple-500 to-cyan-600 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-cyan-700 focus:ring-4 focus:ring-purple-400/20 transition-all duration-300 transform hover:scale-105">
                                    Update Business Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                {% endif %}

                <!-- Security Information Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Security Information
                        </h2>
                        <p class="text-gray-300 mt-2">Your cryptographic identity details</p>
                    </div>

                    <div class="card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">
                                    Public Key
                                </label>
                                <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300 font-mono text-sm break-all">
                                    {{ identity.public_key[:64] }}...
                                </div>
                            </div>

                            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <div>
                                        <h4 class="text-yellow-400 font-semibold mb-1">Security Notice</h4>
                                        <p class="text-yellow-200 text-sm">
                                            Your private key is stored securely and cannot be changed through this interface.
                                            Keep your private key file safe as it controls your identity and assets.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
</div>

<!-- Profile Update Success Animation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to form elements
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('transform', 'scale-105');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('transform', 'scale-105');
        });
    });

    // Form validation feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Updating...';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>
{% endblock %}
